{"baoCao": "Report", "chonTieuChiLocChoBaoCao": "Select filter criteria for the report!", "taiVe": "Download", "xemBaoCao": "View report", "nam": "Year", "thang": "Month", "nha": "Home", "chuaGhepAPI": "API unpaired!", "chonDenNgay": "Please select the time To Date!", "chonTuNgay": "Please select the time From Date!", "khoXuat": "Export warehouse", "soPhieu": "Delivery No.", "khoaThucHien": "Executing Department", "khoaVaoVien": "Department of admission", "phongThucHien": "Performance Room", "vuiLongChonPhongThucHien": "Please select the Performance Room", "phongChiDinh": "Indicated room", "phongNguoiBenh": "Patient room", "chonPhongNguoiBenh": "Select patient room", "hienThiDichVuKhongTrung": "Display non-identical service", "chonPhongThucHien": "Please select the Performance Room", "chonPhongChiDinh": "Please select the Indicating Room", "chonKhoaThucHien": "Please select the Executing Department", "chonKhoaChiDinh": "Please select the Indicating Department", "chonTenNb": "Select patient name", "chonNam": "Select year", "chonThang": "Select month", "chonThangNam": "Select month/year", "chonNgay": "Select date", "denNgay": "To date", "denNgayTiepDon": "To reception date", "denNgayTamUng": "To date of advance payment", "denNgayKham": "To examination date", "tuNgay": "From date", "tuNgayKham": "From examination date", "tuNgayTamUng": "From date of advance payment", "tuNgayTiepDon": "From reception date", "vuiLongChonNam": "Please select year!", "vuiLongChonThang": "Please select month!", "vuiLongChonThangNam": "Please select month/year!", "vuiLongChonToaNha": "Please select building!", "bc01": "BC01. Detailed report by patient", "bc02": "BC02. Detailed report by accepted patients", "bc03": "BC03. Statistics on the number of patients by type of service", "bc04": "BC04. Statistics on the number of services by subject", "bc05": "BC05. Detailed report of service reception by room", "bc06": "BC06. Timesheet report of service performance", "bc07": "BC07. Timesheet report of requested service performance", "bc08": "BC08. Report of number of services from E Hospital", "bc09": "BC09. Report of number of services by patient", "bc10": "BC10. Detailed report of service", "bc10_2": "BC10.2 Detailed service report by department", "bc10_3": "BC10.3 Detailed service report by day", "bc11": "BC11. Summary report of service", "bc12": "BC12. Summary report of used Indicated Set", "bc14": "BC14. Timesheet of procedure with allowance", "bc14_1": "BC14.1. Time sheet for Surgery - Surgical procedures are subsidized", "bc15": "BC15. Timesheet of operations with allowance", "bc16": "BC16. Report on surgery - procedure, diagnostic imaging - functional investigation", "bc17": "BC17. Summary of blood receiption by month", "bc18": "BC18. Blood transfusion report by month", "bc19": "BC19. Procedure report of Department of rehabilitation", "bc20": "BC20. Detailed report on inpatient services - service per patient", "bc21": "BC21. Statistical report of meal sets", "bc22": "BC22. Public report of drugs, laboratory and paraclinical tests, and medical equipment", "bc23": "BC23. Payment statistics by working shift", "bc24": "BC24. Statistical Report on examination", "bc25": "BC25. Statistical report on the number of services performed by department", "bc26": "BC26. Summary table of Surgery - Procedure", "bc27": "BC27. Total monthly test report", "boChiDinh": "Indicated Set", "chonBoChiDinh": "Select Indicated Set", "trangThaiRaVien": "Discharge status", "chonTrangThaiRaVien": "Select discharge status", "chonLoaiThoiGian": "Select time", "loaiDichVu": "Type of service", "chonLoaiDichVu": "Select service type", "vuiLongChonLoaiDichVu": "Please select service type", "loaiThoiGian": "Time type", "vuiLongChonLoaiThoiGian": "Please select a time type!", "vuiLongNhapDotPHCN": "Please enter the rehabilitation period", "chonQuy": "Select quarter", "theoThang": "By month", "theoQuy": "By quarter", "quy": "Quarter", "vuiLongChonQuy": "Please select quarter", "khoaNhan": "Delivery Department", "chonKhoXuat": "Select export warehouse", "chonKhoaNhan": "Select Receiving Department", "soPhieuXuat": "Delivery No.", "chonSoPhieuXuat": "Select delivery No.", "nhomTheoBacSy": "Grouped by doctor", "tinhTheoKhoaChiDinh": "Calculating by the Indicating Department", "titleTinhTheoKhoaChiDinh": "For services that do not have a performance room (medicine, supplies, etc.), the executing department = indicating department", "tenHangHoa": "Name of goods", "chonHangHoa": "Select goods", "vuiLongchonHangHoa": "Please select goods", "hienThiTenHangHoaTheoThau": "Display name of goods by the bid", "hoanThanhToanNgoaiTru": "Refund of payment of outpatient services", "gopQuayThu": "Merge cashier counters", "khoNhap": "Import Warehouse", "chonKhoNhap": "Select import warehouse", "khoaXuat": "Export Department", "chonKhoaXuat": "Select export department", "hienThiMaQuanLy": "Display management code", "hienThiTien": "Display amount", "thuKyQuy": "Deposit Collection", "chonKhongTinhTien": "Select No Charge", "tatToanKyQuy": "Settle Deposit", "soPhieuNhap": "Import Note No.", "chonSoPhieuNhap": "Select Import Note No.", "vuiLongChonKhoNhap": "Please select import warehouse", "vuiLongChonKhoXuat": "Please select export warehouse", "k01": "K01. List of input invoices", "k01_1": "K01.1 Detailed report of import by supplier", "k01_2": "K01.2. Import report by goods", "k01_3": "K01.3. Import report by bidding contract", "k02": "K02. Inventory import & export report", "k02_1": "K02.1. Single Inventory import & export report", "k02_2": "K02.2. Detailed inventory import/export report", "k03": "K03. Detailed report of export", "k04": "K04. Stock card report", "k05": "K05. Detailed report of import", "k05_1": "K05.1 Warehouse receipt summary report", "k07": "K07. Minutes of inventory inspection", "k08": "K08. Minutes of import inspection", "k10": "K10. General report of export", "k11": "K11. In-stock report by lot", "k12": "K12. Detailed report on using general goods", "k13": "K13. General report of import", "k14": "K14. Detailed report on exporting outpatient medicine", "k21": "K21. Payment request form", "k24": "K24. Report on the work of the hospital pharmacy department", "k25": "K25. Report of used medication", "k26": "K26. Report antibiotic group", "k27": "K27. Report of the list of medicine returned by patients to the duty medicine cabinet which increases the base of duty medicine cabinets", "kvt01_1": "KVT01.1. Report of import by supplier", "kvt02": "KVT02. Detailed report of high-tech goods use", "kvt03": "KVT03. Report of high-tech goods use", "chonCongTy": "Select company", "chonHopDong": "Select contract", "congTyKsk": "Company having Health Check service", "hopDongKsk": "Health Check contract", "tenBietDuoc": "Brand name of the drug", "chonTenHoatChat": "Select the active ingredient name", "vuiLongChonHopDongKsk": "Please select Health Check contract", "vuiLongChonCongTyKsk": "Please select Company having Health Check service", "hienThiPhanNhomDvKho": "Display subgroup of warehouse service", "pk01": "PK01. Detailed list of patients", "pk02": "PK02. List of patients with appointments", "pk03": "PK03. List of patients with health insurance", "pk04": "PK04. Lits of patients and appointment schedule", "pk06": "PK06. List of patients requiring intensive examination", "ksk01": "KSK01. Report of contract health check results", "ksk01_1": "KSK01.1. Report of contract health check results", "ksk02": "KSK02. Report for Payment of contract health check by company", "ksk04": "KSK04 – List of contract health check patients for blood collection", "ksk05": "KSK05- List of contract health check patients", "ksk13": "KSK13. Payment Report of Contract health check", "ksk15": "KSK15. Report of health check result", "g01": "G01 – Detailed report of service performance by patient", "g02": "G02. Detailed report of service performance", "g03": "G03. Detailed report of service performance by payment collection note", "g04": "G04. General revenue report", "g05": "G05. Report of debt for collection of patients with service package", "g06": "G06. Report of clinics", "g07": "G07. Detailed report of treatment course by patient", "khth01": "KHTH01. Report of specialized activity by the center", "khth02": "KHTH02. Statistics of patients by type of diseases", "khth03": "KHTH03. Statistics of Discharged patients", "khth04": "KHTH04. Report of bed performance", "khth05": "KHTH05. Statistics of patients with level change", "khth06": "KHTH07. Statistics of deaths", "khth07": "KHTH07. Response of patient feedback of patients changing level", "khth08": "KHTH08. Detailed list of discharged patients", "khth08_1": "KHTH08.1 List of discharged patients", "khth09": "KHTH09. Detailed list of patients changing level", "khth10": "KHTH10. Detailed list of deaths", "khth11": "KHTH11. Detailed list of patients with ongoing treatment", "khth12": "KHTH12. Detailed list of admitted patients", "khth12_1": "KHTH12.1 Detailed list of patients admitted to the hospital", "khth13": "KHTH13. List of archive medical records", "khth14": "KHTH14. Infection report by TT54", "khth15": "KHTH15. Detailed list of patients admitted to the ward", "khth16": "KHTH16. Detailed list of patients discharged from the department", "khth17": "KHTH17. Facilities, beds, and healthcare activities", "khth18": "KHTH18. List of certificates for sick leave and social insurance benefits", "khth19": "KHTH19. Statistics of Hospital morbidity and mortality by ICD10", "dd01": "DD01. Handover and signing for meal set", "chonLoai": "Select type", "chonLoaiBuaAn": "Select meal type", "tuTuoi": "From age", "denTuoi": "To age", "nhapTuoi": "Enter age", "chonKhachHang": "Select client", "trangThaiThanhToanGoi": "Payment status of the package", "chonTrangThaiThanhToanGoi": "Select payment status of the package", "noiGioiThieu": "Referral unit", "tenBenhICD10": "Name of disease (ICD-10)", "tenBenhICD": "ICD disease name", "maBenhICD": "ICD disease code", "chonMaBenhICD": "Select ICD disease code", "chonKetQuaDieuTri": "Select treatment outcome", "khoaNB": "Patient department", "benhAn": "Medical record", "phieuThuNb": "Payment collection note of the patient", "chonTenBenh": "Select disease name", "nhomDichVuCap3": "Service group level 3", "chonNhomDichVuCap3": "Select service group level 3", "tc01": "TC01. Summary report of fee collection from patients", "tc02": "TC02. Detailed table of revenue and expenditure by cashier", "tc03": "TC03. Report of receipt usage", "tc04": "TC04. Detailed report on receipt usage", "tc05": "TC05. Report of fee collection by doctor", "tc06": "TC06. Summary of examination and treatment fee paid by patients with health insurance", "tc06_1": "TC06.1. Summary of examination and treatment fees paid by patients without health insurance", "tc06_2": "TC06.2 Summary of medical examination and treatment costs of health insurance participants", "tc07": "TC07. Report of service fee collection", "tc08": "TC08. Report of service fee collection on request", "tc09": "TC09. Report of service fee collection by currency", "tc10": "TC10. Summary report of total reveue subject to Corporate Income Tax", "tc11": "TC11. Sales report", "tc12": "TC12. Monitor the situation of advance payment and examination/ treatment fee of patients", "tc13": "TC13. Report of list of patients with advance payment", "tc14": "TC14. Summary of total advance payment", "tc15": "TC15. Report of collection and return of advance payment", "tc15_1": "TC15.1 Report of Returned deposit", "tc16": "TC16. Report of advance payment and return of advance payment of the patient", "tc17": "TC17. Report of situation monitoring of advance payment by cashier", "tc17_1": "TC17.1. Report of situation monitoring of advance payment in the month", "tc18": "TC18. Report of cummulative surplus of advance payment", "tc18_1": "TC18.1 Report of cummulative surplus of advance payment", "tc20": "TC20. Timesheet of on-request services on normal days", "tc20_1": "TC20.1. Fee division table of health check service on request", "tc21": "TC21. Summary table of the fee collection for examination and treatment service", "tc22": "TC22. Detailed list of the fee collection for examination and treatment service", "tc23": "TC23. Summary table of the fee collection by executing department", "tc24": "TC24. Structure analysis of collection and payment by executing department", "tc25": "TC25.Summary report of money by time", "tc26": "TC26. Report of voucher use", "tc27": "TC27. Summary report of Revenue by subject type", "tc28": "TC28. Detailed report of inpatient treatment fee of discharged patients", "tc29": "TC29. List of patients with unexecuted payment", "tc29_1": "TC29.1 List of patients with executed payment", "tc31": "TC31. Detailed report on sponsorship by sponsor", "tc33": "TC33. Report of outpatient/inpatient/poor patient list requesting payment - Form 80", "tc36": "TC36. Report of settled deposit", "tc37": "TC37. Summary table of fee collection of examination and treatment service", "tc38": "TC38. Detailed report of service revenue by package in the month", "tc39": "TC39. Payment report of pharmacy prescription", "tc40": "TC40. Summary report on revenue and expenditure for 10-day surgery package", "tc41": "TC41. Report of no-charge fee", "tc42": "TC42. Payment report", "tc42_1": "TC42.1 Detailed report of payment", "tc42_2": "TC42.2 Detailed report of payment by cashier", "tc42_3": "TC42.3 Daily report by payment method", "tc46": "TC46 List of patients with health insurance requesting total payment", "tc48": "TC47 Statistical report of prescription by indicating doctor", "tc50": "TC50. Timesheet report of service fee by each doctor", "tc51": "TC51. Summary report of revenue by patient", "chuongTrinh": "Program", "vuiLongChonChuongTrinh": "Please select program", "voucher": "Voucher", "theoThoiGian": "By time", "chonThoiGianThanhToan": "Select payment time", "trangThaiThanhToan": "Payment status", "chonTrangThaiThanhToan": "Select payment status", "chonTrangThaiNb": "Select patient status", "trangThaiThucHienDichVu": "Service performance status", "chonTrangThaiThucHienDichVu": "Select service performance status", "trangThaiDichVu": "Service status", "trangThaiDichVuDaThucHien": "Status of executed services", "soLuongDichVu": "Number of services", "quyenPhieuThu": "Receipt book", "chonQuyenPhieuThu": "Select the receipt book", "loaiHinhThanhToan": "Payment method", "chonLoaiHinhThanhToan": "Select payment method", "quayThu": "Payment collection counter", "loaiLuuTru": "Type of archive", "trangThaiGoi": "Package status", "chonTrangThaiGoi": "Select package status", "doiTuong": "Subject", "chonDoiTuong": "Select subject", "doiTuongKcbNguoiBenh": "Examination & Treatment subject of Patient", "doiTuongKcbTamUng": "Examination & Treatment subject of Patient at advance payment time", "vuiLongChonDoiTuong": "Please select the subject", "vuiLongChonKhoa": "Please select the department!", "vuiLongChonThoiGianThanhToan": "Please select the payment time", "vuiLongChonTrangThaiGoi": "Please select the package status", "doiTuongNguoiBenh": "Type of patients", "chonDoiTuongNguoiBenh": "Select type of patients", "khoaNguoiBenh": "Patient department", "chonKhoaNguoiBenh": "Select patient department", "NBCapCuu": "Emergency patient", "tenDichVu": "Name of service", "chonDichVu": "Select service", "bacSiChiDinh": "Indicated by doctor", "bacSiChiDinhChuyenVien": "Referral indicated by doctor", "chuyenDenVien": "Referred to hospital", "chonNoiChuyenDen": "Select referral location", "theBhyt": "Health insurance card", "vuiLongNhapMaTheBhyt": "Please enter the health insurance code!", "bacSiThucHien": "Peformed by doctor", "sapXepTheoThoiGian": "Sort by time", "bacSiKham": "Examining doctor", "chonBacSi": "Select doctor", "chonBacSiKham": "Select examining physician", "nhomDichVu": "Service group", "chonNhomDichVu": "Select service group", "nhomDichVuCap1": "Service group level 1", "nhomDichVuCap2": "Service group level 2", "khoaChiDinh": "Indicating department", "chonKhoa": "Select department", "khoaTiepDon": "Reception department", "chonKhoaTiepDon": "Select reception department", "doiTuongKcb": "Subject for examination and treatment", "chonDoiTuongKcb": "Select the Subject for examination and treatment", "doiTuongNb": "Type of patients", "trangThaiThanhLyHopDong": "Contract liquidation status", "nhaThuNgan": "Cashier", "chonNhaThuNgan": "Select cashier", "thuNgan": "Cashier", "chonThuNgan": "Select cashier", "vuiLongChonThuNgan": "Please select cashier!", "loaiPhieuThu": "Type of collection note", "chonLoaiPhieuThu": "Select type of collection note", "loaiDoiTuongKCB": "Type of Subject for examination and treatment", "loaiDoiTuong": "Type of Subject", "khachHangCaNhan": "Individual customers", "khachHangDoanhNghiep": "Corporate Clients", "loaiKhachHang": "Type of Clients", "chonLoaiKhachHang": "Select client type", "dichVuThuocGoi": "Service package", "dichVuLe": "Individual service", "chonKho": "Select warehouse", "vuiLongChonKho": "Please select the warehouse", "knt01": "KNT01. Statistical report of sold medicine by doctor", "knt02": "KNT02. Summary report of Pharmacy cashier", "knt03": "KNT03. Detailed table of Pharmacy cashier by day", "knt04": "KNT04. Detailed report of Pharmacy cashier", "knt05": "KNT05. List of summary sales", "knt06": "KNT06. Log book of controlled drugs purchased by clients", "knt07": "KNT07. Log book of stock input/output of controlled medications", "knt08": "KNT08. Report of medicine return to the pharmacy", "knt10": "KNT10. Report exporting warehouse receipt data and push it to pharmacies", "kho": "Warehouse", "coSo": "Facility", "vuiLongChonCoSo": "Please select facility", "ksk12": "KSK12. List of health checkup company by contract status", "pttt01": "PTTT01. List of surgery and procedure", "pttt02": "PTTT02. List of patients approved for operation", "pttt03": "PTTT03. Statistical report of Surgery and procedure", "keVatTuKTC": "Listing of high-tech supplies", "chonVatTuKTC": "Select high-tech supplies", "nguoiBenh": "Patient", "ptvChinh": "Lead Surgeon", "pvt1": "Surgeon 1", "loaiPttt": "Surgery/ Procedure type", "phanLoaiPttt": "Classification of Surgery/ Procedure", "nhomDv": "Service group", "gopDaiHoaDon": "Merge invoice serial numbers", "vuiLongChonTheoThoiGian": "Please select by time", "nguonGioiThieu": "Referral source", "chonNguonGioiThieu": "Select recommendation source", "nguon": "Source", "chonNguon": "Select source", "doiTac": "Partner", "chonDoiTac": "Select partner", "nguoiGioiThieu": "<PERSON><PERSON><PERSON>", "chonNguoiGioiThieu": "Select referrer", "chenhLech": "Gap", "chonGiaTri": "Select value", "nguonNguoiBenh": "Patient source", "chonNguonNguoiBenh": "Select patient source", "nhanVienTiepDon": "Receptionist", "chonNhanVienTiepDon": "Select receptionist", "chiHienThiDichVuKhamVaCacDichVuChiDinhTuKhamBenh": "Only display examination service and indicated service from examination", "theoYeuCau": "On request", "chonYeuCau": "Select request", "thoiGianThucHien": "Performance time", "vuiLongChonThoiGianThucHien": "Please select performance time", "thoiGianXuatBaoCao": "Report export time", "vuiLongChonThoiGianXuatBaoCao": "Please select report export time", "theoDichVu": "By service", "baoCaoThang": "Monthly report", "phieuThanhToanGoi": "Package service payment sheet", "phieuThanhToanLe": "Individual service payment sheet", "hinhThucTrongNgoaiThau": "Type of In-Contract / Out-of-Contract", "chonHinhThucTrongNgoaiThau": "Select Type of In-Contract / Out-of-Contract", "quyetDinhThau": "Bidding decision", "chonQuyetDinhThau": "Select bidding decision", "soHoaDon": "Invoice Number", "chonSoHoaDon": "Select invoice Number", "vuiLongChonKhoTruocKhiChonSoHoaDon": "Please select warehouse before invoice number!", "phanLoaiHangHoa": "Classification of Goods", "chonPhanLoaiHangHoa": "Select classification of Goods", "hienThiNuocSanXuat": "Display the manufacturing country", "hienThiHangSanXuat": "Display manufacturer", "hienThiTenThuNgan": "Display name of cashier", "hienThiNB": "Display patient", "hienThiPhanLoaiHangHoa": "Display classification of Goods", "hienThiNhomThuoc": "Display medicine group", "hienThiNhomDichVuCap2": "Display service group level 2", "hienThiSoQuyetDinh": "Display Decision Number", "hangSanXuat": "Manufacturer", "chonHangSanXuat": "Select manufacturer", "hoiDong": "Council", "chonHoiDong": "Select council", "nhomHangHoa": "Category of Goods", "chonNhomHangHoa": "Select Category of Goods", "chonSoLo": "Select the lot number", "chonSoPhieu": "Select Note Number", "hoaDonBBBG": "Invoice - Minutes of Handover", "chonHoaDonBBBG": "Select Invoice - Minutes of Handover", "loaiVatTu": "Type of material", "chonLoaiVatTu": "Select type of material", "maTheBH": "Insurance card code", "chonMaTheBH": "Select insurance card code", "chonDoiTuongNb": "Select patient subject", "chonNb": "Select patient", "chonPhuongThucThanhToan": "Select payment mode", "huongDieuTriKham": "Direction of treatment for examination", "chonHuongDieuTriKham": "Select Direction of treatment for examination", "chonHuongDieuTri": "Select Direction of treatment", "chonTrangThaiHoaDon": "Select invoice status", "chonNguoiXuatHoaDon": "Select invoice issuer", "loaiThe": "Type of card", "chonLoaiThe": "Select type of card", "noiDangKyBD": "Primary healthcare service establishment", "chonNoiDangKyBD": "Select Primary healthcare service establishment", "thoiGianQuyetToan": "Settlement time", "donGiaBaoHiem": "Non-insured unit price", "chonDonGiaBaoHiem": "Select non-insured unit price", "tuSoHoaDonDenSoHoaDon": "From invoice number to invoice number", "vuiLongChonDayDuTuSoHoaDonDenSoHoaDon": "Please select from invoice number to invoice number", "vuiLongChonDenSoHoaDonLonHonTuSoHoaDon": "Please select to invoice number larger than from invoice number", "khoaHienTai": "Current department", "chonKhoaHienTai": "Select current department", "coTamUng": "With advance payment", "hinhThucThu": "Collection form", "chonHinhThucThu": "Select collection type", "chonTrangThaiPhieuThu": "Select payment collection note status", "soTienTamUngConLai": "Remaining advance payment amount", "tenNb": "Patient name", "k20": "K20. Statistics of Medication usage by patient", "k20_1": "K20.1. Report of executing doctor orders", "loaiKho": "Type of warehouse", "phanLoaiThuoc": "Medicine classification", "chonPhong": "Select room", "chonLoaiKho": "Select warehouse", "phong": "Room", "loaiMauBaoCao": "Type of report form", "tc19": "TC19. Form 19, 20, 21", "thieuThongTinLoaiMauBaoCao": "Missing type of report form information", "thieuThongTinLoaiThoiGian": "Missing type of time information", "hienThiPhieuThuGoi": "Display payment collection note of package", "kvt04": "KVT04. Report of import/export stock of rated supplies", "hienThiNhaCungCap": "Display supplier", "tenHangSanXuat": "Name of manufacturer", "tenNuocSanXuat": "Name of manufacturing country", "tenNhaCungCap": "Name of supplier", "tc34": "TC34. Report of Hospital fee payment", "pvt2": "Surgeon 2", "gayMe01": "Anesthetist 01", "phuGayMe01": "Anesthetist Assistant 01", "yTaDungCu01": "Operating Room nurse 01", "yTaDungCu02": "Operating Room nurse 02", "chayMayChinh": "Surgical Technologist", "phuMayChinh": "Surgical Technologist assistant", "trangThaiHangHoa": "Status of Goods", "thoiGianPttt": "Surgery and procedure time", "locDichVuBaoHiem": "Filter insurance service", "locTrangThaiDichVu": "Filter service status", "locDichVuTheoYeuCau": "Filter on-request service", "loaiGoi": "Type of service package", "hienThiNhomHangHoa": "Display product group", "phongThucHienNgoaiVien": "Out-of-hospital performance room", "quyenSoPhieuThu": "Receipt book", "chonQuyenSoPhieuThu": "Select receipt book", "nhomPhauThuat": "Surgeon group", "chonLoaiDoiTuong": "Select subject type", "khuVuc": "Area", "chonKhuVuc": "Select area", "sttPhieu": "Note Number", "noiDungThanhToan": "Payment content", "chonNoiDungThanhToan": "Select payment content", "trangThaiNbksk": "Status of health check patient", "chonTrangThaiNbksk": "Select status of health check patient", "nguoiDeNghiThanhToan": "Person requesting payment", "chonNguoiDeNghiThanhToan": "Select Person requesting payment", "boPhan": "Department", "chonBoPhan": "Select department", "hienThiChanKy": "Display signature footer of manager of medical supplies and equipments", "k02_3": "K02.3. Detailed report of inventory import/export for multiple warehouses", "theoNgayHoaDon": "By invoice date", "nhomNguoiBenh": "Patient group", "chonNhomNguoiBenh": "Select patient group", "chonCongTyKsk": "Please select Company having Health Check service", "k20_2": "K20.2. Statistics of Medication usage by patient (without daily details)", "k02_4": "K02.4. Report of Inventory import/export (CCD)", "tiem01": "TIEM_01. Summary report of vaccine delivery", "tiem02": "TIEM_02. Statistical report of vaccination data", "tuNgayPhieuXuat": "From invoice export date", "denNgayPhieuXuat": "To invoice export date", "tenVacXin": "Vaccine name", "soPhieuLinh": "Obtainment paper number", "theoThoiGianPhatHanhHoaDon": "By Invoice issuance time", "baoCaoTaiChinh": "Financial report", "baoCaoDichVu": "Service report", "baoCaoPhongKham": "Clinic report", "baoCaoKho": "Warehouse report", "baoCaoKhoVatTuHoaChat": "Report of material/chemical inventory", "baoCaoNhaThuoc": "Medicine and pharmacy report", "baoCaoSucKhoe": "Health check report", "baoCaoGoiLieuTrinh": "Report of Treatment Course Package", "baoCaoKeHoachTongHop": "Report of General plan", "baoCaoPhauThuatThuThuat": "Report of surgeries and procedures", "baoCaoSuatAn": "Report of Meals", "baoCaoTiemChung": "Vaccination report", "k22": "K22. Report of medication usage by month", "hienThiTheoBacSi": "Display by doctor", "baoHiem": "Insurance", "tiemTuNgay": "Injection from date", "tiemDenNgay": "Injection to date", "hienThiQuyetDinhThau": "Display bid decision", "k23": "K23. Report of medication usage by department", "khoNhan": "Receiving warehouse", "inTheoKhoNhan": "Print by receiving warehouse", "tongThu": "Total revenue", "tongChi": "Total expenditure", "inMauThuThuat": "Print the procedure form", "chonMaHoSo": "Select patient file code", "vuiLongChonNguoiBenh": "Please select patient", "loaiKetQuaKham": "Type of examination results", "khoTaiKhoa": "Warehouse at the department", "tenKhoTaiKhoa": "Name of warehouse at the department", "loaiBaoCao": "Report type", "chonLoaiBaoCao": "Select report type", "inTheoPhanNhomThuoc": "Print by drug group", "hienThiTenHangHoaTrungThau": "Displays the name of the winning goods", "vuiLongNhapPhanLoaiPttt": "Please enter plastic surgery category!", "vuiLongChonDenThoiGian": "Please select a time!", "vuiLongChonTuThoiGian": "Please select from time!", "bc02_1": "BC02.1. Detailed report of patients received", "bc11_1": "BC11.1 CLS Service Summary Report", "bc11_2": "BC11.2. Statistical report on number of services by doctor", "bc28": "BC28. Detailed report of patients using the prescription set", "bc29": "BC29. Report on medical examination and treatment data", "bc29_2": "BC29.2. Statistics on the number of patients examined and tested by doctor", "bc30": "BC30. Blood transfusion summary report", "bc31": "BC31. Blood inventory import and export report", "k04_1": "K04.1 Annual warehouse card report", "k04_2": "K04.2 General warehouse card report", "k14_1": "K14.1. Outpatient prescription detail report", "k14_2": "K14.2. Outpatient prescription summary report", "k15": "K15. Monthly inventory report", "k28": "K28. Report on the list of drugs in the department that need to be destroyed", "k30": "K30. List of patients readmitted to hospital within 30 days", "k32": "K32. Report list of patients with drug interactions", "k34": "K34. Report on antibiotic use by department", "k35": "K35. Hospital-wide antibiotic usage report", "k36": "K36. Report on drug use according to bidding", "k39": "K39. Report on Consumables of Medicines/Materials by Department", "k43": "K43. Detailed report of drugs used by the patient", "k44": "K44. Infusion form and drug disclosure", "k45": "K45. Report on list of returned drugs - details", "k46": "K46. Report on list of paid drugs", "k47": "K47. Detailed report of drugs used by department", "k48": "K48. Total output report by warehouse", "k49": "K49. Outpatient drug dispensing summary report", "k50": "K50. Report on drug costs for departments (by date of issuance)", "k51": "K51. Detailed report on outpatient drug dispensing", "k52": "K52. DDD, DOT, LOT analysis report", "k54": "K54. Report on drug consultation for patients", "k55": "K55. Drug use monitoring report", "k56": "K56. Pre-medication reports of clinical departments", "k57": "K57. Report on quantity and cost of outpatient drugs used by inpatients", "k58": "K58. Warehouse export summary report", "k59": "K59. Antibiotic reporting for inpatients and outpatients", "k60": "K60. Outpatient Prescription Dispensing Report", "kvt05": "KVT05. Report on destruction of medical instruments", "kvt06": "KVT06. Material delivery/return report", "kvt07": "KVT07. Report on medical supplies managed at the entire hospital department", "pk07": "PK07. List of patients to be monitored", "pk08": "PK08. Report on the number of medical examinations of patients registered for examination", "pk09": "PK09. List of patients examined by detailed diagnosis", "pk10": "PK10. Statistical report on number of patients by day", "pk11": "PK11. Examination department meeting report", "pk12": "PK12. Daily report according to the handover book", "ksk16": "KSK16. Statistical report on the number of examinations by doctor", "ksk17": "KSK17. Report on payment proposal for contract health examination patients", "ksk18": "KSK18. Detailed report on payment receipts for contract health examination patients", "khth20": "KHTH20. List of patients according to health insurance card expiration date", "khth22": "KHTH22. Statistics on inpatient treatment activities", "khth23": "KHTH23. Report on elderly data", "khth24": "KHTH24. Children's data report", "khth25": "KHTH25. Statistics of surgical activities and procedures", "khth26": "KHTH26. CLS activity statistics report", "dd03": "DD03. Meal payment summary table", "tc01_1": "TC01.1 Report on patient payment collection by QRCODE", "tc01_2": "TC01.2 Hospital fee summary report", "tc12_1": "TC12.1 Summary of patient balance", "tc13_1": "TC13.1 Temporary hospital admission list", "tc15_2": "TC15.2 Report on hospital fees and advances", "tc15_3": "TC15.3 Summary report of library fees and advances by group of objects", "tc15_4": "TC15.4 Summary report of proposal for refund payment", "tc17_2": "TC17.2. Patient advance payment details report", "tc22_1": "TC22.1. Summary report of outpatient service fees", "tc22_2": "TC22.2. Report on consolidated inpatient service fees", "tc28_1": "TC28.1 Report on hospital fees and advances in detail by patient", "tc41_1": "TC41.1. Other source service list report", "tc42_4": "TC42.4 Summary report of revenue and expenditure by cashier", "tc49": "TC49. Detailed report on voucher usage", "tc51_1": "TC51.1. Detailed summary report of revenue by doctor's order", "tc53": "TC53. Report on total costs of patients in treatment departments", "tc54": "TC54. Report on total collection of hospital fees for patients using services of the CNC TYC Treatment Center", "tc54_1": "TC54.1 Detailed Patient Service Report at TYC CNC Treatment Center", "tc55": "TC55. Total hospital fees collected from patients using on-demand services/health insurance (CNC)", "tc55_1": "TC55.1. Total detailed hospital fees collected by patients using on-demand services/health insurance (CNC)", "tc56": "TC56. Revenue report by service", "tc57": "TC57. Summary report on non-treatment service fees", "tc57_1": "TC57.1. Detailed report on non-medical service fees", "tc58": "TC58. Summary report of free patient fees approved by the director", "tc58_1": "TC58.1 Summary report on medical examination and treatment costs for free patients", "tc59": "TC59. Library fee summary report", "tc60": "TC60. Outpatient/Inpatient Revenue Report", "tc61": "TC61. Summary of hospital fees according to doctor's prescription", "tc62": "TC62. Service fee collection summary report", "tc63": "TC63 Technical service report by bed upon request", "tc63_1": "TC63.1 Detailed technical service report by bed upon request", "tc64": "TC64. Bed report on request", "tc64_1": "TC64.1. Bed detail report upon request", "tc66": "TC66. Detailed report on service revenue by patient", "chonDoiTuongKcbNguoiBenh": "Select patient care object", "vuiLongChonMauBaoCao": "Please select a report template", "knt03_1": "KNT03.1. Detailed table of pharmacy revenue by day (by issuance right)", "knt08_1": "KNT08.1 Drug refund report according to VAT", "knt13": "KNT13. General retail list according to VAT", "knt14": "KNT14. Inventory report", "knt15": "KNT15. ​​Pharmacy revenue report", "knt16": "KNT16. Detailed report on pharmacy revenue and expenditure", "knt17": "KNT17. List of customers buying medicine", "knt18": "KNT18. Report late use of drugs", "knt19": "KNT19. Stable drug sales", "knt20": "KNT20. Pharmacy inventory report", "knt21": "KNT21. Pharmacy revenue report by doctor", "trangThaiQR": "QR Status", "chonTrangThaiQR": "Select QR status", "chuaThanhToanChuaDuyet": "Unpaid, unapproved", "chuaThanhToanDaDuyet": "Payment not approved", "daThanhToanChuaDuyet": "Payment not approved", "daThanhToanDaDuyet": "Payment approved", "inTheoGiaNhap": "Print at import price", "k29": "K29. Drug selling price report", "mauInBaoCao": "Report printing template", "mauBaoCaoNoiTru": "Inpatient report form", "mauBaoCaoNgoaiTru": "Outpatient report form", "inTheoMauCuaSo": "Print according to the sample of the department", "hienThiMaAtc": "Show ATC Code", "hienThiDuongDung": "Show Route", "hienThiSoDangKy": "Display Registration Number", "vuiLongChonBaoCao": "Please select a report", "mau": "<PERSON><PERSON>", "goiPhauThuat": "Surgical package", "goiCanThiep": "Intervention Package", "thuNhaThuoc": "Pharmacy revenue", "thuTrongVien": "Autumn in the hospital", "sinhTuNgay": "Born on", "sinhDenNgay": "Born on", "chonSinhTuNgay": "Select date of birth", "chonSinhDenNgay": "Select date of birth", "soNgay": "Number of days", "k31": "K31. Report on the list of warnings about near-expiry drugs", "nhomLoaiPttt": "Surgery and Procedure Type Group", "chon{{field}}": "Select {{field}}", "k33": "K33. Minimum inventory level report", "hangHoaDuoiMucToiThieu": "Subminimum goods", "hangHoaTrenMucToiThieu": "Goods above minimum", "loai": "Type", "theoThoiGianDuyetPhat": "By browsing time", "theoThoiGianThucHien": "According to the implementation time", "ksnk_01": "KSNK_01. List of hospital infections", "ksnk_02": "KSNK_02. Statistical report of microbiological results", "baoCaoKsnk": "Infection control report", "nhomNhiemKhuan": "Infection group", "chonNhomNhiemKhuan": "Select infection group", "chonTrangThaiDieuTra": "Select investigation status", "dichVuDaThanhToan": "Paid Services", "dichVuHoanSauThanhToan": "Post-payment refund service", "thoiGianApDungGoi": "Package application time", "trongVien": "In the hospital", "raVien": "Discharged from hospital", "baoCaoThongTinChungNB": "Patient  General Information Report", "baoCaoTienSuKhamSucKhoe": "Medical history report", "baoCaoKetQuaKhamLamSang": "Clinical examination report", "baoCaoDauHieuSinhTon": "Vital signs report", "baoCaoKetQuaCDHA": "Imaging Diagnosis and Functional Exploration Result Form", "baoCaoTomTatKhuyenNghi": "Summary report recommendations", "phieuKetQuaXetNghiem": "Test results", "phieuKetQuaCDHA_TDCN": "Imaging and Functional Test Result Report", "baoCaoChupChieuXQuang": "X-ray report", "chayMay": "Run", "khongChayMay": "Do not run the machine", "nhomThuoc": "Drug group", "chonNhomThuoc": "Select drug group", "daDongHoSo": "Case closed", "chuaDongHoSo": "File not closed", "timTenNbMaHsMaNb": "Search by patient name, record ID, or patient ID", "bc10_1": "BC10.1. Service detail report", "hienThiPhieuThu0d": "Show 0 VND receipt", "viSinh": "Microbiology", "chonViSinh": "Select microorganisms", "knt11": "KNT11. Reporting adverse drug reactions", "knt12": "KNT12. Retail sales list according to VAT", "trangThaiBaoCao": "Report status", "chonTrangThaiBaoCao": "Select report status", "ngayIn": "Print date", "k37": "K37. Summary report of drug use analysis form", "trangThaiXacNhan": "Confirmation status", "chonTrangThaiXacNhan": "Select confirmation status", "chuaXacNhan": "Not confirmed", "daXacNhan": "Confirmed", "maTuVan": "Consulting code", "nhapMaTuVan": "Enter the consultation code", "inChiTietThuNgan": "Print cashier details", "k38": "K38. Drug use consultation report", "theoKhoaThucHien": "According to the department", "hienThiTheoSLThuCap": "Display by secondary quantity", "phieuThuChi": "Receipt/payment voucher", "nhanVienThuNgan": "Cashier", "hienThiGhiChu": "Show notes", "luongThuNgan": "Cash Flow", "hienThiDvCoPhuThu": "Show services with additional charges", "doiTuongKhamBenh": "Subject of medical examination", "nguoiChiDinh": "Designated Person", "chonNguoiChiDinh": "Select the nominee", "coMienPhi": "Free", "ngoaiTruBaoHiem": "Outpatient insurance", "ngoaiTruDichVu": "Outpatient services", "thuVienPhi": "Library fees", "chiVienPhi": "Hospital expenses", "thuTamUng": "Advance payment", "lao01": "LAO01. Report on the status of registration for treatment of tuberculosis patients", "baoCaoLao": "Tuberculosis Report", "phanLoaiBaoCao": "Report Classification", "ctcl": "CTCL", "yTeCong": "Public health", "yTeTu": "Private health care", "vuiLongChonThoiGianTuNgayDenNgay": "Please select a time from date - to date!", "vuiLongChonPhanLoaiBaoCao": "Please select report category!", "pttt04": "PTTT04. Payment schedule for Surgery - Procedure allowances", "pttt04_5": "PTTT04.5. Summary table of payment for surgery on request", "chuyenKhoa": "Specialty", "pttt04_1": "PTTT04.1. List of distribution of Surgery - Procedure allowances (by specialty)", "pttt04_2": "PTTT04.2. Report on Payment Schedule for Surgery - Procedures (By Service)", "benhNhan": "Patient", "chonBenhNhan": "Select patient", "hienThiGiaNhapThanhTien": "Display input price and total amount", "hienThiGiaBan": "Show selling price", "nhomTheoSoHoaDon": "Group by invoice number", "mauBaoCao": "Report template", "chonMauBaoCao": "Select report template", "mauBaoCaoChung": "General report template", "mauBaoCaoThuocTraTheoDon": "Sample report of paid drugs according to invoice (for BVP)", "baoCaoKhoDinhDuong": "Nutrition Warehouse Report", "kdd01": "KDD01. Report on the import of nutritional products", "kdd02": "KDD02. CPDD report by using unit", "kdd03": "KDD03. Summary table of CPDD payments by implementation date", "kdd04": "KDD04. CPDD warehouse release report by day", "kdd05": "KDD05. Monthly CPDD warehouse release report", "kdd08": "KDD08. Summary table of CPDD payment by payment time", "kdd09": "KDD09. Nutritional product report by prescriber", "kdd10": "KDD10. Daily nutritional product distribution report", "kdd11": "KDD11. Daily nutritional product summary report", "kdd12": "KDD12. Report on handover and receipt of nutritional products", "inMauBaoCao": "Print report template", "mauThuThuat": "Trick sample", "nhomTheoPhanLoaiPTTT": "Group by surgical and procedural classification", "nhomTheoChuyenKhoa": "Group by specialty", "nhanVienChiDinhCPDD": "CPDD Designated Staff", "taiKhoanKeDv": "Service declaration account", "mauBangKeChung": "General schedule template", "mauBangKeNhaThuoc": "Pharmacy list template", "mauNhomTheoNhaCungCap": "Group by supplier template", "kdd07": "KDD07. General import and export inventory report", "donViCungUng": "Supplier", "chonDonViCungUng": "Select supplier", "kdd13": "KDD13. Report on imported nutritional products returned from departments", "kdd14": "KDD14. Detailed payment report of nutritional products", "loaiKe": "Type of chicken", "chonLoaiKe": "Select type of shelf", "vuiLongChonTrangThaiThanhToan": "Please select payment status", "kdd06": "KDD06. Report on nutritional products exported by department", "thanhToan": "Pay", "qt01": "QT01. Patient service deletion log lookup report", "baoCaoQuanTri": "Management report", "qt02": "QT02. Patient data change log lookup report", "nguoiThucHienChinh": "Main performer", "nguoiDocKq": "Reader Result", "loaiThongTinThayDoi": "Type of information changed", "chonLoaiThongTinThayDoi": "Select the type of information changed", "chonHoiDongKiemKe": "Select the audit committee", "hienThiNhomHangHoaCap1": "Show level 1 product groups", "nhomHangHoaCap1": "Level 1 commodity group", "chonNhomHangHoaCap1": "Select level 1 product group", "chuaLinh": "Not received", "k40": "K40. Warehouse-to-warehouse summary report", "k42": "K42. Detailed report of exported drug list", "tuKho": "From stock", "denKho": "To warehouse", "chonTuKho": "Select from stock", "chonDenKho": "Select to warehouse", "tachHoaDonThuong": "Split regular bills", "nhapTenNbMahsMaBaMaNb": "Enter Patient Name, Record ID, Medical Record Code, or Patient ID", "nhomTheoNhaCungCap": "Group by supplier", "thuocNoi": "Internal medicine", "thuocNgoai": "Foreign medicine", "quocGia": "Nation", "chonQuocGia": "Select country", "mienPhi": "Free of charge", "khongMienPhi": "Not free", "chonMienPhi": "Free Select", "doiTuongKCB": "KCB object", "chonDoiTuongKCB": "Patient category", "vuiLongChonLoaiBaoCao": "Please select report type", "tuSoHoaDon": "From the contract number", "nhapTuSoHoaDon": "Enter from invoice number", "denSoHoaDon": "To the contract number", "nhapDenSoHoaDon": "Enter the contract number", "tuSoPhieuThu": "From receipt number", "nhapTuSoPhieuThu": "Enter from receipt number", "denSoPhieuThu": "To receipt number", "nhapDenSoPhieuThu": "Enter the receipt number", "thuocTiem": "Injections", "thuocVienGoi": "Pills, packets", "thuocKhoNhomTheoNhaCungCap": "Warehouse drugs grouped by supplier", "mienPhiGiamDocDuyet": "Is free director review", "nhapMahsMaBa": "Enter record ID, medical record code", "phongNb": "patient  room", "loaiVTYT": "Type of medical equipment", "chonLoaiVTYT": "Select type of medical equipment", "tenVTYT": "Medical facility name", "chonTenVTYT": "Select medical supplies name", "chonPhanLoaiPttt": "Select surgical and procedural classification", "phanLoaiTheoCheDoBqQl": "Classification by BQ, QL mode", "chonPhanLoaiTheoCheDoBqQl": "Select classification by BQ, QL mode", "phanNhomTheoTddl": "Grouping by TDDL", "chonPhanNhomTheoTddl": "Select group by TDDL", "chonTenThuoc": "Select drug name", "vuiLongChonTenThuoc": "Please select drug name", "khongCoTapTinBaoCao": "No report file!", "doiTuongKcbCuaNb": "Patient's health care object", "phanLoaiTheoPLTT22": "Classification according to PL TT22", "chonPhanLoaiTheoPLTT22": "Select classification according to PL TT22", "hienThiCoSoDuoi": "Show base below", "loaiMienPhi": "Free Type", "chonLoaiMienPhi": "Select free type", "hienThiThanhTien": "Show Total Amount", "baoCaoDuyetDuocLamSang": "Clinical pharmacy review report", "chonMaBenhAn": "Select medical record code", "vuiLongChonDoiTuongNB": "Please select patient  object", "ddls05": "DDLS05. Report of patient using antibiotics for more than 10 days", "tachPhieuThuChi": "Split receipts and payments", "thuNgoai": "External collection", "theoNguoiThucHien": "According to the performer", "theoNguoiChiDinh": "By designee", "hdThuong": "Regular contract", "khamSucKhoe": "Health check", "sapXepDSTheoPhong": "Sort list by room", "ddls06": "DDLS06. Inpatient/outpatient clinical pharmacy report", "ddls07": "DDLS07. ABC/VEN Analysis Report", "tongThuocCuaHS": "Total HS drugs", "giaTriDonThuoc": "Prescription value", "nguongTamUng": "Advance threshold", "dangTaiXuongTapTin": "File is downloading, please check notification for download when complete", "theoThoiGianNgayHoaDon": "By invoice date", "daThu": "Collected", "daHoanTamUng": "Advance payment completed", "hoanTienKhiThanhToan": "Cashback on payment", "daHuyTamUng": "Advance Cancelled", "daKe": "Listed", "chuaKe": "Not listed", "thu": "Collect", "chi": "Spend", "thucTeThuChi": "Actual Income - Expenditure", "loaiThuChi": "Type of income and expenditure", "chonLoaiThuChi": "Select Income and Expenditure type", "vuiLongChonLoaiThuChi": "Please select the type of income and expenditure", "tenNguonKhac": "Other source names", "chonNguonKhac": "Select another source", "timTheoTenNb": "Search by patient  Name", "ngayRaVien": "Discharge date", "ngayVaoVien": "Date of admission", "hoTenNb": "patient 's name", "xuatVien": "Discharge", "vuiLongChonTrangThai": "Please select status", "k53": "K53. Detailed report on drug costs for patients in the entire hospital", "hienThiSoPhieu": "Show vote count", "thuKsk": "Health Examination Fee", "ddls08": "DDLS08. Report on patient's antibiotic use date", "nhanFileTuMucThongBao": "Get file from notification", "tuSoHopDong": "From contract number", "nhapTuSoHopDong": "Enter from contract number", "denSoHopDong": "To contract number", "nhapDenSoHopDong": "Enter contract number", "theoThoiGianDangKy": "By registration time", "khoaThuNgan": "Cashier department", "chonKhoaThuNgan": "Select cashier department", "phanTichDDD": "DDD Analysis", "phanTichDOT": "DOT Analysis", "phanTichLOT": "LOT Analysis", "loaiPhanTich": "Analysis type", "chonLoaiPhanTich": "Select analysis type", "phanTichTheoHoatChat": "Analysis by active ingredient", "phanTichTheoNhomThuoc": "Analysis by drug group", "phanTichTheoKhoa": "Analysis by department", "khongBaoGomDvKhamBenh": "Medical examination services not included", "khongBaoGomDvKhamBenhCtMri": "Not including medical examination, CT, MRI services", "tuSoPhieuThuPhaiNhoHonHoacBangDenSoPhieuThu": "From receipt number must be less than or equal to receipt number", "thoiGianKetLuanKham": "Examination conclusion time", "nhomTheoNgay": "Group by date", "mauBaoCaoTheoNgayKichPhat": "Report template by trigger date", "thuocThau": "Tender medicine", "chonThuocThau": "Select the drug", "chonHopDongKsk": "Select health examination contract", "loaiPhieuThuKsk": "Type of health examination receipt", "chonLoaiPhieuThuKsk": "Select type of health examination receipt", "vuiLongChonLoaiPhieuThuKsk": "Please select the type of health examination receipt!", "nbTuThanhToan": "patient  self-pay", "nbThanhToanTheoHopDong": "patient  pays according to contract", "chiDinhTu": "Specify from", "chonChiDinhTu": "Select the word designation", "chiDinhVaoPhongKham": "Appointment to clinic", "chiDinhTuTiepDon": "Receptionist designation", "tachThuocTiemVien": "Separate injections and tablets", "tachThuocInsulin": "Insulin separation", "locHienThiDvDaHoan": "Filter to display completed services", "khongHienThiDvDaHoan": "Do not show completed service", "coHienThiDvDaHoan": "Show completed service", "ngayTonKho": "Inventory days", "chonNgayTonKho": "Select inventory date", "vuiLongChonNgayTonKho": "Please select inventory date!", "chonThau": "Select a bid", "theoThoiGianTongKetThanhToan": "According to payment summary time", "trangThaiTongKetThanhToan": "Payment summary status", "vien": "<PERSON>ll", "tiem": "Injection", "nhomDonVi": "Unit group", "chonNhomDonVi": "Select unit group", "nuocSanXuat": "Country of origin", "chonNuocSanXuat": "Select country of origin", "chiaTheoThuNgan": "Divide by cashier", "chiaTheoNhomLoaiDoiTuong": "Divide by object type", "theoThoiGianTaoHoSoGiamDinhXml": "Over time XML audit record creation", "vuiLongChonNha": "Please select a home!", "nhaThucHien": "The implementer", "chonNhaThucHien": "Choose the implementer", "hienThiTongSlXuatBnTuTruc": "Display Total Patient Discharges and Duty Cabinet", "hienThiThanhTienXuatNbTuTruc": "Display as patient  cash, direct cabinet", "chonQuayThu": "Select counter", "chonNhomLoaiDoiTuong": "Select object type group", "khoaKhamBenhThuong": "General examination department", "khoaKhamBenhTheoYeuCau": "On-demand medical examination department", "thoiGianDuyetPhat": "Broadcast time", "doiTuongKcbNB": "Subject of medical examination and treatment", "chonDoiTuongKcbNB": "Select the KCB patient  object", "chonDoiTuongKcbTamUng": "Select the subject of temporary medical examination and treatment", "tc13_2": "TC13.2 List of hospital discharge reimbursement expenses", "tc68": "TC68. Report on Cancellation Voucher List", "chonTrangThaiDon": "Select single status", "nhanVienThu": "Collection staff", "nhanVienHuy": "Staff cancel", "phieuHuyTamUng": "Advance cancellation form", "tuNgayHoanUng": "From the date of refund", "denNgayHoanUng": "To the date of refund", "bc32": "BC32. Revenue report by designated department", "bc33": "BC33. Report on the actual time of technical service execution", "bc34": "BC34. Report on examination time statistics", "bc35": "BC35. Report on waiting time for the Outpatient Department", "bc36": "BC36. Bed revenue report required", "bc37": "BC37. Summary report of services as requested by the designated department.", "k04_3": "K04.3 Regular warehouse report", "k04_4": "K04.4 Summary report on drug inventory", "k61": "K61. Summary report of used medication costs", "k62": "K62. Summary report of supplier return slips", "k63": "K63. Daily Drug Inspection Report", "k64": "K64. Drug cancellation report", "k65": "K65. Summary report of inventory by batch, date needed to be locked.", "k66": "K66. Report the list of goods that have not been returned.", "k67": "K67. Report the list of individual goods that have not been issued a receipt, returned.", "pk13": "PK13. Summary report of the number by examining doctor", "ksk19": "KSK19. Acceptance report of the delegation", "khth21": "KHTH21. Meeting activity report", "khth27": "KHTH27. Report on Medical Examination and Treatment Activities", "tc01_3": "TC01.3. Report the list of patients for refunds according to the bank", "tc01_4": "TC01.4. Summary report of clinic payment receipts", "tc01_5": "TC01.5. Clinic revenue report", "tc01_6": "TC01.6. Report Completion Receipt", "tc01_7": "TC01.7. Report of payment receipt for Health Insurance clinic", "tc01_8": "TC01.8. Payment receipt report for hospital discharge", "tc01_9": "TC01.9. Summary of expenditure table", "tc01_10": "TC01.10. Patient payment report via pharmacy QRCODE", "tc01_11": "TC01.11. Outpatient summary report", "tc01_12": "TC01.12. Summary report of the clinic's operation", "tc03_2": "TC03.2 Summary report of invoices", "tc13_3": "TC13.3 Detailed report on deposit collection", "tc15_5": "TC15.5 Summary report of refunds by employee", "tc16_1": "TC16.1 Report on the number of temporary holds", "tc17_3": "TC17.3. Report on outstanding advances by date", "tc21_1": "TC21.1 Service Payment Report", "tc22_3": "TC22.3. Detailed service fee report", "tc29_2": "TC29.2 Report the list of files not yet submitted for final settlement.", "tc67": "TC67. Payment receipt for medical fees", "tc67_1": "TC67.1. Patient payment receipt table", "tc67_2": "TC67.2. Summary table of outpatient/inpatient refund/cancellation receipts", "tc69": "TC69. Report the list of exemptions and reductions", "tc69_1": "TC69.1 Report the list of patient  exempted according to the exemption payment method", "tc69_2": "TC69.2. List of patients exempt from hospital fees", "tc70": "TC70. Summary report of revenue by department", "tc71": "TC71. Report for health insurance exposure approval by cashier staff", "knt22": "KNT22. Summary report of pharmacy revenue collection from patients.", "knt23": "KNT23. Detailed report of pharmacy services", "pttt05": "PTTT05. List of payment for doctors participating in the surgery.", "pttt06": "PTTT06. Report on payment to doctors according to the requested package.", "k02_5": "K02.5. Inventory report of individual items for the entire hospital", "k02_6": "K02.6. Report on inventory import and export for the entire hospital", "tiem03": "TIEM03. Vaccination report", "tiem04": "TIEM04. Report on the situation of vaccine usage for service immunization.", "tiem05": "TIEM05. Report on the results of vaccination services", "qtbh13": "QTBH13. Report the list of beneficiaries who have not created settlement files.", "baoCaoQuyetToanBaoHiem": "Insurance settlement report", "ddls09": "DDLS09. Average drug cost report by number of treatment days", "ddls10": "DDLS10. Report the average total medication cost of the patient in the department/entire treatment course.", "hienThiTenTrungThau": "Display winning bidder name", "hienThiSoVisa": "Display visa number", "hienThiHanSuDung": "Display expiration date", "trangThaiChotSo": "Closing status", "chonTrangThaiChotSo": "Select closing status", "daChotSo": "Closed the books.", "chuaChotSo": "Not yet closed the books.", "vuiLongChonNhaThuNgan": "Please select the cashier!", "bienLaiThuPhi": "Receipt for fee collection", "hoaDonThuPhi": "Invoice for fee collection", "theoThoiGianChotSo": "According to the closing time", "theoThoiGianThanhToan": "According to the payment time", "doiTuongThuoc": "Drug target", "chonDoiTuongThuoc": "Select the drug subject", "khongBaoHiem": "No insurance", "inTheoSoLuongTon": "In the inventory quantity", "doiTuongKcbTheoPhieuTamUng": "Subject of medical examination and treatment according to the advance payment voucher", "chonDoiTuongKcbTheoPhieuTamUng": "Select the medical examination and treatment subject according to the advance payment voucher", "theoThoiGianPhieuHoanThanhToan": "According to the time of the payment completion voucher", "hienThiNhomThuocCap1": "Display group of level 1 drugs", "hienThiThuocHetHsd": "Display expired medication", "trangThaiPhieuTamUng": "Status of the advance payment voucher", "chonTrangThaiPhieuTamUng": "Select the status of the advance payment voucher.", "daThuTamUng": "Advance payment has been collected.", "daHoanTamUngKhiThanhToan": "The advance payment has been settled upon payment.", "daThuTamUngXa": "Advance payment has been collected.", "daHoanTamUngXa": "The advance payment has been completed.", "theoSoPhieuXuat": "According to the number of export invoices", "theoNguoiBenh": "According to the patient", "phanNhomNhoTheoTDDL": "Small grouping according to TDDL", "chonPhanNhomNhoTheoTDDL": "Select subgroups according to TDDL.", "theoBacSi": "According to the doctor", "tongTamUng": "Total advance payment", "hienThiPhanLoaiThuoc": "Display drug classification", "tachBaoCaoTheoNgay": "Report breakdown by date", "khoThuoc": "Medicine cabinet", "hienThiDieuDuongThucHien": "Display nursing performed.", "hienThiDonGia": "Display unit price", "theoThoiGianXuatHoaDon": "According to the invoice issuance time", "chonChiNhanh": "Select branch", "vuiLongChonChiNhanh": "Please select a branch.", "vuiLongChonLoaiHinhThanhToan": "Please select a payment method.", "gopSlDv": "Merge service quantity", "theoKhoa": "According to the science", "theoDotDieuTri": "According to the treatment course", "chonLoaiPhuCap": "Choose the type of allowance.", "phanNhom": "Grouping", "khamXN": "Examination + Test", "khamXNCDHA": "Examination + Tests + Imaging", "khamXNCDHATDCN": "Examination + Testing + Imaging + Nuclear Medicine", "mauBaoCaoNhomTheoNgay": "Daily group report template", "coSoChiNhanh": "Branch - subsidiary", "xntTongHop": "XNT synthesis", "xntTheoNcc": "XNT according to NCC", "xntChiTiet": "XNT details", "mauBaoCaoNhomTheoThang": "Monthly group report template", "gopTheoThuNgan": "Consolidate by cashier", "hienThiKhoaChiDinh": "Display assigned department", "hienThiTenNguoiDuyet": "Display the name of the reviewer", "dichVuKhongHuongBH": "Non-insured services", "m01MauChung": "M01_Common template", "m02dktd": "M02_DKTĐ", "m03DanhChoKhoTong": "M03_For the main warehouse", "m04DanhSachTongKeDuoc": "M04_Drug statistics list", "m05DanhChoKhoNoiTru": "M05_For Inpatient Department", "m02dktdNhaThuoc": "M02_DKTD_Pharmacy", "trangThaiHoanThanhToan": "Payment completion status", "chonTrangThaiHoanThanhToan": "Select payment completion status", "loaiHoanThanhToan": "Payment type", "chonLoaiHoanThanhToan": "Select the type of payment completion.", "gopThuoc": "Combine medicine", "hienThiCoSoTren": "Display the base above.", "gopSoLuongDv": "Combine the quantity of services.", "trangThaiTaoHoSoQuyetToan": "Status of creating the settlement profile", "trangThaiGuiHoSoQuyetToan": "Status of the settlement file submission", "chuaTao": "Not created", "daTao": "Created", "baoCaoTheKho": "Inventory report", "baoCaoXuatNhapTon": "Inventory Report", "loaiHangHoa": "Type of goods", "chonLoaiHangHoa": "Choose the type of goods.", "nhomTheoHangHoa": "Group by goods", "chonLoaiNguonTaiTro": "Select the type of funding source", "vuiLongChonKhoXuatHoacKhoaNhan": "Please select the Export Warehouse or Receiving Department!", "vuiLongChonKhoNhapHoacKhoaXuat": "Please select the Import Warehouse or Export Department!", "trangThaiTiemVacxin": "Vaccine injection status", "chonTrangThaiTiemVacxin": "Choose vaccination status", "xemTheoKhoaNb": "View by patient  department", "hetTon": "End of existence", "sapHetTon": "Almost out of stock.", "nguyCoSapHetTon": "Risk of running out of stock.", "chonCanhBao": "Select warning", "mauBinhThuong": "Normal sample", "mauChuyenKhoa": "Specialty model", "khoaLamSangTraVe": "Clinical department returns", "linhTuKhoChan": "Receive from the even inventory.", "coSoKCB": "Healthcare facility", "chonCoSoKCB": "Choose a medical examination and treatment facility.", "hienThiTongTonCuoiKy": "Display total ending inventory", "chonMacMoi": "Choose new earrings", "daTaoPhieuLinhTra": "Created receipt, returned.", "chonDaTaoPhieuLinhTra": "Select has created a receipt, return", "theoThoiGianRaVien": "According to the discharge time", "daTaoPhieu": "Created a receipt.", "chuaTaoPhieu": "Not yet created a receipt.", "theoThoiGianThu": "According to the autumn time", "theoThoiGianTao": "According to creation time", "loaiHoaDon": "Invoice type", "chonLoaiHoaDon": "Select invoice type", "gopDong": "Merge line", "nhomTheoLoaiPttt": "Group by payment method", "nhomDvKhoCap1": "Level 1 warehouse service group", "chonNhomDvKhoCap1": "Select Level 1 warehouse service group", "daXacNhanBhyt": "Health insurance has been confirmed", "chuaXacNhanBhyt": "Not yet confirmed health insurance.", "sapXepTheoMaHangHoa": "Sort by Item Code", "sapXepTheoTenMoiThau": "Sort by <PERSON><PERSON>der Name", "tongHoanUng": "Total refund", "baoCaoTongHop": "Summary report", "baoCaoChiTiet": "Detailed report", "nhapSoHoaDonFormat": "Enter invoice number (e.g.: 1-3,6)", "vuiLongChonTrangThaiPhieuTamUng": "Please select the status of the advance payment voucher", "hienThiDonGiaThanhTien": "Display Unit Price, Total Amount", "baoCaoHoan": "Refund report", "xoaBaoCaoThanhCong": "Delete report successful", "bc38": "BC38. Detailed report of test results", "bc39": "BC39. Summary report of expenditures by type of subject", "bc40": "BC40. Report on Payment for Service Providers", "k07_1": "K07.1 Actual Inventory Report", "k68": "K68. Detailed inventory report by date", "k69": "K69. Detailed department return report", "k70": "K70. Report statistics of patients not receiving prescriptions.", "k71": "K71. Medication Cancellation Record", "pk14": "PK14. Patient Visit Report", "pk15": "PK15. Customer report", "khth08_2": "KHTH08.2 Detailed list of discharged patients", "khth29": "KHTH29. Statistical Report on Outpatient Examination Activities", "khth30": "KHTH30. Injury Accident Report", "khth31": "KHTH31. List of delivered mothers", "tc01_13": "TC01.13. Summary report of inpatient medical fee payments", "tc01_14": "TC01.14. Summary report of consultation and laboratory test revenue for the clinic.", "tc02_1": "TC02.1 Summary Report of Invoices", "tc02_2": "TC02.2 Detailed Payment Invoice Report", "tc06_4": "TC06.4. Detailed report of hospital fee payments by service group", "tc06_5": "TC06.5. Summary report of hospital payment by day", "tc14_1": "TC14.1 Advance payment report", "tc18_2": "TC18.2. Report on advance payment balance", "tc21_2": "TC21.2 Detailed report on service payment", "tc21_3": "TC21.3 Report on service fee discrepancies NG and TG", "tc59_1": "TC59.1 Summary report of hospital fee payments by service group", "tc64_2": "TC64.2. Summary report on the expenditure for selected bed services.", "knt24": "KNT24. Prescription drug sales tracking log", "knt25": "KNT25. VAT inventory report by day", "knt26": "KNT26. Retail prescription list", "chonCoSo": "Select facility", "pttt04_3": "PTTT04.3. Summary table of surgical procedures and interventions", "pttt04_4": "PTTT04.4. Summary table of surgical expenses by date", "k02_7": "K02.7. Report on inventory in and out by size", "k02_8": "K02.8. Report on inventory in and out for the entire hospital", "chonNhanVienThuNgan": "Select the billing staff", "qt03": "QT03. Report on employee software usage time", "chonDoiTuongKcbCuaNb": "Select the patient's healthcare object.", "m02pstwXNTChiTiet": "M02_PSTW_Detailed Inventory Management", "m08pshnXNT": "M08_PSHN_Inventory Management", "baoCaoChiTietTheoNgay": "Detailed report by date", "loaiDichVuKho": "Warehouse service type", "chonLoaiDichVuKho": "Select the type of warehouse service.", "chonLyDoTamUng": "Select the reason for the advance payment.", "hienThiThuChi": "Display income and expenses", "chonHienThiThuChi": "Select display of income and expenses", "inTheoMauChiTiet": "In the detailed template", "dvKhongDuocHuongBh": "DV is not covered by insurance.", "dvDuocHuongBh": "DV is entitled to insurance.", "chonMucHuong": "Select benefit level", "mauBaoCaoDoanhThu": "Revenue report template", "mauDsThongKeSoCaDvThucHien": "Statistical report form of the number of services performed", "sapXepTheoDvt": "Sort by unit of measure", "maBenhTongHop": "Composite disease code", "chonMaBenhTongHop": "Select the comprehensive disease code.", "chonChiSoCon": "Select the sub-indicator", "giaTriChiSoCon": "Child index value", "nhapGiaTriChiSoCon": "Enter the value of the sub-indicator", "nhapMaTheBH": "Enter insurance card code", "baoCaoTongHopChiTheoLoaiDoiTuong": "Summary report of expenses by type of beneficiary", "baoCaoTongHopChiTien": "Consolidated expenditure report", "hienThiNhomLoaiDoiTuong": "Display the group of payer types", "hienThiTenDichVu": "Display service name", "sapXepTheoTenNb": "Sort by Patient Name", "theoVaiTro": "According to the role", "theoNhanVien": "According to the staff", "mauChiTietDv": "Detailed service form", "inTheoKhoa": "In the department of theory", "hienThiKhoaThucHien": "Display performing department", "tyLeThanhToan": "Payment rate", "chonTyLeThanhToan": "Select payment rate.", "khoaQuanLyNhanVien": "Employee Management Department", "chonKhoaQuanLyNhanVien": "Select the department for employee management", "sldd01": "SLDD01. Report on the nutritional assessment results of patients upon admission to the department.", "ketQuaDanhGiaTinhTrangDanhGiaDd": "Assessment results of nutritional status evaluation.", "baoCaoSangLocDinhDuong": "Nutritional screening report", "sldd02": "SLDD02. Report on nutritional assessment results for patients upon hospital admission.", "inChiTiet": "In detail", "nhomTheoTenHoatChat": "Group by active ingredient", "taiTroBaoHiemBaoLanh": "Sponsorship - Insurance guarantee", "chonTrangThaiHoan": "Select refund status", "inTungNhanVienThuNgan": "In each billing staff member", "mauNgoaiTru": "Outpatient Form", "mauNoiTru": "Inpatient Form", "hoNgheo": "Poor Households", "mucDichSuDung": "Purpose of use", "chonMucDichSuDung": "Select purpose of use", "tongHopThuChi": "Revenue and expenditure summary", "soGioDieuTri": "Treatment hours", "nhapSoGioDieuTri": "Enter the number of treatment hours", "hienThiNhomLoaiBenhAn": "Display the number of new medical records by Medical Record Type Group.", "hienThiVat": "Display VAT", "tuCanNangCon": "From the weight of the child", "nhapTuCanNangCon": "Enter the child's weight", "denCanNangCon": "To the child's weight", "nhapDenCanNangCon": "Enter the child's weight", "hienThiPhongTheoKhoa": "Display rooms by department", "bangKeDonThuocBanLe": "Retail prescription invoice", "tongHopThanhToan": "Payment summary", "sapXepTheoMaDichVu": "Sort by Service Code", "baoCaoThuChiTiet": "Detailed revenue report", "tu0Den6Tuoi": "0 - 6 years old", "lonHon6Tuoi": "> 6 years old", "chonTuoi": "Select age", "theoThuNgan": "According to the cashier", "chonNgoiThai": "Choose the fetus position", "sldd03": "SLDD03. Report on the Nutritional Assessment Rate of Patients upon Admission", "sldd05": "SLDD05. Report on results of screening, assessment and nutritional intervention in inpatients", "sldd06": "SLDD06. Nutrition Screened Patient Detail Report", "baoCaoChenhLechHoan": "Reconciliation report", "nhomTheoMaPhieuLinh": "Group by voucher code", "xuatKhoaPhong": "Discharge from the department", "hienThiPhanNhomThuoc": "Display Drug Classification", "k02PVP": "K02_BVP", "hienThiTatCaHangHoaTrongThau": "Show all goods in bid", "trangThaiDichVuKham": "Examination service status", "chonTrangThaiDichVuKham": "Select examination service status", "nhomTheoNhanVien": "Detailed group by Employee/group/department/team receiving allowance", "khoaTamUng": "Department of Advance", "chonKhoaTamUng": "Choose department of advance", "sapXepTheoSoPhieu": "Sort by Number of votes", "hienThiMaHoSoTenNguoiBenh": "Show patient's record code/name", "chiDinhTrong": "Specify in", "chooseChiDinhTrong": "Choose specify in", "theoSlThuCap": "Print according to secondary units", "khoaLamSangLoc": "Scale making department", "phongKhamThucHien": "Clinic performing", "hienThiGioAn": "Show meal times", "noiBo": "Private", "nhomTheoViTriQuyChe": "Group by position regulations", "loaiXemSoLuongDichVu": "Service Quantity View Type", "chonLoaiXemSoLuongDichVu": "Select type to view quantity of service", "thanhTienDichVu": "Service fee"}