import React, {
  useState,
  forwardRef,
  useEffect,
  useMemo,
  useRef,
  useImperativeHandle,
  memo,
} from "react";
import { Main, TrangThaiDiv } from "./styled";
import { useDispatch } from "react-redux";
import { message } from "antd";
import {
  Checkbox,
  TableWrapper,
  <PERSON><PERSON>,
  HeaderSearch,
  Tooltip,
  InputTimeout,
  Select,
} from "components";
import printProvider, { printJS } from "data-access/print-provider";
import { useTranslation } from "react-i18next";
import { scroller } from "react-scroll";
import { cloneDeep, groupBy, orderBy } from "lodash";
import {
  useConfirm,
  useEnum,
  useLoading,
  useQueryAll,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import { SVG } from "assets";
import {
  ENUM,
  LIST_PHIEU_KY_CUSTOM,
  LIST_PHIEU_IN_WORD_THEO_SO_PHIEU,
  LIST_PHIEU_IN_EDITOR_THEO_SO_PHIEU,
  LIST_PHIEU_IN_THEO_TEN_LEVEL2,
  TRANG_THAI_KY,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
  LIST_PHIEU_KHONG_HUY_KY,
  LOAI_BIEU_MAU,
  LIST_PHIEU_IN_TRUYEN_THEM_KHOA_CHI_DINH,
  HIEN_THI_PHIEU_IN,
} from "constants/index";
import { checkIsPhieuKySo, getThongTinKySo } from "utils/phieu-utils";
import moment from "moment";
import ModalPatientSign from "pages/editor/report/components/ModalPatientSign";
import fileUtils from "utils/file-utils";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import usePhieu from "../usePhieu";
import ModalLichSuKy from "pages/editor/report/components/ModalLichSuKy";
import ModalTrinhKy from "pages/kySo/DanhSachPhieuChoKy/ModalTrinhKy";
import ModalSapXepHsba from "pages/hoSoBenhAn/components/ModalSapXepHsba";
import { LOAI_THEO_DOI } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTheoDoiDieuTriThan";
import { isArray, isNumber } from "utils/index";
import { query } from "redux-store/stores";
import pdfUtils from "utils/pdf-utils";

const DanhSachPhieu = (
  {
    showButtonPrint = true,
    isKyPhieu = false,
    data = {},
    isShowIconScan,
    handleClick = () => { },
    refreshPhieuKy = () => { },
    nbDotDieuTriId,
    isHoSoBenhAn = false,
    renderAction = null,
    kyTheoTungPhieu = false,
    isShow,
    showSapXepHsba = false,
    hideThongTinKy = false,
    onViewScanFile = () => { },
    fromScan = false,
    onPrintPhieu,
    listPhieuDongGoi,
    isTrinhKyToBia,
    onTrinhKyToBia,
    hoSoSinhPhieuInId,
    themeKey,
    showTrangThaiDongGoi,
    ...props
  },
  ref
) => {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const refModalPatientSign = useRef(null);
  const refSelectedIds = useRef([]);
  const refLsKy = useRef(null);
  const refModalTrinhKy = useRef(null);
  const refModalSapXepHoSoBenhAn = useRef(null);
  const refButtonPrint = useRef(null);

  const [state, _setState] = useState({
    data: [],
    visible: false,
    dataSource: [],
    dataSourceDefault: [],
    currentKey: null,
    timKiem: null,
    showTrinhKy: false,
    dsLoaiPhieuInId: null,
    trangThai: null,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { hideLoading, showLoading } = useLoading();
  const currentPhieu = usePhieu(state.currentKey);
  const [listTrangThaiKy] = useEnum(ENUM.TRANG_THAI_KY);
  const [maBaoCaoKhongIn] = useThietLap(
    THIET_LAP_CHUNG.IN_DON_THUOC_MA_BAO_CAO_KHONG_IN_RA_GIAY,
    null
  );

  const [dataHIEN_THI_KY_SO_NB] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KY_SO_NB
  );

  const isLoadingListPhieu = useStore("phieuIn.isLoadingListPhieu", false);
  const selectedIds = useStore("phieuIn.selectedIds", []);
  const elementScrollingPdfKey = useStore(
    "phieuIn.elementScrollingPdfKey",
    null
  );
  const listPhieu = useStore("phieuIn.listPhieu", []);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const fileLoaded = useStore("phieuIn.fileLoaded");

  const auth = useStore("auth.auth", {});
  const { data: listAllLoaiHienThiPhieuIn } = useQueryAll(
    query.loaiHienThiPhieuIn.queryAllLoaiHienThiPhieuIn
  );

  const {
    phieuIn: {
      updateData,
      getFilePhieuIn,
      kyPhieu,
      kyPhieuTheoMa,
      huyKyPhieu,
      tuChoiKyPhieu,
    },
    danhSachPhieuChoKy: { getListPhieu },
  } = useDispatch();

  const listLoaiHienThiPhieuInMemo = useMemo(() => {
    return (listAllLoaiHienThiPhieuIn || [])
      .filter((x) => {
        return isArray(x?.dsVaiTroId, true)
          ? auth?.dsVaiTro?.some((y) => x?.dsVaiTroId.includes(y.id))
          : true;
      })
      .sort((a, b) => {
        if (a.uuTien == null && b.uuTien != null) return 1;
        if (a.uuTien != null && b.uuTien == null) return -1;
        if (a.uuTien == null && b.uuTien == null) return 0;
        return a.uuTien - b.uuTien;
      });
  }, [listAllLoaiHienThiPhieuIn, auth]);

  const onChange = (type, value) => (e) => {
    if (type == "active") {
      const checked = e?.target?.checked;
      if (fromScan) {
        e.stopPropagation();
      }
      let _selectedIds = [];

      if (!checked) {
        if (Array.isArray(value)) {
          _selectedIds = selectedIds?.filter((x) => !value.includes(x)) || [];
        } else {
          _selectedIds = selectedIds?.filter((x) => x != value) || [];
        }
      } else {
        if (Array.isArray(value)) {
          _selectedIds = [...(selectedIds || []), ...value];
        } else {
          _selectedIds = [...(selectedIds || []), value];
        }
      }

      updateData({
        selectedIds: [...new Set(_selectedIds)],
      });
    }
  };

  const onCheckAll = (e) => {
    if (!e?.target?.checked) {
      updateData({ selectedIds: [] });
    } else {
      updateData({ selectedIds: dataIds });
    }
  };

  const onPrint = useRefFunc(async () => {
    let _selectedIds = selectedIds;

    if (onPrintPhieu) {
      onPrintPhieu(_selectedIds);
    } else {
      //nếu ko cho in đơn nhà thuốc => xử lý loại bỏ
      if (maBaoCaoKhongIn) {
        const listKeyPhieuKhongIn = listPhieu
          .filter((item) => maBaoCaoKhongIn.indexOf(item.ma) >= 0)
          .map((item) => item.key);

        if (listKeyPhieuKhongIn.length > 0) {
          _selectedIds = _selectedIds.filter(
            (x) => !listKeyPhieuKhongIn.includes(x)
          );
        }
      }

      if (!_selectedIds?.length) {
        return message.error(t("phieuIn.vuiLongChonPhieu"));
      }

      try {
        showLoading();
        const { dsPhieu } = await getFilePhieuIn({
          selectedIds: _selectedIds,
          ...data,
          isReload: false
        });
        await printProvider.printPdf(dsPhieu);
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  });

  useImperativeHandle(ref, () => ({
    refreshKey: (newKey) => {
      setTimeout(() => {
        setState({ currentKey: newKey });

        scroller.scrollTo(newKey, {
          duration: 500,
          offset: 0,
          smooth: "easeInOutQuint",
          containerId: "containerElementPdf",
        });
      }, 500);
    },
    onPrint: () => {
      refButtonPrint.current?.click();
    },
  }));

  const listPhieuFilter = useMemo(() => {
    let _listPhieu = isArray(listPhieuDongGoi) ? listPhieuDongGoi : listPhieu;

    // Lọc theo inHsba nếu có
    if (state.dsLoaiPhieuInId) {
      _listPhieu = _listPhieu.filter((item) =>
        (item.dsLoaiPhieuInId || []).includes(state.dsLoaiPhieuInId)
      );
    } else {
      _listPhieu = _listPhieu;
    }
    // Lọc theo trạng thái nếu có
    if (state.trangThai !== null && state.trangThai !== undefined) {
      _listPhieu = _listPhieu.filter(
        (item) => item.trangThai === state.trangThai
      );
    }

    // Lọc theo từ khóa tìm kiếm nếu có
    let _data = _listPhieu;
    if (state.timKiem) {
      const valueText = state.timKiem?.trim().toLowerCase().unsignText();
      _data = _listPhieu.filter((item) =>
        item.ten?.toLowerCase().unsignText().includes(valueText)
      );
    }

    return _data;
  }, [
    listPhieu,
    listPhieuDongGoi,
    state.timKiem,
    state.dsLoaiPhieuInId,
    state.trangThai,
  ]);

  useEffect(() => {
    refSelectedIds.current = selectedIds;
  }, [selectedIds]);

  const dataIds = useMemo(() => {
    let _dataIds = [];

    (state?.dataSource || []).forEach((item) => {
      if (!item.isNotPhieuIn) {
        _dataIds.push(item?.key || item?.id + "");
      }

      (item.children || []).forEach((element) => {
        if (!element.isNotPhieuIn) {
          _dataIds.push(element?.key || element?.id + "");
        }
      });
    });

    return _dataIds;
  }, [state.dataSource]);

  useEffect(() => {
    if (
      !(refSelectedIds.current || []).every((item) => dataIds.includes(item)) ||
      !refSelectedIds.current.length
    ) {
      if (isHoSoBenhAn) updateData({ selectedIds: [] });
      else updateData({ selectedIds: cloneDeep(dataIds) });
    }
  }, [dataIds]);

  useEffect(() => {
    if (isShow) {
      setState({ timKiem: null });
    }
  }, [isShow]);

  useEffect(() => {
    const _group = groupBy(listPhieuFilter || [], "ma");
    const _listPhieu = Object.keys(_group).map((key) => {
      //loại phiếu ký và in theo số phiếu
      if (_group[key].length > 1) {
        //với level1 thì hiển thị thông tin của phiếu có trạng thái nhỏ nhất
        const minTrangThaiSoPhieu = (_group[key] || []).reduce((prev, curr) =>
          prev.dsSoPhieu[0].trangThai < curr.dsSoPhieu[0].trangThai
            ? prev
            : curr
        );
        const children = _group[key].map((item) => ({
          ...item,
          isLevel2: true,
          trangThai: item.dsSoPhieu[0]?.trangThai,
          thoiGianThucHien: item.dsSoPhieu[0]?.thoiGianThucHien,
          ten1: item.dsSoPhieu[0]?.ten1,
          ten2: item.dsSoPhieu[0]?.ten2,
          ten3: item.dsSoPhieu[0]?.ten3,
          daDongGoi: item.dsSoPhieu[0]?.daDongGoi,
        }));
        if (key === "P513") {
          children.sort(
            (a, b) =>
              new Date(b.thoiGianThucHien).getTime() -
              new Date(a.thoiGianThucHien).getTime()
          );
        }
        return {
          ..._group[key][0],
          trangThai: minTrangThaiSoPhieu?.dsSoPhieu[0].trangThai,
          key: _group[key][0]?.id + "",
          isNotPhieuIn: true,
          children,
        };
      } else {
        //loại ko chia theo số phiếu
        let item = _group[key][0];

        //với level1 thì hiển thị thông tin của phiếu có trạng thái nhỏ nhất
        const minTrangThaiSoPhieu = (item.dsSoPhieu || []).reduce(
          (prev, curr) => (prev.trangThai < curr.trangThai ? prev : curr)
        );

        item.trangThai = minTrangThaiSoPhieu?.trangThai;
        item.daDongGoi = item.dsSoPhieu.every((i) => i.daDongGoi);

        if (item.dsSoPhieu && item.dsSoPhieu.length > 1) {
          item.children = orderBy(
            item.dsSoPhieu || [],
            (x) => x.thoiGianThucHien,
            "asc"
          ).map((item2, index2) => ({
            ...item2,
            isLevel2: true,
            isNotPhieuIn: true,
            key: `${item.key}-${index2}`,
            kySo: item?.kySo,
            tenBaoCao: item2?.tenBaoCao
              ? item2?.tenBaoCao
              : item?.tenBaoCao || item?.ten,
          }));
        }
        return item;
      }
    });

    const _listPhieuUpdated = _listPhieu.map((p) => {
      if (p.ma === "P242" && p.children?.length) {
        return {
          ...p,
          children: p.children.map((c, idx) => ({
            ...c,
            sttHienThi: idx + 1,
          })),
        };
      }
      return p;
    });
    if (showSapXepHsba) {
      setState({
        dataSource: orderBy(_listPhieuUpdated, "stt"),
        dataSourceDefault: orderBy(_listPhieuUpdated, "stt"),
      });
    } else {
      setState({
        dataSource: _listPhieuUpdated,
        dataSourceDefault: _listPhieuUpdated,
      });
    }

    updateData({ listPhieuTemp: _listPhieuUpdated });
  }, [listPhieuFilter, showSapXepHsba]);

  useEffect(() => {
    let _filterListPhieuKeys = [];
    (state.dataSource || []).forEach((item) => {
      if ((item.children || []).length > 0) {
        item.children.forEach((soPhieu) => {
          _filterListPhieuKeys.push(soPhieu.key);
        });
      } else {
        _filterListPhieuKeys.push(item.key);
      }
    });
    updateData({
      filterListPhieuKeys: _filterListPhieuKeys,
    });
  }, [state.dataSource]);

  const isCheckAll = useMemo(() => {
    return dataIds?.every((id) => selectedIds?.includes(id));
  }, [dataIds, selectedIds]);

  const renderIconKy = (data) => {
    const mess = `Thiếu chữ ký ${data?.trangThai == 0
        ? data?.dsTenChanKy1
          ? data?.dsTenChanKy1[0]
          : ""
        : data?.dsTenChanKyTiepTheo
          ? data?.dsTenChanKyTiepTheo[0]
          : ""
      }`;

    let iconRender = null;
    if (data?.kySo && data?.trangThai != null) {
      iconRender = (
        <span className="flex mr-5">
          {data?.trangThai == 60 ? (
            <SVG.IcCheckHsba />
          ) : (
            <Tooltip title={mess}>
              <SVG.IcWarning />
            </Tooltip>
          )}
        </span>
      );
    }

    return iconRender;
  };

  const renderTenLevel2 = (data) => {
    const formatTime = [
      "P248",
      "P249",
      "P250",
      "P525",
      "P526",
      "P527",
      "P528",
      "P529",
      "P530",
      "P290",
      "P623",
    ].includes(data?.ma)
      ? "DD/MM/YYYY"
      : ["P149", "P244", "P245"].includes(data?.ma)
        ? "DD/MM/YYYY HH:mm"
        : "DD/MM/YYYY HH:mm:ss";
    const thoiGianThucHien = data?.thoiGianThucHien
      ? moment(data?.thoiGianThucHien).format(formatTime)
      : "";
    const tuThoiGian =
      data?.dsSoPhieu && data?.dsSoPhieu[0]?.tuThoiGian
        ? moment(data?.dsSoPhieu[0]?.tuThoiGian).format(formatTime)
        : "";
    const denThoiGian =
      data?.dsSoPhieu && data?.dsSoPhieu[0]?.denThoiGian
        ? moment(data?.dsSoPhieu[0]?.denThoiGian).format(formatTime)
        : "";

    let tieuChi1 = thoiGianThucHien;
    let tieuChi2 = data?.ten1 || "";
    let tieuChi3 = data?.ten2 || "";
    let tieuChi4 = `${tuThoiGian} - ${denThoiGian}`;
    let tieuChi5 = data?.ten3 || "";

    if (data?.ma === "P972") {
      return `${data?.tenBaoCao || data?.ten || ""} - ${t(
        "quanLyNoiTru.tuanThai"
      )} ${data.dsSoPhieu[0].boSung?.tuanThai}`;
    }
    //Nếu 3 trường trên null : lấy = tenBaoCao (nếu tenBaoCao = null => lấy trường ten)
    if (!data?.thoiGianThucHien && !data.ten1 && !data.ten2) {
      return data?.tenBaoCao || data?.ten || "";
    }

    if (["P149"].includes(data?.ma)) {
      return `${tieuChi4} ${tieuChi2 ? ` - ${tieuChi2}` : ""}`;
    }

    if (["P108", "P233"].includes(data?.ma)) {
      return `${tieuChi2}`;
    }

    if (
      ["P248", "P525", "P526", "P527", "P528", "P529", "P530", "P290"].includes(
        data?.ma
      )
    ) {
      return `${tieuChi4} - ${tieuChi2} ${tieuChi3 ? ` - ${tieuChi3}` : ""}`;
    }

    if (data?.ma == "P623") {
      let _loaiTheoDoi = LOAI_THEO_DOI.find((x) => x.id == tieuChi3)?.ten || "";
      return `${tieuChi1} - ${tieuChi2} - ${_loaiTheoDoi}`;
    }

    if (LIST_PHIEU_IN_THEO_TEN_LEVEL2.includes(data?.ma)) {
      return `${tieuChi1} - ${tieuChi2}`;
    }

    if (["P789", "P790"].includes(data?.maPhieu)) {
      let _tieuChi2 = data?.tenBaoCao?.split("-");
      _tieuChi2 = _tieuChi2?.length ? _tieuChi2.slice(-1) : null;
      return `${tieuChi1} - ${_tieuChi2}- ${tieuChi3}`;
    }

    if (data?.ma == "P242") {
      const stt = data?.sttHienThi;
      return `${tieuChi1} ${tieuChi2} ${tieuChi3 ? `- ${tieuChi3}` : ""}${stt ? ` - ${t("hsba.toSo")} ${stt}` : ""
        }`;
    }

    if (["P024", "P059"].includes(data?.ma)) {
      return [tieuChi1, tieuChi2, tieuChi3, tieuChi5]
        .filter(Boolean)
        .join(" - ");
    }

    return `${tieuChi1} ${tieuChi2} ${tieuChi3 ? `- ${tieuChi3}` : ""}`;
  };

  const renderLevel2 = (data) => {
    return (
      <div style={{ display: "flex" }}>
        <span style={{ fontSize: 13, fontStyle: "italic" }}>
          {renderTenLevel2(data)}
        </span>
      </div>
    );
  };

  const listKySo = useMemo(() => {
    if (!isKyPhieu) return [];
    if (listPhieuFilter && listPhieuFilter.length > 0) {
      let data = listPhieuFilter
        .filter((item) => item.kySo)
        .map((item) => getThongTinKySo(item));

      return data;
    }

    return [];
  }, [isKyPhieu, listPhieuFilter]);

  const onXemLichSuKy = (record, index) => async (e) => {
    e.stopPropagation();
    if (isHoSoBenhAn) {
      refLsKy.current &&
        refLsKy.current.show({
          lichSuKyId: record?.dsSoPhieu?.[0].lichSuKyId || record.lichSuKyId,
          linkEditor: record.linkEditor,
        });
    } else {
      const data = await getListPhieu({
        dsId: record?.dsSoPhieu.map((item) => item.lichSuKyId),
        size: "",
      });
      state.dataSource[index].lichSuKy = data;
      const listData = cloneDeep(state.dataSourceDefault);
      let _data = [];
      listData.forEach((item) => {
        _data.push(item);
        item?.lichSuKy?.forEach((x) =>
          _data.push({ ...x, isLichSuKy: true, phieu: item })
        );
      });
      setState({ dataSource: _data });
    }
  };
  const sharedOnCell = (row, index) => {
    if (row.isLichSuKy) {
      return { colSpan: 0 };
    }
  };
  const onGetPhieuKy = async (record) => {
    showLoading();
    const data = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
      id: record?.id?.id,
      chuKySo: record?.id?.chuKySo,
    });
    fileUtils
      .getFromUrl({ url: fileUtils.absoluteFileUrl(data?.data?.file?.pdf) })
      .then(async (s) => {
        const blobUrl = fileUtils.arrayBufferToBlobUrl(
          s,
          "application/pdf",
          true
        );
        updateData({ urlFileLichSuKyLocal: blobUrl });
        hideLoading();
      })
      .finally(() => {
        hideLoading();
      });
  };

  const showOtherAction = !fromScan && !hoSoSinhPhieuInId;

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: !showOtherAction ? "10%" : "15%",
      dataIndex: "index",
      hideSearch: true,
      align: "center",
      render: (item, list, index) =>
        list?.isLevel2 || list?.isLichSuKy ? "" : index + 1,
    },
    {
      title: <HeaderSearch title={t("phieuIn.tenPhieu")} />,
      width: !showOtherAction ? "45%" : "40%",
      dataIndex: "ten",
      hideSearch: true,
      onCell: (row, index) => ({
        colSpan: row.isLichSuKy ? 23 : 1,
        style: { fontWeight: row.isLevel2 ? "normal" : "bold" },
      }),
      render: (item, list) => {
        if (list.isLevel2) {
          return renderLevel2(list);
        }

        return list?.isLichSuKy ? (
          <div>
            {`${list.thoiGianKy &&
              moment(list.thoiGianKy).format("HH:mm:ss - DD/MM/YYYY")
              } ${list.tenNguoiKy ? list.tenNguoiKy : ""}`}
          </div>
        ) : (
          <div data-ma={list.ma} className="flex flex-a-center">
            {item || list?.item?.[0]?.tenBaoCao || list?.ten}{" "}
          </div>
        );
      },
    },
    ...(showTrangThaiDongGoi
      ? [
        {
          title: <HeaderSearch title={t("phieuIn.trangThaiDongGoi")} />,
          width: "25%",
          dataIndex: "daDongGoi",
          hideSearch: true,
          onCell: sharedOnCell,
          render: (item, list) => {
            return item ? t("phieuIn.daDongGoi") : t("phieuIn.chuaDongGoi");
          },
        },
      ]
      : []),
    ...(!hideThongTinKy
      ? [
        {
          title: <HeaderSearch title={t("common.trangThai")} />,
          width: "20%",
          dataIndex: "trangThai",
          hideSearch: true,
          onCell: sharedOnCell,
          hidden: fromScan || !!hoSoSinhPhieuInId,
          render: (item, list) => {
            if (!list?.kySo || item == null) {
              return "";
            }

            const tenTrangThai = (listTrangThaiKy || []).find(
              (x) => x.id == item
            )?.ten;

            return (
              <TrangThaiDiv>
                {list.isLichSuKy
                  ? null
                  : list.isLevel2
                    ? !fromScan && renderIconKy(list)
                    : !fromScan && !hideThongTinKy && renderIconKy(list)}
                {tenTrangThai}
              </TrangThaiDiv>
            );
          },
        },
        {
          title: <HeaderSearch title={t("kySo.lichSuKy")} />,
          width: "24%",
          dataIndex: "lichSuKy",
          hideSearch: true,
          align: "left",
          onCell: sharedOnCell,
          hidden: fromScan || !!hoSoSinhPhieuInId,
          render: (_, data, index) => {
            return (
              ((data.dsSoPhieu?.[0]?.trangThai >= 50 &&
                !data?.children?.length) ||
                (data.isLevel2 && data.trangThai >= 50)) &&
              (data?.dsSoPhieu?.[0]?.lichSuKyId || data?.lichSuKyId) && (
                <a onClick={onXemLichSuKy(data, index)}>
                  {t("kySo.xemLsKy")}
                </a>
              )
            );
          },
        },
        {
          title: (
            <HeaderSearch
              title={<Checkbox onClick={onCheckAll} checked={isCheckAll} />}
            />
          ),
          width: "10%",
          dataIndex: "key",
          hideSearch: true,
          align: "center",
          fixed: "right",
          onCell: sharedOnCell,
          render: (value, item, index) => {
            //nếu là item level 2 nhưng ko phải phiếu in (in theo phiếu cha) => ẩn checkbox
            if (item.isLevel2 && item.isNotPhieuIn) return null;

            //nếu là item level 1(item cha) + in theo số phiếu + ko phải phiếu in
            //=> bắt check nếu all item con được check và ngược lại
            if (
              [
                ...LIST_PHIEU_IN_WORD_THEO_SO_PHIEU,
                ...LIST_PHIEU_IN_EDITOR_THEO_SO_PHIEU,
              ].includes(item.ma) &&
              item.isNotPhieuIn
            ) {
              const allChildIds = (item.children || []).map((x) => x.key);
              const _isCheck = allChildIds.every((x) =>
                selectedIds?.includes(x)
              );

              return (
                <Checkbox
                  onChange={onChange("active", allChildIds)}
                  checked={_isCheck}
                  className="box-item"
                />
              );
            }

            return (
              <Checkbox
                onClick={onChange("active", value)}
                checked={selectedIds?.includes(value)}
                className="box-item"
              />
            );
          },
        },
      ]
      : []),
  ];

  useEffect(() => {
    if (listPhieuFilter?.length > 0 && !currentPhieu) {
      setState({
        currentKey: listPhieuFilter?.[0]?.key,
      });
    }
  }, [listPhieuFilter]);

  useEffect(async () => {
    if (currentPhieu) {
      const thongTinKySo = getThongTinKySo(currentPhieu);
      const {
        quyenKyTiepTheoId: quyenKyId,
        khoaThucHienId,
        khoaChiDinhId,
      } = thongTinKySo || {};

      if (refModalTrinhKy.current) {
        const showTrinhKy = await refModalTrinhKy.current.kiemTraQuyenKy({
          quyenKyId,
          khoaThucHienId,
          khoaChiDinhId,
        });

        setState({ showTrinhKy });
      }
    }
  }, [currentPhieu]);

  const setRowClassName = (record, index) => {
    return (
      fromScan
        ? state.currentKey === record.key
        : elementScrollingPdfKey == record.key
    )
      ? "active"
      : "";
  };

  const onKyPhieu = async ({
    anhKy = null,
    thongTinKy = {},
    fileChuyenDoi,
    soCanCuoc,
    tenNguoiKy,
    soDienThoai,
  }) => {
    const {
      chuKySo,
      baoCaoId,
      lichSuKyId,
      soPhieu,
      tenChanKy,
      kySo,
      tenChanDaKyTruoc,
      baoCaoDaInId,
      ma,
      trangThaiKy,
      ...loaiKy
    } = thongTinKy;
    let payload = {};

    //ký cấp 1
    if (!lichSuKyId) {
      payload = {
        baoCaoDaInId,
        fileChuyenDoi,
        soCanCuoc,
        tenNguoiKy,
        soDienThoai,
      };
    } else {
      //ký cấp tiếp theo
      payload = {
        id: lichSuKyId,
        //nếu trạng thái là Trình ký và ở chữ ký thứ 2 trở đi => cộng thêm 1 vào chữ ký số khi truyền vào API
        chuKySo:
          trangThaiKy == TRANG_THAI_KY.TRINH_KY && chuKySo > 0
            ? chuKySo + 1
            : chuKySo,
        fileChuyenDoi,
        soCanCuoc,
        tenNguoiKy,
        soDienThoai,
      };
    }

    const isKySo = loaiKy[`loaiKy${chuKySo}`] === 0;
    if (anhKy) {
      payload.anhKy = anhKy;
    }

    if (LIST_PHIEU_IN_TRUYEN_THEM_KHOA_CHI_DINH.includes(ma)) {
      payload.khoaChiDinhId = data.khoaChiDinhId;
    }
    if (ma === "P209") {
      //Với giấy báo tử xử lý ký custom tất cả các chữ ký
      await kyPhieuTheoMa({
        ...payload,
        ma,
        id: data?.id || soPhieu,
        idBody: !lichSuKyId || chuKySo === 1 ? null : lichSuKyId,
        chuKySo,
      });
    } else if (
      isKySo &&
      LIST_PHIEU_KY_CUSTOM.includes(ma) &&
      (!lichSuKyId || chuKySo === 1)
    ) {
      //Chỉ xử lý custom với chữ ký đầu tiên
      await kyPhieuTheoMa({
        ...payload,
        ma,
        id: data?.id || soPhieu,
        chuKySo,
        viTri: thongTinKy?.viTri || 1,
      });
    } else if (LIST_PHIEU_KHONG_HUY_KY.includes(ma)) {
      await kyPhieu({
        nbDotDieuTriId,
        soPhieu,
        baoCaoId,
        chuKySo: 1,
        fileChuyenDoi,
        soCanCuoc,
        soDienThoai,
      });
    } else {
      await kyPhieu({ ...payload, fileChuyenDoi, soCanCuoc, soDienThoai });
    }
  };

  const onKyNb = (thongTinKy) => async () => {
    let fileChuyenDoi = null;

    if (dataHIEN_THI_KY_SO_NB.eval()) {
      const litsFile = selectedIds
        .filter((el) => {
          return fileLoaded[el] && currentPhieu.key === el;
        })
        .map((el) => fileLoaded[el]);
      let file = await pdfUtils.mergePdfFromPdf(litsFile);
      fileChuyenDoi = await fileUtils.blobUrlToBase64(file, {
        removeMime: true,
      });
    }

    if (refModalPatientSign.current) {
      refModalPatientSign.current.show(
        {
          isGetImage: true,
          fileChuyenDoi,
        },
        async ({
          image,
          fileChuyenDoi,
          soCanCuoc,
          tenNguoiKy,
          soDienThoai,
        }) => {
          const anhKy = (image || "").replace("data:image/png;base64,", "");
          showLoading();
          try {
            await onKyPhieu({
              anhKy,
              thongTinKy,
              fileChuyenDoi,
              soCanCuoc,
              tenNguoiKy,
              soDienThoai,
            });
            refreshPhieuKy(currentPhieu?.ma);
          } catch (error) {
            console.error(error);
          } finally {
            hideLoading();
          }
        }
      );
    }
  };

  const renderBtnKyNb = () => {
    //lọc ký người bệnh
    const _dsKyNb = listKySo.filter(
      (x) => x.kySo && x[`loaiKy${x.chuKySo}`] == 2
    );

    if (!_dsKyNb?.length) {
      return null;
    }

    return (
      <>
        {_dsKyNb.map((item, index) => (
          <Button
            key={index}
            rightIcon={<SVG.IcEdit />}
            minWidth={100}
            type={"primary"}
            iconHeight={15}
            onClick={onKyNb(item)}
          >
            {t("kySo.ky")} {item?.tenChanKy || ""}
          </Button>
        ))}
      </>
    );
  };

  const renderBtnKy = () => {
    //lọc ký số + ko phải ký nb + lọc phiếu đang được check
    const _dsKySo = listKySo
      .filter((x) => x.kySo && x[`loaiKy${x.chuKySo}`] != 2)
      .filter((x) => selectedIds.includes(x.key));

    if (!_dsKySo?.length) {
      return null;
    }

    //nhóm ký theo tên chân ký
    const kySoGroup = groupBy(_dsKySo, "tenChanKy");

    //call func ký tất cả các chân ký cùng tên
    const onKyMulti =
      (data = []) =>
        () => {
          showLoading();
          Promise.allSettled(
            data.map(async (item) => {
              return await onKyPhieu({ thongTinKy: item });
            })
          )
            .then((values) => {
              //nếu có phiếu được ký thành công thì reload lại
              if (values.some((x) => x.status == "fulfilled")) {
                refreshPhieuKy(currentPhieu.ma);
              }
            })
            .finally(() => {
              hideLoading();
            });
        };

    return (
      <>
        {Object.keys(kySoGroup).map((key, index) => (
          <Button
            key={index}
            rightIcon={<SVG.IcEdit />}
            minWidth={100}
            type={"primary"}
            iconHeight={15}
            onClick={onKyMulti(kySoGroup[key])}
          >
            {t("kySo.ky")} {key == "null" ? "" : key || ""}
          </Button>
        ))}
      </>
    );
  };

  const renderBtnHuyKy = () => {
    //lọc ký số + có lịch sử ký +  + lọc phiếu đang được check
    const _dsKySo = listKySo
      .filter((x) => x.lichSuKyId && x.trangThaiKy != 10)
      .filter((x) => selectedIds.includes(x.key));

    if (!_dsKySo?.length) {
      return null;
    }
    //nhóm hủy ký theo tên chân đã ký trước
    const huyKySoGroup = groupBy(_dsKySo, "tenChanDaKyTruoc");

    //call func hủy ký tất cả các chân đã ký trước cùng tên
    const onHuyKyMulti =
      (data = []) =>
        () => {
          const { chuKySo, lichSuKyId, kySo, trangThaiKy, ...loaiKy } =
            data[0] || {};
          let _chuKySo = trangThaiKy === 60 ? chuKySo : chuKySo - 1;
          const _loaiKy = loaiKy[`loaiKy${_chuKySo}`];

          showConfirm(
            {
              title: t("giayDayCong.xacNhanHuy"),
              content: `${t("kySo.banCoMuonHuy")} ${_loaiKy == 0
                  ? t("kySo.chuKySo")
                  : _loaiKy == 1
                    ? t("kySo.chuKyDienTu")
                    : t("kySo.chuKyNguoiBenh")
                }`,
              cancelText: t("common.huy"),
              okText: t("common.xacNhan"),
              classNameOkText: "button-confirm",
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              showLoading();
              Promise.allSettled(
                data.map(async (item) => {
                  return await huyKyPhieu({
                    id: item?.lichSuKyId,
                    chuKySo:
                      item.trangThaiKy === TRANG_THAI_KY.HOAN_THANH
                        ? item.chuKySo
                        : item.chuKySo - 1,
                    viTri: item.viTri || 1,
                  });
                })
              )
                .then((values) => {
                  //nếu có phiếu được hủy ký thành công thì reload lại
                  if (values.some((x) => x.status == "fulfilled")) {
                    refreshPhieuKy(currentPhieu.ma);
                  }
                })
                .finally(() => {
                  hideLoading();
                });
            }
          );
        };

    return (
      <>
        {Object.keys(huyKySoGroup).map((key, index) => (
          <Button
            key={index}
            rightIcon={<SVG.IcCancel />}
            minWidth={100}
            type={"default"}
            iconHeight={15}
            onClick={onHuyKyMulti(huyKySoGroup[key])}
          >
            {t("kySo.huyKy")} {key == "null" ? "" : key || ""}
          </Button>
        ))}
      </>
    );
  };

  const renderBtnKyVaHuyKy = () => {
    if (
      (kyTheoTungPhieu || isHoSoBenhAn) &&
      currentPhieu &&
      checkIsPhieuKySo(currentPhieu) &&
      currentPhieu.loaiBieuMau !== LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA
    ) {
      const thongTinKySo = getThongTinKySo(currentPhieu);
      //kiểm tra nếu ko load được file chưa ký/ đã ký thì ko hiện button ký/ hủy ký
      if (!thongTinKySo?.baoCaoDaInId && !thongTinKySo?.lichSuKyId) {
        return null;
      }

      return (
        <>
          {thongTinKySo?.lichSuKyId &&
            !LIST_PHIEU_KHONG_HUY_KY.includes(currentPhieu.ma) && (
              <Button
                rightIcon={<SVG.IcCancel />}
                minWidth={100}
                type={"default"}
                iconHeight={15}
                onClick={() => {
                  const { chuKySo, lichSuKyId, kySo, ...loaiKy } = thongTinKySo;
                  const _chuKySo = kySo ? chuKySo - 1 : chuKySo;
                  const _loaiKy = loaiKy[`loaiKy${_chuKySo}`];

                  showConfirm(
                    {
                      title: t("giayDayCong.xacNhanHuy"),
                      content: `${t("kySo.banCoMuonHuy")} ${_loaiKy == 0
                          ? t("kySo.chuKySo")
                          : _loaiKy == 1
                            ? t("kySo.chuKyDienTu")
                            : t("kySo.chuKyNguoiBenh")
                        }`,
                      cancelText: t("common.huy"),
                      okText: t("common.xacNhan"),
                      classNameOkText: "button-confirm",
                      showBtnOk: true,
                      typeModal: "warning",
                    },
                    async () => {
                      showLoading();
                      try {
                        await huyKyPhieu({
                          id: thongTinKySo?.lichSuKyId,
                          chuKySo:
                            thongTinKySo.trangThaiKy ===
                              TRANG_THAI_KY.HOAN_THANH
                              ? thongTinKySo.chuKySo
                              : thongTinKySo.chuKySo - 1,
                          viTri: thongTinKySo.viTri || 1,
                        });

                        refreshPhieuKy(currentPhieu.ma);
                      } catch (error) {
                        console.error(error);
                      } finally {
                        hideLoading();
                      }
                    }
                  );
                }}
              >
                {t("kySo.huyKy")} {thongTinKySo?.tenChanDaKyTruoc || ""}
              </Button>
            )}

          {thongTinKySo?.kySo &&
            thongTinKySo[`loaiKy${thongTinKySo.chuKySo}`] != 2 && (
              <Button
                rightIcon={<SVG.IcEdit />}
                minWidth={100}
                type={"primary"}
                iconHeight={15}
                onClick={async () => {
                  showLoading();
                  try {
                    await onKyPhieu({ thongTinKy: thongTinKySo });

                    refreshPhieuKy(currentPhieu.ma);
                  } catch (error) {
                    console.error(error);
                  } finally {
                    hideLoading();
                  }
                }}
              >
                {t("kySo.ky")} {thongTinKySo?.tenChanKy || ""}
              </Button>
            )}

          {thongTinKySo?.kySo &&
            thongTinKySo[`loaiKy${thongTinKySo.chuKySo}`] == 2 && (
              <Button
                rightIcon={<SVG.IcEdit />}
                minWidth={100}
                type={"primary"}
                iconHeight={15}
                onClick={onKyNb(thongTinKySo)}
              >
                {t("kySo.ky")} {thongTinKySo?.tenChanKy || ""}
              </Button>
            )}
        </>
      );
    }

    return null;
  };

  const onTrinhKy =
    (data = {}) =>
      () => {
        const {
          quyenKyTiepTheoId: quyenKyId,
          khoaThucHienId,
          khoaChiDinhId,
          chuKySo,
          baoCaoDaInId,
          lichSuKyId,
        } = data || {};

        let payload = {};
        //trình ký cấp 1
        if (!lichSuKyId) {
          payload = { baoCaoDaInId };
        } else {
          //trình ký cấp tiếp theo
          payload = {
            id: lichSuKyId,
            chuKySo: chuKySo,
          };
        }

        refModalTrinhKy.current &&
          refModalTrinhKy.current.show(
            {
              quyenKyId,
              ...payload,
              khoaThucHienId,
              khoaChiDinhId,
            },
            () => {
              refreshPhieuKy();
            }
          );
      };

  const renderBtnTrinhKy = () => {
    if (!state.showTrinhKy) {
      return null;
    }
    if (currentPhieu && checkIsPhieuKySo(currentPhieu)) {
      const thongTinKySo = getThongTinKySo(currentPhieu);

      if (
        [TRANG_THAI_KY.DA_KY, TRANG_THAI_KY.CHUA_KY].includes(
          thongTinKySo.trangThaiKy
        )
      ) {
        return (
          <Button
            minWidth={60}
            type={"primary"}
            iconHeight={15}
            onClick={onTrinhKy(thongTinKySo)}
          >
            {t("phieuIn.trinhKy")}
          </Button>
        );
      }

      return null;
    }

    return null;
  };

  const renderBtnTrinhKyToBia = () => {
    return (
      <Button
        minWidth={60}
        type={"primary"}
        iconHeight={15}
        onClick={onTrinhKyToBia}
      >
        {t("hsba.trinhKyToBia")}
      </Button>
    );
  };

  const onTuChoiKy =
    (data = {}) =>
      async () => {
        const { lichSuKyId } = data || {};

        try {
          showLoading();

          await tuChoiKyPhieu({ lichSuKyId });

          refreshPhieuKy();
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      };

  const renderBtnTuChoiKy = () => {
    if (currentPhieu && checkIsPhieuKySo(currentPhieu)) {
      const thongTinKySo = getThongTinKySo(currentPhieu);

      if ([TRANG_THAI_KY.TRINH_KY].includes(thongTinKySo.trangThaiKy)) {
        return (
          <Button
            minWidth={60}
            type={"default"}
            iconHeight={15}
            onClick={onTuChoiKy(thongTinKySo)}
          >
            {t("phieuIn.tuChoiKy")}
          </Button>
        );
      }

      return null;
    }

    return null;
  };

  const onShowSapXepHoSoBenhAn = () => {
    refModalSapXepHoSoBenhAn.current &&
      refModalSapXepHoSoBenhAn.current.show(
        {
          data: state?.dataSource,
          nbDotDieuTriId: nbDotDieuTriId,
        },
        () => refreshPhieuKy()
      );
  };

  const renderBtnTaiFile = () => {
    //lọc phiếu đã ký + lọc phiếu đang được check
    const _dsDaKy = listKySo
      .filter((x) =>
        [
          TRANG_THAI_KY.DA_KY,
          TRANG_THAI_KY.HOAN_THANH,
          TRANG_THAI_KY.TRINH_KY,
        ].includes(x.trangThaiKy)
      )
      .filter((x) => selectedIds.includes(x.key));

    const onTaiFile = async () => {
      try {
        showLoading();
        const onDownloadFile = async (blobUrls) => {
          const { default: JSZip } = await import("jszip");
          const { default: JSZipUtils } = await import("jszip-utils");
          const zip = new JSZip();
          let count = 0;
          const zipFilename =
            blobUrls?.length > 2 ? `DsPhieuKy.zip` : `${blobUrls[0]?.name}.zip`;
          blobUrls.forEach((url, index) => {
            const filename =
              index % 2 === 0
                ? `${url.name}_${index}.pdf`
                : `${url.name}_${index}.xml`;
            // loading a file and add it in a zip file
            JSZipUtils.getBinaryContent(url.content, function (err, data) {
              if (err) {
                throw err; // or handle the error
              }
              zip.file(filename, data, { binary: true });
              count++;
              if (count === blobUrls.length) {
                zip.generateAsync({ type: "blob" }).then(function (content) {
                  fileUtils.downloadBlob(content, zipFilename);
                });
              }
            });
          });
        };
        let _listDownLoad = [];

        await Promise.all(
          _dsDaKy.map(async (_phieu) => {
            //get file phiếu
            const { dsPhieu = [] } = await getFilePhieuIn({
              selectedIds: [_phieu.key],
              ...data,
            });
            //get file xml
            const pdfFile = await fileUtils.getFromUrl({
              url: fileUtils.absoluteFileUrl(dsPhieu[0]?.file?.pdf),
            });
            const pdfBlob = new Blob([new Uint8Array(pdfFile)], {
              type: "application/pdf",
            });
            const pdfFileUrl = window.URL.createObjectURL(pdfBlob);
            _listDownLoad.push({ name: _phieu.tenBaoCao, content: pdfFileUrl });

            //get file xml
            const xmlFile = await fileUtils.getFromUrl({
              url: fileUtils.absoluteFileUrl(_phieu.duongDan),
            });
            const xmlBlob = new Blob([new Uint8Array(xmlFile)], {
              type: "text/xml",
            });
            const xmlFileUrl = window.URL.createObjectURL(xmlBlob);
            _listDownLoad.push({ name: _phieu.tenBaoCao, content: xmlFileUrl });
          })
        );

        onDownloadFile(_listDownLoad);
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    };

    if (_dsDaKy.length > 0) {
      return (
        <Button onClick={onTaiFile} rightIcon={<SVG.IcDownload />}>
          {t("editor.taiFile")}
        </Button>
      );
    } else {
      return null;
    }
  };

  const onChangeLoaiHienThi = (value) => {
    // Cập nhật selectedIds nếu có inHsba
    if (
      isNumber(value) &&
      [HIEN_THI_PHIEU_IN.IN_HSBA, HIEN_THI_PHIEU_IN.IN_HO_SO_SINH].includes(
        value
      )
    ) {
      // Sử dụng nguồn dữ liệu gốc thay vì listPhieuFilter để tránh stale data
      let _listPhieu = isArray(listPhieuDongGoi) ? listPhieuDongGoi : listPhieu;

      // Filter trực tiếp với value mới
      _listPhieu = _listPhieu.filter((item) =>
        (item.dsLoaiPhieuInId || []).includes(value)
      );

      updateData({
        selectedIds: [...new Set(_listPhieu.map((item) => item.key))],
      });
    } else {
      updateData({
        selectedIds: [],
      });
    }

    setState({ dsLoaiPhieuInId: value });
  };

  return (
    <Main
      hideTitle
      subTitle={
        !hideThongTinKy ? (
          <>
            {showOtherAction && (
              <Select
                value={state.dsLoaiPhieuInId}
                onChange={onChangeLoaiHienThi}
                data={listLoaiHienThiPhieuInMemo}
                placeholder={t("danhMuc.chonLoaiHienThi")}
                style={{ width: 200 }}
              />
            )}
            <Select
              value={state.trangThai}
              onChange={(e) => setState({ trangThai: e })}
              data={listTrangThaiKy}
              placeholder={t("common.trangThai")}
              style={{ width: 140, marginLeft: 8 }}
            />
            <InputTimeout
              value={state.timKiem}
              onChange={(e) => setState({ timKiem: e })}
              placeholder={t("common.timKiem")}
              style={{
                marginLeft: 8,
                marginRight: 8,
              }}
            />
            {showSapXepHsba &&
              ![
                TRANG_THAI_NB.DA_RA_VIEN,
                TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
              ].includes(thongTinCoBan?.trangThaiNb) ? (
              <SVG.IcOption
                onClick={() => onShowSapXepHoSoBenhAn()}
                style={{ width: "30px", height: "30px" }}
              ></SVG.IcOption>
            ) : null}
          </>
        ) : (
          <></>
        )
      }
    >
      <div className="__list">
        <TableWrapper
          themeKey={themeKey}
          scroll={{ y: 500, x: 0 }}
          loading={isLoadingListPhieu}
          rowKey={(record) => `${record.key}`}
          columns={columns}
          dataSource={state.dataSource}
          rowClassName={setRowClassName}
          onRow={(record, rowIndex) => {
            return {
              onClick: (event) => {
                let key;
                //check trường hợp phiếu cha chứa items theo dsSoPhieu
                //=> khi nhấn vào item cha => dùng key của item con đầu tiên
                if (
                  [
                    ...LIST_PHIEU_IN_WORD_THEO_SO_PHIEU,
                    ...LIST_PHIEU_IN_EDITOR_THEO_SO_PHIEU,
                  ].includes(record.ma) &&
                  record?.children?.length > 0
                ) {
                  key = record?.children[0].key;
                } else {
                  key = record.isLichSuKy ? record.phieu.key : record.key;
                }

                setState({
                  currentKey: key,
                });

                onViewScanFile(record);

                if (!fromScan) {
                  updateData({
                    elementScrollingPdfKey: key,
                  })
                  if (record.isLichSuKy) {
                    onGetPhieuKy(record);
                  }
                }
              },
            };
          }}
          key={state.dataSource?.length}
        />
      </div>

      {!hideThongTinKy ? (
        <>
          {showButtonPrint && !kyTheoTungPhieu && (
            <div className="__button">
              {showOtherAction && (
                <>
                  {renderBtnTaiFile()}
                  {renderBtnHuyKy()}
                  {renderBtnKy()}
                  {renderBtnKyNb()}
                  {renderBtnTrinhKy()}
                  {renderBtnTuChoiKy()}
                </>
              )}
              {isTrinhKyToBia && !hoSoSinhPhieuInId && renderBtnTrinhKyToBia()}
              <Button
                rightIcon={<SVG.IcPrint />}
                minWidth={100}
                onClick={onPrint}
                type={"primary"}
                iconHeight={15}
                ref={refButtonPrint}
              >
                {t("common.in")} [F2]
              </Button>
            </div>
          )}

          {hoSoSinhPhieuInId && <div className="__action">{renderAction}</div>}
          {renderAction && showOtherAction ? (
            <div className="__action">
              {renderAction}
              {renderBtnTaiFile()}
              {renderBtnKyVaHuyKy()}
            </div>
          ) : null}

          {kyTheoTungPhieu && !hoSoSinhPhieuInId && (
            <div className="__button">
              {renderBtnTaiFile()}
              {renderBtnKyVaHuyKy()}
              {renderBtnTrinhKy()}
              {renderBtnTuChoiKy()}

              <Button
                rightIcon={<SVG.IcPrint />}
                minWidth={100}
                onClick={onPrint}
                type={"primary"}
                iconHeight={15}
                ref={refButtonPrint}
              >
                {t("common.in")} [F2]
              </Button>
            </div>
          )}
        </>
      ) : (
        <></>
      )}
      <ModalPatientSign ref={refModalPatientSign} />
      <ModalLichSuKy ref={refLsKy} />
      <ModalTrinhKy ref={refModalTrinhKy} />
      <ModalSapXepHsba ref={refModalSapXepHoSoBenhAn} />
    </Main>
  );
};

export default memo(forwardRef(DanhSachPhieu));
