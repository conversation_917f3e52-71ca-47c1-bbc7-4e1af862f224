import React, { useContext, useEffect, useMemo, useState } from "react";
import { useLoading, useThietLap } from "hooks";
import {
  addFooterPage,
  convert,
  pageType,
  pdfGenerator,
} from "utils/editor-utils";
import { EMR2Context } from "components/editor/config";
import { getThongTinKySo } from "utils/phieu-utils";
import moment from "moment";
import printProvider, { printJS } from "data-access/print-provider";
import { useDispatch } from "react-redux";
import {
  LOAI_BIEU_MAU,
  LOAI_IN_BANG_KE_CHI_PHI,
  THIET_LAP_CHUNG,
} from "constants/index";
import { get, groupBy } from "lodash";
import { isArray, isNumber } from "utils/index";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { centralizedErrorHandling } from "lib-utils";
import useThietLapChung from "./useThietLapChung";

const WIDTH = [209, 30, 30, 55, 55, 55, 55, 40, 55, 55, 55, 55, 55, 30];
const WIDTH_PSHN = [209, 30, 30, 55, 55, 30, 55, 30, 55, 55, 55, 30, 55, 30];

const useBangKe = (queries) => {
  const { showLoading, hideLoading } = useLoading();
  const { editorId } = useContext(EMR2Context);
  const {
    files: { getFormData, getMauBaoCaoByMa },
    phieuIn: { getListPhieu, updateData, getFilePhieuIn },
    khoa: { getListKhoaTongHop },
  } = useDispatch();
  const { dataBANG_KE_CHI_PHI_KHUNG_DICH_VU, dataHIEN_THI_TEN_NB_BANG_KE } =
    useThietLapChung();
  const [dataTHIET_LAP_CHAN_KY_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.THIET_LAP_CHAN_KY_BANG_KE
  );
  const [dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.THIET_LAP_CHAN_KY_NGANG_BANG_KE
  );
  const [dataBANG_KE_CHI_PHI_NHOM_DV_THEO_KHOA] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_CHI_PHI_NHOM_DV_THEO_KHOA
  );

  const [dataBANG_KE_BHYT_HIEN_THI_SO_LE] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_BHYT_HIEN_THI_SO_LE
  );

  const [dataAN_HIEN_THI_TEN_NGUOI_LAP_PHIEU_TREN_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.AN_HIEN_THI_TEN_NGUOI_LAP_PHIEU_TREN_BANG_KE
  );

  const [dataMA_NHOM_DICH_VU_CAP1_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_VAT_TU
  );

  const [dataMA_NHOM_DICH_VU_CAP1_THUOC] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_THUOC
  );

  const [dataTANG_CO_CHU_CHAN_TONG_TIEN_BANG_KE_TONG_HOP] = useThietLap(
    THIET_LAP_CHUNG.TANG_CO_CHU_CHAN_TONG_TIEN_BANG_KE_TONG_HOP
  );
  const [dataAN_TEN_NGUOI_LAP_BANG_KE_BHYT] = useThietLap(
    THIET_LAP_CHUNG.AN_TEN_NGUOI_LAP_BANG_KE_BHYT
  );
  const [dataAN_TEN_NGUOI_IN_BANG_KE_BHYT] = useThietLap(
    THIET_LAP_CHUNG.AN_TEN_NGUOI_IN_BANG_KE_BHYT
  );
  const [dataSAP_XEP_VI_TRI_CHAN_KY_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.SAP_XEP_VI_TRI_CHAN_KY_BANG_KE
  );
  const [dataBANG_KE_BHYT_HIEN_THI_SO_TIEN_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_BHYT_HIEN_THI_SO_TIEN_TAM_UNG
  );
  const [dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE
  );
  const [dataHIEN_THI_MUC_HUONG_BHYT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_MUC_HUONG_BHYT
  );
  const [dataBANG_KE_BHYT_GOP_CHAN_TONG_KHI_QUA_TRANG] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_BHYT_GOP_CHAN_TONG_KHI_QUA_TRANG
  );
  const [dataHIEN_THI_NGUOI_BAO_LANH_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_NGUOI_BAO_LANH_BANG_KE
  );
  const [dataKHONG_HIEN_THI_TEXT_NHAN_PHIM_TREN_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_TEXT_NHAN_PHIM_TREN_BANG_KE
  );
  const [dataHIEN_THI_TEXT_MOI_QUAN_HE_TREN_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TEXT_MOI_QUAN_HE_TREN_BANG_KE
  );
  const [dataKHONG_HIEN_THI_TIEN_OD_TREN_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_TIEN_OD_TREN_BANG_KE
  );
  const hide0d = dataKHONG_HIEN_THI_TIEN_OD_TREN_BANG_KE?.eval();

  const [dataBANG_KE_AN_PHAN_NHOM_STENT_KTC] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_AN_PHAN_NHOM_STENT_KTC
  );
  const [dataHIEN_THI_MA_DV_BANG_KE_TONG_HOP] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_MA_DV_BANG_KE_TONG_HOP
  );
  const [dataBANG_KE_HIEN_THI_TIEN_DV_PHAI_TRA_KHAC] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_HIEN_THI_TIEN_DV_PHAI_TRA_KHAC
  );

  const [dataPrint, setDataPrint] = useState();
  const [dsChuKyProps, setDsChuKyProps] = useState([]);
  const [dsPhieuInKem, setDsPhieuInKem] = useState([]);

  const renderPrice = (price, options = {}) => {
    const { fixed = 2, key = null } = options;
    let _price = price || 0;

    // SAKURA-65778 sủa logic show 0d
    const keysToHide0d = [
      "tongTienBh",
      "tienBhThanhToan",
      "tienNbCungChiTra",
      "tienNguonKhac",
      "tienNbTuTra",
    ];

    const shouldHide0d = key && keysToHide0d.includes(key);

    if (shouldHide0d && hide0d && _price === 0) {
      return "";
    }

    if (dataBANG_KE_BHYT_HIEN_THI_SO_LE?.eval() && queries.loai == 10) {
      return _price.toFixed(fixed).formatPrice();
    } else {
      return _price.toFixed(0).formatPrice();
    }
  };

  const hideTenNhomVatTuKtc =
    dataBANG_KE_AN_PHAN_NHOM_STENT_KTC?.eval() &&
    ["P032", "P062", "P107"].includes(queries.ma);

  const showMaDichVu =
    dataHIEN_THI_MA_DV_BANG_KE_TONG_HOP?.eval() &&
    queries.loai == LOAI_IN_BANG_KE_CHI_PHI.TONG_HOP;

  const renderTenDv = (dv) => {
    if (showMaDichVu && dv.maDichVu) {
      return `${dv.tenDichVu} [${dv.maDichVu}]`;
    } else {
      return dv.tenDichVu;
    }
  };

  const mapData = (values, loaiBangKe) => {
    const rows = (values || []).map((e) => {
      const temp = [];
      (e?.k || []).forEach((item) => {
        if (queries.loai == LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU) {
          temp.push({
            col1:
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
              ].includes(+queries.loai) ||
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_THU_PHI,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_HAO_PHI,
              ].includes(loaiBangKe) ||
              !dataBANG_KE_CHI_PHI_NHOM_DV_THEO_KHOA?.eval()
                ? item.tenNhomDichVuCap1
                : item.tenKhoaChiDinh,
            col2: renderPrice(item.tongTienBh, { key: "tongTienBh" }),
            col3: renderPrice(item.tienBhThanhToan, { key: "tienBhThanhToan" }),
            col4: renderPrice(item.tienNbCungChiTra, {
              key: "tienNbCungChiTra",
            }),
            col5: renderPrice(item.tienNbTuTra, { key: "tienNbTuTra" }),
            colMerge: 1,
            isBold: true,
          });
        } else {
          temp.push({
            col1:
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
              ].includes(+queries.loai) ||
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_THU_PHI,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_HAO_PHI,
              ].includes(loaiBangKe) ||
              !dataBANG_KE_CHI_PHI_NHOM_DV_THEO_KHOA?.eval()
                ? item.tenNhomDichVuCap1
                : item.tenKhoaChiDinh,
            col2: renderPrice(item.tongTien),
            col3: "",
            col4: renderPrice(item.tongTienBh, { key: "tongTienBh" }),
            col5: renderPrice(item.tienBhThanhToan, { key: "tienBhThanhToan" }),
            col6: renderPrice(item.tienNbCungChiTra, {
              key: "tienNbCungChiTra",
            }),
            col7: renderPrice(item.tienNguonKhac, { key: "tienNguonKhac" }),
            col8: renderPrice(item.tienNbTuTra, { key: "tienNbTuTra" }),
            colMerge: queries.loai == 50 ? 12 : 6,
            isBold: true,
          });
        }

        item.n.forEach((nhomDichVuC1) => {
          const col1 = () => {
            if (
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
              ].includes(+queries.loai) ||
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_THU_PHI,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_HAO_PHI,
              ].includes(loaiBangKe)
            ) {
              const dv = get(nhomDichVuC1, "ktc[0].s[0].dv[0]", {});

              if (
                [
                  dataMA_NHOM_DICH_VU_CAP1_VAT_TU,
                  dataMA_NHOM_DICH_VU_CAP1_THUOC,
                ].includes(dv.maNhomDichVuCap1)
              ) {
                if (loaiBangKe == LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_THU_PHI) {
                  if (dv.sttGoiKtc === null && dv.sttStent === null) {
                    return "NGOÀI DANH MỤC BHYT";
                  }
                } else if (
                  loaiBangKe == LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_HAO_PHI
                ) {
                  if (dv.sttGoiKtc === null && dv.sttStent === null) {
                    if (dv.mucHuongBhyt) {
                      return "TRONG DANH MỤC BHYT";
                    } else {
                      return "NGOÀI DANH MỤC BHYT";
                    }
                  }
                } else if (
                  dv.loaiToDieuTri == "Ra viện" ||
                  dv.loaiNhapXuat == "NB ngoại trú"
                ) {
                  return "ĐƠN THUỐC RA VIỆN";
                } else if (dv.tienBhThanhToan > 0) {
                  return "TRONG DANH MỤC BHYT";
                } else if (dv.tienBhThanhToan === 0) {
                  return "NGOÀI DANH MỤC BHYT";
                }
              } else {
                return nhomDichVuC1.tenNhomDichVuCap2;
              }
              if (nhomDichVuC1.tenNhomDichVuCap1.includes("Gói vật tư y tế")) {
                return nhomDichVuC1.tenNhomDichVuCap1;
              }
            } else {
              if (dataBANG_KE_CHI_PHI_NHOM_DV_THEO_KHOA?.eval()) {
                return nhomDichVuC1.tenNhomDichVuCap1;
              } else {
                return nhomDichVuC1.tenKhoaChiDinh;
              }
            }
          };

          const isSkipLineTrungGoiVatTuYTe =
            item.tenNhomDichVuCap1.includes("Gói vật tư y tế") &&
            nhomDichVuC1.tenNhomDichVuCap1.includes("Gói vật tư y tế") &&
            ([
              LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
              LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
            ].includes(+queries.loai) ||
              [
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_THU_PHI,
                LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_HAO_PHI,
              ].includes(loaiBangKe));

          if (
            !isSkipLineTrungGoiVatTuYTe &&
            queries.loai != LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU
          ) {
            temp.push({
              col1: col1(),
              col2: renderPrice(nhomDichVuC1.tongTien),
              col3: "",
              col4: renderPrice(nhomDichVuC1.tongTienBh, { key: "tongTienBh" }),
              col5: renderPrice(nhomDichVuC1.tienBhThanhToan, {
                key: "tienBhThanhToan",
              }),
              col6: renderPrice(nhomDichVuC1.tienNbCungChiTra, {
                key: "tienNbCungChiTra",
              }),
              col7: renderPrice(nhomDichVuC1.tienNguonKhac, {
                key: "tienNguonKhac",
              }),
              col8: renderPrice(nhomDichVuC1.tienNbTuTra, {
                key: "tienNbTuTra",
              }),
              isBold: true,
              colMerge: queries.loai == 50 ? 12 : 6,
            });
          }

          if (queries.loai != LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU) {
            if (!nhomDichVuC1.tenNhomDichVuCap1.includes("Gói vật tư y tế")) {
              nhomDichVuC1.ktc[0].s[0].dv.forEach((dv) => {
                const slSoCap = dv.soLuong / (dv.heSoDinhMuc || 1);
                let cols = {};
                if (queries.loai == 50) {
                  cols = {
                    col1: renderTenDv(dv),
                    col2:
                      dv.loaiDichVu === "Thuốc"
                        ? dv.tenDvtSoCap
                        : dv.tenDonViTinh,
                    col3:
                      dv.loaiDichVu === "Thuốc"
                        ? slSoCap
                          ? Math.round(slSoCap * 1000) / 1000
                          : ""
                        : dv.soLuong
                        ? Math.round(dv.soLuong * 1000) / 1000
                        : "",
                    col4: renderPrice(dv.giaKhongBaoHiem, { fixed: 3 }),
                    col5: renderPrice(dv.giaBaoHiem, { fixed: 3 }),
                    col6: renderPrice(dv.giaPhuThu, { fixed: 3 }),
                    col7: renderPrice(dv.tongTien),
                    col8: renderPrice(dv.tienBhThanhToan, {
                      key: "tienBhThanhToan",
                    }),
                    col9: renderPrice(dv.tienNbCungChiTra, {
                      key: "tienNbCungChiTra",
                    }),
                    col10: renderPrice(dv.tienNbTuTra, { key: "tienNbTuTra" }),
                    col11: renderPrice(dv.tienMienGiam),
                    col12: renderPrice(dv.thanhTien),
                  };
                } else {
                  cols = {
                    col1: renderTenDv(dv),
                    col2:
                      dv.loaiDichVu === "Thuốc"
                        ? dv.tenDvtSoCap
                        : dv.tenDonViTinh,
                    col3:
                      dv.loaiDichVu === "Thuốc"
                        ? slSoCap
                          ? Math.round(slSoCap * 1000) / 1000
                          : ""
                        : dv.soLuong
                        ? Math.round(dv.soLuong * 1000) / 1000
                        : "",
                    col4: renderPrice(dv.donGia, { fixed: 3 }),
                    col5: renderPrice(dv.giaBaoHiem, { fixed: 3 }),
                    col6: dv.tyLeTtDv,
                    col7: renderPrice(dv.tongTien),
                    col8:
                      queries.maBaoCao === "EMR_BA222" &&
                      (dv.giaBaoHiem <= 0 || dv.tuTra)
                        ? 0
                        : dv.tyLeBhTt,
                    col9: renderPrice(dv.tongTienBh, { key: "tongTienBh" }),
                    col10: renderPrice(dv.tienBhThanhToan, {
                      key: "tienBhThanhToan",
                    }),
                    col11: renderPrice(dv.tienNbCungChiTra, {
                      key: "tienNbCungChiTra",
                    }),
                    col12: renderPrice(dv.tienNguonKhac, {
                      key: "tienNguonKhac",
                    }),
                    col13: renderPrice(dv.tienNbTuTra, { key: "tienNbTuTra" }),
                    col14: [50].includes(dv.thanhToan) ? "X" : "",
                    // 50 trạng thái dịch vụ đã thanh toán
                  };
                }
                temp.push(cols);
              });
            } else {
              nhomDichVuC1.ktc.forEach((ktc) => {
                temp.push({
                  col1: `${ktc.tenGoiVatTuKtc} ${
                    ktc.tenPtTt ? `(${ktc.tenPtTt})` : ``
                  }`,
                  col2: renderPrice(ktc.tongTien),
                  col3: "",
                  col4: renderPrice(ktc.tongTienBh, { key: "tongTienBh" }),
                  col5: renderPrice(ktc.tienBhThanhToan, {
                    key: "tienBhThanhToan",
                  }),
                  col6: renderPrice(ktc.tienNbCungChiTra, {
                    key: "tienNbCungChiTra",
                  }),
                  col7: "",
                  col8: renderPrice(ktc.tienNbTuTra, { key: "tienNbTuTra" }),
                  isBold: true,
                  colMerge: queries.loai == 50 ? 12 : 6,
                });

                ktc.s.forEach((stent) => {
                  if (!hideTenNhomVatTuKtc) {
                    temp.push({
                      col1: stent.tenNhomVatTuKtc,
                      col2: renderPrice(stent.tongTien),
                      col3: "",
                      col4: renderPrice(stent.tongTienBh, {
                        key: "tongTienBh",
                      }),
                      col5: renderPrice(stent.tienBhThanhToan, {
                        key: "tienBhThanhToan",
                      }),
                      col6: renderPrice(stent.tienNbCungChiTra, {
                        key: "tienNbCungChiTra",
                      }),
                      col7: "",
                      col8: renderPrice(stent.tienNbTuTra, {
                        key: "tienNbTuTra",
                      }),
                      colMerge: queries.loai == 50 ? 12 : 6,
                      isBold: true,
                    });
                  }

                  if (queries.loai == 50) {
                    stent.dv.forEach((dv) => {
                      const slSoCap = dv.soLuong / (dv.heSoDinhMuc || 1);
                      let cols = {
                        col1: renderTenDv(dv),
                        col2:
                          dv.loaiDichVu === "Thuốc"
                            ? dv.tenDvtSoCap
                            : dv.tenDonViTinh,
                        col3:
                          dv.loaiDichVu === "Thuốc"
                            ? slSoCap
                              ? Math.round(slSoCap * 1000) / 1000
                              : ""
                            : dv.soLuong
                            ? Math.round(dv.soLuong * 1000) / 1000
                            : "",
                        col4: renderPrice(dv.giaKhongBaoHiem, { fixed: 3 }),
                        col5: renderPrice(dv.giaBaoHiem, { fixed: 3 }),
                        col6: renderPrice(dv.giaPhuThu, { fixed: 3 }),
                        col7: renderPrice(dv.tongTien),
                        col8: renderPrice(dv.tienBhThanhToan, {
                          key: "tienBhThanhToan",
                        }),
                        col9: renderPrice(dv.tienNbCungChiTra, {
                          key: "tienNbCungChiTra",
                        }),
                        col10: renderPrice(dv.tienNbTuTra, {
                          key: "tienNbTuTra",
                        }),
                        col11: renderPrice(dv.tienMienGiam),
                        col12: renderPrice(dv.thanhTien),
                      };
                      temp.push(cols);
                    });
                  } else {
                    const isVuotTran = stent.dv?.some((e) => {
                      return (
                        e.kyThuatCao &&
                        (e.sttStent === 1 || !e.sttStent) &&
                        e.tienBhThanhToan > 0 &&
                        e.tienNbTuTra > 0
                      );
                    });
                    const renderSoTienChiTiet = (key, data) => {
                      // dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE = 0/true: Hiển thị tổng tiền
                      if (
                        dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE ===
                          "0" ||
                        (
                          dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE || ""
                        ).toUpperCase() === "TRUE"
                      ) {
                        return renderPrice(data?.[key], { key });
                      } else if (
                        dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE === "2"
                      ) {
                        // dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE = 2:
                        // Nếu key !== tienNbTuTra thì 0 còn là tienNbTuTra thì hiển thị theo logic dưới
                        // 1, Dịch vụ có mức hưởng = 0  -> hiển thị key tienNbTuTra

                        // (áp dụng với dịch vụ Tự trả = true hoặc dịch vụ có đơn giá bảo hiểm = 0 hoặc Dịch vụ không được bảo hiểm thanh toán do hết hạn thẻ)

                        // 2, Dịch vụ có mức hưởng >0 -> hiển thị key tienNbPhuThu

                        // (áp dụng với dịch vụ được hưởng bảo hiểm thì chỉ hiển thị phần tiền chênh

                        // trường hợp người bệnh trái tuyến vẫn tuân theo nguyên tắc trên -> cột tiền tự trả chỉ hiển thị phần chênh hoặc tiền dịch vụ tích tự trả)

                        if (key === "tienNbTuTra") {
                          if (!data?.mucHuongBhyt) {
                            return renderPrice(data?.["tienNbTuTra"], { key });
                          } else {
                            return renderPrice(data?.["tienNbPhuThu"], {
                              key,
                            });
                          }
                        } else {
                          return hide0d ? "" : "0";
                        }
                      } else {
                        // dataHIEN_THI_TIEN_TRONG_CHI_TIET_GOI_KTC_BANG_KE = 1 hoặc k được thiết lập: Hiển thị theo quy tắc
                        if (isVuotTran) {
                          // TH1: Nếu vượt trần thì hiện  0
                          return hide0d ? "" : "0";
                        } else {
                          // TH1: Nếu k vượt trần thì hiện  giá tiền
                          return renderPrice(data?.[key], { key });
                        }
                      }
                    };
                    stent.dv.forEach((dv) => {
                      const slSoCap = dv.soLuong / (dv.heSoDinhMuc || 1);
                      let cols = {
                        col1: renderTenDv(dv),
                        col2:
                          dv.loaiDichVu === "Thuốc"
                            ? dv.tenDvtSoCap
                            : dv.tenDonViTinh,
                        col3:
                          dv.loaiDichVu === "Thuốc"
                            ? slSoCap
                              ? Math.round(slSoCap * 1000) / 1000
                              : ""
                            : dv.soLuong
                            ? Math.round(dv.soLuong * 1000) / 1000
                            : "",
                        col4: renderPrice(dv.donGia, { fixed: 3 }),
                        col5: renderPrice(dv.giaBaoHiem, { fixed: 3 }),
                        col6: dv.tyLeTtDv,
                        col7: renderPrice(dv.tongTien),
                        col8:
                          queries.maBaoCao === "EMR_BA222" &&
                          (dv.giaBaoHiem <= 0 || dv.tuTra)
                            ? 0
                            : dv.tyLeBhTt,
                        col9: renderPrice(dv.tongTienBh, {
                          key: "tongTienBh",
                        }),
                        col10: renderSoTienChiTiet("tienBhThanhToan", dv),
                        col11: renderSoTienChiTiet("tienNbCungChiTra", dv),
                        col12: renderPrice(dv.tienNguonKhac, {
                          key: "tienNguonKhac",
                        }),
                        col13: renderSoTienChiTiet("tienNbTuTra", dv),
                        col14: [50].includes(dv.thanhToan) ? "X" : "",
                        // 50 trạng thái đã thanh toán
                      };
                      temp.push(cols);
                    });
                  }
                });
              });
            }
          }
        });
      });
      const tongCong =
        queries.loai == LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU
          ? {
              col1: `Tổng cộng`,
              col2: renderPrice(e.tongTienBh, { key: "tongTienBh" }),
              col3: renderPrice(e.tienBhThanhToan, { key: "tienBhThanhToan" }),
              col4: renderPrice(e.tienNbCungChiTra, {
                key: "tienNbCungChiTra",
              }),
              col5: renderPrice(e.tienNbTuTra, { key: "tienNbTuTra" }),
            }
          : {
              col1: `Tổng cộng`,
              col2: renderPrice(e.tongTien),
              col3: "",
              col4: renderPrice(e.tongTienBh, { key: "tongTienBh" }),
              col5: renderPrice(e.tienBhThanhToan, { key: "tienBhThanhToan" }),
              col6: renderPrice(e.tienNbCungChiTra, {
                key: "tienNbCungChiTra",
              }),
              col7: renderPrice(e.tienNguonKhac, { key: "tienNguonKhac" }),
              col8: renderPrice(e.tienNbTuTra, { key: "tienNbTuTra" }),
              colMerge: queries.loai == 50 ? 12 : 6,
              isBold: true,
            };
      return [...temp, tongCong];
    });
    return { rows, values };
  };
  const getDataToPrint = async ({ nbDotDieuTriId }) => {
    const { scale, ...payload } = queries;
    try {
      const file = {
        api: "api/his/v1/nb-dot-dieu-tri/bang-ke-chi-phi",
      };
      if (
        ["P178", "P107", "P727", "P746", "P747"].includes(payload.ma) &&
        payload.dsKhoaChiDinhId
      ) {
        payload.dsKhoaChiDinhId = decodeURIComponent(payload.dsKhoaChiDinhId);
      }
      //phiếu có truyền param thời gian thực hiện thì format lại định dạng truyền lên api
      if (payload.tuThoiGianThucHien && payload.denThoiGianThucHien) {
        payload.tuThoiGianThucHien = decodeURIComponent(
          payload.tuThoiGianThucHien
        );
        payload.denThoiGianThucHien = decodeURIComponent(
          payload.denThoiGianThucHien
        );
      }

      showLoading();
      const baoCao = await getFormData(editorId, {
        id: nbDotDieuTriId,
        file,
        updateState: false,
        queries: payload,
      });
      const data = convert(baoCao);

      const res = await getListKhoaTongHop({
        ma: data[0]?.maKhoaNb,
        page: "",
        size: "",
      });
      const listPhieu = Object.keys(data).map((item) => {
        const dataPrint = data[item];

        dataPrint.maBhyt = res?.payload?.listDataTongHop?.[0]?.maBhyt;
        // Xử lý tách dịch vụ cấp 1 nếu dịch vụ cấp 1 tồn tại dịch vụ c2 là gói vật tư y tế
        if (
          [
            LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
            LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU_BH,
          ].includes(+queries.loai)
        ) {
          (data[item]["t"] || []).forEach((el) => {
            const k = [];

            el.k.forEach((e) => {
              const isTachDichVuC1 = e.n.some((i) =>
                i.tenNhomDichVuCap1.includes("Gói vật tư y tế")
              );
              if (isTachDichVuC1) {
                const groupByDichVuC1 = groupBy(e.n, "tenNhomDichVuCap1");
                Object.keys(groupByDichVuC1).forEach((tenNhomDichVuCap1) => {
                  const nhom = {
                    tenNhomDichVuCap1,
                    n: groupByDichVuC1[tenNhomDichVuCap1],
                    tienBhThanhToan: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienBhThanhToan,
                      0
                    ),
                    tienMienGiam: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienMienGiam,
                      0
                    ),
                    tienNb: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienNb,
                      0
                    ),
                    tienNbCungChiTra: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienNbCungChiTra,
                      0
                    ),
                    tienNbTuTra: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienNbTuTra,
                      0
                    ),
                    tienNbTuTra1: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienNbTuTra1,
                      0
                    ),
                    tienNbTuTra2: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienNbTuTra2,
                      0
                    ),
                    tienNguonKhac: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tienNguonKhac,
                      0
                    ),
                    tongTien: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tongTien,
                      0
                    ),
                    tongTienBh: groupByDichVuC1[tenNhomDichVuCap1].reduce(
                      (a, b) => a + b.tongTienBh,
                      0
                    ),
                  };
                  k.push(nhom);
                });
              } else {
                k.push(e);
              }
            });
            el.k = k;
          });
        }

        return {
          dataPrint,
          listTable: mapData(data[item]["t"], dataPrint.loai),
        };
      });

      setDataPrint(data[0]);
      return {
        listPhieu: listPhieu.length ? listPhieu : [{}],
      };
    } catch (error) {
      console.log("error", error);
      hideLoading();
      return {
        dataPrint: {},
        listTable: [],
        listPhieu: [],
      };
    }
  };
  const renderNumber = () => {
    const elements = [];
    const _length =
      queries.loai == LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU
        ? 6
        : queries.loai == 50
        ? 13
        : queries.loai != 10
        ? 15
        : 14;
    const listWidth =
      dataBANG_KE_CHI_PHI_KHUNG_DICH_VU === "PSHN" ? WIDTH_PSHN : WIDTH;
    for (let i = 1; i < _length; i++) {
      let _width = listWidth[i - 1];
      if (queries.loai == LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU) {
        _width += 30; //tăng thêm chiều rộng có cột
      }
      elements.push(
        <td
          key={i}
          style={{
            width: i === 1 ? "unset" : `${_width}px`,
            maxWidth: i === 1 ? "unset" : _width + "px",
            minWidth: i === 1 ? "unset" : _width + "px",
          }}
          className="center bold"
        >
          ({i})
        </td>
      );
    }
    return elements.map((item) => item);
  };
  const colgroup = () => {
    const arr = [];
    for (let i = 1; i < 10; i++) {
      arr.push(<td key={i}>{i}</td>);
    }
    return arr.map((item, index) => (
      <col key={index} style={{ textAlign: "center", color: "red" }}></col>
    ));
  };
  const renderBody = (data) => {
    return (data || []).map((row, idx) => {
      return (
        <tr key={idx}>
          {new Array(
            row.colMerge
              ? (queries.loai == LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU
                  ? 6
                  : queries.loai == 50
                  ? 13
                  : queries.loai != 10
                  ? 15
                  : 14) - row.colMerge || 1
              : queries.loai == LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU
              ? 5
              : queries.loai == 50
              ? 12
              : queries.loai != 10
              ? 14
              : 13
          )
            .fill(1)
            .map((item, index) => {
              const data = row[`col${index + 1}`] ? row[`col${index + 1}`] : "";
              const colIndex = index + 1;
              return (
                <td
                  key={index}
                  colSpan={row.colMerge && index == 0 ? row.colMerge : 0}
                  className={`col-${colIndex} ${row.isBold ? "bold" : ""}`}
                  style={{
                    textAlign:
                      row.colMerge && index === 1
                        ? "right"
                        : !row.colMerge && (index === 5 || index === 7)
                        ? "center"
                        : "",
                    wordBreak: index > 0 ? "normal" : "break-word",
                    ...(queries.maBaoCao === "EMR_BA134" &&
                    queries.ma === "P032"
                      ? {}
                      : {
                          paddingTop: "5px",
                          paddingBottom: "5px",
                        }),

                    ...(colIndex === 3 &&
                    queries.maBaoCao === "EMR_BA134" &&
                    queries.ma === "P032"
                      ? {
                          fontSize: 14,
                          fontWeight: "bold",
                        }
                      : {}),
                  }}
                >
                  {data}
                </td>
              );
            })}
        </tr>
      );
    });
  };
  const renderTime = (time, format = "DD/MM/YYYY") => {
    return time ? moment(time).format(format) : "";
  };

  const isHideTenNguoiLapBangKe = useMemo(() => {
    return (dataAN_HIEN_THI_TEN_NGUOI_LAP_PHIEU_TREN_BANG_KE || "")
      .split(",")
      .includes(queries.maBaoCao);
  }, [dataAN_HIEN_THI_TEN_NGUOI_LAP_PHIEU_TREN_BANG_KE]);

  const isShowMucHuongBHYT = useMemo(() => {
    return (
      dataPrint?.maTheBhyt &&
      dataHIEN_THI_MUC_HUONG_BHYT.eval() &&
      ((queries?.maBaoCao === "EMR_BA251" && queries.loai == 60) ||
        (queries?.maBaoCao === "EMR_BA134" && queries.loai == 10) ||
        (queries?.maBaoCao === "EMR_BA380" && queries.loai == 12) ||
        (queries?.maBaoCao === "EMR_BA381" && queries.loai == 22))
    );
  }, [dataHIEN_THI_MUC_HUONG_BHYT, dataPrint]);

  const listViTriChanKyBangKe = useMemo(() => {
    return dataSAP_XEP_VI_TRI_CHAN_KY_BANG_KE?.split(",").map((i) => i.trim());
  }, [dataSAP_XEP_VI_TRI_CHAN_KY_BANG_KE]);

  const numberSpan = useMemo(() => {
    const isChanKyNgang = dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE?.eval();
    const isChanKyDaiDienCoSo = dataTHIET_LAP_CHAN_KY_BANG_KE?.eval();
    const isDoiTuongKcb3 = dataPrint?.doiTuongKcb === 3;

    if (isChanKyNgang) {
      if (queries?.maBaoCao == "EMR_BA405") {
        return 12;
      }
      if (
        isArray(listViTriChanKyBangKe, 4) &&
        ["EMR_BA380", "EMR_BA381", "EMR_BA251", "EMR_BA134"].includes(
          queries?.maBaoCao
        )
      ) {
        return 6;
      }
      if (isChanKyDaiDienCoSo && isDoiTuongKcb3) {
        return "4-8";
      }
      return 6;
    }

    if (isChanKyDaiDienCoSo && isDoiTuongKcb3) {
      return 8;
    }
    return 12;
  }, [
    dataTHIET_LAP_CHAN_KY_BANG_KE,
    dataPrint?.doiTuongKcb,
    dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE,
    listViTriChanKyBangKe,
  ]);
  const showButtonPrint = useMemo(() => queries.notPrint === "true");

  const hideTenNguoiInBangKe = useMemo(() => {
    let listMaPhieuAnChanKy = dataAN_TEN_NGUOI_IN_BANG_KE_BHYT
      ?.split(",")
      .map((i) => i.trim());
    if (isArray(listMaPhieuAnChanKy, true) && queries.ma) {
      return listMaPhieuAnChanKy.includes(queries.ma);
    }
    return false;
  }, [dataAN_TEN_NGUOI_IN_BANG_KE_BHYT]);

  const print = async () => {
    try {
      showLoading();
      const { pdfUrls } = await pdfGenerator({
        layout: pageType.A4.v,
        resultHml: false,
        htmlNotEdit: true,
        pageType: "A4",
      });
      const filePdf = await addFooterPage({
        dataPrint,
        urls: pdfUrls,
        showSdt: hideTenNguoiInBangKe,
      });

      let dsFilePdf = [filePdf];

      if (dsPhieuInKem.length > 0) {
        const { nbDotDieuTriId, chiDinhTuLoaiDichVu } = queries || {};

        const { finalFile } = await getFilePhieuIn({
          listPhieus: dsPhieuInKem,
          showError: true,
          nbDotDieuTriId,
          chiDinhTuLoaiDichVu,
        });

        dsFilePdf.push(finalFile);
      }

      hideLoading();

      const s = await centralizedErrorHandling(
        printProvider.getMergePdf(dsFilePdf)
      );

      printJS({
        printable: s,
        type: "pdf",
        onPrintDialogClose: () => {
          if (!(queries.khongDongTab == "true")) {
            window.close();
          }
        },
      });
    } catch (error) {
      console.log(error);
      hideLoading();
    }
  };

  const refreshPhieuKy = async () => {
    try {
      const { kySo, maPhieuKy, ma, ...rest } = queries || {};
      let phieuKy = null;
      let _listPhieu = [];

      if (kySo && (maPhieuKy || rest.lichSuKyId)) {
        if (!rest.lichSuKyId) {
          _listPhieu = await getListPhieu({
            ...rest,
          });
          phieuKy = (_listPhieu || []).find((x) => x.ma === maPhieuKy);
        } else {
          _listPhieu = await getById({
            id: rest.lichSuKyId,
            isAlwayResolve: true,
          });
          // Nếu k tồn tại lịch sử ký id thì gọi lại lấy danh sách phiếu
          phieuKy = {
            dsSoPhieu: _listPhieu,
          };
          if (isObject(_listPhieu) && _listPhieu.code === 602) {
            _listPhieu = await getListPhieu({
              ...rest,
            });
            phieuKy = (_listPhieu || []).find((x) => x.ma === maPhieuKy);
          }
        }
        if (phieuKy) {
          let dsPhieuKy = [];
          (phieuKy.dsSoPhieu || []).forEach((element) => {
            let _phieu = { ...phieuKy, dsSoPhieu: [element] };

            dsPhieuKy.push(getThongTinKySo(_phieu));
          });
          updateData({ dsPhieuKy });
        }
      } else {
        _listPhieu = await getListPhieu({
          ...rest,
        });

        phieuKy = (_listPhieu || []).find((x) => x.ma === ma);
      }

      if (
        _listPhieu.length > 0 &&
        phieuKy &&
        (phieuKy.dsPhieuInKemTheoId || []).length > 0
      ) {
        const _dsPhieuInKemTheo = _listPhieu.filter(
          (x) =>
            phieuKy.dsPhieuInKemTheoId.includes(x.id) &&
            x.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHI_XEM
        );

        setDsPhieuInKem(_dsPhieuInKemTheo);
      } else {
        setDsPhieuInKem([]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getData = async (maBaoCao) => {
    try {
      const baoCao = await getMauBaoCaoByMa({ editorId, maBaoCao });
      setDsChuKyProps(
        (baoCao?.components || []).filter((item) => item.type === "imageSign")
      );
    } catch (error) {
      console.error(error);
    }
  };

  const onChangePhim = (key) => (value) => {
    dataPrint[key] = value;
    nbDotDieuTriProvider.updateSoPhim({
      id: dataPrint.id,
      loaiPhim: dataPrint.loaiPhim,
      soPhim: dataPrint.soPhim,
    });
  };

  useEffect(() => {
    refreshPhieuKy();

    const { maBaoCao } = queries;
    if (maBaoCao) {
      getData(maBaoCao);
    }
  }, [queries]);

  const isTangSizeTienTraLai = useMemo(() => {
    return (
      dataTANG_CO_CHU_CHAN_TONG_TIEN_BANG_KE_TONG_HOP.eval() &&
      ["P178", "P032", "P678"].includes(queries.ma) &&
      dataPrint?.tienTraLai > 0
    );
  }, [dataTANG_CO_CHU_CHAN_TONG_TIEN_BANG_KE_TONG_HOP, dataPrint]);

  const isTangSizeTienThuThem = useMemo(() => {
    return (
      dataTANG_CO_CHU_CHAN_TONG_TIEN_BANG_KE_TONG_HOP.eval() &&
      ["P178", "P032", "P678"].includes(queries.ma) &&
      dataPrint?.tienThuThem > 0
    );
  }, [dataTANG_CO_CHU_CHAN_TONG_TIEN_BANG_KE_TONG_HOP, dataPrint]);

  //bảng kê Tiện ích với NB ngoại trú
  const isBangKeTienIchNbNgoaiTru = queries.ma == "P178" && queries.loai == 60;

  const showSoTienTamUng = dataBANG_KE_BHYT_HIEN_THI_SO_TIEN_TAM_UNG?.eval();

  const showTextQuanHeVoiNguoiBenh =
    dataHIEN_THI_TEXT_MOI_QUAN_HE_TREN_BANG_KE?.eval();

  const hideTenNguoiLapBangKe =
    dataAN_TEN_NGUOI_LAP_BANG_KE_BHYT?.eval() &&
    ["EMR_BA251", "EMR_BA134", "EMR_BA380", "EMR_BA381", "EMR_BA386"].includes(
      queries?.maBaoCao
    );

  const gopChanTongKhiQuaTrang = useMemo(() => {
    return dataBANG_KE_BHYT_GOP_CHAN_TONG_KHI_QUA_TRANG
      ?.split(",")
      .map((i) => i.trim())
      .includes(queries.ma);
  }, [dataBANG_KE_BHYT_GOP_CHAN_TONG_KHI_QUA_TRANG]);

  const showTextNguoiBaoLanh = dataHIEN_THI_NGUOI_BAO_LANH_BANG_KE?.eval();
  const hideTextToiDaNhanPhim =
    dataKHONG_HIEN_THI_TEXT_NHAN_PHIM_TREN_BANG_KE?.eval();
  const showTextNguoiBenhKyTen =
    dataHIEN_THI_TEN_NB_BANG_KE.eval() &&
    [LOAI_IN_BANG_KE_CHI_PHI.TONG_HOP, LOAI_IN_BANG_KE_CHI_PHI.NOI_TRU].some(
      (el) => el == queries.loai
    );

  const formatName = (str) => {
    return str
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const hienThiTienDvPhaiTraKhac = (() => {
    const val = (dataBANG_KE_HIEN_THI_TIEN_DV_PHAI_TRA_KHAC || "")
      .trim()
      .toLowerCase();

    return val === "true" || val === "";
  })();

  return {
    renderPrice,
    mapData,
    renderNumber,
    getDataToPrint,
    colgroup,
    renderBody,
    renderTime,
    numberSpan,
    showButtonPrint,
    print,
    refreshPhieuKy,
    dsChuKyProps,
    isHideTenNguoiLapBangKe,
    isTangSizeTienTraLai,
    isTangSizeTienThuThem,
    isBangKeTienIchNbNgoaiTru,
    hideTenNguoiInBangKe,
    listViTriChanKyBangKe,
    showTextQuanHeVoiNguoiBenh,
    hideTenNguoiLapBangKe,
    showSoTienTamUng,
    isShowMucHuongBHYT,
    gopChanTongKhiQuaTrang,
    showTextNguoiBaoLanh,
    hideTextToiDaNhanPhim,
    onChangePhim,
    showTextNguoiBenhKyTen,
    formatName,
    dsPhieuInKem,
    hienThiTienDvPhaiTraKhac,
  };
};
export default useBangKe;
