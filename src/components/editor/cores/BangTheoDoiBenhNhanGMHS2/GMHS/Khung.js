import React, { useState, useEffect, useRef, useMemo, Fragment } from "react";
import { cloneDeep, get, isEmpty } from "lodash";
import { Button, Checkbox, Input, Modal } from "antd";
import { BLOOD_PRESSURE } from "utils/vital-signs/constants";
import { handleBloodPressure } from "utils/vital-signs/canvas-utils";
import DropDownList from "../../BangTheoDoiBenhNhanHSTC/DropDownList";
import VitalSigns from "./VitalSigns";
import Arrow from "./Arrow";
import { DeboundInput } from "components/editor/config";
import moment from "moment";
import { PlusOutlined, CloseOutlined } from "@ant-design/icons";
import { NHIETS, SIZE } from "../constans";
import { useDispatch } from "react-redux";
import { useStore } from "hooks";
import { LOAI_DICH_VU } from "constants/index";
import { isArray, safeConvertToArray } from "utils";
import { SVG } from "assets";
import CheckGroups from "../../CheckGroups";

const COL_NUM = 1000;
const ASA = [
  { ten: "1", value: 1 },
  { ten: "2", value: 2 },
  { ten: "3", value: 3 },
  { ten: "4", value: 4 },
  { ten: "5", value: 5 },
];
const MALALMAPATI = [
  { ten: "1", value: 1 },
  { ten: "2", value: 2 },
  { ten: "3", value: 3 },
  { ten: "4", value: 4 },
];

const Khung = (
  {
    itemProps,
    form,
    dataForm,
    formChange,
    block,
    refModalBloodPressure,
    refModalInputRespiratory,
    khung = {},
    khungIndex,
    rowId,
    refModalAddLabel,
    refModalInputDongTu,
    refModalSelectCSS,
    refModalRemoveColunm,
    canvasWidth,
    canvasHeight,
    columnWidth,
    onRemove,
    disabled,
    indexKhung,
    show5pMoiCot,
    hienThiThoiGianMoiCot,
    ...props
  },
  refs
) => {
  const COLUMNS = new Array(itemProps.colNumber || 12)
    .fill({})
    .map((el, index) => index);
  const refState = useRef({
    data: [],
    rangeBloodPressure: BLOOD_PRESSURE[5].listShow,
  });
  const refCanNang = useRef(null);
  const refUpdateCanNang = useRef(null);

  const [state, _setState] = useState(refState.current);
  const setState = (data = {}) => {
    _setState((state) => {
      refState.current = { ...refState.current, ...data };
      return refState.current;
    });
  };

  const listAllThuoc = useStore("danhMucThuoc.listAllData", []);

  const silentUpdateState = (data = {}) => {
    refState.current = { ...refState.current, ...data };
    for (let key in refState.current) {
      state[key] = refState.current[key];
    }
  };

  const updateCanNangCSS = (canNang) => {
    refCanNang.current = canNang;

    const values = getValuesVitalSigns();
    onChangeMultiCss(values);
  };
  refUpdateCanNang.current = updateCanNangCSS;

  useEffect(() => {
    const eventMessage = (event = {}) => {
      if (event.data?.TYPE === "CHANGE_VALUE_EDITOR") {
        const value = event.data?.value || {};
        //Bắt giá trị canNang thay đổi
        if (value.key == "canNang") {
          refUpdateCanNang.current && refUpdateCanNang.current(value.value);
        }
      }
    };
    window.addEventListener("message", eventMessage, false);
    return () => {
      window.removeEventListener("message", eventMessage);
    };
  }, []);

  useEffect(() => {
    khung.rangeBloodPressure = getRangeBloodPressure(khung || {});
    setState(khung || {});
  }, [khung]);

  useEffect(() => {
    refCanNang.current = dataForm.canNang;
  }, [dataForm?.canNang]);

  const addFieldCSS = (dataCSS) => {
    let _addFields = {};

    if (!dataCSS.khoaChiDinhId) {
      _addFields.khoaChiDinhId = dataForm?.khoaThucHienId;
    }
    if (!dataCSS.nbDotDieuTriId) {
      _addFields.nbDotDieuTriId = dataForm?.nbDotDieuTriId;
    }
    if (!dataCSS.chiDinhTuLoaiDichVu) {
      _addFields.chiDinhTuLoaiDichVu = LOAI_DICH_VU.PHAU_THUAT_THU_THUAT;
    }

    return _addFields;
  };

  const getNhipTho = (index) => {
    const nhipTho = state[["chiSo", "cot" + (index + 1), "nhipTho"].join("_")];
    const troTho =
      state[["chiSo", "cot" + (index + 1), "troTho"].join("_")] || null;

    if (!nhipTho) return "";

    if (troTho == 10) {
      return `${nhipTho} (BB)`;
    } else if (troTho == 20) {
      return `${nhipTho} (TM)`;
    } else if (troTho == 30) {
      return `${nhipTho} (TT)`;
    } else {
      return nhipTho;
    }
  };

  const onChangeNhipTho = (index) => () => {
    const nhipTho = getNhipTho(index) || "";

    refModalInputRespiratory.current.show({ value: nhipTho }, (s) => {
      const _nhipTho = s
        .replace(" (BB)", "")
        .replace(" (TM)", "")
        .replace(" (TT)", "");
      const _troTho =
        s.indexOf("(BB)") > -1
          ? 10
          : s.indexOf("(TM)") > -1
          ? 20
          : s.indexOf("(TT)") > -1
          ? 30
          : null;

      const key = ["chiSo", "cot" + (index + 1)];
      const newState = {
        [[...key, "nhipTho"].join("_")]: _nhipTho,
        [[...key, "troTho"].join("_")]: _troTho,
      };
      setState(newState);
      formChange.setMultiData(rowId, {
        [[...key, "nhipTho"].join("_")]: _nhipTho,
        [[...key, "troTho"].join("_")]: _troTho,
      });
    });
  };

  const onAddNhan = (index) => () => {
    //kiểm tra nếu chưa thêm thời gian thì bỏ qua
    // if (!state[["chiSo", "cot" + (index + 1), "thoiGianThucHien"].join("_")])
    //   return;
    if (disabled) return;
    const type = ["chiSo", "cot" + (index + 1), "gayMe"];
    const data = state[type.join("_")] || [];
    refModalAddLabel.current &&
      refModalAddLabel.current.show({ data }, (data) => {
        onChangeValueKhung(type)(data);
      });
  };

  const onChangeValueChiSoSong =
    (type, colIndex, saveToState = true) =>
    (value = "") => {
      let fieldName = "";
      if (Array.isArray(type)) {
        fieldName = type.join("_");
        if (saveToState) {
          setState({ [fieldName]: value });
        } else {
          silentUpdateState({ [fieldName]: value });
        }

        let _addFields = {};
        if (!state[`chiSo_cot${colIndex + 1}_id`]) {
          if (!state[`chiSo_cot${colIndex + 1}_khoaChiDinhId`]) {
            _addFields[`chiSo_cot${colIndex + 1}_khoaChiDinhId`] =
              dataForm?.khoaThucHienId;
          }
          if (!state[`chiSo_cot${colIndex + 1}_nbDotDieuTriId`]) {
            _addFields[`chiSo_cot${colIndex + 1}_nbDotDieuTriId`] =
              dataForm?.nbDotDieuTriId;
          }
          if (!state[`chiSo_cot${colIndex + 1}_chiDinhTuLoaiDichVu`]) {
            _addFields[`chiSo_cot${colIndex + 1}_chiDinhTuLoaiDichVu`] =
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT;
          }
        }

        formChange.setMultiData(rowId, {
          [[fieldName].join("_")]: value,
          ..._addFields,
        });
        return;
      }
    };

  const onChangeMultiCss = (values) => {
    let data = {};

    values.forEach((item, index) => {
      data[`chiSo_cot${index + 1}_mach`] = item.mach;
      data[`chiSo_cot${index + 1}_huyetApTamTruong`] = item.huyetApTamTruong;
      data[`chiSo_cot${index + 1}_huyetApTamThu`] = item.huyetApTamThu;
      data[`chiSo_cot${index + 1}_thoiGianThucHien`] = item.thoiGianThucHien;

      if (!item.id) {
        const _addFields = addFieldCSS(item);

        Object.keys(_addFields).forEach((key) => {
          data[`chiSo_cot${index + 1}_${key}`] = _addFields[key];
        });
      }

      //add thêm cân nặng vào chỉ số sống
      if (
        ["mach", "huyetApTamTruong", "huyetApTamThu", "thoiGianThucHien"].some(
          (key) => item[key]
        )
      ) {
        data[`chiSo_cot${index + 1}_canNang`] = refCanNang.current;
      }
    });
    setState(cloneDeep(data));
    formChange.setMultiData(rowId, data);
  };

  const onChangeValueKhung =
    (type, saveToState = true) =>
    (value = "") => {
      if (!formChange) return;
      let fieldName = "";
      if (Array.isArray(type)) {
        fieldName = type.join("_");
        if (saveToState) {
          setState({ [fieldName]: value });
        } else {
          silentUpdateState({ [fieldName]: value });
        }
        formChange.onChange(rowId, [...type].join("_"), value);
      }
    };

  const onChangeValueChung =
    (type, saveToState = true) =>
    (value = "") => {
      if (!formChange) return;
      const newState = {};
      if (
        [
          "asa",
          "daDayDay",
          "capCuu",
          "malalmapati",
          "diUng",
          "tienSu",
          "thuocDangDieuTri",
          "batThuongLamSang",
          "may",
          "me",
          "altmtu",
          "bis",
          "mac",
          "tof",
          "tongMatMau",
          "tongNuocTieu",
          "thoiDiemConRa2",
        ].includes(type)
      ) {
        if (type === "daDayDay" || type === "capCuu") {
          value = value.target.checked;
          newState[type] = value;
        } else {
          newState[type] = value;
        }

        formChange.onChange(null, type, value);
        if (saveToState) {
          setState(newState);
        } else {
          silentUpdateState(newState);
        }
      }
    };

  const getRangeBloodPressure = (state) => {
    const values = new Array(itemProps.colNumber || 12)
      .fill()
      .map(() => cloneDeep({}));
    values.forEach((item, index) => {
      const key = ["chiSo", "cot" + (index + 1)];
      const tamThu = state[[...key, "huyetApTamThu"].join("_")];
      const tamTruong = state[[...key, "huyetApTamTruong"].join("_")];
      if (tamThu && tamTruong) {
        item.huyetAp = `${tamThu}/${tamTruong}`;
      }
    });
    const cloneValues = cloneDeep(values);
    const indexOfLastItemHasValue =
      cloneValues.length -
      1 -
      cloneValues.reverse().findIndex((item) => !!item.huyetAp);
    const newValue = handleBloodPressure(
      values[indexOfLastItemHasValue] && values[indexOfLastItemHasValue].huyetAp
    );

    const listShow =
      BLOOD_PRESSURE.find(
        (item) => item.min <= newValue.systolic && newValue.systolic <= item.max
      ) &&
      BLOOD_PRESSURE.find(
        (item) => item.min <= newValue.systolic && newValue.systolic <= item.max
      ).listShow;

    if (!isEmpty(listShow)) {
      return listShow || [];
    } else {
      return BLOOD_PRESSURE[itemProps.mauBangSinhHieu === 2 ? 6 : 5].listShow;
    }
  };

  const isDisable = (index, key) => {
    return disabled;
  };

  const colTongCong = <td className="center bold vamid">TỔNG CỘNG</td>;

  // Yêu cầu: có moment + lodash.get
  // Ý nghĩa: gom toàn bộ bản ghi gây mê từ 23 cột, lọc bản ghi có thời gian,
  // sắp xếp theo thời gian, ghép cặp trangThai 1->2 và cộng dồn tổng thời gian.
  // Nếu chỉ có 1 cặp, kết quả giống phiên bản cũ (end - start).

  function msToHMS(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    let result = "";
    if (hours > 0) result += `${hours} giờ `;
    if (minutes > 0) result += `${minutes} phút `;
    if (seconds > 0 || result === "") result += `${seconds} giây`;

    return result.trim();
  }

  const tongGayMe = () => {
    try {
      // 1) Gom toàn bộ dữ liệu từ 23 cột
      const all = [];
      for (
        let i = 1;
        i <= (itemProps.colNumber ? +itemProps.colNumber : 12);
        i++
      ) {
        const arr = get(state, `chiSo_cot${i}_gayMe`, []);
        if (Array.isArray(arr)) {
          for (const it of arr) {
            if (it && it.thoiGianGanNhan) {
              all.push(it);
            }
          }
        }
      }

      if (!all.length) return "";

      // 2) Nhóm theo id (mỗi ca gây mê riêng)
      const grouped = all.reduce((acc, item) => {
        const key = item.id; // thay bằng field định danh bệnh nhân/ca
        if (!acc[key]) acc[key] = [];
        acc[key].push(item);
        return acc;
      }, {});

      let totalMs = 0;

      // 3) Với từng nhóm id, sắp xếp theo thời gian, rồi tính tổng theo cặp (1 -> 2)
      Object.values(grouped).forEach((items) => {
        items.sort(
          (a, b) =>
            moment(a.thoiGianGanNhan).valueOf() -
            moment(b.thoiGianGanNhan).valueOf()
        );

        let currentStart = null;
        for (const item of items) {
          const status = Number(item.trangThai);
          if (status === 1 && !currentStart) {
            currentStart = moment(item.thoiGianGanNhan);
          } else if (status === 2 && currentStart) {
            const end = moment(item.thoiGianGanNhan);
            if (end.isAfter(currentStart)) {
              totalMs += end.diff(currentStart);
            }
            currentStart = null;
          }
        }
      });

      if (totalMs <= 0) return "";

      // 4) Format tổng thời gian
      const thoiGianMe = msToHMS(totalMs);

      // 5) Lưu giá trị ra ngoài
      onChangeValueKhung(["thoiGianMe"], false)(thoiGianMe);

      return thoiGianMe;
    } catch (error) {
      console.error(error);
      return "";
    }
  };

  const rowSpanHoHap = useMemo(() => {
    let row = 5;
    if (itemProps.showThuocDangKhi) {
      row += 1;
    }
    return row;
  }, [itemProps]);

  //RENDER + ACTION THUỐC
  const onChangeThuoc = (thuoc, index, isThuocThuong) => (value) => {
    thuoc.soLuong[index].soLuong = value;
    thuoc.tong2 =
      (thuoc.tong1 || 0) +
      thuoc.soLuong.reduce((a, b) => a + (b.soLuong || 0), 0);

    const key = isThuocThuong ? "dsThuoc" : "dsThuocDichTruyen";

    onChangeValueKhung([key], false)(state[key]);
  };

  const onAddThuoc = (isThuocThuong) => () => {
    const newThuoc = {};
    const soLuong = Array(itemProps.colNumber || 12)
      .fill({})
      .map((item, index2) => ({
        stt: index2 + 1,
        soLuong: null,
      }));
    newThuoc.ten = "";
    newThuoc.soLuong = soLuong;
    newThuoc.tong1 = 0;
    newThuoc.tong2 = 0;
    newThuoc.ghiChu = "";

    const key = isThuocThuong ? "dsThuoc" : "dsThuocDichTruyen";

    let _dsThuoc = [...state[key], newThuoc];
    state[key] = _dsThuoc;
    setState({
      [key]: _dsThuoc,
    });

    onChangeValueKhung([key], false)(state[key]);
  };

  const onRemoveThuoc = (thuoc, isThuocThuong) => {
    Modal.confirm({
      content: `Bạn có chắc chắn muốn xóa thuốc ${thuoc.ten} ra khỏi bảng!`,
      onOk() {
        const key = isThuocThuong ? "dsThuoc" : "dsThuocDichTruyen";

        const _dsThuoc = state[key].filter((item) => item.ten !== thuoc.ten);
        setState({
          [key]: _dsThuoc,
        });
        onChangeValueKhung([key], false)(_dsThuoc);
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };

  const onChangeThuocKhac = (thuoc, type, isThuocThuong) => (value) => {
    if (type == "id") {
      const _selectedThuoc = listAllThuoc.find((x) => x.id === value);
      if (_selectedThuoc) {
        thuoc.ten = _selectedThuoc.ten;
        thuoc.tenDonViTinh = _selectedThuoc.tenDvtSuDung;
      }
    }

    thuoc[type] = value;
    const key = isThuocThuong ? "dsThuoc" : "dsThuocDichTruyen";

    onChangeValueKhung([key], false)(state[key]);
  };

  const onChangeTextGhiChu = (thuoc, isThuocThuong) => (value) => {
    thuoc["ghiChu"] = value;
    const key = isThuocThuong ? "dsThuoc" : "dsThuocDichTruyen";

    onChangeValueKhung([key], false)(state[key]);
  };

  const renderThuoc = useMemo(() => {
    const dsThuocThuong = state.dsThuoc || [];
    const dsThuocDichTruyen = state.dsThuocDichTruyen || [];

    const render = (dsThuoc, isThuocThuong) => {
      return dsThuoc.map((thuoc, index) => {
        return (
          <Fragment key={index}>
            <tr>
              {index == 0 && (
                <td rowSpan={dsThuoc.length} colSpan={1} className="col-lv1">
                  {isThuocThuong
                    ? itemProps.labels?.thuoc || "THUỐC"
                    : itemProps.labels?.dichTruyen || "DỊCH TRUYỀN"}
                </td>
              )}
              <td colSpan={4} className="vamid p02 group-btn">
                {index === dsThuoc.length - 1 && (
                  <Button
                    className="btn-add"
                    icon={<PlusOutlined />}
                    onClick={onAddThuoc(isThuocThuong)}
                  ></Button>
                )}

                {!thuoc?.thuocMacDinh && !thuoc?.thuocKhac ? (
                  <>
                    <DropDownList
                      disabled={isDisable(null)}
                      title="Chọn Thuốc"
                      className={"f1 drop-list"}
                      dataSource={listAllThuoc}
                      value={thuoc?.id}
                      onChange={onChangeThuocKhac(thuoc, "id", isThuocThuong)}
                    />
                  </>
                ) : (
                  `${thuoc.ten || ""} ${
                    thuoc?.tenDonViTinh ? `(${thuoc?.tenDonViTinh})` : ""
                  }`
                )}

                <Button
                  icon={<CloseOutlined />}
                  className="btn-close"
                  onClick={() => onRemoveThuoc(thuoc, isThuocThuong)}
                />
              </td>
              {COLUMNS.map((item, idx) => {
                const checkActive = idx === state?.indexColumn;
                return (
                  <td
                    className={`col-lv6 center vamid a ${
                      checkActive ? " active-column" : ""
                    }`}
                    key={idx}
                  >
                    <DeboundInput
                      readOnly={isDisable(idx)}
                      size={"small"}
                      step={1}
                      min={0}
                      value={thuoc?.soLuong?.[idx]?.soLuong || null}
                      onChange={onChangeThuoc(thuoc, idx, isThuocThuong)}
                      type="number"
                    />
                  </td>
                );
              })}
              <td className="vamid center">
                <div style={{ display: "flex" }}>
                  <span style={{ width: "50%" }}>{`${
                    thuoc.tong2 != null ? thuoc.tong2 : ""
                  } ${
                    itemProps.anDonViSuDung ? "" : thuoc.tenDonViTinh || ""
                  }`}</span>
                  <span style={{ width: "50%" }}>
                    <DeboundInput
                      rows={1}
                      readOnly={isDisable(index)}
                      value={thuoc?.ghiChu || ""}
                      onChange={onChangeTextGhiChu(thuoc, isThuocThuong)}
                      type="multipleline"
                      lineHeightText={1}
                      fontSize={9}
                      minHeight={9 + 6}
                    />
                  </span>
                </div>
              </td>
            </tr>
          </Fragment>
        );
      });
    };

    return {
      thuocThuong: render(dsThuocThuong, true),
      thuocDichTruyen: render(dsThuocDichTruyen),
    };
  }, [
    state.dsThuoc,
    state.dsThuocDichTruyen,
    state?.indexColumn,
    listAllThuoc,
  ]);
  //END RENDER + ACTION THUỐC

  //get giá trị nhãn bắt đầu - kết thúc
  const getValuesNhan = () => {
    const values = new Array(itemProps.colNumber || 12)
      .fill()
      .map(() => cloneDeep({}));
    values.forEach((item, index) => {
      const key = ["chiSo", "cot" + (index + 1)];
      let fieldName = [...key, "gayMe"];
      const gayMe = state[fieldName.join("_")];

      if (gayMe?.length > 0) {
        item.thoiGianThucHien = gayMe[0].thoiGianGanNhan;
      }
    });
    return values;
  };

  const getValuesVitalSigns = () => {
    const values = new Array(itemProps.colNumber || 12)
      .fill()
      .map(() => cloneDeep({}));
    let data = {};

    values.forEach((item, index) => {
      const key = ["chiSo", "cot" + (index + 1)];
      let fieldName = [...key, "nhietDo"];
      item.nhietDo = state[fieldName.join("_")];
      fieldName = [...key, "mach"];
      item.mach = state[fieldName.join("_")];
      fieldName = [...key, "canNang"];
      item.canNang = state[fieldName.join("_")];
      const tamThu = state[[...key, "huyetApTamThu"].join("_")];
      const tamTruong = state[[...key, "huyetApTamTruong"].join("_")];
      item.huyetApTamThu = state[[...key, "huyetApTamThu"].join("_")];
      item.huyetApTamTruong = state[[...key, "huyetApTamTruong"].join("_")];
      item.thoiGianThucHien = state[[...key, "thoiGianThucHien"].join("_")];
      item.id = state[[...key, "id"].join("_")];
      item.khoaChiDinhId = state[[...key, "khoaChiDinhId"].join("_")];
      item.nbDotDieuTriId = state[[...key, "nbDotDieuTriId"].join("_")];
      item.chiDinhTuLoaiDichVu =
        state[[...key, "chiDinhTuLoaiDichVu"].join("_")];

      if (item.id) {
        const _addFields = addFieldCSS(item);

        Object.keys(_addFields).forEach((fieldKey) => {
          item[fieldKey] = _addFields[fieldKey];
          data[[...key, fieldKey].join("_")] = _addFields[fieldKey];
        });
      }

      if (tamThu && tamTruong) {
        item.huyetAp = `${tamThu}/${tamTruong}`;
      }
    });

    if (rowId && Object.keys(data).length > 0) {
      formChange.setMultiData(rowId, data);
    }
    return values;
  };

  const onChangeindexColumn = (index) => () => {
    if (index !== state.indexColumn) {
      setState({
        indexColumn: index,
      });
    }
  };

  const changeindexColumn = (index) => {
    if (index !== state.indexColumn) {
      setState({
        indexColumn: index,
      });
    }
  };

  const calculateTabIndex = (idx, rowNo) => (idx + 1) * COL_NUM + rowNo;

  return (
    <Fragment>
      <tr>
        <td colSpan={3} className="bold center remove-col">
          {/* 
            nhưng khung2 được phép xuất hiện nút xóa khung
          */}
          {itemProps.choPhepXoaBang && khungIndex && !disabled ? (
            <SVG.IcDelete className="btn-remove-khung" onClick={onRemove} />
          ) : null}
        </td>
        <td colSpan={2} className="bold center vamid">
          <div className="vital-sign-left-column">GIỜ</div>
        </td>
        {COLUMNS.map((item, index) => {
          let number = khungIndex * 120 + (index + 1) * 10;

          if (itemProps.hienThiThoiGianMoiCot) {
            const thoiGian = state[`chiSo_cot${index + 1}_thoiGianThucHien`];
            number = thoiGian ? (
              <div>
                <div style={{ marginBottom: 2 }}>
                  {" "}
                  {moment(thoiGian).format("DD/MM")}
                </div>
                <div> {moment(thoiGian).format("HH:mm")}</div>
              </div>
            ) : (
              ""
            );
          } else {
            if (itemProps.show5pMoiCot) {
              number = khungIndex * 60 + (index + 1) * 5;
            }

            if (itemProps.colNumber === 24) {
              number = (index * 10) % 60;
            }
          }

          return (
            <td className="col-lv6 center" key={index}>
              <div className="time">{number}</div>
            </td>
          );
        })}
        <td className="col-lv7 p22 nhanXet" rowSpan={4}>
          <div className="bold center fixWidth2 mb10">
            {itemProps.labels?.nhanXet || "NHẬN XÉT VÀ KẾT LUẬN"}
          </div>
          <DeboundInput
            readOnly={isDisable(null, ["ketLuan"])}
            onChange={onChangeValueKhung(["ketLuan"], false)}
            type="multipleline"
            size={2000}
            value={state.ketLuan || ""}
            lineHeightText={1}
            fontSize={9}
            minHeight={9 * 50 + 6}
          />
        </td>
      </tr>
      <tr style={{ height: 50 }}>
        <td rowSpan={2} colSpan={3} className="p02 danhGia">
          <div className="bold m20 fixWidth1">ĐÁNH GIÁ TRƯỚC MỔ:</div>
          <div className="flex mb5">
            <label>- ASA: </label>
            <DropDownList
              disabled={isDisable(null)}
              title="Chọn ASA"
              className={"f1 drop-list"}
              dataSource={ASA}
              value={state.asa}
              onChange={(value) =>
                onChangeValueChung("asa")(
                  isArray(value)
                    ? value
                    : value
                    ? safeConvertToArray(value)
                    : null
                )
              }
            />
          </div>
          <div className="cbox ml10 mb5">
            <Checkbox
              checked={state.daDayDay}
              onChange={onChangeValueChung("daDayDay")}
            />
            <label className="label">Dạ dày đầy</label>
          </div>
          <div className="cbox ml10 mb5">
            <Checkbox
              checked={state.capCuu}
              onChange={onChangeValueChung("capCuu")}
            />
            <label className="label">Cấp cứu</label>
          </div>
          <div className="flex mb5">
            <label>- Malalmapati: </label>
            <DropDownList
              disabled={isDisable(null)}
              title="Chọn Mallampati"
              className={"f1 drop-list"}
              dataSource={MALALMAPATI}
              value={state.malalmapati}
              onChange={(value) =>
                onChangeValueChung("malalmapati")(
                  isArray(value)
                    ? value
                    : value
                    ? safeConvertToArray(value)
                    : null
                )
              }
            />
          </div>
          <div className="flex fdc mb10">
            <label className="block mb5">- Dị ứng: </label>
            <DeboundInput
              value={state.diUng || ""}
              rows={2}
              readOnly={isDisable(null, ["diUng"])}
              onChange={onChangeValueChung("diUng", false)}
              type="multipleline"
              lineHeightText={1}
              fontSize={9}
              minHeight={9 * 2 + 6}
            />
          </div>
          <div className="fdc mb10">
            <label className="block block mb5">
              - Tiền sử/ Thuốc liên quan GHMS:
            </label>
            <DeboundInput
              value={state.tienSu || ""}
              readOnly={isDisable(null, ["tienSu"])}
              onChange={onChangeValueChung("tienSu", false)}
              type="multipleline"
              lineHeightText={1}
              fontSize={9}
              minHeight={9 * 2 + 6}
            />
            <DeboundInput
              value={state.thuocDangDieuTri || ""}
              readOnly={isDisable(null, ["thuocDangDieuTri"])}
              onChange={onChangeValueChung("thuocDangDieuTri", false)}
              type="multipleline"
              lineHeightText={1}
              fontSize={9}
              minHeight={9 * 2 + 6}
            />
          </div>
          <div className="flex fdc mb10">
            <label className="block mb5">
              - Bất thường lâm sàng/ cận lâm sàng liên quan GMHS:{" "}
            </label>
            <DeboundInput
              value={state.batThuongLamSang || ""}
              readOnly={isDisable(null, ["batThuongLamSang"])}
              onChange={onChangeValueChung("batThuongLamSang", false)}
              type="multipleline"
              lineHeightText={1}
              fontSize={9}
              minHeight={9 * 2 + 6}
            />
          </div>
        </td>
        <td colSpan={2} className="vamid">
          <div className="flex acenter jcenter">
            <Arrow start={1} height={10} className="mr5" />
            <div className="w50">Bắt đầu</div>
          </div>
          <div className="flex acenter jcenter">
            <Arrow start={0} height={10} className="mr5" />
            <div className="w50">Kết thúc</div>
          </div>
        </td>
        {COLUMNS.map((item, index) => {
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center curp ${
                checkActive ? "active-column" : ""
              }`}
              key={index}
              onClick={onAddNhan(index)}
            >
              <div className="flex jcenter acenter fdc">
                {(
                  get(
                    state,
                    ["chiSo", "cot" + (index + 1), "gayMe"].join("_")
                  ) || []
                ).map((label, idx) => {
                  return (
                    <>
                      {label.trangThai === 1 && <span>{label.ten || ""}</span>}
                      <Arrow
                        value={label.id}
                        start={label.trangThai === 1 ? 1 : 0}
                        key={idx}
                      />
                      {label.trangThai !== 1 && <span>{label.ten || ""}</span>}
                      {label.thoiGianGanNhan
                        ? moment(label.thoiGianGanNhan).format("HH:mm")
                        : ""}
                    </>
                  );
                })}

                {itemProps.showthoiDiemConRa &&
                (state.thoiDiemConRa2 || []).some((e) => e == index) ? (
                  <span
                    style={{
                      fontSize: 20,
                      color: "red",
                      position: "absolute",
                      bottom: 0,
                    }}
                  >
                    *
                  </span>
                ) : (
                  ""
                )}
              </div>
            </td>
          );
        })}
      </tr>
      <tr style={{ height: 332 }}>
        <td colSpan={2}>
          <table className="table-left-vital-signs">
            <tbody>
              <tr className="left-column-row-height">
                <td className="bold center w75 vamid fo8">HUYẾT ÁP</td>
                <td
                  className="bold center w50 vamid fo8"
                  style={{ borderRight: 0 }}
                >
                  {itemProps.labels?.nhietDo || "Nhiệt độ"}
                </td>
              </tr>
              <tr style={{ height: "312px" }}>
                <td className="pr">
                  <div className="col-ha">
                    <span>v</span>
                    <span>Mx</span>
                    <span>v</span>
                  </div>
                  <div className="line"></div>
                  <div className="col-ha">
                    <span>˄</span>
                    <span>Mn</span>
                    <span>˄</span>
                  </div>
                  <div className="col-ha" style={{ marginTop: 10 }}>
                    <span>v</span>
                    <span>v</span>
                  </div>
                  <div className="line"></div>
                  {state.rangeBloodPressure?.map((item, index) => {
                    if (index) {
                      return (
                        <div
                          key={index}
                          className="pa"
                          style={{
                            top: (index - 1) * SIZE.rowHeight * 25 + 100,
                            left: 30,
                          }}
                        >
                          {item}
                        </div>
                      );
                    } else {
                      return "";
                    }
                  })}
                </td>
                <td className="pr" style={{ borderRight: 0 }}>
                  {NHIETS.map((item, index) => {
                    return (
                      <div
                        key={index}
                        className="pa"
                        style={{
                          top: index * SIZE.rowHeight * 10 + 5,
                          left: 20,
                        }}
                      >
                        {item}
                      </div>
                    );
                  })}
                </td>
              </tr>
            </tbody>
          </table>
        </td>
        <td
          style={{
            position: "relative",
            // height: `${canvasHeight - 3}px`,
          }}
          className={`${state?.indexColumn === 0 ? "active-column" : ""}`}
        >
          <VitalSigns
            show5pMoiCot={itemProps.show5pMoiCot}
            colNumber={itemProps.colNumber}
            khungIndex={khungIndex}
            canvasWidth={canvasWidth}
            canvasHeight={canvasHeight}
            columnWidth={columnWidth}
            rangeBloodPressure={state.rangeBloodPressure || []}
            values={getValuesVitalSigns()}
            getValuesNhan={getValuesNhan}
            bonusSize={2}
            changeindexColumn={changeindexColumn}
            onChangeMultiCss={onChangeMultiCss}
            hienThiThoiGianMoiCot={hienThiThoiGianMoiCot}
            maxValueMach={itemProps.mauBangSinhHieu == 2 ? 320 : 210}
            minValueMach={itemProps.mauBangSinhHieu == 2 ? 0 : 50}
          />
        </td>
        {Array((itemProps.colNumber || 12) - 1)
          .fill([])
          .map((item, index) => {
            const checkActive = index + 1 == state?.indexColumn;
            return (
              <td className={`${checkActive ? "active-column" : ""}`}></td>
            );
          })}
      </tr>
      <tr>
        <td colSpan={5} className="bold center vamid">
          {itemProps.labels?.nhietDo || "Nhiệt độ"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "nhietDo"];
          return (
            <td
              className={`col-lv6 center col-${index + 1}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={34}
                max={42}
                value={state[key.join("_")]}
                onChange={onChangeValueChiSoSong(key, index)}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 1)}
              />
            </td>
          );
        })}
      </tr>

      <tr>
        <td colSpan={5} className="vamid p02 center">
          {itemProps.labels?.matMau || "Mất máu"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "matMau"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={9999}
                value={state[key.join("_")]}
                onChange={(e) => {
                  onChangeValueKhung(key, false)(e);
                  const tongMatMau = COLUMNS.reduce((acc, item) => {
                    const key = ["chiSo", "cot" + (item + 1), "matMau"];
                    return acc + (state[key.join("_")] || 0);
                  }, 0);
                  onChangeValueChung("tongMatMau", true)(tongMatMau);
                }}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 2)}
              />
            </td>
          );
        })}
        <td className="col-lv6 center">{state.tongMatMau ?? ""}</td>
      </tr>
      <tr>
        <td colSpan={5} className="vamid p02 center">
          {itemProps.labels?.nuocTieu || "Nước tiểu"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "nuocTieu"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={9999}
                value={state[key.join("_")]}
                onChange={(e) => {
                  onChangeValueKhung(key, false)(e);
                  const tongNuocTieu = COLUMNS.reduce((acc, item) => {
                    const key = ["chiSo", "cot" + (item + 1), "nuocTieu"];
                    return acc + (state[key.join("_")] || 0);
                  }, 0);
                  onChangeValueChung("tongNuocTieu", true)(tongNuocTieu);
                }}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 3)}
              />
            </td>
          );
        })}
        <td className="col-lv6 center">{state.tongNuocTieu ?? ""}</td>
      </tr>

      <tr>
        <td colSpan={5} className="vamid p02 center">
          {itemProps.labels?.aldmpb || "ALĐMPB"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "aldmpb"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={99}
                value={state[key.join("_")]}
                onChange={onChangeValueKhung(key, false)}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 4)}
              />
            </td>
          );
        })}
      </tr>

      <tr>
        <td colSpan={5} className="vamid p02 center">
          {itemProps.labels?.altmtu || "ALTMTU"}
        </td>

        {COLUMNS.map((item, index) => {
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={disabled}
                size={"small"}
                step={1}
                min={0}
                max={99}
                value={state.altmtu?.[index]}
                onChange={(e) => {
                  const value = cloneDeep(state.altmtu || []);
                  value[index] = e;
                  onChangeValueChung("altmtu", false)(value);
                }}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 5)}
                timeout={0}
              />
            </td>
          );
        })}
      </tr>
      {itemProps.showthoiDiemConRa && (
        <tr>
          <td colSpan={5} className="vamid p02 center">
            Thời điểm con ra
          </td>

          {COLUMNS.map((item, index) => {
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <CheckGroups
                  mode={"editing"}
                  component={{
                    props: {
                      direction: "rtl",
                      type: "multiple",
                      kieuHienThi: "vertical",
                      fieldName: `thoiDiemConRa2_${khungIndex}`,
                      checkList: [{ label: "", value: index }],
                    },
                  }}
                  form={{
                    [`thoiDiemConRa2_${khungIndex}`]: state.thoiDiemConRa2,
                  }}
                  formChange={{
                    [`thoiDiemConRa2_${khungIndex}`]: (e) => {
                      if (Object.keys(state)?.length > 10) {
                        state.thoiDiemConRa2 = e;
                        onChangeValueKhung(["thoiDiemConRa2"], true)(e);
                      }
                    },
                  }}
                ></CheckGroups>
              </td>
            );
          })}
        </tr>
      )}

      {/* NHỊP THỞ */}
      <tr>
        <td colSpan={5} className="vamid p02">
          {itemProps.labels?.nhipTho || "Nhịp thở"}
        </td>

        {COLUMNS.map((item, index) => {
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <Input
                readOnly={true}
                onClick={
                  !isDisable(index, ["nhipTho"]) ? onChangeNhipTho(index) : null
                }
                size={"small"}
                value={getNhipTho(index)}
                tabIndex={calculateTabIndex(index + 5, 6)}
              />
            </td>
          );
        })}
      </tr>

      {/* HÔ HẤP */}
      <tr>
        <td colSpan={1} rowSpan={rowSpanHoHap} className="col-lv1">
          {itemProps.labels?.hoHap || "Hô hấp"}
        </td>
        <td colSpan={2} rowSpan={3} className="">
          <DeboundInput
            value={state.may || ""}
            rows={2}
            label={"Máy: "}
            readOnly={isDisable(null, ["may"])}
            onChange={onChangeValueChung("may", false)}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            minHeight={9 * 2 + 6}
            tabIndex={calculateTabIndex(1, 7)}
          />
        </td>
        <td colSpan={2} className="vamid p02">
          {itemProps.labels?.ttlt || "TTLT"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "ttlt"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={9999}
                value={state[key.join("_")]}
                onChange={onChangeValueKhung(key, false)}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 8)}
              />
            </td>
          );
        })}
      </tr>
      {/* FeCO2 */}
      <tr>
        <td colSpan={2} className="vamid p02">
          {itemProps.labels?.feco2 || "FeCO2"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "feco2"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={99}
                value={state[key.join("_")]}
                onChange={onChangeValueChiSoSong(key, index, false)}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 9)}
              />
            </td>
          );
        })}
      </tr>
      {/* Áp lực */}
      <tr>
        <td colSpan={2} className="vamid p02">
          {itemProps.textApLuc === undefined ? "Áp lực" : itemProps.textApLuc}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "apLucDongTho"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={99}
                value={state[key.join("_")]}
                onChange={onChangeValueKhung(key, false)}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 10)}
              />
            </td>
          );
        })}
      </tr>
      {/* SpO2 (%) */}
      <tr>
        <td
          colSpan={2}
          rowSpan={itemProps.showThuocDangKhi ? 3 : 2}
          className=""
        >
          <DeboundInput
            value={state.me || ""}
            rows={2}
            label={"Mê: "}
            readOnly={isDisable(null, ["me"])}
            onChange={onChangeValueChung("me", false)}
            type="multipleline"
            lineHeightText={1}
            fontSize={9}
            minHeight={9 * 2 + 6}
            tabIndex={calculateTabIndex(1, 11)}
          />
        </td>
        <td colSpan={2} className="vamid p02">
          {itemProps.labels?.spo2 || "SpO2 (%)"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "spo2"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={999}
                value={state[key.join("_")]}
                onChange={onChangeValueChiSoSong(key, index, false)}
                type="number"
                tabIndex={calculateTabIndex(
                  index + 5,
                  itemProps.colNumber || 12
                )}
              />
            </td>
          );
        })}
      </tr>
      {/* FiO2 (%) */}
      <tr>
        <td colSpan={2} className="vamid p02">
          {itemProps.labels?.fio2 || "FiO2 (%)"}
        </td>
        {COLUMNS.map((item, index) => {
          const key = ["chiSo", "cot" + (index + 1), "fio2"];
          const checkActive = index === state?.indexColumn;
          return (
            <td
              className={`col-lv6 center ${checkActive ? "active-column" : ""}`}
              key={index}
              onClick={onChangeindexColumn(index)}
            >
              <DeboundInput
                readOnly={isDisable(index, key)}
                size={"small"}
                step={1}
                min={0}
                max={999}
                value={state[key.join("_")]}
                onChange={onChangeValueChiSoSong(key, index, false)}
                type="number"
                tabIndex={calculateTabIndex(index + 5, 13)}
              />
            </td>
          );
        })}
        {!itemProps.showThuocDangKhi &&
          !itemProps.showServoran &&
          !itemProps.showDesflurane &&
          !itemProps.showHalotan &&
          !itemProps.showMoreInfo &&
          colTongCong}
      </tr>
      {itemProps.showThuocDangKhi && (
        <tr>
          <td colSpan={2} className="vamid p02">
            <div className="flex">
              <DeboundInput
                readOnly={isDisable(null, ["thuocDangKhi"])}
                size={50}
                className="f1"
                value={state[["chiSo", "cot1", "thuocDangKhi"].join("_")]}
                onChange={onChangeValueChiSoSong(
                  ["chiSo", "cot1", "thuocDangKhi"],
                  0,
                  false
                )}
                type="multipleline"
                lineHeightText={1}
                fontSize={9}
                tabIndex={calculateTabIndex(0, 15)}
              />
            </div>
          </td>
          {COLUMNS.map((item, index) => {
            const key = ["chiSo", "cot" + (index + 1), "soLuongThuocDangKhi"];
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <DeboundInput
                  readOnly={isDisable(index, key)}
                  size={"small"}
                  step={1}
                  min={0}
                  max={999}
                  value={state[key.join("_")]}
                  onChange={onChangeValueChiSoSong(key, index, false)}
                  type="number"
                  tabIndex={calculateTabIndex(index + 5, 13)}
                />
              </td>
            );
          })}
          {!itemProps.showServoran &&
            !itemProps.showDesflurane &&
            !itemProps.showHalotan &&
            !itemProps.showMoreInfo &&
            colTongCong}
        </tr>
      )}

      {itemProps.showServoran && (
        <tr>
          <td colSpan={2} className="vamid p02">
            {itemProps.labels?.servoran || "Servoran (%)"}
          </td>
          {COLUMNS.map((item, index) => {
            const key = ["chiSo", "cot" + (index + 1), "servoran"];
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <DeboundInput
                  readOnly={isDisable(index, key)}
                  size={"small"}
                  step={1}
                  min={0}
                  max={100}
                  value={state[key.join("_")]}
                  onChange={onChangeValueKhung(key, false)}
                  type="number"
                  tabIndex={calculateTabIndex(
                    index + 5,
                    itemProps.colNumber || 12
                  )}
                />
              </td>
            );
          })}
          {!itemProps.showDesflurane &&
            !itemProps.showHalotan &&
            !itemProps.showMoreInfo &&
            colTongCong}
        </tr>
      )}
      {itemProps.showDesflurane && (
        <tr>
          <td colSpan={2} className="vamid p02">
            {itemProps.labels?.desflurane || "Desflurane (%)"}
          </td>
          {COLUMNS.map((item, index) => {
            const key = ["chiSo", "cot" + (index + 1), "desflurane"];
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <DeboundInput
                  readOnly={isDisable(index, key)}
                  size={"small"}
                  step={1}
                  min={0}
                  max={100}
                  value={state[key.join("_")]}
                  onChange={onChangeValueKhung(key, false)}
                  type="number"
                  tabIndex={calculateTabIndex(index + 5, 13)}
                />
              </td>
            );
          })}
          {!itemProps.showHalotan && !itemProps.showMoreInfo && colTongCong}
        </tr>
      )}
      {itemProps.showHalotan && (
        <tr>
          <td colSpan={2} className="vamid p02">
            {itemProps.labels?.halotan || "Halotan (%)"}
          </td>
          {COLUMNS.map((item, index) => {
            const key = ["chiSo", "cot" + (index + 1), "halontan"];
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <DeboundInput
                  readOnly={isDisable(index, key)}
                  size={"small"}
                  step={1}
                  min={0}
                  max={100}
                  value={state[key.join("_")] || ""}
                  onChange={onChangeValueKhung(key, false)}
                  type="number"
                  tabIndex={calculateTabIndex(index + 5, 14)}
                />
              </td>
            );
          })}
          {!itemProps.showMoreInfo && colTongCong}
        </tr>
      )}
      {itemProps.showMoreInfo && (
        <tr>
          <td colSpan={2} className="vamid p02">
            <div className="flex">
              <DeboundInput
                readOnly={isDisable(null, ["tenHoHapTuNhap"])}
                size={50}
                className="f1"
                value={state.tenHoHapTuNhap || ""}
                onChange={onChangeValueKhung(["tenHoHapTuNhap"], false)}
                type="multipleline"
                lineHeightText={1}
                fontSize={9}
                tabIndex={calculateTabIndex(0, 15)}
              />
              (%)
            </div>
          </td>
          {COLUMNS.map((item, index) => {
            const key = ["chiSo", "cot" + (index + 1), "hoHapTuNhap"];
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <DeboundInput
                  readOnly={isDisable(index, key)}
                  size={"small"}
                  step={1}
                  min={0}
                  max={100}
                  value={state[key.join("_")]}
                  onChange={onChangeValueKhung(key, false)}
                  type="number"
                  tabIndex={calculateTabIndex(index + 5, 15)}
                />
              </td>
            );
          })}
          {colTongCong}
        </tr>
      )}

      {itemProps.showDoMe && (
        <>
          <tr>
            <td colSpan={1} rowSpan={2} className="col-lv1">
              {itemProps.labels?.doMe || "Độ mê"}
            </td>
            <td colSpan={4} className="vamid p02">
              {itemProps.labels?.bis || "BIS"}
            </td>
            {COLUMNS.map((item, index) => {
              const checkActive = index === state?.indexColumn;
              return (
                <td
                  className={`col-lv6 center ${
                    checkActive ? "active-column" : ""
                  }`}
                  key={index}
                  onClick={onChangeindexColumn(index)}
                >
                  <DeboundInput
                    readOnly={disabled}
                    size={"small"}
                    value={state.bis?.[index]}
                    timeout={0}
                    onChange={(e) => {
                      const value = cloneDeep(state.bis || []);
                      value[index] = e;
                      onChangeValueChung("bis", false)(value);
                    }}
                    tabIndex={calculateTabIndex(index + 5, 16)}
                    type="multipleline"
                  />
                </td>
              );
            })}
          </tr>
          <tr>
            <td colSpan={4} className="vamid p02">
              {itemProps.labels?.mac || "MAC"}
            </td>
            {COLUMNS.map((item, index) => {
              const checkActive = index === state?.indexColumn;
              return (
                <td
                  className={`col-lv6 center ${
                    checkActive ? "active-column" : ""
                  }`}
                  key={index}
                  onClick={onChangeindexColumn(index)}
                >
                  <DeboundInput
                    readOnly={disabled}
                    size={"small"}
                    value={state.mac?.[index]}
                    timeout={0}
                    onChange={(e) => {
                      const value = cloneDeep(state.mac || []);
                      value[index] = e;
                      onChangeValueChung("mac", false)(value);
                    }}
                    tabIndex={calculateTabIndex(index + 5, 17)}
                    type="multipleline"
                  />
                </td>
              );
            })}
          </tr>
        </>
      )}

      {itemProps.showGianCo && (
        <tr>
          <td colSpan={1} className="col-lv1">
            {itemProps.labels?.gianCo || "Giãn cơ"}
          </td>
          <td colSpan={4} className="vamid p02">
            {itemProps.labels?.tof || "TOF"}
          </td>
          {COLUMNS.map((item, index) => {
            const checkActive = index === state?.indexColumn;
            return (
              <td
                className={`col-lv6 center ${
                  checkActive ? "active-column" : ""
                }`}
                key={index}
                onClick={onChangeindexColumn(index)}
              >
                <DeboundInput
                  readOnly={disabled}
                  size={"small"}
                  value={state.tof?.[index]}
                  timeout={0}
                  onChange={(e) => {
                    const value = cloneDeep(state.tof || []);
                    value[index] = e;
                    onChangeValueChung("tof", false)(value);
                  }}
                  tabIndex={calculateTabIndex(index + 5, 18)}
                  type="multipleline"
                />
              </td>
            );
          })}
        </tr>
      )}

      {renderThuoc.thuocThuong}
      {renderThuoc.thuocDichTruyen}
      <tr>
        <td colSpan={1} className="col-lv1">
          {itemProps.labels?.quanSat || "QUAN SÁT"}
        </td>
        <td colSpan={4 + (itemProps.colNumber || 12)} className="">
          <DeboundInput
            readOnly={isDisable(null, ["quanSat"])}
            onChange={onChangeValueKhung(["quanSat"], false)}
            type="multipleline"
            value={state.quanSat}
            lineHeightText={1}
            fontSize={9}
            tabIndex={calculateTabIndex(5 + COLUMNS.length, 15)}
          />
        </td>
        <td className="p22">
          <div className="bold fixWidth2">
            {itemProps.labels?.tongThoiGianGayMe || "TỔNG THỜI GIAN GÂY MÊ:"}{" "}
            {tongGayMe()}
          </div>
        </td>
      </tr>
    </Fragment>
  );
};

export default Khung;
