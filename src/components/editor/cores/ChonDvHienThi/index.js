import React, { forwardRef, useEffect, useMemo, useRef, useState } from "react";
import T from "prop-types";
import { useDispatch } from "react-redux";
import { Main, ContentSeviceStyled } from "./styled";
import { getValueForm, MODE } from "utils/editor-utils";
import { valuesRef } from "pages/editor/report/components/File";
import { Checkbox, Empty, Popover } from "antd";
import { SVG } from "assets";
import { useTranslation } from "react-i18next";
import { cloneDeep, get } from "lodash";

const ChonDvHienThi = forwardRef((props, ref) => {
  const {
    component: { init },
  } = useDispatch();

  const { t } = useTranslation();
  const { mode, component, form, formChange } = props;
  const itemProps = component.props;

  const [state, _setState] = useState({
    disable: false,
    values: [],
    allData: [],
    listData: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };
  const valuesRef = useRef();

  useEffect(() => {
    valuesRef.current = getValueForm(form, itemProps.fieldName) || [];
    debugger;
    setState({
      listData: valuesRef.current,
    });
  }, [form, itemProps.fieldName]);

  const onChange = () => {
    valuesRef.current = cloneDeep(valuesRef.current);
    formChange[itemProps.fieldName] &&
      formChange[itemProps.fieldName](valuesRef.current);
    setState({
      listData: valuesRef.current,
    });
  };

  const _listData = useMemo(() => {
    return (state.listData || []).filter((e) => e.hieuLuc);
  }, [state.listData]);

  return (
    <Main
      onClick={handleFocus}
      fontWeight={itemProps.fontWeight}
      align={itemProps.align}
      textAlign={itemProps.align}
      textTransform={itemProps.textTransform}
      contentColor={itemProps.contentColor || "black"}
    >
      <span>
        <span>
          <span className="title">
            {itemProps.tenDichVu || (mode === MODE.config ? "Tên dịch vụ" : "")}
          </span>
          <Popover
            style={{
              boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px !important",
            }}
            placement="bottom"
            overlayClassName={"popover-select-sevices"}
            title={t("editor.chonDichVuDeHienThi")}
            content={
              <ContentSevice data={state.listData} onChange={onChange} />
            }
            trigger="click"
          >
            <span
              className="icon-select-sevices"
              title={t("editor.chonDichVuDeHienThi")}
            >
              <SVG.IcSetting width={24} height={24} />
            </span>
          </Popover>
        </span>
      </span>
      {_listData.map((item, index) => {
        if (item.dsChiSoCon?.length) {
          const dsChiSoCon = item.dsChiSoCon.filter((csc) => csc.hieuLuc);
          return (
            <span key={item.id}>
              <span>{item.tenDichVu}: </span>
              <span>
                {dsChiSoCon
                  .filter((el) => el.ketQua)
                  .map((csc) => `${csc.ketQua || ""}`)
                  .join(", ")}
              </span>

              <span>{index !== _listData?.length - 1 ? ", " : ""} </span>
            </span>
          );
        } else {
          return (
            <span key={item.id}>
              {item.tenDichVu}{" "}
              <span>{index !== _listData?.length - 1 ? ", " : ""} </span>
            </span>
          );
        }
      })}
    </Main>
  );
});

const ContentSevice = ({ onChange, onChangeChiSoCon, data }) => {
  const { t } = useTranslation();

  return (
    <ContentSeviceStyled>
      {data?.length ? (
        data.map((item) => {
          return (
            <div key={item.id}>
              <Checkbox
                checked={item.hieuLuc}
                onChange={() => {
                  item.hieuLuc = !item.hieuLuc;
                  onChange();
                }}
              >
                <span
                  dangerouslySetInnerHTML={{
                    __html: item.tenDichVu,
                  }}
                />

                {item.hieuLuc && (
                  <>
                    {/* {isSelectedDv && (item.dsChiSoCon || []).length > 0 && (
                  <div>
                    <Checkbox
                      checked={isHideAllCSC}
                      onChange={onHideAllChiSoCon(item.id)}
                    >
                      <i>Ẩn toàn bộ chỉ số con</i>
                    </Checkbox>
                  </div>
                )} */}

                    <div
                      style={{
                        marginLeft: 5,
                        display: "grid",
                        gridTemplateColumns: `repeat(${7}, 1fr)`,
                      }}
                    >
                      {(item.dsChiSoCon || []).map((csc) => {
                        return (
                          <Checkbox
                            key={`${csc.id}-${csc.chiSoConId}`}
                            checked={csc.hieuLuc}
                            onChange={() => {
                              csc.hieuLuc = !csc.hieuLuc;
                              onChange();
                            }}
                          >
                            <span
                              dangerouslySetInnerHTML={{
                                __html: `${csc.tenChiSoCon || ""} ${
                                  csc.ketQua || ""
                                } ${csc.donVi || ""}`,
                              }}
                            />
                          </Checkbox>
                        );
                      })}
                    </div>
                  </>
                )}
              </Checkbox>
            </div>
          );
        })
      ) : (
        <Empty description={t("editor.khongTonTaiDichVu")}></Empty>
      )}
    </ContentSeviceStyled>
  );
};

ChonDvHienThi.defaultProps = {
  form: {},
};

ChonDvHienThi.propTypes = {
  form: T.shape({}),
};

export default ChonDvHienThi;
