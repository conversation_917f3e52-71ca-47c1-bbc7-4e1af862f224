import React, { useState } from "react";
import { TableDiv } from "../styled";
import { DatePicker, Select } from "components";
import { ENUM } from "constants/index";
import { useEnum } from "hooks";

const { RangePicker } = DatePicker;

const TableDVKT = ({ data = [], rowsPerPage = 20 }) => {
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);

  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(data.length / rowsPerPage);

  const handlePrev = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const startIndex = (currentPage - 1) * rowsPerPage;
  const currentData = data.slice(startIndex, startIndex + rowsPerPage);

  return (
    <TableDiv>
      <h3>3. DVKT chưa thực hiện</h3>

      <div className="filter-box">
        <div className="filter-box-label">Thời gian: </div>
        <RangePicker
          className="custom-range-picker"
          placeholder={["Từ thời gian", "Đến thời gian"]}
          format="DD/MM/YYYY HH:mm"
          showTime
        />

        <div className="filter-box-label">Loại dịch vụ: </div>
        <Select
          data={[
            { id: 20, ten: "Xét nghiệm" },
            { id: 30, ten: "CĐHA - TDCN" },
            { id: 40, ten: "PTTT" },
          ]}
          className="custom-select"
          placeholder={"Chọn loại dịch vụ"}
        />
      </div>

      <table>
        <thead>
          <tr>
            <th>STT</th>
            <th>Mã hồ sơ</th>
            <th>Họ tên BN</th>
            <th>Năm sinh</th>
            <th>Số giường / phòng</th>
            <th>Loại dịch vụ</th>
            <th>Tên dịch vụ</th>
            <th>Khoa thực hiện</th>
            <th>Phòng thực hiện</th>
            <th>Trạng thái</th>
            <th>Thời gian chỉ định</th>
            <th>Thời gian thực hiện</th>
            <th>Ghi chú</th>
          </tr>
        </thead>
        <tbody>
          {currentData.map((row, index) => (
            <tr key={index} className="text-center">
              <td>{startIndex + index + 1}</td>
              <td>{row.maHoSo}</td>
              <td>{row.tenNb}</td>
              <td>
                {row.ngaySinh ? moment(row.ngaySinh).format("DD/MM/YYYY") : ""}
              </td>
              <td>{`${row.soHieuGiuong || ""} ${
                row.tenPhong ? `/ ${row.tenPhong}` : ""
              }`}</td>
              <td>{row.tenDichVu}</td>
              <td>{row.tenKhoaThucHien}</td>
              <td>{row.tenPhongThucHien}</td>
              <td>
                {listTrangThaiDichVu.find((x) => x.id == row.trangThai)?.ten ||
                  ""}
              </td>
              <td>{row.thoiGianChiDinh}</td>
              <td>{row.thoiGianThucHien}</td>
              <td>{row.ghiChu1}</td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="pagination">
        <button onClick={handlePrev} disabled={currentPage === 1}>
          {`<`}
        </button>
        <span>
          Trang {currentPage} / {totalPages}
        </span>
        <button onClick={handleNext} disabled={currentPage === totalPages}>
          {`>`}
        </button>
      </div>
    </TableDiv>
  );
};

export default TableDVKT;
