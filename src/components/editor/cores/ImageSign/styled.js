import styled, { createGlobalStyle } from "styled-components";
import { convertPosition } from "./constanst";
export const GlobalStyle = createGlobalStyle``;

export const Main = styled.div`
  display: flex;
  justify-content: ${(props) => props.contentAlign};
  .sign-image {
    position: relative;
    display: flex;
    &.column {
      flex-direction: column;
    }
    .box-sign {
      display: flex;
      ${({ itemProps }) => {
        switch (itemProps.viTriAnhCa) {
          case "top":
            return `
          flex-direction: column;
          .image-ca{
            order:0;
          }
          .info-sign{
            order:1;
          }
          `;
          case "bottom":
            return `
          flex-direction: column;
          .image-ca{
            order:1;
          }
          .info-sign{
            order:0;
          }
          `;
          case "left":
            return `
          flex-direction: row;
          align-items: flex-end;
          .image-ca{
            order:0;
          }
          .info-sign{
            order:1;
          }
          `;
          case "right":
            return `
          flex-direction: row;
          align-items: flex-end;
          .image-ca{
            order:1;
          }
          .info-sign{
            order:0;
          }
          `;
          default:
            return `
          flex-direction: row;
          align-items: flex-end;
          .image-ca{
            order:1;
          }
          .info-sign{
            order:0;
          }
          `;
        }
      }}
    }
    .image-ca {
      width: ${({ itemProps }) =>
        itemProps.widthCa ? itemProps.widthCa + "px" : "100%"};
      object-fit: contain;
      ${({ itemProps }) =>
        ["bottom", "top"].includes(itemProps.viTriAnhCa)
          ? `align-self: ${convertPosition(itemProps.canLeCa)};`
          : `justify-self: ${convertPosition(itemProps.canLeCa)};`}
    }
    .ca-valid {
      width: ${({ itemProps }) =>
        itemProps.widthCa ? itemProps.widthCa + "px" : "100%"};
      display: flex;

      ${({ itemProps }) =>
        ["bottom", "top"].includes(itemProps.viTriAnhCa)
          ? `justify-content: ${convertPosition(itemProps.canLeCa)};`
          : `align-content: ${convertPosition(itemProps.canLeCa)};`}
      svg {
        font-size: 16px;
        margin-left: 4px;
        color: green;
      }
    }
    .info-sign {
      display: flex;
      ${({ itemProps }) => {
        switch (itemProps.viTriCa) {
          case "top":
            return `
          flex-direction: column;
          img{
            order:1;
          }
          .sign-ca{
            order:0;
          }
          align-items:center;
          `;
          case "bottom":
            return `
          flex-direction: column;
          img{
            order:0;
          }
          .sign-ca{
            order:1;
          }
          align-items:center;
          `;
          case "left":
            return `
          flex-direction: row;
          align-items: flex-end;
          img{
            order:1;
          }
          .sign-ca{
            order:0;
          }
          `;
          case "right":
            return `
          flex-direction: row;
          align-items: flex-end;
          img{
            order:0;
          }
          .sign-ca{
            order:1;
          }
          `;
          default:
            return `
          flex-direction: row;
          align-items: flex-end;
          img{
            order:1;
          }
          .sign-ca{
            order:0;
          }
          `;
        }
      }}
    }

    .text-patient-sign {
      width: ${(props) => props?.itemProps?.width || 200}px;
      height: ${(props) => props?.itemProps?.height || 100}px;
      object-fit: contain;
    }
    .text-name-patient-sign {
      width: ${(props) => props?.itemProps?.width || 200}px;
      height: ${(props) => props?.itemProps?.height || 100}px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .text-short-name-patient {
      width: ${(props) => props?.itemProps?.width || 200}px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .sign-ca {
      font-size: ${(props) => props?.itemProps?.fontSize || 12}pt;
      color: ${(props) => props?.itemProps?.contentColor || "#000"};
    }
    button {
      padding: 5px 2px;
      ${({ itemProps }) => {
        return itemProps?.width
          ? `
          max-width: ${itemProps?.width}px;
          .button-content {
            font-size: clamp(8px, ${itemProps?.width / 5}px, 14px);
          }
        `
          : ``;
      }}

      @media print {
        display: none;
      }
    }

    .btn-huyKy {
      z-index: 2;
      position: absolute;
      top: 30px;
      right: 2px;
      @media print {
        display: none;
      }
    }
  }
`;
