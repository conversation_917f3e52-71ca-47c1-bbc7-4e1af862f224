import React, {
  forwardRef,
  memo,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { Button } from "antd";
import { MODE, combineFields, convert } from "utils/editor-utils";
import {
  SettingOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import moment from "moment";
import { useListAll } from "hooks";
import { groupBy, orderBy } from "lodash";
import ImageSign from "../../ImageSign";

export const ContextPhieu = React.createContext();
export const ContextPhieuProvider = ContextPhieu.Provider;

const MAX_WIDTH = 750;
const TEN_LOAI_DICH_VU = {
  10: "Kh<PERSON>m bệnh",
  20: "Xét nghiệm",
  30: "CDHA, TDCN",
  40: "Phẫu thuật",
  60: "DV ngoài điều trị",
  90: "Thuốc",
  100: "VTYT",
  110: "Hóa chất",
  120: "Máu",
  130: "Giường",
};

const CHUKY_INDEX = {
  dsChuKyDieuDuong: "",
  dsChuKyNguoiBenh: 4,
};

const CongKhaiDichVuKham = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    dsDichVu: [],
    dsNgay: [],
    showBoSung: true,
    dsChuKyDieuDuong: [],
    dsChuKyNguoiBenh: [],
    signCellWidth: 30,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    tuThoiGian: tuThoiGianThucHien,
    denThoiGian: denThoiGianThucHien,
    dsLoaiDichVu,
  } = getAllQueryString();
  const { component, mode, form, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };
  const refDsDichVu = useRef([]);
  const refDsChuKyDieuDuong = useRef([]);
  const refDsChuKyNguoiBenh = useRef([]);

  useEffect(() => {
    if (Object.keys(form || {}).length) {
      let _dsDichVu = [];
      let tuThoiGian = moment(tuThoiGianThucHien);
      let denThoiGian = moment(denThoiGianThucHien);

      //duyệt qua ds 1 lượt + nhóm các dịch vụ theo ngày thực hiện
      (form.dsDichVu || []).forEach((dv) => {
        //check nếu ngày thực hiện nằm trong khoảng filter => thêm vào mảng
        let _ngayThucHien = moment(dv.ngayThucHien);
        if (
          form.id ||
          (_ngayThucHien.isSameOrAfter(tuThoiGian, "day") &&
            _ngayThucHien.isSameOrBefore(denThoiGian, "day"))
        ) {
          let _findIdx = _dsDichVu.findIndex(
            (x) => x.tenDichVu == dv.tenDichVu
          );
          if (_findIdx > -1) {
            //đẩy giá trị ngày thực hiện sớm nhất ra ngoài để phục vụ cho sắp xếp
            if (
              moment(dv.ngayThucHien).isBefore(
                moment(_dsDichVu[_findIdx].ngayThucHien)
              )
            ) {
              _dsDichVu[_findIdx].ngayThucHien = dv.ngayThucHien;
            }

            _dsDichVu[_findIdx].dsNgayThucHien.push({
              ngayThucHien: dv.ngayThucHien,
              soLuong: dv.soLuong,
            });
            _dsDichVu[_findIdx].soLuongTong =
              _dsDichVu[_findIdx].soLuongTong + dv.soLuong;
          } else {
            _dsDichVu.push({
              ...dv,
              soLuongTong: dv.soLuong,
              dsNgayThucHien: [
                {
                  ngayThucHien: dv.ngayThucHien,
                  soLuong: dv.soLuong,
                },
              ],
            });
          }
        }
      });

      let _dsLoaiDichVu = [];
      if (dsLoaiDichVu) {
        dsLoaiDichVu.split(",").map((item) => _dsLoaiDichVu.push(Number(item)));
      }

      let _dsNgay = [];

      if (form?.id) {
        _dsNgay = Object.keys(groupBy(form.dsDichVu, "ngayThucHien")).map(
          (el) => moment(el)
        );
        _dsNgay = _dsNgay.sort(
          (a, b) => new Date(a).getTime() - new Date(b).getTime()
        );

        //Với form đã lưu => lấy giá trị từ thời gian / đến thời gian trong form
        let _tuThoiGianForm = moment(form?.tuThoiGian);
        let _denThoiGianFom = moment(form?.denThoiGian);
        //tạo mảng ngày theo tham số tuThoiGianThucHien => denThoiGianThucHien
        let startingMomentForm = _tuThoiGianForm
          .clone()
          .set("hour", 0)
          .set("minute", 0)
          .set("second", 0);
        let _indexNgay = 0;
        while (startingMomentForm.isBefore(_denThoiGianFom)) {
          _indexNgay++;
          //Nếu phiếu đã có chữ ký của cột ngày => add thêm vào _dsNgay để hiển thị được ra chữ ký
          if (
            (form?.lichSuKy?.dsChuKy || []).findIndex(
              (x) => x.viTri == _indexNgay
            ) > -1
          ) {
            const _addNgay = startingMomentForm.clone();
            if (
              _dsNgay.findIndex(
                (x) => x.format("YYYY-MM-DD") == _addNgay.format("YYYY-MM-DD")
              ) == -1
            ) {
              _dsNgay.push(_addNgay);
            }
          }
          startingMomentForm.add(1, "days");
        }
      } else {
        //tạo mảng ngày theo tham số tuThoiGianThucHien => denThoiGianThucHien
        let startingMoment = tuThoiGian
          .clone()
          .set("hour", 0)
          .set("minute", 0)
          .set("second", 0);
        while (startingMoment.isBefore(denThoiGian)) {
          _dsNgay.push(startingMoment.clone()); // clone to add new object
          startingMoment.add(1, "days");
        }

        //Lọc chỉ hiển thị ngày có chỉ định
        if (itemProps?.hienThiNgayCoChiDinh) {
          const _dsNgayThucHien = Object.keys(
            groupBy(form.dsDichVu, "ngayThucHien")
          ).map((el) => moment(el));

          _dsNgay = _dsNgay.filter((ngay) =>
            _dsNgayThucHien.some((ngayThucHien) =>
              ngayThucHien.isSame(ngay, "day")
            )
          );
        }
      }

      //lọc theo loại dịch vụ
      if (!form.id) {
        _dsDichVu = _dsDichVu.filter((item) =>
          _dsLoaiDichVu.includes(item.loaiDichVu)
        );
      }

      //sắp xếp theo sttBangKe
      _dsDichVu = orderBy(
        _dsDichVu,
        ["sttBangKe", "ngayThucHien"],
        ["asc", "asc"]
      );

      refDsDichVu.current = _dsDichVu;
      let signCellWidth = Math.round(MAX_WIDTH / (_dsNgay.length || 1));

      setState({
        dsDichVu: _dsDichVu,
        dsNgay: _dsNgay,
        signCellWidth,
      });
    }
  }, [
    Object.keys(form || {}).length,
    tuThoiGianThucHien,
    denThoiGianThucHien,
    dsLoaiDichVu,
    itemProps,
  ]);

  useEffect(() => {
    if (Object.keys(form).length && state.dsNgay.length) {
      const dsChuKyDieuDuong = state.dsNgay.map((item) => {
        const ngayItem = moment(item).format("YYYY-MM-DD");
        let returnItem = {};

        const findIndex = (form.dsChuKyDieuDuong || []).findIndex(
          (x) => x.ngay == ngayItem
        );
        if (findIndex > -1) {
          const ck = form.dsChuKyDieuDuong[findIndex];
          returnItem = {
            ngay: ck.ngay,
            chuKy: {
              nguoiKyId: ck.nguoiKyId,
              thoiGianKy: ck.thoiGianKy,
            },
          };
        } else {
          returnItem = {
            ngay: ngayItem,
          };
        }
        return returnItem;
      });

      const dsChuKyNguoiBenh = state.dsNgay.map((item) => {
        const ngayItem = moment(item).format("YYYY-MM-DD");
        let returnItem = {};

        const findIndex = (form.dsChuKyNguoiBenh || []).findIndex(
          (x) => x.ngay == ngayItem
        );
        if (findIndex > -1) {
          const ck = form.dsChuKyNguoiBenh[findIndex];
          returnItem = {
            ngay: ck.ngay,
            chuKy: {
              anhKy: ck.anhKy,
              thoiGianKy: ck.thoiGianKy,
            },
          };
        } else {
          returnItem = {
            ngay: moment(item).format("YYYY-MM-DD"),
          };
        }
        return returnItem;
      });

      refDsChuKyDieuDuong.current = dsChuKyDieuDuong;
      refDsChuKyNguoiBenh.current = dsChuKyNguoiBenh;

      setState({
        dsChuKyDieuDuong: dsChuKyDieuDuong,
        dsChuKyNguoiBenh: dsChuKyNguoiBenh,
      });
    }
  }, [Object.keys(form)?.length, state.dsNgay]);

  const renderChuKy = (index, key) => {
    const idx = CHUKY_INDEX[key];
    const _thoiGianRaVien = form?.thoiGianRaVien
      ? moment(form?.thoiGianRaVien)
      : null;

    //Nếu ngày > thời gian ra viện => ẩn nút ký
    if (
      !form?.id &&
      _thoiGianRaVien &&
      state[key][index]?.ngay &&
      _thoiGianRaVien.isBefore(state[key][index]?.ngay, "day")
    ) {
      return null;
    }

    return state[key][index]?.ngay ? (
      <ImageSign
        component={{
          props: {
            fieldName: itemProps[`fieldNameKy${idx}`],
            allowReset: itemProps[`allowReset${idx}`],
            fontSize: 12,
            capKy: itemProps[`capKy${idx}`],
            loaiKy: key === "dsChuKyNguoiBenh" ? 2 : 1,
            width: state[key].length > 15 ? 40 : 60,
            height: state[key].length > 15 ? 30 : 40,
            showCa: false,
            isMultipleSign: true,
            showPatientSign: false,
            isKyTheoCot: true,
            viTri: index + 1,
            customText: "Ký",
            contentAlign: "center",
            hideIconSign: true,
            dataSign: {
              id: form.id,
              soPhieu: form.soPhieu,
              lichSuKyId: form?.lichSuKy?.id,
            },
          },
        }}
        form={{
          ...combineFields(combineFields(state[key][index])),
          lichSuKy: form.lichSuKy,
        }}
        mode={props.mode}
      />
    ) : null;
  };

  const renderFooter = () => {
    let numCol = state.dsNgay.length;

    return (
      <>
        {Array.from(Array(3).keys()).map((key) => (
          <tr className="w35">
            {new Array(numCol + 4).fill({}).map((item, index) => (
              <td key={index}>
                <div className="flex-td"></div>
              </td>
            ))}
          </tr>
        ))}
        <tr className="w35">
          <td colSpan={2}>Người lập phiếu (hàng ngày ghi tên vào ô)</td>
          <td></td>
          {Array.from({ length: numCol }, (_, i) => i).map((item) => (
            <td key={item}>{renderChuKy(item, "dsChuKyDieuDuong")}</td>
          ))}
          <td></td>
        </tr>

        <tr className="w35">
          <td colSpan={2}>Ký xác nhận của người bệnh, người nhà</td>
          <td></td>
          {Array.from({ length: numCol }, (_, i) => i).map((item) => (
            <td key={item}>{renderChuKy(item, "dsChuKyNguoiBenh")}</td>
          ))}
          <td></td>
        </tr>
      </>
    );
  };

  const renderTable = useMemo(() => {
    return (
      <>
        {(state?.dsDichVu || []).map((item, index) => {
          let trLoaiDv = null;
          if (
            index == 0 ||
            (index > 0 &&
              item.nhomDichVuCap1Id !==
                state?.dsDichVu[index - 1].nhomDichVuCap1Id)
          ) {
            trLoaiDv = (
              <tr className="loai-dich-vu">
                <td className="stt">{item.sttBangKe}</td>
                <td>
                  {listAllNhomDichVuCap1.find(
                    (x) => x.id === item.nhomDichVuCap1Id
                  )?.ten || ""}
                </td>
                <td></td>
                {new Array(state.dsNgay.length).fill({}).map((item, index) => (
                  <td key={index}></td>
                ))}
                <td></td>
              </tr>
            );
          }
          return (
            <>
              {trLoaiDv}
              <tr className="dich-vu">
                <td className="stt"></td>
                <td>{item.tenDichVu}</td>
                <td className="center">
                  <div className="flex-td">{item?.tenDonViTinh} </div>
                </td>

                {state.dsNgay.map((ngay, index) => {
                  let _soLuong = "";
                  let _ngay = ngay.format("YYYY-MM-DD");
                  let _findIdx = item.dsNgayThucHien.findIndex(
                    (x) => x.ngayThucHien === _ngay
                  );

                  if (_findIdx > -1) {
                    _soLuong = item.dsNgayThucHien[_findIdx].soLuong;
                  }
                  return (
                    <td className="center" key={index}>
                      {_soLuong}
                    </td>
                  );
                })}

                <td className={`center mw-40`}>{item.soLuongTong}</td>
              </tr>
            </>
          );
        })}
      </>
    );
  }, [state.dsDichVu, formChange, listAllNhomDichVuCap1]);

  return (
    <Main
      className="phieu-cong-khai-dv-kham"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-cong-khai-dv-kham"
    >
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}

      <table className="table-cong-khai-dv-kham">
        <thead>
          <tr className="thead">
            <td rowSpan={2} className="stt" width={40}>
              STT
              {!state.showBoSung ? (
                <EyeInvisibleOutlined
                  className="icon-eye"
                  onClick={() => {
                    setState({ showBoSung: true });
                  }}
                />
              ) : (
                <EyeOutlined
                  className="icon-eye"
                  onClick={() => {
                    setState({ showBoSung: false });
                  }}
                />
              )}
            </td>
            <td rowSpan={2} width={220} className={"mw-200"}>
              Tên Dịch vụ khám bệnh, chữa bệnh
            </td>
            <td rowSpan={2} width={40}>
              Đơn vị
            </td>

            <td colSpan={state.dsNgay.length}>Số lượng (theo ngày/ tháng)</td>

            <td rowSpan={2} width={30}>
              Tổng
            </td>
          </tr>
          <tr className="thead">
            {state.dsNgay.map((item, idx) => (
              <td key={idx} className="col-time">
                {item.format("DD/MM")}
              </td>
            ))}
          </tr>
        </thead>
        <tbody>
          {renderTable}
          {renderFooter()}
        </tbody>
      </table>
    </Main>
  );
});

export default memo(CongKhaiDichVuKham);
