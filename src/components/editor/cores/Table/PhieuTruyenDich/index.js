import React, { useEffect, useMemo, useState, useContext } from "react";
import { GlobalStyle, Main, Tr } from "./styled";
import { useEditor, EMR2Context, EMRContext } from "components/editor/config";
import { useListAll, useQueryString } from "hooks";
import { DatePicker as DatePickerComponent } from "components";
import { cloneDeep, groupBy, orderBy, sumBy } from "lodash";
import stringUitls from "mainam-react-native-string-utils";
import moment from "moment";
import { getUniqueData } from "utils";
import { useTranslation } from "react-i18next";
import {
  generateUniqueNumber,
  intToRoman,
  quyTacSoLuong,
  romanToInt,
  renderName,
} from "./config";
import { refConfirm } from "app";
import { gopThuocKhacLo } from "utils/chi-dinh-thuoc-utils";
import { setQueryStringValues } from "hooks/useQueryString/queryString";
import RenderBody from "./RenderBody";
import { useCallback } from "react";

const { RangePicker } = DatePickerComponent;
const isNumber = (value) => {
  const regex = /^[-+]?(\d{1,3}(,\d{3})*|\d+)(\.\d+)?$/;
  return regex.test(value);
};

const PhieuTruyenDich = (props) => {
  const { component, mode, formChange, form } = props;
  const itemProps = component.props || {};
  const { editorId } = useContext(EMR2Context);
  const context = useContext(EMRContext);
  const signStatus = useEditor(editorId, "signStatus", {});
  const { t } = useTranslation();
  const [listAllNhanVien] = useListAll("nhanVien", {
    page: "",
    size: "",
    active: true,
  });
  const [tuThoiGianTemp] = useQueryString("tuThoiGianTemp", null);
  const [denThoiGianTemp] = useQueryString("denThoiGianTemp", null);

  const [state, _setState] = useState({
    dsThuoc: [],
    dsSTTDaSinh: [],
    disable: false,
  });
  const setState = useCallback((data) => {
    _setState((prev) => ({
      ...prev,
      ...data,
    }));
  }, []);

  const disable = useMemo(() => {
    return context.isDisable({ itemProps, signStatus, props });
  }, [context.isDisable, itemProps, signStatus, props]);

  const formDsThuoc = useMemo(() => {
    //khoa.pm (August 6th, 2025 3:50 PM) //Gộp thuốc khác lô
    //[SAKURA-69511] FE (QLYC-2558) [Danh sách thuốc đã chỉ định + các phiếu] Xử lý gộp thuốc hiển thị khi 2 lô
    //[SAKURA-70972] => biến đổi dsDungKem ở đây để phục vụ khi thêm mới thuốc
    const _dsThuoc = itemProps?.khongXuLyGopThuoc
      ? form?.dsThuoc || []
      : gopThuocKhacLo(form?.dsThuoc || []);

    return _dsThuoc.map((item) => ({
      ...item,
      dsThuocDungKem: Object.entries(
        groupBy(item.dsThuocDungKem || [], "chiDinhId")
      ).map(([chiDinhId, items]) => {
        return {
          ...items[0],
          soLuong: sumBy(items, "soLuong"),
          soLuongSuDung: sumBy(items, "soLuongSuDung"),
        };
      }),
    }));
  }, [form, itemProps?.khongXuLyGopThuoc]);

  useEffect(() => {
    if (form) {
      let newData = [];
      formDsThuoc.forEach((item) => {
        item = {
          ...item,
          newId: stringUitls.guid(),
          hieuLuc: item.hieuLuc === null || item.hieuLuc ? true : false,
          ghiChu:
            itemProps.macDinhGhiChuLaCachDung && item.cachDung && !item.ghiChu
              ? item.cachDung
              : item.ghiChu,
          timestamp: item.thoiGianThucHien
            ? new Date(
                moment(item.thoiGianThucHien).format("YYYY-MM-DD")
              ).getTime()
            : new Date().getTime(),
        };
        const groupByTen = groupBy(item.dsThuocDungKem, "tenDichVu");
        const ids = [];
        Object.keys(groupByTen).forEach((thuoc, index) => {
          groupByTen[thuoc][0].soLuong = groupByTen[thuoc].reduce(
            (a, b) => a + b.soLuong,
            0
          );
          groupByTen[thuoc].forEach((el, idx) => {
            if (idx) {
              ids.push(el.id);
            }
          });
        });
        (item.dsThuocDungKem || []).forEach((el) => {
          el.show = true;
          //tính lại giá trị soLuongSuDung nếu null
          if (el.soLuongSuDung === null) {
            el.soLuongSuDung = quyTacSoLuong(el);
          }
          if (ids.includes(el.id)) {
            el.soLuong = 0;
            el.show = false;
          }
        });

        //tính lại giá trị soLuongSuDung nếu null
        if (item.soLuongSuDung === null) {
          item.soLuongSuDung = quyTacSoLuong(item);
        }
        /*
        hai.nv (July 29th, 2024 9:25 AM) 
        [SAKURA-51082]FE (DKTD0063) [Phiếu theo dõi truyền dịch] Config form: Thêm setting Hiển thị số la mã tốc độ truyền với ĐVT giọt/ phút
        */
        if (itemProps.romanToInt) {
          if (item.tocDoTruyen) {
            if (item.donViTocDoTruyen === 10 && isNumber(+item.tocDoTruyen)) {
              item.tocDoTruyen = intToRoman(item.tocDoTruyen);
            }
            if (item.donViTocDoTruyen === 20 && !isNumber(+item.tocDoTruyen)) {
              item.tocDoTruyen = romanToInt(item.tocDoTruyen);
            }
          }
        }
        newData.push(item);
      });

      const newDataGroup = groupBy(newData, "id");
      let dsThuoc = [];
      Object.keys(newDataGroup).forEach((key) => {
        dsThuoc = [...dsThuoc, ...updateThoiGianThucHien(0, newDataGroup[key])];
      });

      dsThuoc = orderBy(dsThuoc, "timestamp", "asc", "tenDichVu", "desc");

      let _dsSTTThuoc = [
        ...new Set(
          dsThuoc.filter((item) => item.stt != null).map((item) => item.stt)
        ),
      ];

      let _dsThuoc2 = dsThuoc.map((item, index) => {
        let newItem = { ...item, index };

        if (newItem.stt === null) {
          newItem.stt = `${newItem.id}${index + 1}0000`;
          if (
            (form?.lichSuKy?.dsChuKy || []).findIndex(
              (x) => x.viTri == newItem.stt
            ) > -1
          ) {
            //đánh dấu là chữ ký cũ => lấy vị trí theo cấu trúc cũ
            newItem.isChuKyCu = true;
          }
        }

        //nếu stt cũ bị trùng thì cũng sinh ra stt mới
        if (
          Number(newItem.stt) > 1000000000 ||
          dsThuoc.findIndex((x) => x.stt === item.stt) !== index
        ) {
          const safeInt = generateUniqueNumber(_dsSTTThuoc);

          newItem.stt = safeInt;
          _dsSTTThuoc.push(safeInt);
        }

        return newItem;
      });
      if (dsThuoc.some((item) => item.stt === null)) {
        formChange["dsThuoc"]?.(_dsThuoc2);
      }

      setState({
        dsThuoc: _dsThuoc2,
        dsSTTDaSinh: _dsSTTThuoc, //Lưu lại các STT đã có trong bảng,
      });
    }
  }, [form, formDsThuoc, itemProps.romanToInt]);

  const onChange =
    ({ key, idx, isNew, updateSilent }) =>
    (data) => {
      if (key === "id") {
        if (data) {
          let thuoc = listThuoc.find((item) => item.id === data);
          thuoc = cloneDeep(thuoc);
          if (isNew) {
            thuoc.newId = stringUitls.guid();

            const safeInt = generateUniqueNumber(state.dsSTTDaSinh);
            state.dsSTTDaSinh.push(safeInt);

            thuoc.stt = safeInt;
            thuoc.chiDinhId = null;
            thuoc.index = state.dsThuoc[idx]?.index;
            thuoc.thoiGianThucHien =
              state.dsThuoc[idx]?.thoiGianThucHien || thuoc?.thoiGianThucHien;
          }
          let lastThuoc = null;
          state.dsThuoc.forEach((el, index) => {
            if (el.id == thuoc.id && index < idx) {
              lastThuoc = el;
            }
          });
          if (lastThuoc && lastThuoc.thoiGianKetThuc) {
            thuoc.thoiGianBatDau = moment(lastThuoc.thoiGianKetThuc).add(
              lastThuoc.cachGio || 0,
              "hours"
            );
          }
          thuoc.thoiGianKetThuc = null;
          thuoc.hieuLuc = true;
          state.dsThuoc[idx] = thuoc;
        }
      }

      if (
        key === "id" ||
        key === "soLuongSuDung" ||
        key === "donViTocDoTruyen" ||
        key === "tocDoTruyen" ||
        key === "thoiGianBatDau" ||
        key === "thoiGianKetThuc"
      ) {
        state.dsThuoc[idx][key] = data;
        const dichVu = state.dsThuoc[idx];
        let _soLuong = Number(dichVu.soLuongSuDung);
        //10: giọt/ph; 20: ml/h
        if (dichVu.tocDoTruyen && _soLuong && dichVu.thoiGianBatDau) {
          let _slThuocDungKem = (dichVu.dsThuocDungKem || []).reduce(
            (a, b) => a + Number(b.soLuongSuDung) || 0,
            0
          );

          let _tocDoTruyen = isNumber(+dichVu.tocDoTruyen)
            ? dichVu.tocDoTruyen
            : romanToInt(dichVu.tocDoTruyen);

          let _soGiot =
            dichVu.donViTocDoTruyen == 20
              ? 60
              : !dichVu.soGiot
              ? 20 //Trường số giọt/ml cho mặc định = 20 khi Đơn vị tốc độ truyền = giọt/ph và giọt/ml = null trên phiếu
              : dichVu.soGiot;

          const soPhutTruyen =
            ((_slThuocDungKem + _soLuong) * _soGiot) / _tocDoTruyen;

          if (key === "thoiGianKetThuc") {
            state.dsThuoc[idx][key] = data;
          } else {
            state.dsThuoc[idx].thoiGianKetThuc = moment(
              dichVu.thoiGianBatDau
            ).add(soPhutTruyen, "minutes");
          }

          //nếu chưa phải bản ghi cuối => thực hiện tính toán giá trị thời gian bắt đầu/ kết thúc của line tiếp theo
          if (
            idx < state.dsThuoc.length - 1 &&
            state.dsThuoc[idx + 1].id == state.dsThuoc[idx].id &&
            !state.dsThuoc[idx + 1].thoiGianBatDau
          ) {
            state.dsThuoc[idx + 1].thoiGianBatDau = moment(
              state.dsThuoc[idx].thoiGianKetThuc
            ).add(state.dsThuoc[idx + 1].cachGio || 0, "hours");

            state.dsThuoc = updateThoiGianThucHien(idx + 1, state.dsThuoc);
          }
        }
      } else {
        state.dsThuoc[idx][key] = data;
      }
      const dsThuoc = cloneDeep(state.dsThuoc);

      if (itemProps.romanToInt) {
        state.dsThuoc.forEach((item) => {
          if (item.tocDoTruyen) {
            if (item.donViTocDoTruyen === 10 && isNumber(+item.tocDoTruyen)) {
              item.tocDoTruyen = intToRoman(item.tocDoTruyen);
            }
            if (item.donViTocDoTruyen === 20 && !isNumber(+item.tocDoTruyen)) {
              item.tocDoTruyen = romanToInt(item.tocDoTruyen);
            }
          }
        });
      }

      dsThuoc.forEach((item) => {
        if (item.tocDoTruyen && !isNumber(+item.tocDoTruyen)) {
          item.tocDoTruyen = romanToInt(item.tocDoTruyen);
        }
      });

      formChange["dsThuoc"]?.(dsThuoc);
      if (!updateSilent) {
        setState({
          dsThuoc: dsThuoc,
          dsSTTDaSinh: state.dsSTTDaSinh,
        });
      }
    };
  const onChangeThuocDungKem =
    ({ key, idx, idxThuoc }) =>
    (data) => {
      state.dsThuoc[idxThuoc].dsThuocDungKem[idx][key] = data;
      if (key === "soLuongSuDung") {
        const dichVu = state.dsThuoc[idxThuoc];
        let _soLuong = Number(dichVu.soLuongSuDung);

        //10: giọt/ph; 20: ml/h
        if (dichVu.tocDoTruyen && _soLuong && dichVu.thoiGianBatDau) {
          let _slThuocDungKem = (dichVu.dsThuocDungKem || []).reduce((a, b) => {
            return a + (Number(b.soLuongSuDung) || 0);
          }, 0);

          let _tocDoTruyen = isNumber(+dichVu.tocDoTruyen)
            ? dichVu.tocDoTruyen
            : romanToInt(dichVu.tocDoTruyen);

          let _soGiot =
            dichVu.donViTocDoTruyen == 20
              ? 60
              : !dichVu.soGiot
              ? 20 //Trường số giọt/ml cho mặc định = 20 khi Đơn vị tốc độ truyền = giọt/ph và giọt/ml = null trên phiếu
              : dichVu.soGiot;

          if (!state.dsThuoc[idx].thoiGianKetThuc) {
            const soPhutTruyen =
              ((_slThuocDungKem + _soLuong) * _soGiot) / _tocDoTruyen;

            state.dsThuoc[idxThuoc].thoiGianKetThuc = moment(
              dichVu.thoiGianBatDau
            ).add(soPhutTruyen, "minutes");
          }

          //nếu chưa phải bản ghi cuối => thực hiện tính toán giá trị thời gian bắt đầu/ kết thúc của line tiếp theo
          if (
            idxThuoc < state.dsThuoc.length - 1 &&
            state.dsThuoc[idx + 1].id == state.dsThuoc[idx].id &&
            !state.dsThuoc[idx + 1].thoiGianBatDau
          ) {
            state.dsThuoc[idxThuoc + 1].thoiGianBatDau = moment(
              state.dsThuoc[idxThuoc].thoiGianKetThuc
            ).add(state.dsThuoc[idxThuoc + 1].cachGio || 0, "hours");

            state.dsThuoc = updateThoiGianThucHien(idxThuoc + 1, state.dsThuoc);
          }
        }
      }
      const dsThuoc = cloneDeep(state.dsThuoc);

      dsThuoc.forEach((item) => {
        if (item.tocDoTruyen && !isNumber(+item.tocDoTruyen)) {
          item.tocDoTruyen = romanToInt(item.tocDoTruyen);
        }
      });

      formChange["dsThuoc"]?.(dsThuoc);
      setState({
        dsThuoc: state.dsThuoc,
      });
    };

  //tính toán thời gian bắt đầu, kết thúc của các line theo thứ tự
  const updateThoiGianThucHien = (idx, data) => {
    const dichVu = data[idx];
    if (!dichVu) return data;

    let _soLuong = dichVu.tachDong ? dichVu.soLuong1Lan : dichVu.soLuong;
    //10: giọt/ph; 20: ml/h
    if (dichVu.tocDoTruyen && _soLuong && dichVu.thoiGianBatDau) {
      _soLuong = Number(dichVu.soLuongSuDung);

      let _slThuocDungKem = (dichVu.dsThuocDungKem || []).reduce(
        (a, b) => a + Number(b.soLuongSuDung) || 0,
        0
      );
      let _tocDoTruyen = isNumber(+dichVu.tocDoTruyen)
        ? dichVu.tocDoTruyen
        : romanToInt(dichVu.tocDoTruyen);

      let _soGiot =
        dichVu.donViTocDoTruyen == 20
          ? 60
          : !dichVu.soGiot
          ? 20 //Trường số giọt/ml cho mặc định = 20 khi Đơn vị tốc độ truyền = giọt/ph và giọt/ml = null trên phiếu
          : dichVu.soGiot;

      if (!data[idx].thoiGianKetThuc) {
        const soPhutTruyen =
          ((_slThuocDungKem + _soLuong) * _soGiot) / _tocDoTruyen;

        data[idx].thoiGianKetThuc = moment(dichVu.thoiGianBatDau).add(
          soPhutTruyen,
          "minutes"
        );
      }

      if (
        idx < data.length - 1 &&
        data[idx + 1].id == data[idx].id &&
        !data[idx + 1]?.thoiGianBatDau
      ) {
        data[idx + 1].thoiGianBatDau = moment(data[idx].thoiGianKetThuc).add(
          data[idx + 1].cachGio || 0,
          "hours"
        );

        return updateThoiGianThucHien(idx + 1, data);
      }
    }

    return data;
  };

  const handleDelete = (id) => () => {
    refConfirm.current.show(
      {
        title: t("common.thongBao"),
        content: `${t("common.banCoChacMuonXoa")}`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        const index = state.dsThuoc.findIndex(
          (item, index) => item.newId === id
        );
        if (index >= 0) {
          state.dsThuoc[index].hieuLuc = false;
          const dsThuoc = cloneDeep(state.dsThuoc);
          setState({
            dsThuoc: dsThuoc,
          });
          dsThuoc.forEach((item) => {
            if (item.tocDoTruyen && !isNumber(+item.tocDoTruyen)) {
              item.tocDoTruyen = romanToInt(item.tocDoTruyen);
            }
          });
          formChange["dsThuoc"]?.(dsThuoc);
        }
      }
    );
  };

  const listThuoc = useMemo(() => {
    const tuThoiGian = state.thoiGianThucHien && state.thoiGianThucHien[0];
    const denThoiGian = state.thoiGianThucHien && state.thoiGianThucHien[1];
    const tuThoiGianM = tuThoiGian ? tuThoiGian.startOf("day") : null;
    const denThoiGianM = denThoiGian ? denThoiGian.endOf("day") : null;

    const data = formDsThuoc
      .filter((item, index, self) => {
        if (tuThoiGianM && denThoiGianM) {
          const thoiGianThucHien = moment(item.thoiGianThucHien);
          return (
            tuThoiGianM.isSameOrBefore(thoiGianThucHien) &&
            denThoiGianM.isSameOrAfter(thoiGianThucHien)
          );
        }

        return self.findIndex((t) => t.id === item.id) === index;
      })
      .map((item) => ({
        ...item,
        ten: renderName(item, {
          showHamLuong: itemProps.showHamLuong,
          showHoatChat: itemProps.showHoatChat,
        }),
      }));
    return getUniqueData(data, "id");
  }, [formDsThuoc, state.thoiGianThucHien]);

  useEffect(() => {
    setState({
      thoiGianThucHien: [
        tuThoiGianTemp ? moment(+tuThoiGianTemp) : null,
        denThoiGianTemp ? moment(+denThoiGianTemp) : null,
      ],
    });
  }, [tuThoiGianTemp, denThoiGianTemp]);

  useEffect(() => {
    //dùng cái này chủ yếu để lọc cho ít dữ liệu đi.
    //nếu là headless thì không có điều kiện lọc, phải show ra hết để in
    if (
      state.dsThuoc?.length > 20 &&
      !context.headless &&
      state.thoiGianThucHien &&
      (!state.thoiGianThucHien[0] || !state.thoiGianThucHien[1])
    ) {
      const groups = groupBy(state.dsThuoc, "thoiGianThucHien");
      const maxTime = Math.max(
        ...Object.keys(groups).map((item) => item?.toDateObject().getTime())
      );
      const now = Date.now();
      let result;

      if (maxTime > now) {
        // max ở tương lai
        setState({ thoiGianThucHien: [moment(now), moment(maxTime)] });
        setQueryStringValues({
          tuThoiGianTemp: now,
          denThoiGianTemp: maxTime,
        });
      } else {
        // max ở quá khứ hoặc bằng hiện tại
        setState({ thoiGianThucHien: [moment(maxTime), moment(now)] });
        setQueryStringValues({
          tuThoiGianTemp: maxTime,
          denThoiGianTemp: now,
        });
      }
    }
  }, [state.thoiGianThucHien, state.dsThuoc, context.headless]);

  const onAddRow = useCallback(
    (index) => {
      const dsThuoc = state.dsThuoc; // copy để tránh mutate trực tiếp state cũ
      const newThuoc = {
        hieuLuc: true,
        index: index + 1,
        newId: stringUitls.guid(),
        isNew: true,
        thoiGianThucHien: moment(),
      };
      dsThuoc.splice(index + 1, 0, newThuoc);
      setState({
        dsThuoc: dsThuoc.map((item, index) => ({ ...item, index })),
      });
    },
    [state.dsThuoc, setState] // dependencies
  );

  const onChangeThoiGianThucHien = useCallback(
    (e) => {
      setState({ thoiGianThucHien: e });
      setQueryStringValues({
        tuThoiGianTemp: e ? e[0] : null,
        denThoiGianTemp: e ? e[1] : null,
      });
    },
    [setQueryStringValues] // không cần setState ở đây
  );

  return (
    <Main lineHeight={15} mode={mode} fontSize={11}>
      <GlobalStyle />
      <div className="date">
        <span style={{ paddingRight: 10 }}>
          {t("common.thoiGianThucHien")}
          {itemProps.isShowEn && "/ Execution time"}:
        </span>
        <RangePicker
          placeholder={[
            `${t("common.tuNgay")}${itemProps.isShowEn ? "/ Start date" : ""}`,
            `${t("common.denNgay")}${itemProps.isShowEn ? "/ End date" : ""}`,
          ]}
          value={state.thoiGianThucHien}
          onChange={onChangeThoiGianThucHien}
        />
      </div>
      <RenderBody
        onAddRow={onAddRow}
        dsThuoc={state.dsThuoc}
        dsSTTDaSinh={state.dsSTTDaSinh}
        thoiGianThucHien={state.thoiGianThucHien}
        onChangeThuocDungKem={onChangeThuocDungKem}
        updateThoiGianThucHien={updateThoiGianThucHien}
        handleDelete={handleDelete}
        listThuoc={listThuoc}
        disable={disable}
        listAllNhanVien={listAllNhanVien}
        {...props}
        onChange={onChange}
      />
    </Main>
  );
};

PhieuTruyenDich.propTypes = {};

export default PhieuTruyenDich;
