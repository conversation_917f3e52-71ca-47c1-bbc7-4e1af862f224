import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>and<PERSON>,
  useState,
} from "react";
import T from "prop-types";
import { Button } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { SettingOutlined } from "@ant-design/icons";
import { MODE } from "utils/editor-utils";
import { DeboundInput } from "module_html_editor/Components";
import AppDatePicker from "components/editor/cores/DatePicker";
import { cloneDeep } from "lodash";

const ListData = [
  {
    stt: 1,
    ten: "Chiếu Plasma",
    donGia: "65.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    ten: "VTTH Plasma",
    donGia: "230.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 2,
    ten: "Siêu âm 2D",
    donGia: "196.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 3,
    ten: "Siêu âm đầu dò",
    donGia: "287.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 4,
    ten: "Tắm khô",
    donGia: "200.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 5,
    ten: "Gội khô",
    donGia: "100.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 6,
    ten: "Giường bạt",
    donGia: "100.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 7,
    ten: "Massage vai, cổ, gáy",
    donGia: "158.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 8,
    ten: "Xông hơi sản chậu chưa thuốc",
    donGia: "100.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
  {
    stt: 9,
    ten: "VTTH Xông hơi sản chậu",
    donGia: "50.000 đ/lần",
    soLan: "",
    soLanNgay1: "",
    soLanNgay2: "",
    soLanNgay3: "",
    soLanNgay4: "",
    soLanNgay5: "",
  },
];

const DonTuNguyenDangKy = forwardRef((props, ref) => {
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { component, mode, formChange, form } = props;

  const init = useDispatch().component.init;

  const itemProps = component.props || {};

  useImperativeHandle(ref, () => ({}));

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    const data = form?.duLieu1?.donTuNguyenDangKy || {};
    const {
      dsThuoc,
      thoiGianThucHien1,
      thoiGianThucHien2,
      thoiGianThucHien3,
      thoiGianThucHien4,
      thoiGianThucHien5,
    } = data;
    setState({
      dsThuoc: dsThuoc?.length ? dsThuoc : ListData,
      thoiGianThucHien1,
      thoiGianThucHien2,
      thoiGianThucHien3,
      thoiGianThucHien4,
      thoiGianThucHien5,
    });
  }, [form]);

  const onChange = (key, index) => (e) => {
    let value = e?.target ? e.target.value : e;
    if (key.startsWith("thoiGianThucHien")) {
      formChange["duLieu1_donTuNguyenDangKy"] &&
        formChange[`duLieu1_donTuNguyenDangKy_${key}`](value);
    } else {
      const dataThuoc = cloneDeep(state.dsThuoc);
      dataThuoc[index][key] = value;
      setState({ dsThuoc: dataThuoc });
      formChange["duLieu1_donTuNguyenDangKy"] &&
        formChange["duLieu1_donTuNguyenDangKy_dsThuoc"](dataThuoc);
    }
  };
  const renderTongDiem = (item) => {
    return (
      (Number(item.soLanNgay1) || 0) +
      (Number(item.soLanNgay2) || 0) +
      (Number(item.soLanNgay3) || 0) +
      (Number(item.soLanNgay4) || 0) +
      (Number(item.soLanNgay5) || 0)
    );
  };
  const renderTable = () => {
    return (
      <table>
        <thead>
          <tr>
            <th rowspan="3">STT</th>
            <th rowspan="3">Nội dung</th>
            <th rowspan="3">Giá tiền</th>
            <th rowspan="3">Số lần</th>
            <th colspan="7">Người bệnh đăng ký</th>
          </tr>
          <tr>
            <th colspan="7">Ngày</th>
          </tr>
          <tr>
            <th>
              <AppDatePicker
                component={{
                  props: {
                    contentAlign: "center",
                    dateTimeFormat: "D/M/Y",
                    fieldName: "value",
                    disableOnblur: true,
                    fontSize: 8,
                  },
                }}
                form={{
                  value: state?.thoiGianThucHien1,
                }}
                mode={mode}
                formChange={{
                  value: (e) => {
                    try {
                      onChange("thoiGianThucHien1")(e);
                    } catch (error) {}
                  },
                }}
              ></AppDatePicker>
            </th>
            <th>
              {" "}
              <AppDatePicker
                component={{
                  props: {
                    contentAlign: "center",
                    dateTimeFormat: "D/M/Y",
                    fieldName: "value",
                    disableOnblur: true,
                    fontSize: 8,
                  },
                }}
                form={{
                  value: state?.thoiGianThucHien2,
                }}
                mode={mode}
                formChange={{
                  value: (e) => {
                    try {
                      onChange("thoiGianThucHien2")(e);
                    } catch (error) {}
                  },
                }}
              ></AppDatePicker>
            </th>
            <th>
              {" "}
              <AppDatePicker
                component={{
                  props: {
                    contentAlign: "center",
                    dateTimeFormat: "D/M/Y",
                    fieldName: "value",
                    disableOnblur: true,
                    fontSize: 8,
                  },
                }}
                form={{
                  value: state?.thoiGianThucHien3,
                }}
                mode={mode}
                formChange={{
                  value: (e) => {
                    try {
                      onChange("thoiGianThucHien3")(e);
                    } catch (error) {}
                  },
                }}
              ></AppDatePicker>
            </th>
            <th>
              {" "}
              <AppDatePicker
                component={{
                  props: {
                    contentAlign: "center",
                    dateTimeFormat: "D/M/Y",
                    fieldName: "value",
                    disableOnblur: true,
                    fontSize: 8,
                  },
                }}
                form={{
                  value: state?.thoiGianThucHien4,
                }}
                mode={mode}
                formChange={{
                  value: (e) => {
                    try {
                      onChange("thoiGianThucHien4")(e);
                    } catch (error) {}
                  },
                }}
              ></AppDatePicker>
            </th>
            <th>
              {" "}
              <AppDatePicker
                component={{
                  props: {
                    contentAlign: "center",
                    dateTimeFormat: "D/M/Y",
                    fieldName: "value",
                    disableOnblur: true,
                    fontSize: 8,
                  },
                }}
                form={{
                  value: state?.thoiGianThucHien5,
                }}
                mode={mode}
                formChange={{
                  value: (e) => {
                    try {
                      onChange("thoiGianThucHien5")(e);
                    } catch (error) {}
                  },
                }}
              ></AppDatePicker>
            </th>
            <th>Tổng</th>
          </tr>
        </thead>
        <tbody>
          {(state?.dsThuoc || []).map((item, index) => {
            return (
              <tr>
                {item.stt && (
                  <td
                    rowSpan={item.stt === 1 ? 2 : 1}
                    style={{ textAlign: "center" }}
                  >
                    {item.stt}
                  </td>
                )}
                <td>{item.ten}</td>
                <td>{item.donGia}</td>
                <td>
                  <DeboundInput
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={12}
                    onChange={onChange("soLan", index)}
                    value={item.soLan}
                  />
                </td>
                <td>
                  <DeboundInput
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={12}
                    onChange={onChange("soLanNgay1", index)}
                    value={item.soLanNgay1}
                  />
                </td>
                <td>
                  <DeboundInput
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={12}
                    onChange={onChange("soLanNgay2", index)}
                    value={item.soLanNgay2}
                  />
                </td>
                <td>
                  <DeboundInput
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={12}
                    onChange={onChange("soLanNgay3", index)}
                    value={item.soLanNgay3}
                  />
                </td>
                <td>
                  <DeboundInput
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={12}
                    onChange={onChange("soLanNgay4", index)}
                    value={item.soLanNgay4}
                  />
                </td>
                <td>
                  <DeboundInput
                    type="multipleline"
                    lineHeightText={1.5}
                    fontSize={12}
                    onChange={onChange("soLanNgay5", index)}
                    value={item.soLanNgay5}
                  />
                </td>
                <td style={{ textAlign: "center" }}>{renderTongDiem(item)}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    );
  };
  return (
    <Main data-type="tdht" mode={mode} itemProps={itemProps}>
      {mode === "config" && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {renderTable()}
      <div className="icon-list"></div>
    </Main>
  );
});

DonTuNguyenDangKy.defaultProps = {
  component: {},
  form: {},
};

DonTuNguyenDangKy.propTypes = {
  component: T.shape({}),
  form: T.shape({}),
};

export default DonTuNguyenDangKy;
