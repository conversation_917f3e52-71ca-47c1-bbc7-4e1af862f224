import React from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useParams, useLocation } from "react-router-dom";
import { <PERSON><PERSON>, Card, ThongTinBenhNhan } from "components";
import { useLoading, useStore } from "hooks";
import DanhSachThuoc from "./DanhSachThuoc";
import RightPanel from "./RightPanel";
import { Main, MainPage } from "./styled";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";

const ChiTietLienThongDonThuocDienTu = (props) => {
  const { t } = useTranslation();
  const { state: locationState } = useLocation();
  const { id, nbDotDieuTriId } = useParams();

  const { showLoading, hideLoading } = useLoading();

  const {
    lienThongDonThuoc: { dayLienThong },
  } = useDispatch();

  const thongTinDonThuoc = useStore("danhSachThuoc.thongTinDonThuoc", []);

  const onDayLienThong = () => {
    showLoading();
    dayLienThong(id).finally(() => {
      hideLoading();
    });
  };

  return (
    <MainPage
      breadcrumb={[
        {
          title: t("title.lienThongDonThuocDienTu"),
          link:
            "/lien-thong-don-thuoc-dien-tu" +
            transformObjToQueryString(locationState),
        },
        {
          title: t("common.chiTiet"),
          link: window.location.pathname,
        },
      ]}
      actionRight={
        !thongTinDonThuoc[0] || thongTinDonThuoc[0]?.trangThai === 30 ? null : (
          <Button type="primary" onClick={onDayLienThong}>
            {t("lienThongDonThuoc.dayLienThong")}
          </Button>
        )
      }
      title={t("lienThongDonThuoc.chiTietLienThongDonThuocDienTu")}
      contentRight={
        <Card className="content-right" bottom={0} top={10} noPadding={true}>
          <RightPanel />
        </Card>
      }
    >
      <Main>
        <ThongTinBenhNhan
          nbDotDieuTriId={nbDotDieuTriId}
          isShowThongTinThanhToan={false}
        />
        <DanhSachThuoc />
      </Main>
    </MainPage>
  );
};

export default ChiTietLienThongDonThuocDienTu;
