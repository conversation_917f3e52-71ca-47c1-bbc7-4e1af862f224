import React, { useEffect, useState, memo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useThietLap, useStore } from "hooks";
import { setQueryStringValues } from "hooks/useQueryString/queryString";
import { BaseSearch } from "components";
import { transformQueryString } from "utils/index";
import { ENUM, THIET_LAP_CHUNG } from "constants/index";

const LIST_CO_KHONG = [
  { id: "not-null", i18n: "common.co" },
  { id: "null", i18n: "common.khong" },
];

const TimKiem = () => {
  const { t } = useTranslation();
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [state, _setState] = useState({});
  const [listTrangThaiDonThuoc] = useEnum(ENUM.TRANG_THAI_DON_THUOC);
  const [listdoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const listBacSi = useStore("nhanVien.listBacSi", []);

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    lienThongDonThuoc: { onChangeInputSearch },
  } = useDispatch();

  useEffect(() => {
    if (!isFinish) return;
    const { page, size, dataSortColumn, ...queries } = transformQueryString({
      dsTrangThai: {
        format: (value) => value.split(",").map(Number),
      },
      dsDoiTuongKcb: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [1, 5, 8],
      },
      dsChiDinhTuLoaiDichVu: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [10, 30, 40, 210, 225],
      },
      dsBacSiChiDinhId: {
        format: (value) => value.split(",").map(Number),
      },
      phat: {
        format: (value) => value === "true",
        defaultValue: true,
      },
      tuThoiGianVaoVien: {
        type: "dateOptions",
        defaultValue: moment(),
      },
      denThoiGianVaoVien: {
        type: "dateOptions",
        defaultValue: moment(),
      },
    });

    setState(queries);
    onChangeInputSearch({
      page: parseInt(page || 0),
      size: parseInt(size || dataPageSize),
      ...queries,
      tuThoiGianVaoVien: queries.tuThoiGianVaoVien?.format(
        "YYYY-MM-DD 00:00:00"
      ),
      denThoiGianVaoVien: queries.denThoiGianVaoVien?.format(
        "YYYY-MM-DD 23:59:59"
      ),
    });
  }, [isFinish, dataPageSize]);

  const onSearch = (e) => {
    setState(e);
    onChangeInputSearch(e);
    setQueryStringValues({ size: 10, page: 0 });
  };

  return (
    <BaseSearch
      cacheData={state}
      dataInput={[
        {
          widthInput: "232px",
          placeholder: t("lienThongDonThuoc.timMaDonThuoc"),
          keyValueInput: "maDonThuoc",
          functionChangeInput: onSearch,
        },
        {
          widthInput: "300px",
          placeholder: t("lienThongDonThuoc.doiTuongKcb"),
          keyValueInput: "dsDoiTuongKcb",
          functionChangeInput: onSearch,
          type: "select",
          listSelect: listdoiTuongKcb,
          mode: "multiple",
          showArrow: true,
        },
        {
          widthInput: "150px",
          placeholder: t("lienThongDonThuoc.trangThaiPhatDonThuoc"),
          keyValueInput: "phat",
          functionChangeInput: onSearch,
          type: "select",
          listSelect: [
            {
              id: true,
              ten: t("common.daPhat"),
            },
            {
              id: false,
              ten: t("common.chuaPhat"),
            },
          ],
        },
        {
          widthInput: "250px",
          placeholder: t("lienThongDonThuoc.timTheoTrangThai"),
          keyValueInput: "dsTrangThai",
          functionChangeInput: onSearch,
          type: "select",
          listSelect: listTrangThaiDonThuoc,
          mode: "multiple",
          defaultValue: [],
          showArrow: true,
        },
        {
          widthInput: "300px",
          placeholder: t("lienThongDonThuoc.timLoaiDichVu"),
          keyValueInput: "dsChiDinhTuLoaiDichVu",
          functionChangeInput: onSearch,
          type: "select",
          listSelect: listLoaiDichVu,
          mode: "multiple",
          showArrow: true,
        },
        {
          widthInput: "350px",
          placeholder: t("common.timTenNbQrNbMaNbMaHoSo"),
          functionChangeInput: onSearch,
          isScanQR: true,
          qrGetValue: "maHoSo",
          keysFlexible: [
            {
              key: "tenNb",
              type: "string",
            },
            {
              key: "maHoSo",
              type: "maHoSo",
            },
            {
              key: "maNb",
              type: "startLettersEndNumbers",
            },
          ],
        },
        {
          widthInput: "300px",
          placeholder: t("lienThongDonThuoc.timBacSiChiDinh"),
          keyValueInput: "dsBacSiChiDinhId",
          functionChangeInput: onSearch,
          type: "select",
          listSelect: listBacSi,
          mode: "multiple",
          showArrow: true,
        },
        {
          widthInput: "220px",
          type: "dateOptions",
          functionChangeInput: (e) => {
            setState({
              tuThoiGianVaoVien: e.tuThoiGianVaoVien,
              denThoiGianVaoVien: e.denThoiGianVaoVien,
            });
            onChangeInputSearch({
              tuThoiGianVaoVien: e.tuThoiGianVaoVien?.format(
                "YYYY-MM-DD 00:00:00"
              ),
              denThoiGianVaoVien: e.denThoiGianVaoVien?.format(
                "YYYY-MM-DD 23:59:59"
              ),
            });
          },
          keyValueInput: ["tuThoiGianVaoVien", "denThoiGianVaoVien"],
          title: t("hsba.ngayDangKy"),
          state,
          format: "DD/MM/YYYY",
        },
        {
          widthInput: "200px",
          placeholder: t("lienThongDonThuoc.taiKhoanLienThong"),
          keyValueInput: "taiKhoanDonThuoc",
          functionChangeInput: onSearch,
          type: "select",
          listSelect: LIST_CO_KHONG,
        },
      ]}
    />
  );
};

export default memo(TimKiem);
