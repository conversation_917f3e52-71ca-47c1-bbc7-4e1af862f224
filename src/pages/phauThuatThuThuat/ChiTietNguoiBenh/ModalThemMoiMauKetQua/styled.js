import styled from "styled-components";

export const Main = styled.div`
  padding: 15px;
  padding-bottom: 0;
  max-height: 85vh;
  overflow-y: auto;
  @media (max-width: 1366px) {
    max-height: 75vh;
  }
  .modal-form-them-moi-mau-ket-qua {
    display: flex;
    flex-wrap: wrap;
    .ant-form-item {
      width: 49%;
      margin-bottom: 12px;
      padding-right: 0.75rem;
      padding-left: 0.75rem;
      font-weight: 600;
      color: #172b4d;
      .ant-select-selection-placeholder {
        font-weight: 400;
      }
    }
  }
`;

export const ImageEditStyled = styled.div`
  min-height: 260px;
  display: flex;
  justify-content: center;
  height: 100%;
  .img-view {
    max-height: 100%;
    width: 100%;
    object-fit: cover;
  }
  .btn-camera {
    bottom: 15px;
    right: 15px;
  }
  .ant-btn-icon-only {
    padding: 1px;
  }
`;
