import React, {
  forwardRef,
  useState,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import { Form, Input } from "antd";
import { ModalTemplate, Button, Select, ImageEdit, Checkbox } from "components";
import { useTranslation } from "react-i18next";
import { Main, ImageEditStyled } from "./styled";
import FormWraper from "components/FormWraper";
import { useDispatch } from "react-redux";
import { useLoading, useListAll, useStore } from "hooks";
import { LOAI_DICH_VU } from "constants/index";
import { SVG } from "assets";

const ModalThemMoiMauKetQua = (props, ref) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refCallback = useRef(null);
  const refModal = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({ show: false, luocDo: null });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    pttt: { updateThongTinPTTT },
    mauKetQuaPTTT: { createOrEdit },
  } = useDispatch();

  const [listAllPhuongPhapVoCam] = useListAll(
    "phuongPhapVoCam",
    {
      active: true,
    },
    state.show
  );
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const nhanVienId = useStore("auth.auth.nhanVienId", null);

  useImperativeHandle(ref, () => ({
    show: (data = {}, callback) => {
      const {
        phuongPhapVoCamId,
        chanDoan,
        phuongPhap,
        cachThuc,
        ketLuan,
        luocDo,
      } = data || {};
      setState({ show: true, luocDo: luocDo || null });

      form.setFieldsValue({
        ma: null,
        ten: null,
        ketLuan: ketLuan || null,
        phuongPhapVoCamId: phuongPhapVoCamId || null,
        chanDoan: chanDoan || null,
        phuongThuc: phuongPhap || null,
        cachThuc: cachThuc || null,
        luocDo: luocDo || null,
        dsKhoaId: null,
        dsBacSiChiDinhId: [nhanVienId],
        active: true,
      });
      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({
        show: false,
      });
    }
  };

  const onFinish = async (values) => {
    try {
      showLoading();
      let res = await createOrEdit({
        ...values,
        loaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        active: true,
      });
      updateThongTinPTTT({
        ketLuan: values?.ketLuan,
        phuongPhapVoCamId: values?.phuongPhapVoCamId,
        chanDoan: values?.chanDoan,
        phuongPhap: values?.phuongThuc,
        cachThuc: values?.cachThuc,
        luocDo: values?.luocDo,
      });
      refCallback.current && refCallback.current(res.data);
      onOk(false)();
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={600}
      title={t("pttt.themNhanhMauKetQua")}
      onCancel={onOk(false)}
      actionLeft={<Button.QuayLai onClick={onOk(false)} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onOk(true)}
          rightIcon={<SVG.IcSave />}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <FormWraper
          className="modal-form-them-moi-mau-ket-qua"
          form={form}
          layout="vertical"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            label={t("danhMuc.maMauKetQuaPttt")}
            name="ma"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapMaMauKetQuaPttt") + "!",
              },
              {
                max: 20,
                message:
                  t("danhMuc.vuiLongNhapMaMauKetQuaPtttKhongQua20KyTu") + "!",
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapMaMauKetQuaPttt") + "!",
              },
            ]}
          >
            <Input
              autoFocus={true}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapMaMauKetQuaPttt")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.tenMauKetQuaPttt")}
            name="ten"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTenMauKetQuaPttt") + "!",
              },
              {
                max: 1000,
                message:
                  t("danhMuc.vuiLongNhapTenMauKetQuaPtttKhongQua1000KyTu") +
                  "!",
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapTenMauKetQuaPttt") + "!",
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenMauKetQuaPttt")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.phuongPhapVoCam")}
            name="phuongPhapVoCamId"
          >
            <Select
              data={listAllPhuongPhapVoCam}
              className="input-option"
              placeholder={t("danhMuc.vuiLongChonPhuongPhap")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.ketLuan")} name="ketLuan">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapKetLuan")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.cachThucPttt")}
            className="w100"
            name="cachThuc"
          >
            <Input.TextArea
              rows={3}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapCachThuc")}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.phuongPhapPttt")}
            className="w100"
            name="phuongThuc"
          >
            <Input.TextArea
              rows={3}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapPhuongPhap")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.chanDoanSauPttt")} name="chanDoan">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapChanDoan")}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.khoaChiDinh")} name="dsKhoaId">
            <Select
              className="input-option"
              data={listAllKhoa}
              placeholder={t("danhMuc.vuiLongChonKhoaChiDinh")}
              mode="multiple"
            />
          </Form.Item>
          <Form.Item label={t("pttt.bacSiChiDinh")} name="dsBacSiChiDinhId">
            <Select
              className="input-option"
              data={listAllNhanVien}
              placeholder={t("danhMuc.vuiLongChonBacSiChiDinh")}
              mode="multiple"
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.anhLuocDoPhauThuatThuThuat")}
            name="luocDo"
          >
            <ImageEditStyled>
              <ImageEdit
                typeApi="dmMauPtttLuocDo"
                src={state?.luocDo}
                afterSave={(s) => {
                  setState({ luocDo: s });
                  form.setFieldsValue({ luocDo: s });
                }}
                tooltipTitle={t("pttt.nhanDeTaiAnhMauKetQua")}
                placeholder={t("pttt.nhanDeTaiAnhMauKetQua")}
              />
            </ImageEditStyled>
          </Form.Item>
        </FormWraper>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalThemMoiMauKetQua);
