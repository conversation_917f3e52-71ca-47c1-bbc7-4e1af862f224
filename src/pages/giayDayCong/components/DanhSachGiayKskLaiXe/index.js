import React, {
  forwardRef,
  useRef,
  useState,
  useImperativeHandle,
} from "react";
import {
  TableWrapper,
  Pagination,
  Tooltip,
  HeaderSearch,
  Checkbox,
} from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import moment from "moment";
import { useConfirm, useEnum, useLoading, useSearchParams } from "hooks";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { cleanEmptyData, cleanObjectUpdate, combineSort } from "utils";
import nbKhamKskLaiXeProvider from "data-access/nb-kham-ksk-lai-xe-provider";
import { ENUM, ROLES } from "constants/index";
import { Main } from "./styled";
import { getTrangThaiKy } from "../TimKiemGiayChungSinh";

const { Column, Setting } = TableWrapper;

const DanhSachGiayKskLaiXe = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);
  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);

  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const { searchParams, setSearchParams } = useSearchParams({
    defaultValue: {
      tuThoiGianVaoVien: moment()
        .subtract(6, "days")
        .format("YYYY/MM/DD 00:00:00"),
      denThoiGianVaoVien: moment()
        .subtract(6, "days")
        .format("YYYY/MM/DD 23:59:59"),
    },
  });
  const [totalElements, setTotalElements] = useState(0);
  const { sort: dataSortColumn = {}, page = 0, size = 10 } = searchParams;

  const params = cleanEmptyData({
    page,
    size,
    tenNb: searchParams.tenNb,
    maHoSo: searchParams.maHoSo,
    maBenhAn: searchParams.maBenhAn,
    maNb: searchParams.maNb,
    dsTrangThai: searchParams.dsTrangThai,
    dsTrangThaiNb: searchParams.dsTrangThaiNb,
    dsTrangThaiKy:
      searchParams.dsTrangThaiKy?.length === 3
        ? null
        : searchParams.dsTrangThaiKy?.flatMap(
            (i) => getTrangThaiKy(i)?.referIds || []
          ),
    tuThoiGianVaoVien: searchParams.tuThoiGianVaoVien,
    denThoiGianVaoVien: searchParams.denThoiGianVaoVien,
    sort: combineSort(dataSortColumn),
  });

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["queryGiayKskLaiXe", params],
    queryFn: () => nbKhamKskLaiXeProvider.queryAll(params),
    onSuccess: (s) => {
      setTotalElements(s?.totalElements || 0);
    },
  });

  const {
    giayKskLaiXe: {
      dayGiayKskLaiXeById,
      huyGiayKskLaiXeById,
      updateData,
      kyHangLoat,
    },
    phieuIn: { showFileEditor, getListPhieu },
  } = useDispatch();

  const onKyHangLoat = () => {
    showConfirm(
      {
        title: t("giayDayCong.xacNhanKy"),
        content: t("giayDayCong.xacNhanKyHangLoatCacPhieu"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        const payload = state.selectedRowKeys.map((item) => ({
          id: item,
        }));
        kyHangLoat(payload).then((s) => {
          refetch();
          setState({
            isCheckedAll: false,
            selectedRowKeys: [],
          });

          updateData({ selectedRowKeys: [] });
          if (s?.messageError) {
            showConfirm({
              title: t("common.thongBao"),
              content: s?.messageError,
              cancelText: t("common.huy"),
              typeModal: "error",
            });
          }
        });
      }
    );
  };

  useImperativeHandle(ref, () => ({
    onKyHangLoat,
  }));

  const onClickSort = (key, value) => {
    const newSort = cleanObjectUpdate(dataSortColumn, key, value);
    setSearchParams({ sort: newSort, page: 0 });
  };

  const onGuiChungTu = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    dayGiayKskLaiXeById(item.id).then(() => {
      refetch();
    });
  };

  const onHuyChungNhan = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyChungNhan", {
          ten: item.HO_TEN || "",
        }),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        showLoading();
        huyGiayKskLaiXeById(item.id)
          .then(() => {
            hideLoading();
            refetch();
          })
          .catch(() => {
            hideLoading();
          });
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "80px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (_, __, index) => page * size + index + 1,
    }),
    Column({
      title: t("common.maNb"),
      sort_key: "maNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maNb"] || "",
      width: "120px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
    }),
    Column({
      title: t("giayDayCong.maHoSo"),
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("kho.hoTenNb"),
      sort_key: "HOTEN",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HOTEN"] || "",
      width: "200px",
      dataIndex: "HOTEN",
      key: "HOTEN",
      i18Name: "kho.hoTenNb",
    }),
    Column({
      title: t("giayDayCong.ngaySinh"),
      width: "120px",
      dataIndex: "NGAYSINH",
      key: "NGAYSINH",
      i18Name: "giayDayCong.ngaySinh",
      render: (field, item, index) =>
        field
          ? moment(field).format(item?.chiNamSinh ? "YYYY" : "DD / MM / YYYY")
          : "",
    }),
    Column({
      title: t("giayDayCong.diaChi"),
      width: "220px",
      dataIndex: "diaChi",
      key: "diaChi",
      i18Name: "giayDayCong.diaChi",
    }),
    Column({
      title: t("tenTruong.trangThaiDayCong"),
      sort_key: "trangThaiDayCong",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThaiDayCong"] || "",
      width: "150px",
      dataIndex: "trangThaiDayCong",
      key: "trangThaiDayCong",
      i18Name: "tenTruong.trangThaiDayCong",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.thoiGianVaoVien"),
      sort_key: "thoiGianVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.thoiGianVaoVien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            {checkRole([ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_CHI_TIET]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye className="ic-action" onClick={onViewDetail(item)} />
              </Tooltip>
            )}
            {list.trangThaiDayCong == 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_HUY]) && (
                <Tooltip title={t("giayDayCong.action.huyGiamDinh")}>
                  <SVG.IcCloseCircle
                    className="ic-action huyGiamDinh"
                    onClick={onHuyChungNhan(item)}
                  />
                </Tooltip>
              )}
            {list.trangThaiDayCong != 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_DAY_LE]) && (
                <Tooltip title={t("giayDayCong.action.guiGiamDinh")}>
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiChungTu(item)}
                  />
                </Tooltip>
              )}
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    setSearchParams({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setSearchParams({ page: 0, size });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onViewDetail(record)(event);
        }
      },
    };
  };

  const onViewDetail = (data) => async (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!checkRole([ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_CHI_TIET])) return;

    try {
      showLoading();

      const { id, nbDotDieuTriId } = data || {};
      let listPhieu = await getListPhieu({
        nbDotDieuTriId,
        maManHinh: "029",
        maViTri: "02901",
      });

      let phieuP196 = (listPhieu || []).find((o) => o.ma === "P196");

      if (phieuP196) {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(phieuP196)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            nbDotDieuTriId,
            maManHinh: "029",
            maViTri: "02901",
            kySo: true,
            maPhieuKy: phieuP196.ma,
          };
        }

        showFileEditor({
          phieu: phieuP196,
          nbDotDieuTriId,
          nbDvKhamId: id,
          mhParams,
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const oncheckAll = (e) => {
    const _selectedRowKeys = e.target?.checked
      ? (data?.data || []).map((item) => {
          return item?.id;
        })
      : [];

    setState({
      selectedRowKeys: _selectedRowKeys,
      isCheckedAll: e.target?.checked,
    });

    updateData({ selectedRowKeys: _selectedRowKeys });
  };

  const onSelectChange = (selectedRowKeys) => {
    let updatedSelectedKeys = selectedRowKeys;
    updatedSelectedKeys = [...new Set(updatedSelectedKeys)];

    setState({
      isCheckedAll: (data?.data || []).length === updatedSelectedKeys.length,
      selectedRowKeys: updatedSelectedKeys,
    });

    updateData({ selectedRowKeys: updatedSelectedKeys });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            style={{ color: "#03317c" }}
            onChange={oncheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        rowSelection={rowSelection}
        columns={columns}
        dataSource={data?.data || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        tableName="table_GIAYDAYCONG_GiayKskLaiXe"
        ref={refSettings}
        loading={isLoading}
      />
      {!!totalElements && (
        <Pagination
          listData={data?.data || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
    </Main>
  );
};

export default forwardRef(DanhSachGiayKskLaiXe);
