import React, { useEffect, useRef } from "react";
import { Main } from "./styled";
import { Tooltip, TableWrapper, Pagination } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useConfirm, useEnum, useStore } from "hooks";
import { ENUM, ROLES, TRANG_THAI_DAY_CONG } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { SVG } from "assets";
import { getAllQueryString } from "hooks/useQueryString/queryString";

const { Column, Setting } = TableWrapper;

const DanhSachPhieuTomTatBa = (props) => {
  const { selectedRowKeys, setSelectedRowKeys } = props;
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refSettings = useRef(null);

  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);

  const { listData, totalElements, page, size, dataSortColumn } = useStore(
    "phieuTomTatBa",
    {},
    { fields: "listData, totalElements, page, size, dataSortColumn" }
  );

  const {
    phieuTomTatBa: {
      onSearch,
      onSizeChange,
      onSortChange,
      searchPhieuTomTatBaByParams,
      dayTomTatBAById,
    },
  } = useDispatch();

  useEffect(() => {
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = getAllQueryString();
    if (queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien =
        queries.tuThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.tuThoiGianVaoVien)).format(
              "YYYY-MM-DD 00:00:00"
            );
    } else {
      queries.tuThoiGianVaoVien = moment()
        .subtract(6, "days")
        .set("hour", 0)
        .set("minute", 0)
        .set("second", 0)
        .format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien =
        queries.denThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.denThoiGianVaoVien)).format(
              "YYYY-MM-DD 23:59:59"
            );
    } else {
      queries.denThoiGianVaoVien = moment()
        .set("hour", 23)
        .set("minute", 59)
        .set("second", 59)
        .format("YYYY-MM-DD HH:mm:ss");
    }
    if (queries.dsTrangThaiDayCong) {
      queries.dsTrangThaiDayCong = queries.dsTrangThaiDayCong
        .split(",")
        .map(Number);
    } else {
      queries.dsTrangThaiDayCong = [
        TRANG_THAI_DAY_CONG.TAO_MOI,
        TRANG_THAI_DAY_CONG.TAO_LAI,
        TRANG_THAI_DAY_CONG.GUI_THAT_BAI,
        TRANG_THAI_DAY_CONG.GUI_THANH_CONG,
      ];
    }

    searchPhieuTomTatBaByParams(queries);
  }, []);

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const refreshList = () => {
    onSizeChange({ page, size });
  };

  const onGuiChungTu = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    dayTomTatBAById(item.id).then(() => {
      refreshList();
    });
  };

  const onHuyChungNhan = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyChungNhan", {
          ten: item.HO_TEN || "",
        }),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        // huyGiayNghiBaoHiemById(item.id).then(() => {
        //   refreshList();
        // });
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("giayDayCong.HO_TEN"),
      sort_key: "HO_TEN",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN"] || "",
      width: "250px",
      dataIndex: "HO_TEN",
      key: "HO_TEN",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.tenNb",
    }),
    Column({
      title: t("giayDayCong.maBenhAn"),
      sort_key: "maBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maBenhAn"] || "",
      width: "120px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maBenhAn",
    }),
    Column({
      title: t("giayDayCong.maHoSo"),
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("giayDayCong.MA_THE"),
      width: "150px",
      dataIndex: "MA_THE",
      key: "MA_THE",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.MA_THE",
    }),
    Column({
      title: t("giayDayCong.trangThai"),
      sort_key: "trangThai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThai"] || "",
      width: "170px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "giayDayCong.trangThai",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.ngayChungTu"),
      sort_key: "ngayChungTu",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngayChungTu"] || "",
      width: "150px",
      dataIndex: "ngayChungTu",
      i18Name: "giayDayCong.ngayChungTu",
      key: "ngayChungTu",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.thoiGianVaoVien"),
      sort_key: "thoiGianVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.thoiGianVaoVien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),

    Column({
      title: t("giayDayCong.GIOI_TINH"),
      width: "100px",
      dataIndex: "GIOI_TINH",
      key: "GIOI_TINH",
      show: false,
      i18Name: "giayDayCong.GIOI_TINH",
      align: "center",
      render: (item) => listgioiTinh.find((gt) => gt.id === item)?.ten,
    }),
    Column({
      title: t("giayDayCong.ngaySinh"),
      width: "150px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "giayDayCong.ngaySinh",
      render: (field, item, index) =>
        field
          ? moment(field).format(item?.chiNamSinh ? "YYYY" : "DD / MM / YYYY")
          : "",
      show: false,
    }),
    Column({
      title: t("giayDayCong.diaChi"),
      width: "220px",
      dataIndex: "diaChi",
      key: "diaChi",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.diaChi",
      show: false,
    }),
    Column({
      title: t("giayDayCong.MA_BHXH"),
      sort_key: "MA_BHXH",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["MA_BHXH"] || "",
      width: "150px",
      dataIndex: "MA_BHXH",
      i18Name: "giayDayCong.MA_BHXH",
      key: "MA_BHXH",
      show: false,
    }),
    Column({
      title: t("common.thoiGianRaVien"),
      sort_key: "thoiGianRaVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianRaVien"] || "",
      width: "150px",
      dataIndex: "thoiGianRaVien",
      key: "thoiGianRaVien",
      i18Name: "common.thoiGianRaVien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
      show: false,
    }),
    Column({
      title: t("giayDayCong.tenKhoaNb"),
      width: "220px",
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
      show: false,
      i18Name: "giayDayCong.tenKhoaNb",
    }),

    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            {checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_CHI_TIET]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye onClick={onViewDetail(item)} className="ic-action" />
              </Tooltip>
            )}

            {list.trangThai == 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_HUY]) && (
                <Tooltip title={t("giayDayCong.action.huyGiamDinh")}>
                  <SVG.IcCloseCircle
                    className="ic-action"
                    onClick={onHuyChungNhan(item)}
                  />
                </Tooltip>
              )}

            {list.trangThai != 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_DAY_LE]) && (
                <Tooltip title={t("giayDayCong.action.guiGiamDinh")}>
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiChungTu(item)}
                  />
                </Tooltip>
              )}
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onViewDetail(record)(event);
        }
      },
    };
  };

  const onViewDetail = (data) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_CHI_TIET])) return;
    const { nbDotDieuTriId } = data;
    window.open("/editor/bao-cao/EMR_BA049/" + nbDotDieuTriId);
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        tableName="table_GIAYDAYCONG_PhieuTomTatBa"
        ref={refSettings}
        rowSelection={{
          onChange: (selectedRowKeys) => {
            setSelectedRowKeys(selectedRowKeys);
          },
          selectedRowKeys,
        }}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
    </Main>
  );
};

export default DanhSachPhieuTomTatBa;
