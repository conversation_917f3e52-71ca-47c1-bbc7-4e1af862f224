import React, { useRef, useState } from "react";
import { Tooltip, TableWrapper, Pagination, ModalSignPrint } from "components";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { SVG } from "assets";
import { useQuery } from "@tanstack/react-query";
import { checkRole } from "lib-utils/role-utils";
import { useDispatch } from "react-redux";
import {
  useConfirm,
  useEnum,
  useLoading,
  useSearchParams,
  useThietLap,
} from "hooks";
import printProvider from "data-access/print-provider";
import { message } from "antd";
import { cleanEmptyData, cleanObjectUpdate, combineSort } from "utils";
import {
  ROLES,
  ENUM,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
  TRANG_THAI_DAY_CONG,
  THIET_LAP_CHUNG,
} from "constants/index";
import nbBienBanKiemDiemTuVongProvider from "data-access/nb-bien-ban-kiem-diem-tu-vong-provider";
import { Main } from "./styled";
import { getTrangThaiKy } from "../TimKiemGiayChungSinh";

const { Column, Setting } = TableWrapper;

const DanhSachNbTuVong = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);
  const refModalSignPrint = useRef(null);
  const { onShowTTTuVong, selectedRowKeys, setSelectedRowKeys } = props;
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);

  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);

  const { searchParams, setSearchParams } = useSearchParams({
    defaultValue: {
      dsTrangThaiDayCong: [TRANG_THAI_DAY_CONG.TAO_MOI],
      tuThoiGianRaVien: moment()
        .subtract(6, "days")
        .format("YYYY/MM/DD 00:00:00"),
      denThoiGianRaVien: moment().format("YYYY/MM/DD 23:59:59"),
      size: dataPageSize,
      sort: {
        thoiGianTuVong: 2,
      },
    },
  });

  const [totalElements, setTotalElements] = useState(0);
  const {
    sort: dataSortColumn = {},
    page = 0,
    size = dataPageSize,
  } = searchParams;

  const params = cleanEmptyData({
    page,
    size,
    maNb: searchParams.maNb,
    maHoSo: searchParams.maHoSo,
    maBenhAn: searchParams.maBenhAn,
    tenNb: searchParams.tenNb,
    tuThoiGianRaVien: searchParams.tuThoiGianRaVien,
    denThoiGianRaVien: searchParams.denThoiGianRaVien,
    dsTrangThaiDayCong: searchParams.dsTrangThaiDayCong,
    dsTrangThaiNb: searchParams.dsTrangThaiNb,
    dsTrangThaiKy:
      searchParams.dsTrangThaiKy?.length === 3
        ? null
        : searchParams.dsTrangThaiKy?.flatMap(
            (i) => getTrangThaiKy(i)?.referIds || []
          ),
    sort: combineSort(dataSortColumn),
  });

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["queryNbTuVong", params],
    queryFn: () => nbBienBanKiemDiemTuVongProvider.search(params),
    onSuccess: (s) => {
      setTotalElements(s?.totalElements || 0);
    },
    enabled: isFinish,
  });

  const {
    nbTuVong: { guiGiamDinhTuVong, huyGiayBaoTu },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
  } = useDispatch();

  const onGuiGiamDinh = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    guiGiamDinhTuVong(item.id);
  };
  const onPrint = (nbDotDieuTriId, id) => (e) => {
    e.stopPropagation();
    e.preventDefault();

    const maManHinh = MAN_HINH_PHIEU_IN.NB_TU_VONG,
      maViTri = VI_TRI_PHIEU_IN.GIAY_DAY_CONG.IN_GIAY_CHUNG_TU,
      maPhieu = "P209";

    getListPhieu({
      nbDotDieuTriId,
      maManHinh,
      maViTri,
    }).then(async (res) => {
      const phieu = res?.filter((o) => o.ma === maPhieu)?.[0];
      if (!phieu) {
        message.error(
          t("giayDayCong.maKhongDuocThietLapTaiManHinhTaiViTri", {
            maPhieu,
            maManHinh,
            maViTri,
          })
        );

        return;
      }
      if (phieu?.type == "editor") {
        showFileEditor({
          phieu,
          nbDotDieuTriId,
        });
      } else {
        try {
          showLoading();
          const response = await getFilePhieuIn({
            listPhieus: [phieu],
            showError: true,
            nbDotDieuTriId,
          });

          if (
            phieu.kySo &&
            phieu.ma === maPhieu &&
            phieu.dsSoPhieu.length === 1 &&
            phieu.dsSoPhieu[0]?.soPhieu == nbDotDieuTriId &&
            phieu.dsSoPhieu[0]?.trangThai !== 10
          ) {
            refModalSignPrint.current &&
              refModalSignPrint.current.showToSign({
                phieuKy: phieu,
                payload: {
                  nbDotDieuTriId,
                  maManHinh,
                  maViTri,
                  id,
                },
              });
          } else if ((response.dsPhieu || []).every((x) => x?.loaiIn == 20)) {
            openInNewTab(response.finalFile);
          } else {
            printProvider.printPdf(response.dsPhieu);
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          hideLoading();
        }
      }
    });
  };

  const onHuyGiamDinh = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyGiamDinh", {
          ten: item.tenNb || "",
        }),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        try {
          showLoading();
          await huyGiayBaoTu(item.id);

          refetch();
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onClickSort = (key, value) => {
    const newSort = cleanObjectUpdate(dataSortColumn, key, value);
    setSearchParams({ sort: newSort, page: 0 });
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (_, __, index) => page * size + index + 1,
    }),
    Column({
      title: t("giayDayCong.tenNb"),
      width: "250px",
      dataIndex: "tenNb",
      key: "tenNb",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.tenNb",
    }),
    Column({
      title: t("giayDayCong.maBenhAn"),
      width: "120px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maBenhAn",
    }),
    Column({
      title: t("giayDayCong.maHoSo"),
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("tenTruong.trangThaiDayCong"),
      sort_key: "trangThaiDayCong",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThaiDayCong"] || "",
      width: "170px",
      dataIndex: "trangThaiDayCong",
      key: "trangThaiDayCong",
      i18Name: "tenTruong.trangThaiDayCong",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.ngaySinh"),
      width: "150px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "giayDayCong.ngaySinh",
      render: (field, item, index) =>
        field
          ? moment(field).format(item?.chiNamSinh ? "YYYY" : "DD / MM / YYYY")
          : "",
    }),
    Column({
      title: t("giayDayCong.diaChi"),
      width: "220px",
      dataIndex: "diaChi",
      key: "diaChi",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.diaChi",
    }),
    Column({
      title: t("giayDayCong.nbTuVong.thoiGianTuVong"),
      width: "150px",
      dataIndex: "thoiGianTuVong",
      key: "thoiGianTuVong",
      i18Name: "giayDayCong.nbTuVong.thoiGianTuVong",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.nbTuVong.thoiGianChungTu"),
      width: "150px",
      dataIndex: "thoiGianThucHien",
      i18Name: "giayDayCong.nbTuVong.thoiGianChungTu",
      key: "thoiGianThucHien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.nbTuVong.thoiGianVaoVien"),
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.nbTuVong.thoiGianVaoVien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.maGiayChungTu"),
      width: "180px",
      dataIndex: "maGiayBaoTu",
      key: "maGiayBaoTu",
      i18Name: "giayDayCong.maGiayChungTu",
    }),
    Column({
      title: t("giayDayCong.soGiayChungTu"),
      width: "150px",
      dataIndex: "soPhieu",
      key: "soPhieu",
      i18Name: "giayDayCong.soGiayChungTu",
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "150px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <>
            <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
              <SVG.IcEye className="ic-action" onClick={onShowTTTuVong(list)} />
            </Tooltip>
            <Tooltip title={t("giayDayCong.inGiayChungTu")}>
              <SVG.IcPrint
                className="ic-action"
                onClick={onPrint(item.nbDotDieuTriId, item.id)}
              />
            </Tooltip>

            {list.trangThaiDayCong == 40 && (
              <Tooltip
                title={t("giayDayCong.action.huyGiamDinh")}
                placement="topLeft"
              >
                <SVG.IcCloseCircle
                  className="ic-action"
                  onClick={onHuyGiamDinh(item)}
                />
              </Tooltip>
            )}

            {list.trangThaiDayCong != 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].NB_TU_VONG_DAY_LE]) && (
                <Tooltip
                  title={t("giayDayCong.action.dayCongGiamDinh")}
                  placement="topLeft"
                >
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiGiamDinh(item)}
                  />
                </Tooltip>
              )}
          </>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    setSearchParams({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setSearchParams({ page: 0, size });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onShowTTTuVong(record)(event);
        }
      },
    };
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={data?.data || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        ref={refSettings}
        tableName="table_GIAYDAYCONG_DsNbTuVong"
        loading={isLoading}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
      />
      {!!totalElements && (
        <Pagination
          listData={data?.data || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
      <ModalSignPrint ref={refModalSignPrint} />
    </Main>
  );
};

export default DanhSachNbTuVong;
