import React, { useEffect, useRef, useState } from "react";
import { Main } from "./styled";
import { Tooltip, Checkbox, TableWrapper, Pagination } from "components";
import { connect, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useConfirm, useEnum, useLoading } from "hooks";
import { ENUM, ROLES, TRANG_THAI_DAY_CONG } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { SVG } from "assets";
import { transformQueryString } from "utils/index";
const { Column, Setting } = TableWrapper;

const DanhSachGiayNghiHuong = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);
  const {
    listData,
    page,
    size,
    totalElements,
    dataSortColumn,

    onSearch,
    onSizeChange,
    onSortChange,
    showFileEditor,
    selectedRowKeys,
    setSelectedRowKeys,
  } = props;

  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);

  const {
    giayNghiHuong: {
      dayGiayNghiBaoHiemById,
      huyGiayNghiBaoHiemById,
      searchGiayNghiHuongByParams,
      deleteGiayNghiBaoHiem,
    },
  } = useDispatch();

  const [state, _setState] = useState({
    isCheckedAll: false,
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useEffect(() => {
    const queries = transformQueryString({
      tuThoiGianVaoVien: {
        defaultValue: moment()
          .subtract(6, "days")
          .format("YYYY-MM-DD 00:00:00"),
        format: (date) => date.format("YYYY-MM-DD 00:00:00"),
        type: "dateOptions",
      },
      denThoiGianVaoVien: {
        defaultValue: moment().format("YYYY-MM-DD 23:59:59"),
        format: (date) => date.format("YYYY-MM-DD 23:59:59"),
        type: "dateOptions",
      },
      dsTrangThai: {
        format: (value) => {
          return value.split(",").map(Number);
        },
        defaultValue: [TRANG_THAI_DAY_CONG.TAO_MOI],
      },
    });

    searchGiayNghiHuongByParams(queries);
  }, []);

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const refreshList = () => {
    onSizeChange({ page, size });
  };

  const onGuiChungTu = (item) => async (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      showLoading();
      await dayGiayNghiBaoHiemById(item.id).then(() => {
        refreshList();
      });
    } catch (error) {
      console.error(error?.message || error);
    } finally {
      hideLoading();
    }
  };

  const onHuyChungNhan = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyChungNhan", {
          ten: item.HO_TEN || "",
        }),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        showLoading();
        await huyGiayNghiBaoHiemById(item.id)
          .then(() => {
            hideLoading();
            refreshList();
          })
          .catch(() => {
            hideLoading();
          });
      }
    );
  };

  const onXoaHoSo = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("common.thongBao"),
        content: `${t("common.banCoChacMuonXoa")}`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        showLoading();
        await deleteGiayNghiBaoHiem(item.id)
          .then(() => {
            hideLoading();
            refreshList();
          })
          .catch(() => {
            hideLoading();
          });
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("giayDayCong.HO_TEN"),
      sort_key: "HO_TEN",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN"] || "",
      width: "250px",
      dataIndex: "HO_TEN",
      key: "HO_TEN",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.tenNb",
    }),
    Column({
      title: t("giayDayCong.SO_KCB"),
      sort_key: "SO_KCB",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["SO_KCB"] || "",
      width: "120px",
      dataIndex: "SO_KCB",
      key: "SO_KCB",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("giayDayCong.MA_THE"),
      width: "150px",
      dataIndex: "MA_THE",
      key: "MA_THE",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.MA_THE",
    }),
    Column({
      title: t("giayDayCong.trangThai"),
      sort_key: "trangThai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThai"] || "",
      width: "170px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "giayDayCong.trangThai",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.ngayChungTu"),
      sort_key: "ngayChungTu",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngayChungTu"] || "",
      width: "150px",
      dataIndex: "ngayChungTu",
      i18Name: "giayDayCong.ngayChungTu",
      key: "ngayChungTu",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.TEN_NGUOI_HANH_NGHE"),
      width: "200px",
      dataIndex: "TEN_NGUOI_HANH_NGHE",
      key: "TEN_NGUOI_HANH_NGHE",
      i18Name: "giayDayCong.TEN_NGUOI_HANH_NGHE",
      render: (item) => {
        return item;
      },
    }),
    Column({
      title: t("giayDayCong.thongTinTraVe"),
      width: "120px",
      dataIndex: "ketQua",
      key: "ketQua",
      i18Name: "giayDayCong.thongTinTraVe",
      render: (item) => {
        return item;
      },
    }),
    Column({
      title: t("giayDayCong.thoiGianVaoVien"),
      sort_key: "thoiGianVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.thoiGianVaoVien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),

    Column({
      title: t("giayDayCong.MA_BHXH"),
      sort_key: "MA_BHXH",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["MA_BHXH"] || "",
      width: "150px",
      dataIndex: "MA_BHXH",
      i18Name: "giayDayCong.MA_BHXH",
      key: "MA_BHXH",
      show: false,
    }),
    Column({
      title: t("giayDayCong.DON_VI"),
      sort_key: "DON_VI",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["DON_VI"] || "",
      width: "150px",
      dataIndex: "DON_VI",
      i18Name: "giayDayCong.DON_VI",
      key: "DON_VI",
      show: false,
    }),
    Column({
      title: t("giayDayCong.dsCdChinh"),
      sort_key: "dsCdChinh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["dsCdChinh"] || "",
      width: "200px",
      dataIndex: "dsCdChinh",
      show: false,
      i18Name: "giayDayCong.dsCdChinh",
      key: "dsCdChinh",
      render: (item) => (item || []).map((x) => x.ten).join(", "),
    }),
    Column({
      title: t("giayDayCong.phuongPhapDieuTri"),
      sort_key: "phuongPhapDieuTri",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["phuongPhapDieuTri"] || "",
      width: "150px",
      dataIndex: "phuongPhapDieuTri",
      i18Name: "giayDayCong.phuongPhapDieuTri",
      key: "phuongPhapDieuTri",
      show: false,
    }),
    Column({
      title: t("giayDayCong.soNgay"),
      sort_key: "soNgay",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["soNgay"] || "",
      width: "150px",
      show: false,
      dataIndex: "soNgay",
      i18Name: "giayDayCong.soNgay",
      key: "soNgay",
    }),
    Column({
      title: t("giayDayCong.tuNgay"),
      sort_key: "tuNgay",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tuNgay"] || "",
      width: "150px",
      dataIndex: "tuNgay",
      i18Name: "giayDayCong.tuNgay",
      key: "tuNgay",
      show: false,
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.denNgay"),
      sort_key: "denNgay",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["denNgay"] || "",
      width: "150px",
      show: false,
      dataIndex: "denNgay",
      i18Name: "giayDayCong.denNgay",
      key: "denNgay",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.HO_TEN_CHA"),
      sort_key: "HO_TEN_CHA",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN_CHA"] || "",
      width: "150px",
      dataIndex: "HO_TEN_CHA",
      i18Name: "giayDayCong.HO_TEN_CHA",
      key: "HO_TEN_CHA",
      show: false,
    }),
    Column({
      title: t("giayDayCong.HO_TEN_ME"),
      sort_key: "HO_TEN_ME",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN_ME"] || "",
      width: "150px",
      dataIndex: "HO_TEN_ME",
      i18Name: "giayDayCong.HO_TEN_ME",
      key: "HO_TEN_ME",
      show: false,
    }),
    Column({
      title: t("giayDayCong.THU_TRUONG_DV"),
      sort_key: "THU_TRUONG_DV",
      onClickSort: onClickSort,
      show: false,
      dataSort: dataSortColumn["THU_TRUONG_DV"] || "",
      width: "180px",
      dataIndex: "THU_TRUONG_DV",
      i18Name: "giayDayCong.THU_TRUONG_DV",
      key: "THU_TRUONG_DV",
    }),
    Column({
      title: t("giayDayCong.TEKT"),
      sort_key: "TEKT",
      onClickSort: onClickSort,
      show: false,
      align: "center",
      dataSort: dataSortColumn["TEKT"] || "",
      width: "80px",
      dataIndex: "TEKT",
      i18Name: "giayDayCong.TEKT",
      key: "TEKT",
      render: (item) => <Checkbox checked={item == 1} />,
    }),

    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            {checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_CHI_TIET]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye className="ic-action" />
              </Tooltip>
            )}

            {list.trangThai == 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_HUY]) && (
                <Tooltip title={t("giayDayCong.action.huyGiamDinh")}>
                  <SVG.IcCloseCircle
                    className="ic-action"
                    onClick={onHuyChungNhan(item)}
                  />
                </Tooltip>
              )}

            {list.trangThai != 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_DAY_LE]) && (
                <Tooltip title={t("giayDayCong.action.guiGiamDinh")}>
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiChungTu(item)}
                  />
                </Tooltip>
              )}
            {list.trangThai != 40 && (
              <Tooltip title={t("common.xoaDuLieu")}>
                <SVG.IcDelete className="ic-action" onClick={onXoaHoSo(item)} />
              </Tooltip>
            )}
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => ({
    onClick: () => {
      if (checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_CHI_TIET])) {
        showFileEditor({
          phieu: { ma: "P044" },
          id: record.id,
        });
      }
    },
  });

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        tableName="table_GIAYDAYCONG_GiayNghiHuong"
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
    </Main>
  );
};

const mapStateToProps = (state) => {
  const {
    giayNghiHuong: { listData, totalElements, page, size, dataSortColumn },
  } = state;
  return {
    listData,
    totalElements,
    page,
    size,
    dataSortColumn,
  };
};

const mapDispatchToProps = ({
  giayNghiHuong: { onSearch, onSizeChange, onSortChange },
  phieuIn: { showFileEditor },
}) => ({
  onSearch,
  onSizeChange,
  onSortChange,
  showFileEditor,
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(DanhSachGiayNghiHuong);
