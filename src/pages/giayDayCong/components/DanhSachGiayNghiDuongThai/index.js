import React, { useEffect, useRef } from "react";
import { Tooltip, Checkbox, TableWrapper, Pagination } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore, useConfirm, useLoading } from "hooks";
import { ENUM, ROLES, TRANG_THAI_DAY_CONG } from "constants/index";
import { SVG } from "assets";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { checkRole } from "lib-utils/role-utils";
import { Main } from "./styled";

const { Column, Setting } = TableWrapper;

const DanhSachGiayNghiDuongThai = ({ selectedRowKeys, setSelectedRowKeys }) => {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);
  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);
  const { listData, totalElements, page, size, dataSortColumn, isLoading } =
    useStore(
      "nbGiayNghiDuongThai",
      {},
      {
        fields: "listData,totalElements,page,size,dataSortColumn,isLoading",
      }
    );

  const {
    nbGiayNghiDuongThai: {
      onSearch,
      onSizeChange,
      onSortChange,
      searchNbGiayNghiDuongThaiByParams,
      dayGiayNghiDuongThaiById,
      huyGiayNghiDuongThaiById,
      deleteGiayNghiDuongThaiById,
    },
    phieuIn: { showFileEditor },
  } = useDispatch();

  useEffect(() => {
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = getAllQueryString();

    if (queries.dsTrangThaiDayCong) {
      queries.dsTrangThaiDayCong = queries.dsTrangThaiDayCong
        .split(",")
        .map((item) => parseInt(item));
    } else {
      queries.dsTrangThaiDayCong = [TRANG_THAI_DAY_CONG.TAO_MOI];
    }

    if (queries.tuThoiGianVaoVien) {
      queries.tuThoiGianVaoVien =
        queries.tuThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.tuThoiGianVaoVien)).format(
              "YYYY-MM-DD 00:00:00"
            );
    }
    if (queries.denThoiGianVaoVien) {
      queries.denThoiGianVaoVien =
        queries.denThoiGianVaoVien === "-"
          ? undefined
          : moment(Number(queries.denThoiGianVaoVien)).format(
              "YYYY-MM-DD 23:59:59"
            );
    }

    searchNbGiayNghiDuongThaiByParams({
      ...queries,
    });
  }, []);

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const refreshList = () => {
    onSizeChange({ page, size });
  };

  const onGuiChungTu = (item) => async (e) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      showLoading();
      await dayGiayNghiDuongThaiById(item.id).then(() => {
        refreshList();
      });
    } catch (error) {
      console.error(error?.message || error);
    } finally {
      hideLoading();
    }
  };

  const onHuyChungNhan = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyChungNhan", {
          ten: item.HO_TEN || "",
        }),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        showLoading();
        await huyGiayNghiDuongThaiById(item.id)
          .then(() => {
            hideLoading();
            refreshList();
          })
          .catch(() => {
            hideLoading();
          });
      }
    );
  };

  const onXoaHoSo = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("common.thongBao"),
        content: `${t("common.banCoChacMuonXoa")}`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        showLoading();
        await deleteGiayNghiDuongThaiById(item.id)
          .then(() => {
            hideLoading();
            refreshList();
          })
          .catch(() => {
            hideLoading();
          });
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("giayDayCong.HO_TEN"),
      sort_key: "HO_TEN",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN"] || "",
      width: "250px",
      dataIndex: "HO_TEN",
      key: "HO_TEN",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.tenNb",
    }),
    Column({
      title: t("giayDayCong.SO_KCB"),
      sort_key: "SO_KCB",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["SO_KCB"] || "",
      width: "120px",
      dataIndex: "SO_KCB",
      key: "SO_KCB",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("giayDayCong.MA_THE"),
      width: "150px",
      dataIndex: "MA_THE",
      key: "MA_THE",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.MA_THE",
    }),
    Column({
      title: t("giayDayCong.trangThai"),
      sort_key: "trangThaiDayCong",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThaiDayCong"] || "",
      width: "170px",
      dataIndex: "trangThaiDayCong",
      key: "trangThaiDayCong",
      i18Name: "giayDayCong.trangThai",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.ngayChungTu"),
      sort_key: "ngayChungTu",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngayChungTu"] || "",
      width: "150px",
      dataIndex: "ngayChungTu",
      i18Name: "giayDayCong.ngayChungTu",
      key: "ngayChungTu",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.TEN_NGUOI_HANH_NGHE"),
      width: "200px",
      dataIndex: "TEN_NGUOI_HANH_NGHE",
      key: "TEN_NGUOI_HANH_NGHE",
      i18Name: "giayDayCong.TEN_NGUOI_HANH_NGHE",
      render: (item) => {
        return item;
      },
    }),
    Column({
      title: t("giayDayCong.thoiGianVaoVien"),
      sort_key: "thoiGianVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.thoiGianVaoVien",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),

    Column({
      title: t("giayDayCong.MA_BHXH"),
      sort_key: "MA_BHXH",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["MA_BHXH"] || "",
      width: "150px",
      dataIndex: "MA_BHXH",
      i18Name: "giayDayCong.MA_BHXH",
      key: "MA_BHXH",
      show: false,
    }),
    Column({
      title: t("giayDayCong.DON_VI"),
      sort_key: "DON_VI",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["DON_VI"] || "",
      width: "150px",
      dataIndex: "DON_VI",
      i18Name: "giayDayCong.DON_VI",
      key: "DON_VI",
      show: false,
    }),
    Column({
      title: t("giayDayCong.soNgayNghi"),
      sort_key: "soNgayNghi",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["soNgayNghi"] || "",
      width: "100px",
      dataIndex: "soNgayNghi",
      key: "soNgayNghi",
      i18Name: "giayDayCong.soNgayNghi",
      align: "center",
      show: false,
    }),
    Column({
      title: t("giayDayCong.nghiTuNgay"),
      sort_key: "nghiTuNgay",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["nghiTuNgay"] || "",
      width: "120px",
      dataIndex: "nghiTuNgay",
      key: "nghiTuNgay",
      i18Name: "giayDayCong.nghiTuNgay",
      render: (field) => (field ? moment(field).format("DD/MM/YYYY") : ""),
      show: false,
    }),
    Column({
      title: t("giayDayCong.nghiDenNgay"),
      sort_key: "nghiDenNgay",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["nghiDenNgay"] || "",
      width: "120px",
      dataIndex: "nghiDenNgay",
      key: "nghiDenNgay",
      i18Name: "giayDayCong.nghiDenNgay",
      render: (field) => (field ? moment(field).format("DD/MM/YYYY") : ""),
      show: false,
    }),
    Column({
      title: t("giayDayCong.HO_TEN_CHA"),
      sort_key: "HO_TEN_CHA",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN_CHA"] || "",
      width: "150px",
      dataIndex: "HO_TEN_CHA",
      i18Name: "giayDayCong.HO_TEN_CHA",
      key: "HO_TEN_CHA",
      show: false,
    }),
    Column({
      title: t("giayDayCong.HO_TEN_ME"),
      sort_key: "HO_TEN_ME",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN_ME"] || "",
      width: "150px",
      dataIndex: "HO_TEN_ME",
      i18Name: "giayDayCong.HO_TEN_ME",
      key: "HO_TEN_ME",
      show: false,
    }),
    Column({
      title: t("giayDayCong.THU_TRUONG_DV"),
      sort_key: "THU_TRUONG_DV",
      onClickSort: onClickSort,
      show: false,
      dataSort: dataSortColumn["THU_TRUONG_DV"] || "",
      width: "180px",
      dataIndex: "THU_TRUONG_DV",
      i18Name: "giayDayCong.THU_TRUONG_DV",
      key: "THU_TRUONG_DV",
    }),
    Column({
      title: t("giayDayCong.TEKT"),
      sort_key: "TEKT",
      onClickSort: onClickSort,
      show: false,
      align: "center",
      dataSort: dataSortColumn["TEKT"] || "",
      width: "80px",
      dataIndex: "TEKT",
      i18Name: "giayDayCong.TEKT",
      key: "TEKT",
      render: (item) => <Checkbox checked={item == 1} />,
    }),

    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            {checkRole([
              ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_CHI_TIET,
            ]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye onClick={onViewDetail(item)} className="ic-action" />
              </Tooltip>
            )}

            {list.trangThaiDayCong == 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_HUY]) && (
                <Tooltip title={t("giayDayCong.action.huyGiamDinh")}>
                  <SVG.IcCloseCircle
                    className="ic-action"
                    onClick={onHuyChungNhan(item)}
                  />
                </Tooltip>
              )}

            {list.trangThaiDayCong != 40 &&
              checkRole([
                ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_DAY_LE,
              ]) && (
                <Tooltip title={t("giayDayCong.action.guiGiamDinh")}>
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiChungTu(item)}
                  />
                </Tooltip>
              )}
            {list.trangThaiDayCong != 40 && (
              <Tooltip title={t("common.xoaDuLieu")}>
                <SVG.IcDelete className="ic-action" onClick={onXoaHoSo(item)} />
              </Tooltip>
            )}
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onViewDetail(record)(event);
        }
      },
    };
  };

  const onViewDetail = (data) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!checkRole([ROLES["GIAY_DAY_CONG"].GIAY_NGHI_DUONG_THAI_CHI_TIET]))
      return;
    const { id } = data;
    showFileEditor({
      phieu: { ma: "P218" },
      nbDotDieuTriId: id,
    });
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        columns={columns}
        loading={!!isLoading}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        tableName="table_GIAYDAYCONG_GiayNghiDuongThai"
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
    </Main>
  );
};

export default DanhSachGiayNghiDuongThai;
