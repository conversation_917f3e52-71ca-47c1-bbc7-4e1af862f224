import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import moment from "moment";
import {
  Tooltip,
  TableWrapper,
  Pagination,
  ModalSignPrint,
  Checkbox,
  HeaderSearch,
} from "components";
import {
  useConfirm,
  useEnum,
  useLazyKVMap,
  useLoading,
  useSearchParams,
} from "hooks";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import printProvider from "data-access/print-provider";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import nbGiayChungSinhProvider from "data-access/nb-giay-chung-sinh-provider";
import { cleanEmptyData, cleanObjectUpdate, combineSort } from "utils";
import { ENUM, ROLES, TRANG_THAI_DAY_CONG } from "constants/index";
import { listTrangThaiKy } from "../TimKiemGiayChungSinh";
import { Main } from "./styled";

const { Column, Setting } = TableWrapper;

const DanhSachGiayChungSinh = (props, ref) => {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);
  const refModalSignPrint = useRef(null);

  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);

  const [getTrangThaiKy] = useLazyKVMap(listTrangThaiKy);

  const [totalElements, setTotalElements] = useState(0);
  const { searchParams, setSearchParams } = useSearchParams({
    defaultValue: {
      dsTrangThai: [TRANG_THAI_DAY_CONG.TAO_MOI],
    },
  });

  const { sort: dataSortColumn = {}, page = 0, size = 50 } = searchParams;

  const params = cleanEmptyData({
    page,
    size,
    hoTenCon: searchParams.hoTenCon,
    maHoSo: searchParams.maHoSo,
    tuThoiGianVaoVien: searchParams.tuThoiGianVaoVien,
    denThoiGianVaoVien: searchParams.denThoiGianVaoVien,
    dsTrangThai: searchParams.dsTrangThai,
    dsTrangThaiNb: searchParams.dsTrangThaiNb,
    dsTrangThaiKy:
      searchParams.dsTrangThaiKy?.length === 3
        ? null
        : searchParams.dsTrangThaiKy?.flatMap(
            (i) => getTrangThaiKy(i)?.referIds || []
          ),
    sort: combineSort(dataSortColumn),
  });

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["queryGiayChungSinh", params],
    queryFn: () => nbGiayChungSinhProvider.queryAll(params),
    onSuccess: (s) => {
      setTotalElements(s?.totalElements || 0);
    },
  });

  const {
    nbGiayChungSinh: {
      dayGiayChungSinhById,
      huyGiayChungSinh,
      updateData,
      kyHangLoat,
    },
    phieuIn: { getPhieuIn, showFileEditor, getFilePhieuIn },
  } = useDispatch();
  const onKyHangLoat = () => {
    showConfirm(
      {
        title: t("giayDayCong.xacNhanKy"),
        content: t("giayDayCong.xacNhanKyHangLoatCacPhieu"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        const payload = state.selectedRowKeys.map((item) => ({
          id: item?.split("-")[0],
          conThu: item?.split("-")[1],
        }));

        kyHangLoat(payload).then((s) => {
          refetch();
          setState({
            isCheckedAll: false,
            selectedRowKeys: [],
          });

          updateData({ selectedRowKeys: [] });
          if (s?.messageError) {
            showConfirm({
              title: t("common.thongBao"),
              content: s?.messageError,
              cancelText: t("common.huy"),
              typeModal: "error",
            });
          }
        });
      }
    );
  };

  useImperativeHandle(ref, () => ({
    onKyHangLoat,
  }));

  const onClickSort = (key, value) => {
    const newSort = cleanObjectUpdate(dataSortColumn, key, value);
    setSearchParams({ sort: newSort, page: 0 });
  };

  const onGuiChungTu = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    dayGiayChungSinhById({ ...item, nbDotDieuTriId: item.id }).then(() => {
      refetch();
    });
  };
  const onPrint =
    ({ id, nbDotDieuTriId }) =>
    () => {
      getPhieuIn({
        nbDotDieuTriId: id,
        maManHinh: "027",
        maViTri: "02701",
      }).then(async (res) => {
        const phieu = res?.filter((o) => o.ma === "P217")?.[0];
        if (!phieu) return;
        if (phieu?.type == "editor") {
          let mhParams = {};
          if (checkIsPhieuKySo(phieu)) {
            mhParams = {
              nbDotDieuTriId: id,
              maManHinh: "027",
              maViTri: "02701",
              kySo: true,
              maPhieuKy: item.ma,
            };
          }
          showFileEditor({
            phieu,
            nbDotDieuTriId: id,
            mhParams,
          });
        } else {
          try {
            showLoading();
            const response = await getFilePhieuIn({
              listPhieus: [phieu],
              showError: true,
              nbDotDieuTriId: id,
            });

            if (
              phieu.kySo &&
              phieu.ma === "P217" &&
              phieu.dsSoPhieu.length === 1 &&
              phieu.dsSoPhieu[0]?.soPhieu == id &&
              phieu.dsSoPhieu[0]?.trangThai !== 10
            ) {
              refModalSignPrint.current &&
                refModalSignPrint.current.showToSign({
                  phieuKy: phieu,
                  payload: {
                    nbDotDieuTriId: id,
                    maManHinh: "027",
                    maViTri: "02701",
                    id,
                  },
                });
            } else if ((response.dsPhieu || []).every((x) => x?.loaiIn == 20)) {
              openInNewTab(response.finalFile);
            } else {
              printProvider.printPdf(response.dsPhieu);
            }
          } catch (error) {
            console.log("error", error);
          } finally {
            hideLoading();
          }
        }
      });
    };

  const renderHtml = (item) => {
    if (!item) return "";
    const doc = new DOMParser().parseFromString(item, "text/html");
    const text = doc.body.textContent;

    return text;
  };

  const onHuyGiamDinh = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyGiamDinh", {
          ten: item.TEN_CON || "",
        }),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        try {
          showLoading();
          await huyGiayChungSinh(item);

          refetch();
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "80px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (_, __, index) => page * size + index + 1,
    }),
    Column({
      title: t("giayDayCong.tenCon"),
      sort_key: "TEN_CON",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["TEN_CON"] || "",
      width: "200px",
      dataIndex: "TEN_CON",
      key: "TEN_CON",
      i18Name: "giayDayCong.tenCon",
    }),
    Column({
      title: t("giayDayCong.hoTenMeNND"),
      sort_key: "HOTEN_NND",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HOTEN_NND"] || "",
      width: "200px",
      dataIndex: "HOTEN_NND",
      key: "HOTEN_NND",
      i18Name: "giayDayCong.hoTenMeNND",
    }),
    Column({
      title: t("common.maBenhAn"),
      sort_key: "maBenhAn",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maBenhAn"] || "",
      width: "100px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "common.maBenhAn",
    }),
    Column({
      title: t("giayDayCong.maHoSo"),
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("giayDayCong.maTheBhytNND"),
      sort_key: "MA_THE_NND",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["MA_THE_NND"] || "",
      width: "150px",
      dataIndex: "MA_THE_NND",
      key: "MA_THE_NND",
      i18Name: "giayDayCong.maTheBhytNND",
      render: renderHtml,
    }),
    Column({
      title: t("giayDayCong.ngaySinhNND"),
      width: "120px",
      dataIndex: "NGAYSINH_NND",
      key: "NGAYSINH_NND",
      i18Name: "giayDayCong.ngaySinhNND",
      render: (field, item, index) =>
        field ? moment(field).format("DD / MM / YYYY") : "",
    }),
    Column({
      title: t("giayDayCong.trangThai"),
      sort_key: "trangThai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThai"] || "",
      width: "150px",
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "giayDayCong.trangThai",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.ngayChungTu"),
      sort_key: "NGAY_CT",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["NGAY_CT"] || "",
      width: "150px",
      dataIndex: "NGAY_CT",
      i18Name: "giayDayCong.ngayChungTu",
      key: "NGAY_CT",
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.tenCha"),
      sort_key: "HO_TEN_CHA",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["HO_TEN_CHA"] || "",
      width: "150px",
      dataIndex: "HO_TEN_CHA",
      key: "HO_TEN_CHA",
      i18Name: "giayDayCong.tenCha",
      show: false,
    }),
    Column({
      title: t("giayDayCong.maSoBaoHiemXaHoiNND"),
      sort_key: "MA_BHXH_NND",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["MA_BHXH_NND"] || "",
      width: "150px",
      dataIndex: "MA_BHXH_NND",
      key: "MA_BHXH_NND",
      i18Name: "giayDayCong.maSoBaoHiemXaHoiNND",
      show: false,
      render: renderHtml,
    }),
    Column({
      title: t("giayDayCong.thoiGianVaoVien"),
      sort_key: "thoiGianVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianVaoVien"] || "",
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.thoiGianVaoVien",
      show: false,
      render: (field, item, index) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.ngaySinhCon"),
      sort_key: "ngaySinhCon",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngaySinhCon"] || "",
      width: "150px",
      dataIndex: "ngaySinhCon",
      key: "ngaySinhCon",
      i18Name: "giayDayCong.ngaySinhCon",
      show: false,
      render: (field, item, index) =>
        field ? moment(field).format("DD / MM / YYYY") : "",
    }),
    Column({
      title: t("giayDayCong.maChungSinh"),
      sort_key: "maChungSinh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maChungSinh"] || "",
      width: "120px",
      dataIndex: "MA_GCS",
      key: "MA_GCS",
      i18Name: "giayDayCong.maChungSinh",
      show: true,
    }),
    Column({
      title: t("giayDayCong.soChungSinh"),
      sort_key: "soChungSinh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["soChungSinh"] || "",
      width: "120px",
      dataIndex: "SO",
      key: "SO",
      i18Name: "giayDayCong.soChungSinh",
      show: true,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="ic-action">
            {checkRole([ROLES["GIAY_DAY_CONG"].GIAY_CHUNG_SINH_CHI_TIET]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye onClick={onViewDetail(item)} className="ic-action" />
              </Tooltip>
            )}
            {list.trangThai != 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].GIAY_CHUNG_SINH_DAY_LE]) && (
                <Tooltip title={t("giayDayCong.action.guiGiamDinh")}>
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiChungTu(item)}
                  />
                </Tooltip>
              )}
            {list.trangThai == 40 && (
              <Tooltip
                title={t("giayDayCong.action.huyGiamDinh")}
                placement="topLeft"
              >
                <SVG.IcCloseCircle
                  className="ic-action"
                  onClick={onHuyGiamDinh(item)}
                />
              </Tooltip>
            )}
            <Tooltip
              title={t("giayDayCong.inGiayChungSinh")}
              placement="topLeft"
            >
              <SVG.IcPrint className="icon" onClick={onPrint(item)} />
            </Tooltip>
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    setSearchParams({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setSearchParams({ size, page: 0 });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onViewDetail(record)(event);
        }
      },
    };
  };

  const onViewDetail = (data) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    if (!checkRole([ROLES["GIAY_DAY_CONG"].GIAY_CHUNG_SINH_CHI_TIET])) return;
    const { id, conThu, baoCaoId } = data;

    showFileEditor({
      phieu: { ma: "P217", baoCaoId },
      ma: "P217",
      nbDotDieuTriId: id,
      conThu,
      mhParams: { nbDotDieuTriId: id },
    });
  };

  const oncheckAll = (e) => {
    const _selectedRowKeys = e.target?.checked
      ? (data?.data || []).map((item) => {
          return `${item.id}-${item.conThu}`;
        })
      : [];

    setState({
      selectedRowKeys: _selectedRowKeys,
      isCheckedAll: e.target?.checked,
    });

    updateData({ selectedRowKeys: _selectedRowKeys });
  };

  const onSelectChange = (selectedRowKeys) => {
    let updatedSelectedKeys = selectedRowKeys;
    updatedSelectedKeys = [...new Set(updatedSelectedKeys)];
    setState({
      isCheckedAll: (data?.data || []).length === updatedSelectedKeys.length,
      selectedRowKeys: updatedSelectedKeys,
    });

    updateData({ selectedRowKeys: updatedSelectedKeys });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            style={{ color: "#03317c" }}
            onChange={oncheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        rowSelection={rowSelection}
        columns={columns}
        dataSource={data?.data || []}
        onRow={onRow}
        rowKey={(record) => `${record.id}-${record.conThu}`}
        tableName="table_GIAYDAYCONG_GiayChungSinh"
        ref={refSettings}
        loading={isLoading}
      />
      {!!totalElements && (
        <Pagination
          listData={data?.data || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
      <ModalSignPrint ref={refModalSignPrint} />
    </Main>
  );
};

export default forwardRef(DanhSachGiayChungSinh);
