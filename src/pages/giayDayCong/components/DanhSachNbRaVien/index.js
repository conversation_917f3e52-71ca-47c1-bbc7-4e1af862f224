import React, { useEffect, useRef, useState } from "react";
import { Tooltip, Checkbox, TableWrapper, Pagination } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useConfirm, useEnum, useLoading, useStore } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { transformQueryString } from "utils";
import { ENUM, ROLES, TRANG_THAI_DAY_CONG } from "constants/index";
import { SVG } from "assets";
import { Main } from "./styled";

const { Column, Setting } = TableWrapper;

const DanhSachNbRaVien = (props) => {
  const { onShowTTRaVien, selectedRowKeys, setSelectedRowKeys } = props;
  const { t } = useTranslation();

  const { showConfirm } = useConfirm();
  const refSettings = useRef(null);
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listDinhChiThaiNghen] = useEnum(ENUM.DINH_CHI_THAI_NGHEN);
  const [listTrangThaiDayCong] = useEnum(ENUM.TRANG_THAI_DAY_CONG);
  const { showLoading, hideLoading } = useLoading();

  const {
    nbRaVien: {
      dayPhieuRaVienById,
      huyPhieuRaVienById,
      searchNbRaVienByParams,
      onSearch,
      onSizeChange,
      onSortChange,
    },
  } = useDispatch();

  const { listData, totalElements, page, size } = useStore(
    "nbRaVien",
    {},
    {
      fields: "listData, totalElements, page, size",
    }
  );

  useEffect(() => {
    setSelectedRowKeys([]);
  }, [listData]);

  useEffect(() => {
    const queries = transformQueryString({
      tuThoiGianRaVien: {
        defaultValue: moment()
          .subtract(6, "days")
          .format("YYYY-MM-DD 00:00:00"),
        format: (date) => date.format("YYYY-MM-DD 00:00:00"),
        type: "dateOptions",
      },
      denThoiGianRaVien: {
        defaultValue: moment().format("YYYY-MM-DD 23:59:59"),
        format: (date) => date.format("YYYY-MM-DD 23:59:59"),
        type: "dateOptions",
      },
      tuThoiGianNgaySinh: {
        format: (date) => date.format("YYYY-MM-DD 00:00:00"),
        type: "dateOptions",
      },
      denThoiGianNgaySinh: {
        format: (date) => date.format("YYYY-MM-DD 23:59:59"),
        type: "dateOptions",
      },
      dsTrangThaiDayCong: {
        format: (value) => {
          return value.split(",").map(Number);
        },
        defaultValue: [
          TRANG_THAI_DAY_CONG.TAO_MOI,
          TRANG_THAI_DAY_CONG.TAO_LAI,
          TRANG_THAI_DAY_CONG.GUI_THAT_BAI,
          TRANG_THAI_DAY_CONG.GUI_THANH_CONG,
        ],
      },
    });

    searchNbRaVienByParams(queries);
  }, []);

  const onClickSort = (key, value) => {
    onSortChange({ [key]: value });
  };

  const refreshList = () => {
    onSizeChange({ page, size });
  };

  const onGuiChungTu = (item) => () => {
    dayPhieuRaVienById(item.id).then(() => {
      refreshList();
    });
  };

  const onHuyChungNhan = (item) => () => {
    showConfirm(
      {
        title: t("giayDayCong.xacNhanHuy"),
        content: t("giayDayCong.banChacChanMuonHuyChungNhan", {
          ten: item.HO_TEN || "",
        }),
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-confirm",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        showLoading();

        huyPhieuRaVienById(item.id)
          .then(() => {
            hideLoading();
            refreshList();
          })
          .catch(() => {
            hideLoading();
          });
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("giayDayCong.HO_TEN"),
      width: "250px",
      dataIndex: "HO_TEN",
      key: "HO_TEN",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.HO_TEN",
    }),
    Column({
      title: t("giayDayCong.maBenhAn"),
      width: "120px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      render: (item) => {
        return item;
      },
      i18Name: "giayDayCong.maBenhAn",
    }),
    Column({
      title: t("giayDayCong.maHoSo"),
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "giayDayCong.maHoSo",
    }),
    Column({
      title: t("giayDayCong.MA_THE"),
      width: "160px",
      dataIndex: "MA_THE",
      key: "MA_THE",
      i18Name: "giayDayCong.MA_THE",
    }),
    Column({
      title: t("giayDayCong.MA_KHOA"),
      width: "120px",
      dataIndex: "MA_KHOA",
      key: "MA_KHOA",
      i18Name: "giayDayCong.MA_KHOA",
    }),
    Column({
      title: t("giayDayCong.trangThai"),
      width: "170px",
      dataIndex: "trangThaiDayCong",
      key: "trangThaiDayCong",
      i18Name: "giayDayCong.trangThai",
      render: (item) =>
        (listTrangThaiDayCong || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("giayDayCong.nbRaVien.ngayChungTu"),
      width: "150px",
      dataIndex: "thoiGianChungTu",
      i18Name: "giayDayCong.nbRaVien.ngayChungTu",
      key: "thoiGianChungTu",
      render: (field) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.thongTinTraVe"),
      width: "180px",
      dataIndex: "ketQua",
      key: "ketQua",
      i18Name: "giayDayCong.thongTinTraVe",
    }),
    Column({
      title: t("giayDayCong.thoiGianVaoVien"),
      width: "150px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
      i18Name: "giayDayCong.thoiGianVaoVien",
      render: (field) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.GIOI_TINH"),
      width: "100px",
      dataIndex: "GIOI_TINH",
      key: "GIOI_TINH",
      show: false,
      i18Name: "giayDayCong.GIOI_TINH",
      align: "center",
      render: (item) => listgioiTinh.find((gt) => gt.id === item)?.ten,
    }),
    Column({
      title: t("giayDayCong.ngaySinh"),
      width: "120px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      show: false,
      i18Name: "giayDayCong.ngaySinh",
      render: (field, item) =>
        field
          ? moment(field).format(item?.chiNamSinh ? "YYYY" : "DD / MM / YYYY")
          : "",
    }),
    Column({
      title: t("giayDayCong.diaChi"),
      width: "200px",
      dataIndex: "diaChi",
      key: "diaChi",
      show: false,
      i18Name: "giayDayCong.diaChi",
    }),
    Column({
      title: t("giayDayCong.MA_BHXH"),
      width: "140px",
      dataIndex: "MA_BHXH",
      key: "MA_BHXH",
      show: false,
      i18Name: "giayDayCong.MA_BHXH",
    }),
    Column({
      title: t("giayDayCong.MA_DANTOC"),
      width: "120px",
      dataIndex: "MA_DANTOC",
      key: "MA_DANTOC",
      show: false,
      i18Name: "giayDayCong.MA_DANTOC",
    }),
    Column({
      title: t("giayDayCong.DINH_CHI_THAI_NGHEN"),
      width: "200px",
      dataIndex: "DINH_CHI_THAI_NGHEN",
      key: "DINH_CHI_THAI_NGHEN",
      show: false,
      i18Name: "giayDayCong.DINH_CHI_THAI_NGHEN",
      render: (item) => listDinhChiThaiNghen.find((x) => x.id === item)?.ten,
    }),
    Column({
      title: t("giayDayCong.TUOI_THAI"),
      width: "100px",
      dataIndex: "TUOI_THAI",
      key: "TUOI_THAI",
      show: false,
      i18Name: "giayDayCong.TUOI_THAI",
    }),
    Column({
      title: t("giayDayCong.TEKT"),
      sort_key: "TEKT",
      onClickSort: onClickSort,
      show: false,
      align: "center",
      width: "80px",
      dataIndex: "TEKT",
      i18Name: "giayDayCong.TEKT",
      key: "TEKT",
      render: (item) => <Checkbox checked={item == 1} />,
    }),
    Column({
      title: t("common.thoiGianRaVien"),
      width: "150px",
      dataIndex: "thoiGianRaVien",
      key: "thoiGianRaVien",
      i18Name: "common.thoiGianRaVien",
      render: (field) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.tuThoiGianNghiNgoaiTru"),
      width: "200px",
      dataIndex: "tuThoiGianNghiNgoaiTru",
      key: "tuThoiGianNghiNgoaiTru",
      show: false,
      i18Name: "giayDayCong.tuThoiGianNghiNgoaiTru",
      render: (field) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.denThoiGianNghiNgoaiTru"),
      width: "200px",
      dataIndex: "denThoiGianNghiNgoaiTru",
      key: "denThoiGianNghiNgoaiTru",
      show: false,
      i18Name: "giayDayCong.denThoiGianNghiNgoaiTru",
      render: (field) =>
        field ? moment(field).format("DD/MM/YYYY HH:mm") : "",
    }),
    Column({
      title: t("giayDayCong.dsCdChinh"),
      width: "200px",
      dataIndex: "dsCdChinh",
      key: "dsCdChinh",
      show: false,
      i18Name: "giayDayCong.dsCdChinh",
      render: (item) => (item || []).map((x) => x.ten).join(", "),
    }),
    Column({
      title: t("giayDayCong.PP_DIEUTRI"),
      width: "160px",
      dataIndex: "PP_DIEUTRI",
      key: "PP_DIEUTRI",
      show: false,
      i18Name: "giayDayCong.PP_DIEUTRI",
    }),
    Column({
      title: t("giayDayCong.GHI_CHU"),
      width: "200px",
      dataIndex: "GHI_CHU",
      key: "GHI_CHU",
      show: false,
      i18Name: "giayDayCong.GHI_CHU",
    }),
    Column({
      title: t("giayDayCong.tenKhoaNb"),
      width: "220px",
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
      show: false,
      i18Name: "giayDayCong.tenKhoaNb",
    }),
    Column({
      title: t("giayDayCong.TEN_TRUONGKHOA"),
      width: "200px",
      dataIndex: "TEN_TRUONGKHOA",
      key: "TEN_TRUONGKHOA",
      show: false,
      i18Name: "giayDayCong.TEN_TRUONGKHOA",
    }),
    Column({
      title: t("giayDayCong.MA_CCHN_TRUONGKHOA"),
      width: "220px",
      dataIndex: "MA_CCHN_TRUONGKHOA",
      key: "MA_CCHN_TRUONGKHOA",
      show: false,
      i18Name: "giayDayCong.MA_CCHN_TRUONGKHOA",
    }),

    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item) => {
        return (
          <div className="ic-action">
            {checkRole([ROLES["GIAY_DAY_CONG"].NB_RA_VIEN_CHI_TIET]) && (
              <Tooltip title={t("giayDayCong.action.xemChiTiet")}>
                <SVG.IcEye
                  className="ic-action"
                  onClick={onShowTTRaVien(list)}
                />
              </Tooltip>
            )}

            {list.trangThaiDayCong == 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].NB_RA_VIEN_HUY]) && (
                <Tooltip title={t("giayDayCong.action.huyGiamDinh")}>
                  <SVG.IcCloseCircle
                    className="ic-action"
                    onClick={onHuyChungNhan(item)}
                  />
                </Tooltip>
              )}

            {list.trangThaiDayCong != 40 &&
              checkRole([ROLES["GIAY_DAY_CONG"].NB_RA_VIEN_DAY_LE]) && (
                <Tooltip title={t("giayDayCong.action.guiGiamDinh")}>
                  <SVG.IcGuiCt
                    className="ic-action"
                    onClick={onGuiChungTu(item)}
                  />
                </Tooltip>
              )}
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: (event) => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onShowTTRaVien(record)(event);
        }
      },
    };
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listData || []}
        onRow={onRow}
        rowKey={(record) => record.id}
        ref={refSettings}
        tableName="table_GIAYDAYCONG_DsNbRaVien"
        rowSelection={{
          onChange: (selectedRowKeys) => {
            setSelectedRowKeys(selectedRowKeys);
          },
          selectedRowKeys,
        }}
      />
      {!!totalElements && (
        <Pagination
          listData={listData || []}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          pageSizeOptions={[10, 20, 50, 100, 200]}
        />
      )}
    </Main>
  );
};

export default DanhSachNbRaVien;
