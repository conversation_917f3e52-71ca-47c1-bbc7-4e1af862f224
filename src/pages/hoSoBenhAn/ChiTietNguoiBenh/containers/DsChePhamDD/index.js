import React, { useEffect, useImperativeHandle, useRef } from "react";
import {
  Checkbox,
  TableWrapper,
  Select,
  InputTimeout,
  DatePicker,
  Pagination,
} from "components";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useStore } from "hooks";
import moment from "moment";
import { useSelector } from "react-redux";
import { HIEU_LUC } from "constants";
import { Empty } from "antd";
import { ENUM } from "constants";

const DA_THANH_TOAN = 50;

const { Column } = TableWrapper;

const DsChePhamDD = React.forwardRef(
  (
    {
      nbDotDieuTriId,
      tableName,
      dsChiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const refSettings = useRef(null);

    const { listDsChePhamDD, dataSortColumn, totalElements, page, size } =
      useSelector((state) => state.dsChePhamDD);
    const {
      dsChePhamDD: {
        onSearch,
        onSortChange,
        onSizeChange,
        onChangeInputSearch,
      },
    } = useDispatch();

    const [listAllNhanVien] = useListAll(
      "nhanVien",
      { page: "", size: "", active: true },
      true
    );
    const [listAllKhoa] = useListAll(
      "khoa",
      { page: "", size: "", active: true },
      true
    );
    const [listAllKho] = useListAll(
      "kho",
      { page: "", size: "", active: true },
      true
    );
    const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);

    const onSettings = () => {
      refSettings && refSettings.current.settings();
    };
    useEffect(() => {
      if (nbDotDieuTriId) {
        onSearch({
          dataSearch: {
            nbDotDieuTriId,
            dsChiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            dsTrangThaiHoan: dsTrangThaiHoan ?? [0, 10, 20],
          },
          page: 0,
          size: 10,
          dataSortColumn: { thoiGianThucHien: 1 },
        });
      }
    }, [
      nbDotDieuTriId,
      dsChiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    ]);

    useImperativeHandle(ref, () => ({
      onSettings,
    }));
    const onSearchChange = (key, value) => {
      onChangeInputSearch({ [key]: value });
    };
    const onSearchDate = (key, value) => {
      const newSearch = {
        [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
        [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
      };
      onChangeInputSearch(newSearch);
    };
    const onClickSort = (key, value) => {
      onSortChange({ [key]: value });
    };
    const onChangePage = (page) => {
      onSearch({ page: page - 1 });
    };
    const onHandleSizeChange = (size) => {
      onSizeChange({ size: size });
    };
    const sharedOnCell = (row, index) => {
      if (row.isGroup) {
        return { colSpan: 0 };
      }
    };

    const columns = [
      Column({
        title: t("common.stt"),
        width: 80,
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenChePhamDD"),
        width: "200px",
        dataIndex: "tenDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.tenChePhamDD",
        key: "tenDichVu",
        sort_key: "tenDichVu",
        onCell: sharedOnCell,
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        render: (item, list) => item,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapTenChePhamDD")}
            onChange={(e) => {
              onSearchChange("tenDichVu", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.toDieuTri.soLuongKe"),
        width: 120,
        dataIndex: "soLuongYeuCau",
        key: "soLuongYeuCau",
        sort_key: "soLuongYeuCau",
        align: "center",
        dataSort: dataSortColumn["soLuongYeuCau"] || "",
        i18Name: "quanLyNoiTru.toDieuTri.soLuongKe",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => item,
      }),
      Column({
        title: t("quanLyNoiTru.toDieuTri.soLuongDung"),
        width: "120px",
        dataIndex: "soLuong",
        key: "soLuong",
        align: "center",
        sort_key: "soLuong",
        dataSort: dataSortColumn["soLuong"] || "",
        i18Name: "quanLyNoiTru.toDieuTri.soLuongDung",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => item,
      }),
      Column({
        title: t("quanLyNoiTru.toDieuTri.cachDung"),
        width: "120px",
        dataIndex: "cachDung",
        key: "cachDung",
        sort_key: "cachDung",
        dataSort: dataSortColumn["cachDung"] || "",
        i18Name: "quanLyNoiTru.toDieuTri.cachDung",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => item,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.toDieuTri.nhapCachDung")}
            onChange={(e) => {
              onSearchChange("cachDung", e);
            }}
          />
        ),
      }),
      Column({
        title: t("common.thanhTien"),
        width: 120,
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "center",
        sort_key: "thanhTien",
        dataSort: dataSortColumn["thanhTien"] || "",
        onClickSort: onClickSort,
        i18Name: "common.thanhTien",
        onCell: sharedOnCell,
        render: (value) => (value || 0).formatPrice(),
      }),
      Column({
        title: t("khamBenh.donThuoc.kho"),
        width: 120,
        dataIndex: "tenKho",
        key: "tenKho",
        sort_key: "tenKho",
        dataSort: dataSortColumn["tenKho"] || "",
        i18Name: "khamBenh.donThuoc.kho",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => item,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKho}
            mode="multiple"
            placeholder={t("baoCao.chonKho")}
            onChange={(e) => {
              onSearchChange("dsKhoId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("khamBenh.donThuoc.daDuyetPhat"),
        width: 120,
        dataIndex: "phat",
        key: "phat",
        sort_key: "phat",
        align: "center",
        dataSort: dataSortColumn["phat"] || "",
        i18Name: "khamBenh.donThuoc.daDuyetPhat",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearchChange("phat", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.toDieuTri.trangThaiThanhToan"),
        width: "120px",
        dataIndex: "thanhToan",
        key: "thanhToan",
        sort_key: "thanhToan",
        align: "center",
        i18Name: "quanLyNoiTru.toDieuTri.trangThaiThanhToan",
        dataSort: dataSortColumn["thanhToan"] || "",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => <Checkbox checked={item === DA_THANH_TOAN} />,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiChiDinh"),
        width: "160px",
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        sort_key: "tenBacSiChiDinh",
        dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiChiDinh",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => item,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("quanLyNoiTru.dvNoiTru.chonNguoiChiDinh")}
            onChange={(e) => {
              onSearchChange("dsBacSiChiDinhId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenKhoaChiDinh"),
        width: 160,
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        sort_key: "tenKhoaChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => item,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKhoa}
            placeholder={t("baoCao.chonKhoaChiDinh")}
            onChange={(e) => {
              onSearchChange("khoaChiDinhId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh"),
        sort_key: "thoiGianChiDinh",
        width: "160px",
        dataIndex: "thoiGianChiDinh",
        key: "thoiGianChiDinh",
        sort_key: "thoiGianChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianChiDinh",
        dataSort: dataSortColumn["thoiGianChiDinh"] || "",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("hsba.thoiGianThucHien"),
        sort_key: "thoiGianThucHien",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        align: "center",
        i18Name: "hsba.thoiGianThucHien",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianPhat"),
        sort_key: "thoiGianDuyet",
        width: "160px",
        dataIndex: "thoiGianDuyet",
        key: "thoiGianDuyet",
        sort_key: "thoiGianDuyet",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhat",
        dataSort: dataSortColumn["thoiGianDuyet"] || "",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianDuyet", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.cpdd.loaiChiDinh"),
        width: "120px",
        dataIndex: "loaiChiDinh",
        key: "loaiChiDinh",
        align: "center",
        sort_key: "loaiChiDinh",
        dataSort: dataSortColumn["loaiChiDinh"] || "",
        i18Name: "quanLyNoiTru.cpdd.loaiChiDinh",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) =>
          listLoaiChiDinh?.find((x) => x.id === item)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listLoaiChiDinh}
            placeholder={t("quanLyNoiTru.cpdd.loaiChiDinh")}
            onChange={(e) => {
              onSearchChange("loaiChiDinh", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tuTra"),
        width: "120px",
        dataIndex: "tuTra",
        key: "tuTra",
        align: "center",
        sort_key: "tuTra",
        dataSort: dataSortColumn["tuTra"] || "",
        i18Name: "quanLyNoiTru.dvNoiTru.tuTra",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearchChange("tuTra", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.khongTinhTien"),
        width: "120px",
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        align: "center",
        sort_key: "khongTinhTien",
        dataSort: dataSortColumn["khongTinhTien"] || "",
        i18Name: "quanLyNoiTru.dvNoiTru.khongTinhTien",
        onClickSort: onClickSort,
        onCell: sharedOnCell,
        render: (item, list) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearchChange("khongTinhTien", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
    ];

    const renderEmptyTextTable = () => {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
          />
        </div>
      );
    };

    return (
      <Main>
        <TableWrapper
          columns={columns}
          ref={refSettings}
          scroll={{ x: 2900 }}
          dataSource={listDsChePhamDD}
          rowKey={(record) => record.id}
          tableName={tableName}
          locale={{
            emptyText: renderEmptyTextTable(),
          }}
        />
        {!!totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listDsChePhamDD}
            onShowSizeChange={onHandleSizeChange}
            style={{ flex: 1, justifyContent: "flex-end" }}
          />
        ) : null}
      </Main>
    );
  }
);

export default React.memo(DsChePhamDD);
