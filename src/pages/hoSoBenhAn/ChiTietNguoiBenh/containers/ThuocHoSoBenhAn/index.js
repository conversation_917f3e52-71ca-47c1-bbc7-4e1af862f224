import React, {
  forwardRef,
  useRef,
  useImperative<PERSON>andle,
  useEffect,
  useState,
} from "react";
import {
  TableWrapper,
  Checkbox,
  Pagination,
  InputTimeout,
  Select,
  DatePicker,
} from "components";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import moment from "moment";
import { useTranslation } from "react-i18next";
import {
  DOI_TUONG,
  ENUM,
  LOAI_THUOC,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  TRANG_THAI_THUOC,
} from "constants/index";
import { useEnum, useListAll, useConfirm, useLoading, useStore } from "hooks";
import { Empty, Tooltip } from "antd";
import { SVG } from "assets";
import SuaThongTinThuoc from "pages/khamBenh/DonThuoc/SuaThongTinThuoc";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";
import { gopThuoc<PERSON>hac<PERSON>o } from "utils/chi-dinh-thuoc-utils";

const { Column, Setting } = TableWrapper;

const ThuocHoSoBenhAn = forwardRef(
  (
    {
      tableName = "TABLE_HSBA_THUOC",
      thongTinBenhNhan,
      khoaLamViecId,
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
      configData,
      isShowAction,
    },
    ref
  ) => {
    const refSettings = useRef(null);
    const refSuaThongTinThuoc = useRef(null);
    const { showLoading, hideLoading } = useLoading();
    const { showConfirm } = useConfirm();

    const [listTrangThaiThuoc] = useEnum(ENUM.TRANG_THAI_THUOC);
    const [listloaiThuoc] = useEnum(ENUM.LOAI_THUOC);
    const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
    const [listAllNhanVien] = useListAll("nhanVien", {
      page: "",
      size: "",
      active: true,
    });
    const [listAllKhoa] = useListAll("khoa", {
      page: "",
      size: "",
      active: true,
    });
    const [listAllKho] = useListAll("kho", {
      page: "",
      size: "",
      active: true,
    });

    const [state, _setState] = useState({});
    const setState = (data) => {
      _setState((pre) => ({ ...pre, data }));
    };
    const { t } = useTranslation();
    const listAllPhieuThuChotDot = useStore(
      "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
      []
    );

    const {
      dataSortColumn,
      listDsThuoc: listThuocOrigin,
      totalElements,
      page,
      size,
    } = useSelector((state) => state.dsThuoc);

    const listDsThuoc = gopThuocKhacLo(listThuocOrigin || []);

    const {
      dsThuoc: { onSortChange, onSizeChange, onSearch, onChangeInputSearch },
      chiDinhDichVuKho: { onDeleteDichVu, onDeleteDichVuNhaThuoc, onDeleteAll },
    } = useDispatch();
    useImperativeHandle(ref, () => ({
      onSettings,
    }));

    const onSettings = () => {
      refSettings && refSettings.current.settings();
    };

    const onClickSort = (key, value) => {
      onSortChange({ [key]: value });
    };

    useEffect(() => {
      if (nbDotDieuTriId) {
        onSearch({
          dataSearch: {
            nbDotDieuTriId,
            chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            dsTrangThaiHoan: dsTrangThaiHoan ?? [0, 10, 20, 42],
          },
          page: 0,
          size: 10,
          dataSortColumn: { thoiGianThucHien: 1 },
        });
      }
    }, [
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    ]);
    const onSearchInput = (key) => (e) => {
      let value = "";
      if (e?.target) {
        if (e.target.hasOwnProperty("checked")) value = e.target.checked;
        else value = e.target.value;
      } else value = e;
      setState({ searchLocal: { ...state.searchLocal, [key]: value } });
      onChangeInputSearch({
        [key]: value,
        nbDotDieuTriId,
      });
    };

    const onSearchDate = (key, e) => {
      const value = e ? e.format("YYYY-MM-DD") : null;
      onChangeInputSearch({ [key]: value });
    };

    const isTrangThaiTuChoiDuyetDLS = (listDsThuoc || []).some(
      (item) =>
        item.trangThai == TRANG_THAI_THUOC.TU_CHOI_DUYET_DUOC_LAM_SANG.id
    );

    const onDelete = (record) => {
      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content:
            t("common.banCoChacMuonXoa") +
            (record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "") +
            "?",
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "error",
        },
        async () => {
          try {
            showLoading();

            if (record?.nhaThuoc) {
              await onDeleteDichVuNhaThuoc(record.id);
            } else {
              if (record?.dsThuocGopId) {
                //Xóa các thuốc gộp khác lô
                await onDeleteAll(record?.dsThuocGopId);
              } else {
                await onDeleteDichVu(record.id);
              }
            }

            onSearch({
              dataSearch: {
                nbDotDieuTriId,
                chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu,
                dsTrangThaiHoan,
              },
              page: 0,
              size: 10,
              dataSortColumn: { thoiGianThucHien: 1 },
            });
          } catch (error) {
            console.error(error);
          } finally {
            hideLoading();
          }
        }
      );
    };

    const onEdit = (record) => () => {
      refSuaThongTinThuoc.current &&
        refSuaThongTinThuoc.current.show(
          {
            data: record,
            chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
            nbDotDieuTriId: configData.nbDotDieuTriId,
            chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
            isTuTruc: record?.loai == LOAI_THUOC.TU_TRUC,
            khoBhyt: record?.loai == LOAI_THUOC.BHYT,
            thuocNhaThuoc: record.nhaThuoc,
          },
          () => {
            onSearch({
              dataSearch: {
                nbDotDieuTriId,
                chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu,
                dsTrangThaiHoan,
              },
              page: 0,
              size: 10,
              dataSortColumn: { thoiGianThucHien: 1 },
            });
          }
        );
    };
    const columns = [
      Column({
        title: t("common.stt"),
        width: "80px",
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
      Column({
        title: t("danhMuc.maThuoc"),
        sort_key: "maDichVu",
        dataSort: dataSortColumn["maDichVu"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "maDichVu",
        key: "maDichVu",
        i18Name: "danhMuc.maThuoc",
        className: "maDichVu",
        renderSearch: (
          <InputTimeout
            placeholder={t("common.timKiem")}
            onChange={onSearchInput("maDichVu")}
          />
        ),
      }),
      Column({
        title: t("hsba.tenThuoc"),
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        width: "300px",
        dataIndex: "ten",
        key: "ten",
        i18Name: "hsba.tenThuoc",
        className: "tenDichVu",
        renderSearch: (
          <InputTimeout
            placeholder={t("common.timKiem")}
            onChange={onSearchInput("tenDichVu")}
          />
        ),
        render: (item, data) => {
          return (
            <div>{`${data?.tenDichVu || data?.ten || ""} - ${
              data?.tenHoatChat ? data?.tenHoatChat : ""
            }  ${data?.hamLuong ? `- ` + data?.hamLuong : ""}`}</div>
          );
        },
      }),
      Column({
        title: t("hsba.slKe"),
        sort_key: "soLuongYeuCau",
        dataSort: dataSortColumn["soLuongYeuCau"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "soLuongYeuCau",
        key: "soLuongYeuCau",
        align: "right",
        i18Name: "hsba.slKe",
      }),
      Column({
        title: t("hsba.slDung"),
        sort_key: "soLuong",
        dataSort: dataSortColumn["soLuong"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "soLuong",
        key: "soLuong",
        align: "right",
        i18Name: "hsba.slDung",
      }),
      Column({
        title: t("common.thanhTien"),
        sort_key: "thanhTien",
        dataSort: dataSortColumn["thanhTien"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "right",
        i18Name: "common.thanhTien",
        render: (value) => value && value.formatPrice(),
      }),
      Column({
        title: t("kho.kho"),
        sort_key: "tenKho",
        dataSort: dataSortColumn["tenKho"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenKho",
        key: "tenKho",
        i18Name: "kho.kho",
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKho}
            mode="multiple"
            placeholder={t("kho.chonKho")}
            onChange={onSearchInput("dsKhoId")}
          />
        ),
        render: (value) => value,
      }),
      Column({
        title: t("common.daPhat"),
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "daPhat",
        key: "daPhat",
        i18Name: "common.daPhat",
        align: "center",
        render: (value, record) => (
          <Checkbox checked={record?.trangThai >= 30} />
        ),
      }),
      Column({
        title: t("common.ttThanhToan"),
        width: "130px",
        dataIndex: "thanhToan",
        key: "thanhToan",
        i18Name: "common.ttThanhToan",
        align: "center",
        render: (value) => (
          <Checkbox
            checked={value === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
          />
        ),
      }),
      Column({
        title: t("common.lieuDung"),
        sort_key: "tenLieuDung",
        dataSort: dataSortColumn["tenLieuDung"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenLieuDung",
        key: "tenLieuDung",
        i18Name: "common.lieuDung",
      }),
      Column({
        title: t("common.cachDung"),
        sort_key: "cachDung",
        dataSort: dataSortColumn["cachDung"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "cachDung",
        key: "cachDung",
        i18Name: "common.cachDung",
      }),
      Column({
        title: t("hsba.phongThucHien"),
        sort_key: "tenPhongThucHien",
        dataSort: dataSortColumn["tenPhongThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenPhongThucHien",
        key: "tenPhongThucHien",
        i18Name: "hsba.phongThucHien",
        align: "center",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiChiDinh"),
        sort_key: "tenBacSiChiDinh",
        dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiChiDinh",
        align: "center",
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("quanLyNoiTru.dvNoiTru.chonNguoiChiDinh")}
            onChange={onSearchInput("dsBacSiChiDinhId")}
          />
        ),
      }),
      Column({
        title: t("hsba.khoaChiDinh"),
        sort_key: "tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        i18Name: "hsba.khoaChiDinh",
        align: "center",
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKhoa}
            mode="multiple"
            placeholder={t("baoCao.chonKhoaChiDinh")}
            value={state.searchLocal?.dsKhoaChiDinhId || khoaLamViecId}
            onChange={onSearchInput("dsKhoaChiDinhId")}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianChiDinh"),
        sort_key: "thoiGianChiDinh",
        dataSort: dataSortColumn["thoiGianChiDinh"] || "",
        onClickSort: onClickSort,
        width: "180px",
        dataIndex: "thoiGianChiDinh",
        key: "thoiGianChiDinh",
        i18Name: "hsba.thoiGianChiDinh",
        align: "center",
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("thoiGianChiDinh", e);
            }}
          />
        ),
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("hsba.thoiGianThucHien"),
        sort_key: "thoiGianThucHien",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        align: "center",
        i18Name: "hsba.thoiGianThucHien",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("hsba.thoiGianPhat"),
        sort_key: "thoiGianDuyetDls",
        dataSort: dataSortColumn["thoiGianDuyetDls"] || "",
        onClickSort: onClickSort,
        width: "180px",
        dataIndex: "thoiGianDuyet",
        key: "thoiGianDuyet",
        i18Name: "hsba.thoiGianPhat",
        align: "center",
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("thoiGianDuyetDls", e);
            }}
          />
        ),
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: "TT20",
        sort_key: "tenMucDich",
        dataSort: dataSortColumn["tenMucDich"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenMucDich",
        key: "tenMucDich",
        i18Name: "TT20",
      }),
      Column({
        title: t("common.tuTra"),
        width: "100px",
        dataIndex: "tuTra",
        key: "tuTra",
        i18Name: "common.tuTra",
        align: "center",
        render: (value) => <Checkbox checked={value} />,
      }),
      Column({
        title: t("hsba.khongTinhTien"),
        width: "140px",
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        i18Name: "hsba.khongTinhTien",
        align: "center",
        render: (value) => <Checkbox checked={value} />,
      }),
      Column({
        title: t("kho.trangThaiThuoc"),
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        width: "180px",
        dataIndex: "trangThai",
        key: "trangThai",
        i18Name: "kho.trangThaiThuoc",
        align: "left",
        render: (item) => listTrangThaiThuoc.find((x) => x.id === item)?.ten,
      }),
      Column({
        title: t("kho.ghiChuKhoaDuoc"),
        sort_key: "ghiChuDls",
        dataSort: dataSortColumn["ghiChuDls"] || "",
        onClickSort: onClickSort,
        width: "180px",
        dataIndex: "ghiChuDls",
        key: "ghiChuDls",
        i18Name: "kho.ghiChuKhoaDuoc",
        align: "left",
        hidden: !isTrangThaiTuChoiDuyetDLS,
      }),
      Column({
        title: t("hsba.loaiThuoc"),
        sort_key: "loai",
        dataSort: dataSortColumn["loai"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "loai",
        key: "loai",
        i18Name: "hsba.loaiThuoc",
        render: (value) => listloaiThuoc.find((x) => x.id === value)?.ten,
      }),
      Column({
        title: t("common.loaiChiDinh"),
        sort_key: "loaiChiDinh",
        dataSort: dataSortColumn["loaiChiDinh"] || "",
        onClickSort: onClickSort,
        width: 120,
        dataIndex: "loaiChiDinh",
        key: "loaiChiDinh",
        i18Name: "common.loaiChiDinh",
        align: "center",
        render: (value) => {
          return listLoaiChiDinh.find((x) => x.id === value)?.ten;
        },
      }),
      Column({
        title: <Setting refTable={refSettings} />,
        width: "80px",
        key: "setting",
        dataIndex: "setting",
        align: "center",
        fixed: "right",
        ignore: true,
        render: (item, record, index) => {
          const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
            {
              thoiGian: record.thoiGianThucHien,
            },
            listAllPhieuThuChotDot
          );
          return (
            isChotThoiGianDotDieuTri &&
            isShowAction && (
              <div>
                <Tooltip title={t("pttt.suaThongTinThuoc")} placement="bottom">
                  <SVG.IcEdit onClick={onEdit(record)} className="ic-action" />
                </Tooltip>
                <Tooltip title={t("pttt.xoaThuoc")} placement="bottom">
                  <SVG.IcDelete
                    onClick={() => onDelete(record)}
                    className="ic-action"
                  />
                </Tooltip>
              </div>
            )
          );
        },
      }),
    ];
    const setRowClassName = (record) => {
      if (
        record?.tenMucDich &&
        thongTinBenhNhan?.doiTuong === DOI_TUONG.BAO_HIEM
      ) {
        if (record.khongTinhTien) {
          return "row-tt35 green-color";
        }
        if (record.tuTra) {
          return "row-tt35 orange-color";
        }
        return "row-tt35";
      }
      if (record.khongTinhTien) {
        return "green-color";
      }
      if (record.tuTra) {
        return "orange-color";
      }
    };

    const onChangePage = (page) => {
      onSearch({ page: page - 1 });
    };
    const onHandleSizeChange = (size) => {
      onSizeChange({ size: size });
    };
    const renderEmptyTextTable = () => {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
          />
        </div>
      );
    };

    return (
      <Main>
        <TableWrapper
          columns={columns}
          dataSource={listDsThuoc}
          rowKey={(record) => record.id}
          rowClassName={setRowClassName}
          tableName={tableName}
          ref={refSettings}
          scroll={{ x: 2900 }}
          locale={{
            emptyText: renderEmptyTextTable(),
          }}
        />{" "}
        {!!totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listDsThuoc}
            onShowSizeChange={onHandleSizeChange}
            style={{ flex: 1, justifyContent: "flex-end" }}
          />
        ) : null}
        <SuaThongTinThuoc ref={refSuaThongTinThuoc} />
      </Main>
    );
  }
);

export default ThuocHoSoBenhAn;
