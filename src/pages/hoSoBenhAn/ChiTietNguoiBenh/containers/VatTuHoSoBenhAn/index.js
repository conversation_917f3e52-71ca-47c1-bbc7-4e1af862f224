import React, {
  forwardRef,
  useRef,
  useImperative<PERSON><PERSON><PERSON>,
  useEffect,
} from "react";
import { Checkbox, TableWrapper, Pagination } from "components";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import moment from "moment";
import { Empty, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { TRANG_THAI_PHIEU_THU_THANH_TOAN } from "constants";
import { useEnum, useConfirm, useStore } from "hooks";
import { ENUM } from "constants/index";
import ChinhSuaVatTu from "pages/chiDinhDichVu/DichVuVatTu/ChinhSuaVatTu";
import { SVG } from "assets";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";

const { Column, Setting } = TableWrapper;

const VatTuHoSoBenhAn = forwardRef(
  (
    {
      tableName = "TABLE_HSBA_VAT_TU",
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
      isShowAction = false,
    },
    ref
  ) => {
    const refSettings = useRef(null);
    const { t } = useTranslation();
    const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
    const refSuaThongTin = useRef(null);
    const { showConfirm } = useConfirm();

    const { listDsVatTu, dataSortColumn, totalElements, page, size } =
      useSelector((state) => state.dsVatTu);
    const listAllPhieuThuChotDot = useStore(
      "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
      []
    );

    const {
      dsVatTu: { onSortChange, onSizeChange, onSearch },
      chiDinhVatTu: { onDeleteDichVu },
    } = useDispatch();

    useEffect(() => {
      if (nbDotDieuTriId) {
        onSearch({
          dataSearch: {
            nbDotDieuTriId,
            chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            dsTrangThaiHoan: dsTrangThaiHoan ?? [0, 10, 20],
          },
          page: 0,
          size: 10,
          dataSortColumn: { thoiGianThucHien: 1 },
        });
      }
    }, [
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    ]);

    useImperativeHandle(ref, () => ({
      onSettings,
    }));
    const onSettings = () => {
      refSettings && refSettings.current.settings();
    };

    const onClickSort = (key, value) => {
      onSortChange({ [key]: value });
    };

    const onEdit = (record) => () => {
      refSuaThongTin.current &&
        refSuaThongTin.current.show(record, () => {
          onSearch({
            dataSearch: {
              nbDotDieuTriId,
              chiDinhTuDichVuId,
              chiDinhTuLoaiDichVu,
              dsTrangThaiHoan,
            },
            page: 0,
            size: 10,
            dataSortColumn: { thoiGianThucHien: 1 },
          });
        });
    };

    const onDelete = (record) => {
      const ten = record.ten || record.tenDichVu || "";
      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: t("common.banCoChacMuonXoa") + ten + "?",
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          onDeleteDichVu(record.id).then((s) =>
            onSearch({
              dataSearch: {
                nbDotDieuTriId,
                chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu,
                dsTrangThaiHoan,
              },
              page: 0,
              size: 10,
              dataSortColumn: { thoiGianThucHien: 1 },
            })
          );
        },
        () => {}
      );
    };

    const columns = [
      Column({
        title: t("common.stt"),
        width: "80px",
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
      Column({
        title: t("danhMuc.maVatTu"),
        sort_key: "maDichVu",
        dataSort: dataSortColumn["maDichVu"] || "",
        onClickSort: onClickSort,
        width: "120px",
        dataIndex: "maDichVu",
        key: "maDichVu",
        i18Name: "danhMuc.maVatTu",
        className: "maDichVu",
      }),
      Column({
        title: t("hsba.tenVatTu"),
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "hsba.tenVatTu",
        className: "tenDichVu",
      }),
      Column({
        title: t("hsba.slKe"),
        sort_key: "soLuongYeuCau",
        dataSort: dataSortColumn["soLuongYeuCau"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "soLuongYeuCau",
        key: "soLuongYeuCau",
        align: "right",
        i18Name: "hsba.slKe",
      }),
      Column({
        title: t("hsba.slDung"),
        sort_key: "soLuong",
        dataSort: dataSortColumn["soLuong"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "soLuong",
        key: "soLuong",
        align: "right",
        i18Name: "hsba.slDung",
      }),
      Column({
        title: t("common.thanhTien"),
        sort_key: "thanhTien",
        dataSort: dataSortColumn["thanhTien"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "right",
        i18Name: "common.thanhTien",
        render: (value) => value && value.formatPrice(),
      }),
      Column({
        title: t("kho.kho"),
        sort_key: "tenKho",
        dataSort: dataSortColumn["tenKho"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenKho",
        key: "tenKho",
        i18Name: "kho.kho",
        render: (value) => value,
      }),
      Column({
        title: t("common.daPhat"),
        sort_key: "phat",
        dataSort: dataSortColumn["phat"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "phat",
        key: "phat",
        i18Name: "common.daPhat",
        align: "center",
        render: (value) => <Checkbox checked={value} />,
      }),
      Column({
        title: t("common.ttThanhToan"),
        sort_key: "thanhToan",
        dataSort: dataSortColumn["thanhToan"] || "",
        onClickSort: onClickSort,
        width: "130px",
        dataIndex: "thanhToan",
        key: "thanhToan",
        i18Name: "common.ttThanhToan",
        align: "center",
        render: (value) => (
          <Checkbox
            checked={value === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
          />
        ),
      }),
      Column({
        title: t("hsba.phongThucHien"),
        sort_key: "tenPhongThucHien",
        dataSort: dataSortColumn["tenPhongThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenPhongThucHien",
        key: "tenPhongThucHien",
        i18Name: "hsba.phongThucHien",
        align: "center",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiChiDinh"),
        sort_key: "tenBacSiChiDinh",
        dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiChiDinh",
        align: "center",
      }),
      Column({
        title: t("hsba.khoaChiDinh"),
        sort_key: "tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        i18Name: "hsba.khoaChiDinh",
        align: "center",
      }),
      Column({
        title: t("hsba.thoiGianChiDinh"),
        sort_key: "thoiGianChiDinh",
        dataSort: dataSortColumn["thoiGianChiDinh"] || "",
        onClickSort: onClickSort,
        width: "180px",
        dataIndex: "thoiGianChiDinh",
        key: "thoiGianChiDinh",
        i18Name: "hsba.thoiGianChiDinh",
        align: "center",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("hsba.thoiGianThucHien"),
        sort_key: "thoiGianThucHien",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        i18Name: "hsba.thoiGianThucHien",
        align: "center",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("hsba.thoiGianPhat"),
        sort_key: "thoiGianDuyet",
        dataSort: dataSortColumn["thoiGianDuyet"] || "",
        onClickSort: onClickSort,
        width: "180px",
        dataIndex: "thoiGianDuyet",
        key: "thoiGianDuyet",
        i18Name: "hsba.thoiGianPhat",
        align: "center",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("common.tuTra"),
        sort_key: "tuTra",
        dataSort: dataSortColumn["tuTra"] || "",
        onClickSort: onClickSort,
        width: "100px",
        dataIndex: "tuTra",
        key: "tuTra",
        i18Name: "common.tuTra",
        align: "center",
        render: (value) => <Checkbox checked={value} />,
      }),
      Column({
        title: t("hsba.khongTinhTien"),
        sort_key: "khongTinhTien",
        dataSort: dataSortColumn["khongTinhTien"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        i18Name: "hsba.khongTinhTien",
        align: "center",
        render: (value) => <Checkbox checked={value} />,
      }),
      Column({
        title: t("common.loaiChiDinh"),
        sort_key: "loaiChiDinh",
        dataSort: dataSortColumn["loaiChiDinh"] || "",
        onClickSort: onClickSort,
        width: "90px",
        dataIndex: "loaiChiDinh",
        key: "loaiChiDinh",
        i18Name: "common.loaiChiDinh",
        align: "center",
        render: (value) => {
          return listLoaiChiDinh.find((x) => x.id === value)?.ten;
        },
      }),
      Column({
        title: <Setting refTable={refSettings} />,
        width: "90px",
        key: "setting",
        dataIndex: "setting",
        align: "center",
        fixed: "right",
        ignore: true,
        render: (item, record, index) => {
          const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
            {
              thoiGian: record.thoiGianThucHien,
            },
            listAllPhieuThuChotDot
          );
          return (
            isChotThoiGianDotDieuTri &&
            isShowAction && (
              <div className="action-btn">
                <Tooltip title={t("tiepDon.suaThongTin")} placement="bottom">
                  <SVG.IcEdit className="ic-action" onClick={onEdit(record)} />
                </Tooltip>
                <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                  <SVG.IcDelete
                    className="ic-action"
                    onClick={() => onDelete(record)}
                  />
                </Tooltip>
              </div>
            )
          );
        },
      }),
    ];

    const onChangePage = (page) => {
      onSearch({ page: page - 1 });
    };
    const onHandleSizeChange = (size) => {
      onSizeChange({ size: size });
    };
    const renderEmptyTextTable = () => {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
          />
        </div>
      );
    };

    const setRowClassName = (record) => {
      if (record.khongTinhTien) {
        return "green-color";
      }
      if (record.tuTra) {
        return "orange-color";
      }
    };

    return (
      <Main>
        <TableWrapper
          columns={columns}
          dataSource={listDsVatTu}
          //   onRow={onRow}
          rowKey={(record) => record.id}
          tableName={tableName}
          ref={refSettings}
          locale={{
            emptyText: renderEmptyTextTable(),
          }}
          rowClassName={setRowClassName}
        />{" "}
        {!!totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listDsVatTu}
            onShowSizeChange={onHandleSizeChange}
            style={{ flex: 1, justifyContent: "flex-end" }}
          />
        ) : null}
        <ChinhSuaVatTu ref={refSuaThongTin} />
      </Main>
    );
  }
);

export default VatTuHoSoBenhAn;
