import React, {
  forwardRef,
  useRef,
  useImperative<PERSON><PERSON><PERSON>,
  useEffect,
} from "react";
import { Empty } from "antd";
import { Checkbox, TableWrapper, Pagination } from "components";
import { useSelector, useDispatch } from "react-redux";
import { useEnum } from "hooks";
import { ENUM } from "constants/index";
import { Main } from "./styled";
import moment from "moment";
import { useTranslation } from "react-i18next";
const { Column, Setting } = TableWrapper;

const Mau = forwardRef(
  (
    {
      tableName = "TABLE_HSBA_MAU",
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    },
    ref
  ) => {
    const refSettings = useRef(null);
    const { t } = useTranslation();
    const listDsMau = useSelector((state) => state.dsMau.listDsMau);
    const [listTrangThaiMau] = useEnum(ENUM.TRANG_THAI_MAU);
    const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
    const [listKetQuaXetNghiem] = useEnum(ENUM.KET_QUA_XET_NGHIEM);
    const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);

    const { totalElements, page, size } = useSelector((state) => state.dsMau);

    const {
      dsMau: { onSizeChange, onSearch },
    } = useDispatch();

    useEffect(() => {
      if (nbDotDieuTriId) {
        onSearch({
          dataSearch: {
            nbDotDieuTriId,
            chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            dsTrangThaiHoan: dsTrangThaiHoan ?? [0, 10, 20],
          },
          page: 0,
          size: 10,
          dataSortColumn: { thoiGianThucHien: 1 },
        });
      }
    }, [
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    ]);
    useImperativeHandle(ref, () => ({
      onSettings,
    }));

    const onSettings = () => {
      refSettings && refSettings.current.settings();
    };

    const columns = [
      Column({
        title: t("common.stt"),
        width: "50px",
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.chePhamMau"),
        width: "200px",
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.chePhamMau",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianThucHien"),
        width: "150px",
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianThucHien",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("hsba.slKe"),
        width: "100px",
        dataIndex: "soLuongYeuCau",
        key: "soLuongYeuCau",
        align: "right",
        i18Name: "hsba.slKe",
      }),
      Column({
        title: t("hsba.slDung"),
        width: "100px",
        dataIndex: "soLuong",
        key: "soLuong",
        align: "right",
        i18Name: "hsba.slDung",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.soLuongTra"),
        width: "100px",
        dataIndex: "soLuongTra",
        key: "soLuongTra",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.soLuongTra",
      }),
      Column({
        title: t("common.trangThai"),
        width: "80px",
        dataIndex: "trangThai",
        key: "trangThai",
        align: "center",
        i18Name: "common.trangThai",
        render: (item) => listTrangThaiMau.find((x) => x.id == item)?.ten || "",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.khongTinhTien"),
        width: "100px",
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.khongTinhTien",
        render: (item) => <Checkbox checked={item} />,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tuTra"),
        width: "80px",
        dataIndex: "tuTra",
        key: "tuTra",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.tuTra",
        render: (item) => <Checkbox checked={item} />,
      }),
      Column({
        title: t("common.thanhTien"),
        width: "100px",
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "right",
        i18Name: "common.thanhTien",
        render: (value) => (value || 0).formatPrice(),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nhomMauNb"),
        width: "100px",
        dataIndex: "nhomMauNb",
        key: "nhomMauNb",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.nhomMauNb",
        render: (item) => listNhomMau.find((x) => x.id == item)?.ten || "",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nhomMauPhat"),
        width: "120px",
        dataIndex: "nhomMau",
        key: "nhomMau",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.nhomMauPhat",
        render: (item) => listNhomMau.find((x) => x.id == item)?.ten || "",
      }),
      Column({
        title: t("quanLyNoiTru.mau.maTuiMau"),
        width: "80px",
        dataIndex: "maTuiMau",
        key: "maTuiMau",
        align: "center",
        i18Name: "quanLyNoiTru.mau.maTuiMau",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.theTich"),
        width: "80px",
        dataIndex: "theTich",
        key: "theTich",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.theTich",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenDonViTinh"),
        width: "80px",
        dataIndex: "tenDonViTinh",
        key: "tenDonViTinh",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.tenDonViTinh",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianPhatMau"),
        width: "120px",
        dataIndex: "thoiGianPhat",
        key: "thoiGianPhat",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhatMau",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianDung"),
        width: "100px",
        dataIndex: "thoiGianPhat",
        key: "thoiGianPhat",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianDung",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.duongDung"),
        width: "100px",
        dataIndex: "tenDuongDung",
        key: "tenDuongDung",
        i18Name: "quanLyNoiTru.dvNoiTru.duongDung",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenKhoaChiDinh"),
        width: "200px",
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.tenKhoaChiDinh",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiChiDinh"),
        width: "120px",
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiChiDinh",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiDuyetMau"),
        width: "120px",
        dataIndex: "tenNguoiDuyet",
        key: "tenNguoiDuyet",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiDuyetMau",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiPhatMau"),
        width: "120px",
        dataIndex: "tenNguoiPhat1",
        key: "tenNguoiPhat1",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiPhatMau",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ngaySanXuat"),
        width: "100px",
        dataIndex: "ngaySanXuat",
        key: "ngaySanXuat",
        i18Name: "quanLyNoiTru.dvNoiTru.ngaySanXuat",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.hanSuDung"),
        width: "100px",
        dataIndex: "ngayHanSuDung",
        key: "ngayHanSuDung",
        i18Name: "quanLyNoiTru.dvNoiTru.hanSuDung",
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ghiChu"),
        width: "100px",
        dataIndex: "ghiChu",
        key: "ghiChu",
        i18Name: "quanLyNoiTru.dvNoiTru.ghiChu",
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.sotRet"),
        width: "100px",
        dataIndex: "sotRet",
        key: "sotRet",
        i18Name: "quanLyNoiTru.dvNoiTru.sotRet",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.giangMai"),
        width: "100px",
        dataIndex: "giangMai",
        key: "giangMai",
        i18Name: "quanLyNoiTru.dvNoiTru.giangMai",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: "HCV",
        width: "80px",
        dataIndex: "hcv",
        key: "hcv",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: "HBV",
        width: "80px",
        dataIndex: "hbv",
        key: "hbv",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: "HIV",
        width: "80px",
        dataIndex: "hiv",
        key: "hiv",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ong1MoiTruongMuoi22oC"),
        width: "180px",
        dataIndex: "muoi1",
        key: "muoi1",
        i18Name: "quanLyNoiTru.dvNoiTru.ong1MoiTruongMuoi22oC",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ong2MoiTruongMuoi22oC"),
        width: "180px",
        dataIndex: "muoi2",
        key: "muoi2",
        i18Name: "quanLyNoiTru.dvNoiTru.ong2MoiTruongMuoi22oC",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng1"),
        width: "180px",
        dataIndex: "globulin1",
        key: "globulin1",
        i18Name: "quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng1",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng2"),
        width: "180px",
        dataIndex: "globulin2",
        key: "globulin2",
        i18Name: "quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng2",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng1"),
        width: "180px",
        dataIndex: "phanUngCheo1",
        key: "phanUngCheo1",
        i18Name: "quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng1",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng2"),
        width: "180px",
        dataIndex: "phanUngCheo2",
        key: "phanUngCheo2",
        i18Name: "quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng2",
        render: (value) =>
          (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.manHinhChiDinh"),
        width: "130px",
        dataIndex: "chiDinhTuLoaiDichVu",
        key: "chiDinhTuLoaiDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.manHinhChiDinh",
        render: (value) =>
          (listLoaiDichVu || []).find((e) => e.id === value)?.ten,
      }),
      Column({
        title: t("danhMuc.donGiaBh"),
        width: "100px",
        dataIndex: "giaBaoHiem",
        key: "giaBaoHiem",
        align: "right",
        i18Name: "danhMuc.donGiaBh",
        render: (value) => (value || 0).formatPrice(),
      }),
      Column({
        title: t("danhMuc.donGiaKhongBh"),
        width: "120px",
        dataIndex: "giaKhongBaoHiem",
        key: "giaKhongBaoHiem",
        align: "right",
        i18Name: "danhMuc.donGiaKhongBh",
        render: (value) => (value || 0).formatPrice(),
      }),
      Column({
        title: <Setting refTable={refSettings} />,
        width: "40px",
        key: "setting",
        dataIndex: "setting",
        align: "center",
        fixed: "right",
        ignore: true,
      }),
    ];

    const onChangePage = (page) => {
      onSearch({ page: page - 1 });
    };
    const onHandleSizeChange = (size) => {
      onSizeChange({ size: size });
    };
    const renderEmptyTextTable = () => {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
          />
        </div>
      );
    };

    return (
      <Main>
        <TableWrapper
          columns={columns}
          dataSource={listDsMau}
          rowKey={(record) => record.id}
          tableName={tableName}
          ref={refSettings}
          locale={{
            emptyText: renderEmptyTextTable(),
          }}
        />{" "}
        {!!totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listDsMau}
            onShowSizeChange={onHandleSizeChange}
          />
        ) : null}
      </Main>
    );
  }
);

export default Mau;
