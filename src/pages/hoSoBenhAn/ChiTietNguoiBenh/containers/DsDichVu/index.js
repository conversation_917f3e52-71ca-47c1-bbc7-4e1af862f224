import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
} from "react";
import {
  TableWrapper,
  Tooltip,
  Checkbox,
  Pagination,
  InputTimeout,
  Select,
  DatePicker,
  Popover,
} from "components";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import {
  ENUM,
  HIEU_LUC,
  LOAI_DICH_VU,
  ROLES,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  LIST_TRANG_THAI_THANH_TOAN,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_DV_KHONG_THUC_HIEN,
  THIET_LAP_CHUNG,
  DOI_TUONG_SU_DUNG,
} from "constants/index";
import moment from "moment";
import { useTranslation } from "react-i18next";
import {
  useStore,
  useEnum,
  useLoading,
  useThietLap,
  useListAll,
  useConfirm,
  useQueryAll,
} from "hooks";
import ModalChiTietDichVu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/DvNgoaiTru/ModalChiTietDichVu";
import { groupBy, flatten } from "lodash";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import { Empty } from "antd";
import ChinhSuaDVKyThuat from "pages/chiDinhDichVu/DichVuKyThuat/ChinhSuaDVKyThuat";
import { useDonViKetNoiPacs } from "pages/hoSoBenhAn/ChiTietNguoiBenh/hooks/useDonViKetNoiPacs";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";
import ModalScanPrint from "pages/hoSoBenhAn/components/ModalScanPrint";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import stringUtils from "mainam-react-native-string-utils";
import KetQuaXetNghiem from "pages/khamBenh/ChiDinhDichVu/components/KetQuaXetNghiem";
import ModalNhapKetQuaDichVu from "pages/khamBenh/ChiDinhDichVu/ModalNhapKetQuaDichVu";
import { query } from "redux-store/stores";

const { Column, Setting } = TableWrapper;

const DsDichVu = forwardRef(
  (
    {
      tableName = "TABLE_HSBA_DSDV",
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
      isShowAction = false,
    },
    ref
  ) => {
    const refSettings = useRef(null);
    const refModalChiTietDichVu = useRef(null);
    const refModalSignPrint = useRef(null);
    const { showConfirm } = useConfirm();
    const refSuaThongTin = useRef(null);
    const refModalNhapKetQua = useRef(null);

    const { t } = useTranslation();
    const { showLoading, hideLoading } = useLoading();
    const { listDichVuKyThuat, dataSortColumn, totalElements, page, size } =
      useSelector((state) => state.dsDichVuKyThuat);
    const thongTinBenhNhanTongHop = useStore(
      "nbDotDieuTri.thongTinBenhNhanTongHop",
      {}
    );
    const listAllPhieuThuChotDot = useStore(
      "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
      []
    );

    const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
    const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
    const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
    const [listAllNhanVien] = useListAll("nhanVien", {
      page: "",
      size: "",
      active: true,
    });
    const [listAllKhoa] = useListAll("khoa", {
      page: "",
      size: "",
      active: true,
    });
    const [listAllPhong] = useListAll("phong", {
      page: "",
      size: "",
      active: true,
    });
    const [listhinhThucTtKsk] = useEnum(ENUM.HINH_THUC_TT_KSK);
    const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE);
    const [dataBAT_POPUP_KY_SO_KHI_IN_KET_QUA] = useThietLap(
      THIET_LAP_CHUNG.BAT_POPUP_KY_SO_KHI_IN_KET_QUA
    );
    const { tienChuaThanhToan, tienTamUng, tienConLai } = useStore(
      "nbDotDieuTri.thongTinCoBan",
      {},
      { field: "tienChuaThanhToan,tienTamUng,tienConLai" }
    );
    const { data: listAllNhomDichVuCap1 } = useQueryAll(
      query.nhomDichVuCap1.queryAllNhomDichVuCap1
    );
    const { checkDonViKetNoi } = useDonViKetNoiPacs();

    const { isKhamBenh, isHSBA } = useMemo(() => {
      const pathname = window.location.pathname;
      return {
        isKhamBenh: pathname.includes("/kham-benh"),
        isHSBA: pathname.includes("/ho-so-benh-an"),
      };
    }, [window.location.pathname]);

    const isNbThieuTien = useMemo(() => {
      // Nếu bệnh nhân không thiếu tiền → luôn được xem kết quả
      const isKhongThieuTien =
        tienChuaThanhToan < tienTamUng || tienConLai >= 0;
      if (isKhongThieuTien) return false;

      // Nếu bệnh nhân thiếu tiền → chỉ chặn xem kết quả nếu không có quyền
      if (isKhamBenh && !checkRole([ROLES["KHAM_BENH"].CHAN_XEM_KQ_KHAM])) {
        return true; // không có quyền ở màn khám bệnh → chặn xem
      }

      if (isHSBA && !checkRole([ROLES["HO_SO_BENH_AN"].CHAN_XEM_KQ_HSBA])) {
        return true; // không có quyền ở màn HSBA → chặn xem
      }

      // Nếu có quyền hoặc không thuộc màn HSBA, Khám bệnh → cho xem
      return false;
    }, [tienChuaThanhToan, tienTamUng, tienConLai, isKhamBenh, isHSBA]);

    const {
      dsDichVuKyThuat: {
        onSortChange,
        onSizeChange,
        onSearch,
        onChangeInputSearch,
      },
      chiDinhKhamBenh: {
        inPhieuKetQua,
        onDeleteDichVu,
        themThongTinDV,
        coKetQua,
      },
      pacs: { getUrl },
      dsPhieuThuChotDotDieuTri: { getListAllPhieuThuChotDotDieuTri },
    } = useDispatch();

    useEffect(() => {
      if (isFinish && nbDotDieuTriId) {
        onSearch({
          dataSearch: {
            nbDotDieuTriId,
            chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu,
            dsTrangThaiHoan: dsTrangThaiHoan ?? [0, 10, 20, 40],
          },
          page: 0,
          size: dataPageSize || 10,
          dataSortColumn: { thoiGianThucHien: 1 },
        });
        getListAllPhieuThuChotDotDieuTri({
          page: "",
          size: "",
          nbDotDieuTriId,
          chotDotDieuTri: true,
        });
      }
    }, [
      isFinish,
      dataPageSize,
      nbDotDieuTriId,
      chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu,
      dsTrangThaiHoan,
    ]);

    const onViewPacs = (data) => {
      getUrl({ id: data?.id }).then((res) => {
        if (res) {
          window.open(res, "_blank").focus();
        }
      });
    };

    const refreshList = () => {
      onSearch({
        dataSearch: {
          nbDotDieuTriId,
          chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu,
          dsTrangThaiHoan,
        },
        page: 0,
        size: dataPageSize || 10,
        dataSortColumn: { thoiGianThucHien: 1 },
      });
    };

    useImperativeHandle(ref, () => ({
      onSettings,
    }));

    const listTrangThaiDvMemo = useMemo(() => {
      const groupTrangThai = groupBy(listTrangThaiDichVu, "ten");
      return Object.keys(groupTrangThai).map((key, index) => ({
        ten: key,
        id: index,
        value: groupTrangThai[key].map((x) => x.id),
      }));
    }, [listTrangThaiDichVu]);

    const listDVKTMemo = useMemo(() => {
      const _group = groupBy(listDichVuKyThuat, "tenNhomDichVuCap1");
      let data = [];

      Object.keys(_group).forEach((key) => {
        data.push({
          id: key,
          key: key,
          isGroup: true,
          tenDichVu: key,
        });

        (_group[key] || []).forEach((element, index) => {
          data.push({ ...element, index: index + 1 });
        });
      });

      return data;
    }, [listDichVuKyThuat]);
    const onSettings = () => {
      refSettings && refSettings.current.settings();
    };

    const onViewPdf = async (data) => {
      const { nbDotDieuTriId, soPhieuId, loaiDichVu, soKetNoi } = data;

      if (isNbThieuTien) {
        showConfirm({
          title: t("common.canhBao"),
          content: t("khamBenh.nguoiBenhThieuTienCanDiThanhToan"),
          cancelText: t("common.quayLai"),
          typeModal: "warning",
        });
        return;
      }

      try {
        showLoading();

        const obj = {
          nbDotDieuTriId,
          loaiDichVu,
        };

        switch (loaiDichVu) {
          case LOAI_DICH_VU.XET_NGHIEM:
          case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
            obj.dsSoPhieuId = soPhieuId ? [soPhieuId] : null;
            break;
          case LOAI_DICH_VU.CDHA:
            obj.dsSoKetNoi = soKetNoi ? [soKetNoi] : null;
            break;
          default:
            break;
        }

        if (!dataBAT_POPUP_KY_SO_KHI_IN_KET_QUA?.eval()) {
          await inPhieuKetQua(obj);
          return;
        }
        const s = await inPhieuKetQua({ ...obj, returnData: true });

        if (!s.length) return;
        const _listPhieu = s.map((item) => {
          return {
            ...item,
            key: stringUtils.guid(),
            data: {
              filePdf: item.dsDuongDan
                ? item.dsDuongDan.length
                  ? item.dsDuongDan[0]
                  : null
                : item.file.pdf,
            },
            ten: `Kết quả pdf`,
            nbDotDieuTriId: item.nbDotDieuTriId || nbDotDieuTriId,
            trangThai: item?.lichSuKy?.trangThai,
            baoCaoId: item.baoCaoId,
            soPhieu: item.soPhieu,
            lichSuKy: item.lichSuKy,
          };
        });

        const listPhieu = await Promise.all(
          _listPhieu.map(async (item) => {
            if (item.lichSuKy) {
              const itemDaKy = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                id: item.lichSuKy?.id,
                chuKySo: item.lichSuKy?.chuKySo,
              });

              return {
                ...item,
                data: { filePdf: itemDaKy.data?.file?.pdf },
              };
            }
            return item;
          })
        );

        refModalSignPrint.current &&
          refModalSignPrint.current.show(
            {
              dsPhieu: listPhieu,
              isSignPdf: true,
              nbDotDieuTriId,
              baoCaoId: listPhieu[0].baoCaoId,
            },
            async () => {
              const s = await inPhieuKetQua({ ...obj, returnData: true });
              const _listPhieu = s.map((item) => {
                return {
                  ...item,
                  key: stringUtils.guid(),
                  data: {
                    filePdf: item.dsDuongDan
                      ? item.dsDuongDan.length
                        ? item.dsDuongDan[0]
                        : null
                      : item.file.pdf,
                  },
                  ten: `Kết quả pdf`,
                  nbDotDieuTriId: item.nbDotDieuTriId || nbDotDieuTriId,
                  trangThai: item?.lichSuKy?.trangThai,
                  baoCaoId: item.baoCaoId,
                  soPhieu: item.soPhieu,
                  lichSuKy: item.lichSuKy,
                };
              });

              const listPhieu = await Promise.all(
                _listPhieu.map(async (item) => {
                  if (item.lichSuKy) {
                    const itemDaKy =
                      await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                        id: item.lichSuKy?.id,
                        chuKySo: item.lichSuKy?.chuKySo,
                      });

                    return {
                      ...item,
                      data: { filePdf: itemDaKy.data?.file?.pdf },
                    };
                  }
                  return item;
                })
              );
              return listPhieu;
            }
          );
      } catch (error) {
      } finally {
        hideLoading();
      }
    };

    const onNoExecute = async ({ loaiDichVu, id, nbDotDieuTriId }) => {
      try {
        showLoading();
        let obj = {
          id,
          body: {
            nbDvKyThuat: {
              khongThucHien: true,
            },
          },
          loaiDichVu,
        };
        await themThongTinDV(obj).then((s) => {
          if (s.code === 0) refreshList();
        });
      } catch (error) {
      } finally {
        hideLoading();
      }
    };

    const onChangeSearch = (key, value) => {
      onChangeInputSearch({ [key]: value });
    };
    const onSearchDate = (key, value) => {
      const newSearch = {
        [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
        [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
      };

      onChangeInputSearch(newSearch);
    };

    const onClickSort = (key, value) => {
      onSortChange({ [key]: value });
    };

    const sharedOnCell = (row, index) => {
      if (row.isGroup) {
        return { colSpan: 0 };
      }
    };

    const onDelete = (record) => () => {
      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: `${t("khamBenh.chiDinh.xacNhanXoaChiDinh")} ${
            record.tenDichVu || ""
          }?`,
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "error",
        },
        async () => {
          try {
            showLoading();

            const s = await onDeleteDichVu({
              id: record.id,
              loaiDichVu: record.loaiDichVu,
            });

            if (s.code === 0) refreshList();
          } catch (error) {
            console.error(error);
          } finally {
            hideLoading();
          }
        }
      );
    };

    const onEdit = (record) => () => {
      refSuaThongTin.current &&
        refSuaThongTin.current.show(record, () => {
          refreshList();
        });
    };

    const onCoKetQua = (record) => async () => {
      if (!record.ketQua && record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
        refModalNhapKetQua.current &&
          refModalNhapKetQua.current.show(
            {
              id: record.id,
              loaiDichVu: record.loaiDichVu,
            },
            async () => {
              const s = await coKetQua({
                payload: [record.id],
                loaiDichVu: record.loaiDichVu,
              });
              if (s.code === 0) refreshList();
            }
          );
      } else {
        try {
          showLoading();
          let s = null;
          if (
            [LOAI_DICH_VU.CDHA, LOAI_DICH_VU.PHAU_THUAT_THU_THUAT]?.includes(
              record.loaiDichVu
            )
          ) {
            s = await coKetQua({
              payload: [
                {
                  id: record.id,
                  thoiGianCoKetQua: moment(),
                },
              ],
              loaiDichVu: record.loaiDichVu,
            });
          } else {
            s = await coKetQua({
              payload: [record.id],
              loaiDichVu: record.loaiDichVu,
            });
          }

          if (s.code === 0) refreshList();
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    };

    const columns = [
      Column({
        title: t("common.stt"),
        width: "80px",
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
      Column({
        title: t("common.maDv"),
        sort_key: "maDichVu",
        dataSort: dataSortColumn["maDichVu"] || "",
        onClickSort: onClickSort,
        width: "120px",
        dataIndex: "maDichVu",
        key: "maDichVu",
        i18Name: "common.maDv",
        className: "maDichVu",
        onCell: (row, index) => ({
          colSpan: row.isGroup ? 27 : 1,
        }),
        render: (item, list) => (
          <span className={list?.isGroup ? "nhom-dv-cap-1" : ""}>
            {list.isGroup ? list.tenDichVu : item}
          </span>
        ),
        renderSearch: (
          <InputTimeout
            placeholder={t("common.timKiem")}
            onChange={(e) => {
              onChangeSearch("maDichVu", e);
            }}
          />
        ),
      }),
      Column({
        title: t("common.tenDichVu"),
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        width: "250px",
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "common.tenDichVu",
        className: "tenDichVu",
        onCell: sharedOnCell,
        renderSearch: (
          <InputTimeout
            placeholder={t("common.nhapTenDichVu")}
            onChange={(e) => {
              onChangeSearch("tenDichVu", e);
            }}
          />
        ),
      }),
      Column({
        title: t("danhMuc.chanDoanBenh"),
        width: "250px",
        i18Name: "danhMuc.chanDoanBenh",
        onCell: sharedOnCell,
        render: (item, list) => (
          <>
            <p>
              {list?.dsCdChinh
                ?.map((item) => `${item?.ma} - ${item?.ten}`)
                ?.join(", ")}
            </p>
            <span>
              {list?.dsCdKemTheo
                ?.map((item) => `${item?.ma} - ${item?.ten}`)
                ?.join(", ")}
            </span>
          </>
        ),
      }),
      Column({
        title: t("common.trangThai"),
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "trangThai",
        key: "trangThai",
        align: "center",
        i18Name: "common.trangThai",
        onCell: sharedOnCell,
        render: (value) =>
          (listTrangThaiDichVu || []).find((e) => e.id === value)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiDvMemo}
            mode="multiple"
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              const selectedTrangThai = listTrangThaiDvMemo.filter((item) =>
                (e || []).includes(item.id)
              );
              const dsTrangThai = (selectedTrangThai || []).reduce(
                (a, b) => [...a, ...b.value],
                []
              );

              onChangeSearch("dsTrangThai", dsTrangThai);
            }}
          />
        ),
      }),
      Column({
        title: t("common.thanhTien"),
        sort_key: "thanhTien",
        dataSort: dataSortColumn["thanhTien"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "right",
        i18Name: "common.thanhTien",
        onCell: sharedOnCell,
        render: (value) => (value || 0).formatPrice(),
      }),
      Column({
        title: t("hsba.ttThanhToan"),
        sort_key: "thanhToan",
        dataSort: dataSortColumn["thanhToan"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "thanhToan",
        key: "thanhToan",
        i18Name: "hsba.ttThanhToan",
        align: "center",
        onCell: sharedOnCell,
        render: (value) => (
          <Checkbox
            checked={value === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
          />
        ),
        selectSearch: true,
        renderSearch: (
          <Select
            data={LIST_TRANG_THAI_THANH_TOAN}
            defaultValue={""}
            onChange={(e) => {
              onChangeSearch("thanhToan", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.ttHoan"),
        sort_key: "trangThaiHoan",
        dataSort: dataSortColumn["trangThaiHoan"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "trangThaiHoan",
        key: "trangThaiHoan",
        align: "center",
        i18Name: "hsba.ttHoan",
        onCell: sharedOnCell,
        render: (value) =>
          (listTrangThaiHoan || []).find((element) => element.id === value)
            ?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiHoan}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("dsTrangThaiHoan", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.tuVanVien"),
        sort_key: "tenTuVanVien",
        dataSort: dataSortColumn["tenTuVanVien"] || "",
        onClickSort: onClickSort,
        width: "170px",
        dataIndex: "tenTuVanVien",
        key: "tenTuVanVien",
        i18Name: "hsba.tuVanVien",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("khamBenh.chiDinh.chonTuVanVien")}
            onChange={(e) => {
              onChangeSearch("dsTuVanVienId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiChiDinh"),
        sort_key: "tenBacSiChiDinh",
        dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
        onClickSort: onClickSort,
        width: "170px",
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiChiDinh",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("quanLyNoiTru.dvNoiTru.chonNguoiChiDinh")}
            onChange={(e) => {
              onChangeSearch("dsBacSiChiDinhId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("cdha.nguoiThucHien"),
        sort_key: "tenNguoiThucHien",
        dataSort: dataSortColumn["tenNguoiThucHien"] || "",
        onClickSort: onClickSort,
        width: "170px",
        dataIndex: "tenNguoiThucHien",
        key: "tenNguoiThucHien",
        align: "center",
        i18Name: "cdha.nguoiThucHien",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("cdha.chonNguoiThucHien")}
            onChange={(e) => {
              onChangeSearch("dsNguoiThucHienId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.khoaChiDinh"),
        sort_key: "tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        align: "center",
        i18Name: "hsba.khoaChiDinh",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKhoa}
            mode="multiple"
            placeholder={t("baoCao.chonKhoaChiDinh")}
            onChange={(e) => {
              onChangeSearch("dsKhoaChiDinhId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianChiDinh"),
        sort_key: "thoiGianChiDinh",
        dataSort: dataSortColumn["thoiGianChiDinh"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianChiDinh",
        key: "thoiGianChiDinh",
        align: "center",
        i18Name: "hsba.thoiGianChiDinh",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianChiDinh", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianThucHien"),
        sort_key: "thoiGianThucHien",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        align: "center",
        i18Name: "hsba.thoiGianThucHien",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianThucHien", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.manHinhChiDinh"),
        sort_key: "chiDinhTuLoaiDichVu",
        width: 160,
        dataIndex: "chiDinhTuLoaiDichVu",
        key: "chiDinhTuLoaiDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.manHinhChiDinh",
        dataSort: dataSortColumn["chiDinhTuLoaiDichVu"] || "",
        onClickSort: onClickSort,
        render: (value) =>
          (listLoaiDichVu || []).find((e) => e.id === value)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listLoaiDichVu}
            mode="multiple"
            placeholder={t("quanLyNoiTru.dvNoiTru.chonManHinhChiDinh")}
            onChange={(e) => {
              onChangeSearch("dsChiDinhTuLoaiDichVu", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianKetLuan"),
        sort_key: "thoiGianKetLuan",
        dataSort: dataSortColumn["thoiGianKetLuan"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianKetLuan",
        key: "thoiGianKetLuan",
        align: "center",
        i18Name: "hsba.thoiGianKetLuan",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianKetLuan", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.thoiGianCoKetQua"),
        sort_key: "thoiGianCoKetQua",
        dataSort: dataSortColumn["thoiGianCoKetQua"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "thoiGianCoKetQua",
        key: "thoiGianCoKetQua",
        align: "center",
        i18Name: "hsba.thoiGianCoKetQua",
        onCell: sharedOnCell,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearchDate("ThoiGianCoKetQua", e);
            }}
          />
        ),
      }),
      Column({
        title: t("hsba.phongThucHien"),
        sort_key: "tenPhongThucHien",
        dataSort: dataSortColumn["tenPhongThucHien"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenPhongThucHien",
        key: "tenPhongThucHien",
        align: "center",
        i18Name: "hsba.phongThucHien",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllPhong}
            mode="multiple"
            placeholder={t("baoCao.chonPhongThucHien")}
            onChange={(e) => {
              onChangeSearch("dsPhongThucHienId", e);
            }}
          />
        ),
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ketQua"),
        width: 160,
        dataIndex: "ketQua",
        key: "ketQua",
        i18Name: "quanLyNoiTru.dvNoiTru.ketQua",
        render: (value, row, index) => {
          return (
            <KetQuaXetNghiem
              data={{ value, row, index }}
              isNbThieuTien={isNbThieuTien}
              showKetLuan={false}
              isXetNghiem={value?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM}
            />
          );
        },
      }),
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ketLuan"),
        width: 160,
        dataIndex: "ketLuan",
        key: "ketLuan",
        i18Name: "quanLyNoiTru.dvNoiTru.ketLuan",
        render: (item) => {
          const resultDiv = (
            <div
              className={isNbThieuTien ? "blur-ket-qua" : ""}
              dangerouslySetInnerHTML={{
                __html: item,
              }}
            />
          );

          return isNbThieuTien ? (
            <Popover
              content={
                <span style={{ color: "red" }}>
                  {t("khamBenh.nguoiBenhThieuTienCanDiThanhToan")}
                </span>
              }
            >
              {resultDiv}
            </Popover>
          ) : (
            resultDiv
          );
        },
      }),
      Column({
        title: "TT35",
        sort_key: "tenMucDich",
        dataSort: dataSortColumn["tenMucDich"] || "",
        onClickSort: onClickSort,
        width: "200px",
        dataIndex: "tenMucDich",
        key: "tenMucDich",
        i18Name: "TT35",
        onCell: sharedOnCell,
      }),
      Column({
        title: t("khamBenh.ketQua.giaTriThamChieu"),
        sort_key: "ketQuaThamChieu",
        width: 100,
        dataIndex: "ketQuaThamChieu",
        key: "ketQuaThamChieu",
        i18Name: "khamBenh.ketQua.giaTriThamChieu",
        sort_key: "ketQuaThamChieu",
        dataSort: dataSortColumn["ketQuaThamChieu"] || "",
        onClickSort: onClickSort,
        render: (value) => (value || []).join(", "),
      }),
      Column({
        title: t("hsba.tuTra"),
        sort_key: "tuTra",
        dataSort: dataSortColumn["tuTra"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "tuTra",
        key: "tuTra",
        i18Name: "hsba.tuTra",
        align: "center",
        onCell: sharedOnCell,
        render: (value) => <Checkbox checked={value} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("tuTra", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.khongTinhTien"),
        sort_key: "khongTinhTien",
        dataSort: dataSortColumn["khongTinhTien"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        i18Name: "hsba.khongTinhTien",
        align: "center",
        onCell: sharedOnCell,
        render: (value) => <Checkbox checked={value} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("khongTinhTien", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.thanhToanSau"),
        sort_key: "thanhToanSau",
        dataSort: dataSortColumn["thanhToanSau"] || "",
        onClickSort: onClickSort,
        width: "150px",
        dataIndex: "thanhToanSau",
        key: "thanhToanSau",
        i18Name: "hsba.thanhToanSau",
        align: "center",
        onCell: sharedOnCell,
        render: (item) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("thanhToanSau", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("hsba.khongThucHien"),
        width: "150px",
        dataIndex: "khongThucHien",
        key: "khongThucHien",
        i18Name: "hsba.khongThucHien",
        align: "center",
        onCell: sharedOnCell,
        render: (value, item) => <Checkbox checked={value} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("khongThucHien", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: t("common.moTaLuuy"),
        width: 200,
        dataIndex: "ghiChu",
        key: "ghiChu",
        i18Name: "common.moTaLuuy",
        onCell: sharedOnCell,
      }),
      Column({
        title: t("hsba.hinhThucThanhToanKsk"),
        width: 100,
        dataIndex: "hinhThucTtKsk",
        key: "hinhThucTtKsk",
        i18Name: "hsba.hinhThucThanhToanKsk",
        onCell: sharedOnCell,
        render: (item, data) => {
          return listhinhThucTtKsk.find((x) => x.id === item)?.ten;
        },
      }),
      Column({
        title: t("baoCao.nhomDichVuCap1"),
        width: 150,
        dataIndex: "tenNhomDichVuCap1",
        key: "tenNhomDichVuCap1",
        i18Name: "baoCao.nhomDichVuCap1",
        onCell: sharedOnCell,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhomDichVuCap1}
            defaultValue=""
            onChange={(e) => {
              onChangeSearch("dsNhomDichVuCap1Id", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
      Column({
        title: "ecrCl",
        width: 80,
        dataIndex: "ecrCl",
        key: "ecrCl",
        i18Name: "ecrCl",
        onCell: sharedOnCell,
      }),
      Column({
        title: "egf",
        width: 80,
        dataIndex: "egfr",
        key: "egfr",
        i18Name: "egfr",
        onCell: sharedOnCell,
      }),
      Column({
        title: (
          <>
            {t("common.thaoTac")}
            <Setting refTable={refSettings} />
          </>
        ),
        width: "150px",
        key: "thaoTac",
        align: "center",
        fixed: "right",
        ignore: true,
        onCell: sharedOnCell,
        render: (data, item) => {
          if (item.isParent) return null;

          const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
            { thoiGian: item?.thoiGianThucHien },
            listAllPhieuThuChotDot
          );

          const isThucHienTaiKhoa = (data.dsDoiTuongSuDung || []).includes(
            DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
          );

          return (
            <div className="d-flex justify-content-center">
              <Tooltip title={t("common.xemChiTiet")}>
                <SVG.IcEye
                  className="ic-action"
                  onClick={() =>
                    refModalChiTietDichVu.current &&
                    refModalChiTietDichVu.current.show({
                      ttDichVu: item,
                      ttNb: thongTinBenhNhanTongHop,
                    })
                  }
                />
              </Tooltip>

              {(data.loaiDichVu === LOAI_DICH_VU.CDHA ||
                checkDonViKetNoi(data)) && (
                <Tooltip title={t("hsba.xemKqPacs")} placement="bottomLeft">
                  <SVG.IcViewImagePacs
                    className="ic-action"
                    onClick={() => onViewPacs(data)}
                  />
                </Tooltip>
              )}
              {[
                TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
                TRANG_THAI_DICH_VU.DA_DUYET,
                TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU,
              ].includes(data.trangThai) &&
                [LOAI_DICH_VU.XET_NGHIEM, LOAI_DICH_VU.CDHA].includes(
                  data.loaiDichVu
                ) && (
                  <Tooltip title={t("hsba.xemKqPdf")} placement="bottomLeft">
                    <SVG.IcPdf
                      onClick={() => onViewPdf(data)}
                      className="ic-action"
                    />
                  </Tooltip>
                )}

              {flatten([
                TRANG_THAI_DICH_VU.CHO_TIEP_NHAN,
                TRANG_THAI_DICH_VU.DA_TIEP_NHAN,
                TRANG_THAI_DV_KHONG_THUC_HIEN.KHAM,
                TRANG_THAI_DV_KHONG_THUC_HIEN.XET_NGHIEM,
                TRANG_THAI_DV_KHONG_THUC_HIEN.CDHA,
                TRANG_THAI_DV_KHONG_THUC_HIEN.PTTT,
              ]).includes(data.trangThai) &&
                data.trangThaiHoan === 0 &&
                data.khongThucHien === false &&
                checkRole([ROLES["KHAM_BENH"].KHONG_THUC_HIEN]) && (
                  <Tooltip
                    title={t("hsba.khongThucHien")}
                    placement="bottomLeft"
                  >
                    <SVG.IcCloseCircle
                      color={"var(--color-red-primary)"}
                      onClick={() => onNoExecute(data)}
                      className="ic-action"
                    />
                  </Tooltip>
                )}

              {isThucHienTaiKhoa &&
                data.trangThai >= TRANG_THAI_DICH_VU.TIEP_NHAN_MAU &&
                data.trangThai < TRANG_THAI_DICH_VU.DA_CO_KET_QUA && (
                  <Tooltip title={t("common.coKetQua")} placement="bottom">
                    <SVG.IcTick
                      color="var(--color-green-primary)"
                      className="ic-action"
                      onClick={onCoKetQua(data)}
                    />
                  </Tooltip>
                )}

              {isChotThoiGianDotDieuTri && (
                <>
                  {isShowAction && (
                    <Tooltip
                      title={t("quanLyNoiTru.chinhSuaDichVu")}
                      placement="bottom"
                    >
                      <SVG.IcEdit
                        className="ic-action"
                        onClick={onEdit(item)}
                      />
                    </Tooltip>
                  )}
                  {isShowAction && (
                    <Tooltip
                      title={t("khamBenh.chiDinh.xoaDichVu")}
                      placement="bottom"
                    >
                      <SVG.IcDelete
                        className="ic-action-1"
                        onClick={onDelete(data)}
                      />
                    </Tooltip>
                  )}
                </>
              )}
            </div>
          );
        },
      }),
    ];

    const onChangePage = (page) => {
      onSearch({ page: page - 1 });
    };
    const onHandleSizeChange = (size) => {
      onSizeChange({ size: size });
    };
    const renderEmptyTextTable = () => {
      return (
        <div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
          />
        </div>
      );
    };

    const setRowClassName = (record) => {
      if (record.khongTinhTien) {
        return "green-color";
      }
      if (record.tuTra) {
        return "orange-color";
      }
    };

    return (
      <Main>
        <TableWrapper
          columns={columns}
          dataSource={listDVKTMemo}
          rowKey={(record) => record.id}
          tableName={tableName}
          ref={refSettings}
          locale={{
            emptyText: renderEmptyTextTable(),
          }}
          rowClassName={setRowClassName}
        />
        {!!totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listDichVuKyThuat}
            onShowSizeChange={onHandleSizeChange}
          />
        ) : null}

        <ModalChiTietDichVu ref={refModalChiTietDichVu} />
        <ChinhSuaDVKyThuat ref={refSuaThongTin} />
        <ModalScanPrint ref={refModalSignPrint} />
        <ModalNhapKetQuaDichVu ref={refModalNhapKetQua} />
      </Main>
    );
  }
);

export default DsDichVu;
