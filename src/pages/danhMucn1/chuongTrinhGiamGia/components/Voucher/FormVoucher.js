import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { t } from "i18next";
import { Input, Form, InputNumber } from "antd";
import { InputDecimal } from "components/common";
import { Checkbox, CreatedWrapper, Select } from "components";
import { useDispatch, useSelector } from "react-redux";
import { HOTKEY, ROLES } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import DanhSachNb from "./DanhSachNb";
const { TextArea } = Input;

const FormChuongTrinh = (
  {
    handleSubmit,
    onCancel,
    onComplete,
    onUndoComplete,
    onVerify,
    onUndoVerify,
    edited,
    dataSort,
    trangThai,
    onCreateMulti,
    editStatus,

    layerId,
    ...props
  },
  ref
) => {
  const [form] = Form.useForm();
  const formRef = React.useRef();
  const [dataEdit, setDataEdit] = useState(null);
  const [addMultiple, setAddMultiple] = useState(false);
  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const refAutoFocus = useRef(null);
  const { onRegisterHotkey } = useDispatch().phimTat;
  const listAllChuongTrinhGiamGia = useSelector(
    (state) => state.chuongTrinhGiamGia.listAllChuongTrinhGiamGia
  );
  // register layerId
  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
  }, []);

  const onSave = () => {
    form
      .validateFields()
      .then(({ soLuongConLai, ...values }) => {
        if (addMultiple) {
          onCreateMultiVoucher(values);
        } else {
          handleSubmit(values);
        }
      })
      .catch((error) => {});
  };
  refClickBtnSave.current = onSave;
  refClickBtnAdd.current = () => {
    form.resetFields();
    setDataEdit(null);
    setAddMultiple(false);
    if (refAutoFocus.current) refAutoFocus.current.focus();
  };

  const onCreateMultiVoucher = (values) => {
    let data = [];
    for (let index = values.maSoBatDau; index <= values.maSoKetThuc; index++) {
      let voucher = Object.assign({}, values);
      delete voucher.maSoBatDau;
      delete voucher.maSoKetThuc;
      voucher.ma = `${values.ma}${index}`;
      data.push(voucher);
    }
    onCreateMulti(data);
  };

  const onChangeAddMultiple = () => {
    setAddMultiple(!addMultiple);
  };

  useImperativeHandle(
    ref,
    () => ({
      setfields: (data) => {
        if (data?.info?.id) {
          form.setFieldsValue(data?.info);
          setDataEdit(data?.info);
          setAddMultiple(false);
        } else {
          form.resetFields();
          setDataEdit(null);
          setAddMultiple(false);
        }
      },
      resetFields: (data) => {
        form.resetFields();
        setDataEdit(null);
        setAddMultiple(false);
        setTimeout(() => {
          if (refAutoFocus.current) refAutoFocus.current.focus();
        }, 50);
      },
    }),
    []
  );

  return (
    <>
      <CreatedWrapper
        title={t("common.thongTinChiTiet")}
        onCancel={onCancel}
        cancelText={t("common.huy")}
        onOk={onSave}
        okText={t("common.luuF4")}
        roleSave={[ROLES["DANH_MUC"].CHUONG_TRINH_GIAM_GIA_THEM]}
        roleEdit={[ROLES["DANH_MUC"].CHUONG_TRINH_GIAM_GIA_SUA]}
        editStatus={editStatus}
      >
        <fieldset
          disabled={
            !checkRole([ROLES["DANH_MUC"].CHUONG_TRINH_GIAM_GIA_THEM]) &&
            !editStatus
          }
        >
          <Form
            ref={formRef}
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom"
          >
            <Form.Item
              label={t("danhMuc.chuongTrinhGiamGia")}
              name="chuongTrinhGiamGiaId"
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongChonChuongTrinhGiamGia"),
                },
              ]}
            >
              <Select
                refSelect={refAutoFocus}
                data={listAllChuongTrinhGiamGia}
                placeholder={t("danhMuc.vuiLongNhapMaVoucher")}
              />
            </Form.Item>
            {!dataEdit && (
              <Form.Item label=" " name="addMultiple" valuePropName="checked">
                <Checkbox onChange={onChangeAddMultiple}>
                  {t("danhMuc.themMoiHangLoat")}
                </Checkbox>
              </Form.Item>
            )}
            {dataEdit && <Form.Item />}
            <Form.Item
              label={t("danhMuc.maVoucher")}
              name="ma"
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongNhapMaVoucher"),
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapMaVoucher")}
              />
            </Form.Item>
            {addMultiple && (
              <Form.Item
                label={t("danhMuc.maSoBatDau")}
                name="maSoBatDau"
                style={{ width: "25%" }}
                rules={[
                  {
                    required: true,
                    message: t("danhMuc.vuiLongNhapMaSoBatDau"),
                  },
                ]}
              >
                <InputNumber
                  className="input-option"
                  placeholder={t("danhMuc.maSoBatDau")}
                  min={0}
                />
              </Form.Item>
            )}
            {addMultiple && (
              <Form.Item
                label={t("danhMuc.maSoKetThuc")}
                name="maSoKetThuc"
                style={{ width: "25%" }}
                rules={[
                  {
                    required: true,
                    message: t("danhMuc.vuiLongNhapMaSoKetThuc"),
                  },
                ]}
              >
                <InputNumber
                  className="input-option"
                  placeholder={t("danhMuc.maSoKetThuc")}
                  min={1}
                />
              </Form.Item>
            )}
            <Form.Item
              label={t("common.soLuong")}
              name="soLuong"
              rules={[
                {
                  required: true,
                  message: t("common.vuiLongNhapSoLuong"),
                },
                {
                  validator: (_, value, callback) => {
                    if (value < form.getFieldValue("soLuongDaSuDung")) {
                      callback(
                        new Error(t("danhMuc.soLuongPhaiSoLuongDaSuDung"))
                      );
                    } else {
                      callback();
                    }
                  },
                },
              ]}
            >
              <InputDecimal
                placeholder={t("danhMuc.vuiLongNhapSoLuong")}
                min={1}
              />
            </Form.Item>

            <Form.Item
              label={t("danhMuc.soLuongDaSuDung")}
              name="soLuongDaSuDung"
            >
              <Input
                className="input-option"
                readOnly
                placeholder={t("danhMuc.vuiLongNhapSoLuongDaSuDung")}
              />
            </Form.Item>

            <Form.Item
              label={t("danhMuc.soLuongConLai")}
              name="soLuongConLai"
              valuePropName="none"
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapSoLuong")}
                readOnly
                value={
                  parseInt(form.getFieldValue("soLuong") || 0) -
                  parseInt(form.getFieldValue("soLuongDaSuDung") || 0)
                }
              />
            </Form.Item>
            {addMultiple && <Form.Item />}
            <Form.Item
              label={t("danhMuc.moTa")}
              name="moTa"
              style={{ width: "200%" }}
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongNhapMoTa"),
                },
              ]}
            >
              <TextArea placeholder={t("danhMuc.vuiLongNhapMoTa")} />
            </Form.Item>

            {dataEdit && (
              <Form.Item name="active" valuePropName="checked">
                <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
              </Form.Item>
            )}
          </Form>
        </fieldset>
        <DanhSachNb />
      </CreatedWrapper>
    </>
  );
};

export default forwardRef(FormChuongTrinh);
