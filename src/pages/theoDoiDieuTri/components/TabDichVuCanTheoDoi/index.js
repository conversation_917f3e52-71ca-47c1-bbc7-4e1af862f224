import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import moment from "moment";
import { useEnum, useLoading } from "hooks";
import printProvider from "data-access/print-provider";
import {
  EllipsisText,
  HeaderSearch,
  TableWrapper,
  Tooltip,
  Select,
  Checkbox,
  HTMLReactParser,
} from "components";
import {
  TRANG_THAI_DICH_VU,
  LOAI_IN,
  ENUM,
  LOAI_DICH_VU,
  YES_NO,
} from "constants/index";
import { isArray, locPhieuLisPacs, openInNewTab } from "utils";
import { SVG } from "assets";
import { orderBy } from "lodash";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import ModalScanPrint from "pages/hoSoBenhAn/components/ModalScanPrint";
import stringUtils from "mainam-react-native-string-utils";

const { Column, Setting } = TableWrapper;

const TabDichVuCanTheoDoi = ({ data: _data, getData, getDataSave }) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const refModalSignPrint = useRef(null);

  const [dataTRANG_THAI_DICH_VU] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const { showLoading, hideLoading } = useLoading();
  const [data, setData] = useState([]);
  const [state, _setState] = useState({});

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    setData(_data);
  }, [_data]);

  const {
    pacs: { getUrl },
    choTiepDonDV: { getPhieuKetQua },
    nbXetNghiem: { getKetQuaXNPdf },
    chiDinhKhamBenh: { inPhieuKetQua },
  } = useDispatch();

  const listTrangThaiDichVuMemo = useMemo(() => {
    return dataTRANG_THAI_DICH_VU.reduce((acc, item) => {
      acc[item.id] = item.ten;
      return acc;
    }, {});
  }, [dataTRANG_THAI_DICH_VU]);

  const dataMemo = useMemo(() => {
    return orderBy(
      data,
      (item) => item.trangThai < TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
      "asc"
    ).sort((a, b) => {
      if (a.trangThai === b.trangThai) {
        return a.tenDichVu.localeCompare(b.tenDichVu);
      }
      return 0;
    });
  }, [data]);

  const onViewPdf = (data) => async () => {
    if ([TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(data?.trangThai)) {
      try {
        showLoading();
        const getResult =
          data?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
            ? getKetQuaXNPdf
            : getPhieuKetQua;

        const s = await getResult({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          dsSoKetNoi: [data?.soKetNoi],
        });
        onPrintPdf(s);
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const onInVaKy = (data) => async () => {
    if ([TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(data?.trangThai)) {
      try {
        showLoading();
        // const getResult =
        //   data?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
        //     ? getKetQuaXNPdf
        //     : getPhieuKetQua;

        const s = await inPhieuKetQua({
          loaiDichVu: data?.loaiDichVu,
          nbDotDieuTriId: data?.nbDotDieuTriId,
          dsSoKetNoi: [data?.soKetNoi],
          returnData: true,
        });

        if (!s.length) return;
        const _listPhieu = s.map((item) => {
          return {
            ...item,
            key: stringUtils.guid(),
            data: {
              filePdf: item.dsDuongDan
                ? item.dsDuongDan.length
                  ? item.dsDuongDan[0]
                  : null
                : item.file.pdf,
            },
            ten: `Kết quả pdf`,
            nbDotDieuTriId: item.nbDotDieuTriId || data?.nbDotDieuTriId,
            trangThai: item?.lichSuKy?.trangThai,
            baoCaoId: item.baoCaoId,
            soPhieu: item.soPhieu,
            lichSuKy: item.lichSuKy,
          };
        });

        const listPhieu = await Promise.all(
          _listPhieu.map(async (item) => {
            if (item.lichSuKy) {
              const itemDaKy = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                id: item.lichSuKy?.id,
                chuKySo: item.lichSuKy?.chuKySo,
              });

              return {
                ...item,
                data: { filePdf: itemDaKy.data?.file?.pdf },
              };
            }
            return item;
          })
        );

        refModalSignPrint.current &&
          refModalSignPrint.current.show(
            {
              dsPhieu: listPhieu,
              isSignPdf: true,
              nbDotDieuTriId: data?.nbDotDieuTriId,
              baoCaoId: listPhieu[0].baoCaoId,
            },
            async () => {
              const s = await inPhieuKetQua({
                loaiDichVu: data?.loaiDichVu,
                nbDotDieuTriId: data?.nbDotDieuTriId,
                dsSoKetNoi: [data?.soKetNoi],
                returnData: true,
              });
              const _listPhieu = s.map((item) => {
                return {
                  ...item,
                  key: stringUtils.guid(),
                  data: {
                    filePdf: item.dsDuongDan
                      ? item.dsDuongDan.length
                        ? item.dsDuongDan[0]
                        : null
                      : item.file.pdf,
                  },
                  ten: `Kết quả pdf`,
                  nbDotDieuTriId: item.nbDotDieuTriId || data?.nbDotDieuTriId,
                  trangThai: item?.lichSuKy?.trangThai,
                  baoCaoId: item.baoCaoId,
                  soPhieu: item.soPhieu,
                  lichSuKy: item.lichSuKy,
                };
              });

              const listPhieu = await Promise.all(
                _listPhieu.map(async (item) => {
                  if (item.lichSuKy) {
                    const itemDaKy =
                      await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                        id: item.lichSuKy?.id,
                        chuKySo: item.lichSuKy?.chuKySo,
                      });

                    return {
                      ...item,
                      data: { filePdf: itemDaKy.data?.file?.pdf },
                    };
                  }
                  return item;
                })
              );
              return listPhieu;
            }
          );
        // onPrintPdf(s);
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    }
  };

  const onPrintPdf = async (s) => {
    const dsPhieu = locPhieuLisPacs(s, {
      allData: false,
      isLocPhieu: true,
      isCdha: true,
    });
    const dsPhieuFull = locPhieuLisPacs(s, {
      allData: true,
      isLocPhieu: true,
      isCdha: true,
    });
    if (
      isArray(dsPhieuFull, true) &&
      dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
    ) {
      // [].every luôn luôn true
      const finalFile = await printProvider.getMergePdf(dsPhieu);
      openInNewTab(finalFile);
    } else {
      printProvider.printPdf(dsPhieuFull);
    }
  };

  const onViewPacs = (data) => async () => {
    if (data?.guiPacs) {
      try {
        showLoading();
        const s = await getUrl({ id: data?.id });
        if (s) window.open(s, "_blank").focus();
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const renderActionButton = (type, data) => {
    switch (type) {
      case "inVaKy":
        return (
          <Tooltip title={t("phieuIn.inVaKy")}>
            <SVG.IcPrint
              className={classNames("ic-action", {
                disabled: ![TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(
                  data?.trangThai
                ),
              })}
              onClick={onInVaKy(data)}
            />
          </Tooltip>
        );
      case "viewPdf":
        return (
          <Tooltip title={t("cdha.xemKetQuaPdf")}>
            <SVG.IcPdf
              className={classNames("ic-action", {
                disabled: ![TRANG_THAI_DICH_VU.DA_CO_KET_QUA].includes(
                  data?.trangThai
                ),
              })}
              onClick={onViewPdf(data)}
            />
          </Tooltip>
        );
      case "viewPacs":
        return (
          <Tooltip title={t("cdha.xemKetQuaPacs")}>
            <SVG.IcViewImagePacs
              className={classNames("ic-action", {
                disabled: !data.guiPacs,
              })}
              onClick={onViewPacs(data)}
            />
          </Tooltip>
        );
    }
  };

  const onSearch = (dsTrangThai) => {
    return getData(
      { dsTrangThai },
      {
        onSucess: (data) => {
          setData(data?.dsDichVu || []);
        },
        ignoreSetState: true,
      }
    );
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      fixed: "left",
      render: (_, __, index) => index + 1,
    }),
    Column({
      title: t("thuNgan.maDv"),
      width: 120,
      dataIndex: "maDichVu",
      key: "maDichVu",
      i18Name: "thuNgan.maDv",
    }),
    Column({
      title: t("xetNghiem.tenDV"),
      width: 150,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "xetNghiem.tenDV",
    }),
    Column({
      title: t("common.trangThai"),
      renderSearch: (
        <Select
          data={[
            {
              id: "63,66,90",
              ten: t("quanLyNoiTru.chuaCoKetQua"),
            },
            {
              id: "155,160",
              ten: t("xetNghiem.daCoKetQua"),
            },
          ]}
          placeholder={t("danhMuc.chonTitle", {
            title: t("common.trangThai").toLowerCase(),
          })}
          onChange={onSearch}
          defaultValue={"63,66,90,155,160"}
          hasAllOption={true}
          optionAllValue="63,66,90,155,160"
        />
      ),
      selectSearch: true,
      width: 150,
      dataIndex: "trangThai",
      key: "trangThai",
      align: "center",
      i18Name: "common.trangThai",
      render: (item) => (
        <EllipsisText.Tooltip
          content={listTrangThaiDichVuMemo[item]}
          limitLine={3}
        />
      ),
    }),
    Column({
      title: t("quanLyNoiTru.dichVuCoKetQuaLau"),
      renderSearch: (
        <Select
          data={YES_NO}
          placeholder={t("danhMuc.chonTitle", {
            title: t("quanLyNoiTru.dichVuCoKetQuaLau").toLowerCase(),
          })}
          onChange={(e) => {
            return getData(
              { ketQuaLau: e === "true" },
              {
                onSucess: (data) => {
                  setData(data?.dsDichVu || []);
                },
                ignoreSetState: true,
              }
            );
          }}
        />
      ),
      selectSearch: true,
      width: 120,
      dataIndex: "ketQuaLau",
      key: "ketQuaLau",
      align: "center",
      i18Name: "quanLyNoiTru.dichVuCoKetQuaLau",
      render: (val) => <Checkbox checked={val} readonly />,
    }),
    Column({
      title: t("quanLyNoiTru.daThongBao"),
      width: 100,
      dataIndex: "trangThaiThongBao",
      key: "trangThaiThongBao",
      i18Name: "quanLyNoiTru.daThongBao",
      align: "center",
      render: (val, record) => (
        <Checkbox checked={val === 50} readonly></Checkbox>
      ),
    }),
    Column({
      title: t("xetNghiem.daGuiLis"),
      width: 80,
      dataIndex: "guiLis",
      key: "guiLis",
      i18Name: "xetNghiem.daGuiLis",
      align: "center",
      render: (item) => <Checkbox checked={item}></Checkbox>,
    }),
    Column({
      title: t("cdha.daGuiPacs"),
      width: 100,
      dataIndex: "guiPacs",
      key: "guiPacs",
      i18Name: "cdha.daGuiPacs",
      align: "center",
      render: (item) => <Checkbox checked={item}></Checkbox>,
    }),
    Column({
      title: t("thuNgan.phongThucHien"),
      width: 200,
      dataIndex: "tenPhongThucHien",
      key: "tenPhongThucHien",
      i18Name: "thuNgan.phongThucHien",
    }),
    Column({
      title: t("common.ngayThucHien"),
      width: 120,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "common.ngayThucHien",
      align: "center",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY");
      },
    }),
    Column({
      title: t("theoDoiDieuTri.ngayTiepNhan"),
      width: 120,
      dataIndex: "thoiGianTiepNhan",
      key: "thoiGianTiepNhan",
      i18Name: "theoDoiDieuTri.ngayTiepNhan",
      align: "center",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY");
      },
    }),
    Column({
      title: t("khamSucKhoe.lenLich.ngayLayMau"),
      width: 120,
      dataIndex: "thoiGianLayMau",
      key: "thoiGianLayMau",
      i18Name: "khamSucKhoe.lenLich.ngayLayMau",
      align: "center",
      align: "center",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY");
      },
    }),
    Column({
      title: t("common.ketQua"),
      width: 100,
      dataIndex: "ketQua",
      key: "ketQua",
      i18Name: "common.ketQua",
      render: (value) => (
        <EllipsisText.Tooltip
          content={HTMLReactParser(value || "")}
          limitLine={3}
          overlayClassName="ellipsis-text-tooltip-ket-qua"
        />
      ),
    }),
    Column({
      title: t("common.ketLuan"),
      width: 200,
      dataIndex: "ketLuan",
      key: "ketLuan",
      i18Name: "common.ketLuan",
      render: (value) => (
        <EllipsisText.Tooltip
          content={HTMLReactParser(value || "")}
          limitLine={3}
        />
      ),
    }),
    Column({
      title: t("xetNghiem.giaTriThamChieu"),
      width: 150,
      dataIndex: "ketQuaThamChieu",
      key: "ketQuaThamChieu",
      i18Name: "xetNghiem.giaTriThamChieu",
    }),
    Column({
      title: t("xetNghiem.bsChiDinh"),
      width: 200,
      dataIndex: "vietTatHhhvBsChiDinh",
      key: "vietTatHhhvBsChiDinh",
      i18Name: "xetNghiem.bsChiDinh",
      render: (_, record) => (
        <>
          {record.vietTatHhhvBsChiDinh}
          {record.tenBacSiChiDinh}
        </>
      ),
    }),
    Column({
      title: t("xetNghiem.thoiGianCoKetQua"),
      width: 150,
      dataIndex: "thoiGianCoKetQua",
      key: "thoiGianCoKetQua",
      align: "center",
      i18Name: "xetNghiem.thoiGianCoKetQua",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    }),
    Column({
      title: t("quanLyNoiTru.toDieuTri.dvt"),
      width: 120,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "quanLyNoiTru.toDieuTri.dvt",
    }),
    Column({
      title: t("common.soLuong"),
      width: 80,
      dataIndex: "soLuong",
      key: "soLuong",
      i18Name: "common.soLuong",
    }),
    Column({
      title: (
        <>
          {t("common.xemChiTiet")}
          <Setting refTable={refSettings} />
        </>
      ),
      width: 120,
      align: "center",
      fixed: "right",
      ignore: true,
      render: (_, record) => {
        return (
          <>
            {[LOAI_DICH_VU.XET_NGHIEM, LOAI_DICH_VU.CDHA].includes(
              record.loaiDichVu
            ) && renderActionButton("viewPdf", record)}
            {[LOAI_DICH_VU.CDHA].includes(record.loaiDichVu) &&
              renderActionButton("viewPacs", record)}
            {[LOAI_DICH_VU.XET_NGHIEM, LOAI_DICH_VU.CDHA].includes(
              record.loaiDichVu
            ) && renderActionButton("inVaKy", record)}
          </>
        );
      },
    }),
  ];
  const onCheckAll = (e) => {
    const selectedRowKeys = e.target?.checked ? dataMemo?.map((o) => o.id) : [];
    getDataSave(e.target?.checked ? dataMemo : []);
    setState({
      selectedRowKeys: selectedRowKeys,
      isCheckedAll: e.target?.checked,
    });
  };

  const onChangeSelection = (selectedRowKeys, data) => {
    getDataSave(data);
    setState({ selectedRowKeys });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={<Checkbox onChange={onCheckAll} checked={state.isCheckedAll} />}
      />
    ),
    // columnWidth: 50,
    onChange: onChangeSelection,
    selectedRowKeys: state.selectedRowKeys,
    preserveSelectedRowKeys: true,
  };

  return (
    <>
      <TableWrapper
        columns={columns}
        dataSource={dataMemo || []}
        rowSelection={rowSelection}
        tableName="table-tab-dich-vu-can-theo-doi"
        columnResizable
        ref={refSettings}
        rowKey={(record) => record?.id}
      />

      <ModalScanPrint ref={refModalSignPrint} />
    </>
  );
};

export default TabDichVuCanTheoDoi;
