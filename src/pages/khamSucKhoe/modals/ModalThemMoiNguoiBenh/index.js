import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useState,
  useRef,
} from "react";
import { Main, MainHeader } from "./styled";
import FormWraper from "components/FormWraper";
import { Input, Form, Row, Col, message } from "antd";
import {
  DatePicker,
  Checkbox,
  Select,
  Button,
  ModalTemplate,
  DOBInput,
  AddressFull,
} from "components";
import { useDispatch, useSelector } from "react-redux";
import { groupBy, intersectionBy } from "lodash";
import moment from "moment";
import { useTranslation } from "react-i18next";
import {
  useEnum,
  useStore,
  useListAll,
  useThietLap,
  useRefFunc,
  useCache,
  useQueryAll,
} from "hooks";
import {
  ENUM,
  HINH_THUC_TT_HD_KSK,
  LOAI_QUAY,
  THIET_LAP_CHUNG,
  ROLES,
  HOTKEY,
  CACHE_KEY,
} from "constants/index";
import { SVG } from "assets";
import { useHistory } from "react-router-dom";
import { checkRole } from "lib-utils/role-utils";
import { isArray } from "utils/index";
import { useKeDichVuKham } from "pages/tiepDon/KeDichVuKham";
import { query } from "redux-store/stores";

const ModalThemMoiNguoiBenh = (props, ref) => {
  const { gotoKeDichVuTiepDon } = useKeDichVuKham();
  const history = useHistory();

  const { t } = useTranslation();
  const refModal = useRef(null);
  const refInput = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  //state
  const [state, _setState] = useState({
    show: false,
    diaChi: "",
    isThemMoi: true,
    quocTichId: null,
    doiTuong: null,
    loaiDoiTuongId: null,
    loaiGiayTo: null,
    danTocId: null,
    xaPhuongId: null,
    chiNamSinh: false,
    loading: false,
    renderReady: false,
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  //redux
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listHinhThucTtDvHdKsk] = useEnum(ENUM.HINH_THUC_TT_DV_HD_KSK);
  const { chiTietHopDong } = useSelector((state) => state.hopDongKSK);
  const { dsGoi, dsDichVuLe } = useSelector((state) => state.dichVuKSK);
  const thongTinNb = useStore("nbKSK.thongTinNb");
  const authId = useStore("auth.auth.id");

  const { listAllQuocGia } = useSelector((state) => state.ttHanhChinh);
  const { data: listKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan
  );
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon", []);
  const listMoiQuanHe = useStore("moiQuanHe.listAllQuanHe", []);
  const [listAllNguonNguoiBenh] = useListAll("nguonNguoiBenh", {}, false);
  const [listAllNguoiGioiThieu] = useListAll("nguoiGioiThieu", {}, false);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, false);
  const quayTiepDonId = Form.useWatch("quayTiepDonId", form);
  const [listAllNhanVien] = useListAll(
    "nhanVien",
    {
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
    },
    true
  );
  const [quayTiepDon, setQuayTiepDon, loadFinish] = useCache(
    authId + LOAI_QUAY.TIEP_DON,
    CACHE_KEY.DATA_NHA_TAM_UNG,
    null,
    false,
    true
  );

  const {
    nbKSK: {
      onSelectAddress,
      postNBKSK,
      patchNbKSK,
      getNBKSK,
      updateData,
      tiepDonNBKSK,
    },
    dichVuKSK: { getDsDichVuTheoGoi },
    hopDongKSK: { searchMaNb },
    loaiDoiTuong: { getListAllLoaiDoiTuong },
    moiQuanHe: { getListAllQuanHe },
    address: { getAllData },
    ttHanhChinh: { getListAllQuocGia },
    quayTiepDon: { getAllQuayTiepDon },
  } = useDispatch();

  const [dataQUOC_TICH_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.QUOC_TICH_MAC_DINH
  );
  const [dataQUOC_GIA_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.QUOC_GIA_MAC_DINH
  );
  const [dataTU_THEM_STT_HD] = useThietLap(THIET_LAP_CHUNG.TU_THEM_STT_HD);
  const [dataTIEP_DON_NB_KSK_TRUC_TIEP] = useThietLap(
    THIET_LAP_CHUNG.TIEP_DON_NB_KSK_TRUC_TIEP
  );

  //memo
  const listGoiMemo = useMemo(() => {
    const _groupGoi = groupBy(dsGoi, "boChiDinhId");

    return Object.keys(_groupGoi).map((x) => ({
      id: x,
      ten: _groupGoi[x][0]?.tenBoChiDinh,
    }));
  }, [dsGoi]);

  const dsDichVuLeMemo = useMemo(() => {
    return dsDichVuLe.map((x) => ({ ...x, ten: x.tenDichVu, id: x.dichVuId }));
  }, [dsDichVuLe]);

  const diaChiMemo = useMemo(() => {
    let quocGiaId = 1,
      quocTichId = 1;

    if (listAllQuocGia && dataQUOC_GIA_MAC_DINH && dataQUOC_TICH_MAC_DINH) {
      const defaultQuocTich = (listAllQuocGia || []).find(
        (x) => x.ma == dataQUOC_TICH_MAC_DINH
      );
      if (defaultQuocTich) {
        quocTichId = defaultQuocTich.id;
      }
      const defaultQuocGia = (listAllQuocGia || []).find(
        (x) => x.ma == dataQUOC_GIA_MAC_DINH
      );

      if (defaultQuocGia) {
        quocGiaId = defaultQuocGia.id;
      }
    }

    return {
      quocGiaId,
      quocTichId,
    };
  }, [listAllQuocGia, dataQUOC_GIA_MAC_DINH, dataQUOC_TICH_MAC_DINH]);

  useEffect(() => {
    getListAllLoaiDoiTuong({ page: "", size: "", active: true });
    getListAllQuanHe({ page: "", size: "" });
    getListAllQuocGia({ page: "", size: "", active: true });
    getAllData();
    getAllQuayTiepDon({ dsLoaiQuay: LOAI_QUAY.TIEP_DON, active: true });
  }, []);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
      setTimeout(() => refInput.current && refInput.current.focus(), 500);
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (state.show && loadFinish && !state.renderReady) {
      if (thongTinNb.id) {
        const {
          maNb,
          maHoSo,
          tenNb,
          soDienThoai,
          gioiTinh,
          ngaySinh,
          email,
          noiLamViec,
          chiNamSinh,
          nbKhamSucKhoe,
          nbGiayToTuyThan,
          nbDiaChi,
          khoaId,
          nbNguoiBaoLanh,
          loaiDoiTuongId,
          nbNguonNb,
          uuTien,
        } = thongTinNb;
        const _diaChi = nbDiaChi?.diaChi
          ? nbDiaChi?.diaChi
          : nbDiaChi?.tinhThanhPho
          ? `${nbDiaChi?.xaPhuong?.ten ? nbDiaChi?.xaPhuong?.ten : ""}${
              nbDiaChi?.quanHuyen?.ten ? `, ${nbDiaChi?.quanHuyen?.ten}` : ""
            }${
              nbDiaChi?.tinhThanhPho?.ten
                ? `, ${nbDiaChi?.tinhThanhPho?.ten}`
                : ""
            }`
          : "";
        const {
          hoTen: hoTenNguoiBaoLanh,
          moiQuanHeId,
          soDienThoai: sdtNguoiBaoLanh,
        } = nbNguoiBaoLanh || {};
        setState({ sdtNguoiBaoLanh, renderReady: true });
        form.setFieldsValue({
          stt: nbKhamSucKhoe?.stt,
          maNb,
          maHoSo,
          tenNb,
          soDienThoai,
          email,
          noiLamViec,
          dsBoChiDinhId: (nbKhamSucKhoe?.dsBoChiDinhId || []).map(
            (x) => `${x}`
          ),
          dsDichVuId: nbKhamSucKhoe?.dsDichVuId,
          hinhThucTtDvNgoaiHd: nbKhamSucKhoe?.hinhThucTtDvNgoaiHd,
          hinhThucTtDvTrongHd: nbKhamSucKhoe?.hinhThucTtDvTrongHd,
          gioiTinh,
          ngaySinh: ngaySinh && {
            str: chiNamSinh
              ? moment(ngaySinh).format("YYYY")
              : moment(ngaySinh).format("DD/MM/YYYY"),
            date: ngaySinh,
          },
          chucVu: nbKhamSucKhoe?.chucVu,
          maNhanVien: nbKhamSucKhoe?.maNhanVien,
          phongBan: nbKhamSucKhoe?.phongBan,
          ngoaiVien: nbKhamSucKhoe?.ngoaiVien,
          denThoiGianKham: nbKhamSucKhoe?.denThoiGianKham
            ? moment(nbKhamSucKhoe?.denThoiGianKham)
            : null,
          denThoiGianLayMau: nbKhamSucKhoe?.denThoiGianLayMau
            ? moment(nbKhamSucKhoe?.denThoiGianLayMau)
            : null,
          tuThoiGianKham: nbKhamSucKhoe?.tuThoiGianKham
            ? moment(nbKhamSucKhoe?.tuThoiGianKham)
            : null,
          tuThoiGianLayMau: nbKhamSucKhoe?.tuThoiGianLayMau
            ? moment(nbKhamSucKhoe?.tuThoiGianLayMau)
            : null,
          diaDiemKham: nbKhamSucKhoe?.diaDiemKham,
          diaDiemLayMau: nbKhamSucKhoe?.diaDiemLayMau,
          diaChi: _diaChi,
          soNha: nbDiaChi?.soNha,
          maSo: nbGiayToTuyThan?.maSo,
          noiCap: nbGiayToTuyThan?.noiCap,
          ngayCap: nbGiayToTuyThan?.ngayCap
            ? moment(nbGiayToTuyThan?.ngayCap)
            : null,
          khoaId,
          hoTenNguoiBaoLanh,
          moiQuanHeId,
          loaiDoiTuongId,
          nguonNbId: nbNguonNb?.nguonNbId,
          nguoiGioiThieuId: nbNguonNb?.nguoiGioiThieuId,
          quayTiepDonId: quayTiepDon?.id,
          bacSiChiDinhId: nbKhamSucKhoe?.bacSiChiDinhId,
          uuTien,
        });
      } else {
        form.setFieldValue("quayTiepDonId", quayTiepDon?.id);
        setState({ renderReady: true });
      }
    }
  }, [thongTinNb, quayTiepDon, state.show, loadFinish]);

  useEffect(() => {
    if (chiTietHopDong?.id) {
      getDsDichVuTheoGoi({ hopDongKskId: chiTietHopDong.id, trongGoi: true });
      getDsDichVuTheoGoi({
        hopDongKskId: chiTietHopDong.id,
        trongGoi: false,
      });
    }
  }, [chiTietHopDong]);

  useImperativeHandle(ref, () => ({
    show: (data = {}, callback = () => {}) => {
      setState({
        show: true,
        currentItem: data,
        isThemMoi: !data?.id,
      });
      if (data.id) {
        getNBKSK(data.id);
      } else {
        form.setFieldsValue({
          hinhThucTtDvNgoaiHd: HINH_THUC_TT_HD_KSK.TU_THANH_TOAN,
          hinhThucTtDvTrongHd: HINH_THUC_TT_HD_KSK.THANH_TOAN_THEO_HOP_DONG,
        });
      }

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show && isArray(listAllLoaiDoiTuong, true)) {
      let khoaId;
      let listLoaiDoiTuongKsk = listAllLoaiDoiTuong.filter(
        (o) => o.khamSucKhoe
      );
      if (quayTiepDonId) {
        const quayTiepDon = listAllQuayTiepDon?.find(
          (o) => o.id === quayTiepDonId
        );
        listLoaiDoiTuongKsk = intersectionBy(
          listLoaiDoiTuongKsk,
          quayTiepDon?.dsLoaiDoiTuongGioiHan,
          "ma"
        );
        khoaId = listKhoaTheoTaiKhoan?.find(
          (o) => o.id === quayTiepDon?.khoaId
        )?.id;
      }
      setState({ listLoaiDoiTuongKsk });
      //nếu chỉnh sửa nb thì ko set mặc định
      if (!state.currentItem?.id) {
        form.setFieldsValue({
          loaiDoiTuongId: listLoaiDoiTuongKsk?.[0]?.id,
          khoaId,
        });
      }
    }
  }, [quayTiepDonId, listAllLoaiDoiTuong, state.show, state.currentItem]);

  //function
  const onClose = () => {
    form.resetFields();
    updateData({ thongTinNb: {} });
    setState({
      show: false,
      loading: false,
      sdtNguoiBaoLanh: null,
      renderReady: false,
    });
  };

  const onFinish = (values) => {
    console.log("Success:", values);
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  const onChangeAdrressText = (e) => {
    if (e != state.diaChi) {
      setState({ diaChi: e });
      form.setFields([{ name: "diaChi", value: e }]);
    }
  };

  const onFormValueChange = (changeValues) => {
    if (changeValues.hasOwnProperty("quayTiepDonId")) {
      let quayTiepDonId = changeValues.quayTiepDonId;
      const quayTiepDon = listAllQuayTiepDon?.find(
        (item) => item.id === quayTiepDonId
      );
      setQuayTiepDon(quayTiepDon);
    }
  };

  const onErrorAddress = (address, listSuggest) => {
    message.error(t("tiepDon.diaChiHanhChinhKhongHopLe"));
  };

  const onSave = useRefFunc(() => {
    form
      .validateFields()
      .then(() => {
        const {
          stt,
          maSo,
          email,
          soDienThoai,
          ngaySinh,
          tenNb,
          gioiTinh,
          chucVu,
          maNhanVien,
          dsBoChiDinhId,
          maNb,
          ngoaiVien,
          phongBan,
          noiLamViec,
          dsDichVuId,
          denThoiGianKham,
          denThoiGianLayMau,
          tuThoiGianKham,
          tuThoiGianLayMau,
          diaDiemKham,
          diaDiemLayMau,
          hinhThucTtDvNgoaiHd,
          hinhThucTtDvTrongHd,
          soNha,
          khoaId,
          hoTenNguoiBaoLanh,
          moiQuanHeId,
          loaiDoiTuongId,
          ngayCap,
          noiCap,
          nguonNbId,
          nguoiGioiThieuId,
          bacSiChiDinhId,
          uuTien,
        } = form.getFieldsValue();

        if (isValidSdtBaoLanh) return;
        const {
          quocGiaId,
          tinhThanhPhoId,
          quocTichId,
          loaiGiayTo,
          danTocId,
          xaPhuongId,
          chiNamSinh,
          sdtNguoiBaoLanh,
        } = state;

        const payload = {
          tenNb,
          gioiTinh,
          chiNamSinh,
          ngaySinh:
            ngaySinh?.date instanceof moment
              ? ngaySinh.date.format("YYYY-MM-DD")
              : ngaySinh.date,
          soDienThoai,
          email,
          quocTichId: quocTichId || diaChiMemo?.quocTichId,
          doiTuong: 1,
          loaiDoiTuongId,
          nbDiaChi: {
            quocGiaId: quocGiaId || diaChiMemo?.quocGiaId,
            tinhThanhPhoId,
            xaPhuongId,
            soNha,
          },
          nbGiayToTuyThan: {
            loaiGiayTo: loaiGiayTo || 1,
            maSo: maSo,
            noiCap: noiCap,
            ngayCap:
              ngayCap instanceof moment
                ? ngayCap.format("YYYY-MM-DD")
                : ngayCap,
          },
          nbNguoiBaoLanh: {
            hoTen: hoTenNguoiBaoLanh,
            moiQuanHeId,
            soDienThoai: sdtNguoiBaoLanh,
          },
          khoaId,
          maNb,
          danTocId,
          khamSucKhoe: true,
          nbKhamSucKhoe: {
            stt,
            hopDongKskId: chiTietHopDong?.id,
            dsBoChiDinhId,
            dsDichVuId,
            maNhanVien,
            chucVu,
            phongBan,
            ngoaiVien,
            hinhThucTtDvNgoaiHd,
            hinhThucTtDvTrongHd,
            bacSiChiDinhId,
            ...(!state.isThemMoi
              ? {
                  denThoiGianKham,
                  denThoiGianLayMau,
                  tuThoiGianKham,
                  tuThoiGianLayMau,
                  diaDiemKham,
                  diaDiemLayMau,
                }
              : {}),
          },
          uuTien,
          noiLamViec,
          nbNguonNb: { nguonNbId, nguoiGioiThieuId },
        };
        setState({ loading: true });
        if (state.isThemMoi) {
          postNBKSK(payload)
            .then(async (res) => {
              onClose();

              if (
                dataTIEP_DON_NB_KSK_TRUC_TIEP &&
                dataTIEP_DON_NB_KSK_TRUC_TIEP.toLowerCase() == "true"
              ) {
                if (checkRole([ROLES["TIEP_DON"].TIEP_NHAN_NHIEU_KSK])) {
                  //thực hiện tiếp đón nb ksk
                  await tiepDonNBKSK([res?.id]);
                }
                gotoKeDichVuTiepDon(res?.id, false);
              } else {
                history.push(
                  `/quan-ly-tiep-don/danh-sach-nb-da-tiep-don/${res?.id}`
                );
              }
            })
            .catch(() => setState({ loading: false }));
        } else {
          patchNbKSK({
            id: state.currentItem?.id,
            ...payload,
          })
            .then(() => {
              if (dataTIEP_DON_NB_KSK_TRUC_TIEP?.eval()) {
                gotoKeDichVuTiepDon(state.currentItem?.id, false);
              } else {
                props.refreshList();
              }
              onClose();
            })
            .catch(() => setState({ loading: false }));
        }
      })
      .catch((e) => {
        message.error("Vui lòng điền đủ thông tin");
      });
  });

  const selectAdress = (data) => {
    onSelectAddress(data).then((address) => {
      form.setFields([{ name: "diaChi", value: address.diaChi }]);
      setState({
        quocGiaId: address.quocGiaId,
        tinhThanhPhoId: address.tinhThanhPhoId,
        diaChi: address.diaChi,
        xaPhuongId: address.xaPhuongId,
      });
    });
  };

  const onChangeTenNb = (e) => {
    const value = e?.target?.value;
    let dataTen = value.toUpperCase();
    let genderVan = dataTen.search("VĂN");
    let genderThi = dataTen.search("THỊ");

    if (genderVan >= 0) {
      form.setFieldsValue({ gioiTinh: 1 });
    } else if (genderThi >= 0) {
      form.setFieldsValue({ gioiTinh: 2 });
    }
  };

  const onKeyDownMaNb = (event) => {
    if (event.nativeEvent.key === "Enter") {
      const value = event?.target?.value;
      if (value.trim()) {
        searchMaNb({ maNb: value, khoaId: quayTiepDon?.khoaId }).then((s) => {
          const {
            tenNb,
            soDienThoai,
            ngaySinh,
            gioiTinh,
            email,
            nbGiayToTuyThan,
            chiNamSinh,
            quocTichId,
            doiTuong,
            loaiDoiTuongId,
            khoaId,
            nbDiaChi,
            danTocId,
            nbNguoiBaoLanh,
          } = s.data || {};
          const _diaChi = nbDiaChi?.diaChi
            ? nbDiaChi?.diaChi
            : nbDiaChi?.tinhThanhPho
            ? `${nbDiaChi?.xaPhuong?.ten ? nbDiaChi?.xaPhuong?.ten : ""}${
                nbDiaChi?.quanHuyen?.ten ? `, ${nbDiaChi?.quanHuyen?.ten}` : ""
              }${
                nbDiaChi?.tinhThanhPho?.ten
                  ? `, ${nbDiaChi?.tinhThanhPho?.ten}`
                  : ""
              }`
            : "";
          const {
            hoTen: hoTenNguoiBaoLanh,
            moiQuanHeId,
            soDienThoai: sdtNguoiBaoLanh,
          } = nbNguoiBaoLanh || {};
          form.setFieldsValue({
            tenNb,
            soDienThoai,
            ngaySinh: ngaySinh && {
              str: chiNamSinh
                ? moment(ngaySinh).format("YYYY")
                : moment(ngaySinh).format("DD/MM/YYYY"),
              date: ngaySinh,
            },
            gioiTinh,
            email,
            maSo: nbGiayToTuyThan?.maSo,
            noiCap: nbGiayToTuyThan?.noiCap,
            ngayCap: nbGiayToTuyThan?.ngayCap
              ? moment(nbGiayToTuyThan?.ngayCap)
              : null,
            diaChi: _diaChi,
            soNha: nbDiaChi?.soNha,
            khoaId,
            hoTenNguoiBaoLanh,
            moiQuanHeId,
            sdtNguoiBaoLanh,
          });

          setState({
            quocGiaId: nbDiaChi?.quocGiaId,
            tinhThanhPhoId: nbDiaChi?.tinhThanhPhoId,
            xaPhuongId: nbDiaChi?.xaPhuongId,
            quocTichId,
            doiTuong,
            loaiDoiTuongId,
            diaChi: _diaChi,
            loaiGiayTo: nbGiayToTuyThan?.loaiGiayTo,
            danTocId,
            soNha: nbDiaChi?.soNha,
            hoTenNguoiBaoLanh,
            moiQuanHeId,
            sdtNguoiBaoLanh,
          });
        });
      }
    }
  };

  const isValidSdtBaoLanh =
    state.sdtNguoiBaoLanh &&
    !state?.sdtNguoiBaoLanh?.replaceAll(" ", "").isPhoneNumber();

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onClose();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onSave();
      },
    },
  ];

  return (
    <ModalTemplate
      ref={refModal}
      width={"85%"}
      onCancel={onClose}
      hotKeys={hotKeys}
      title={
        <MainHeader className="header-title">
          <div className="title">{t("khamSucKhoe.themMoiNguoiBenh")}</div>
          <div className="title-nb">{chiTietHopDong?.doiTac?.ten}</div>
        </MainHeader>
      }
      closable={false}
      actionLeft={<Button.QuayLai onClick={onClose} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onSave}
          iconHeight={20}
          rightIcon={<SVG.IcSave />}
          loading={state?.loading}
        >
          {t("common.luuF4")}
        </Button>
      }
      destroyOnClose={true}
    >
      <Main>
        <div className="form-nb">
          <FormWraper
            name="basic"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            layout={"vertical"}
            onFinishFailed={onFinishFailed}
            onValuesChange={onFormValueChange}
            autoComplete="off"
            form={form}
          >
            <Row>
              <Col span={12}>
                <Row>
                  <Col span={6} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.dsNb.sttHd")}
                      name="stt"
                      rules={[
                        {
                          required: dataTU_THEM_STT_HD.toLowerCase() === "true",
                          message: t("khamSucKhoe.dsNb.vuiLongNhapSttHd"),
                        },
                      ]}
                    >
                      <Input
                        style={{ width: "100%" }}
                        placeholder={
                          dataTU_THEM_STT_HD.toLowerCase() === "true"
                            ? t("khamSucKhoe.dsNb.nhapSttHd")
                            : ""
                        }
                        disabled={dataTU_THEM_STT_HD.toLowerCase() === "false"}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={10} style={{ padding: 0 }}>
                    <Form.Item label={t("common.maNguoiBenh")} name="maNb">
                      <Input
                        placeholder={t("common.nhapMaNguoiBenh")}
                        onKeyDown={onKeyDownMaNb}
                        readOnly={!state.isThemMoi}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8} style={{ padding: 0 }}>
                    <Form.Item label={t("common.maHoSo")} name="maHoSo">
                      <Input
                        placeholder={t("common.nhapMaHoSo")}
                        readOnly={true}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <Row>
                  <Col span={12} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("danhMuc.quayTiepDon")}
                      name="quayTiepDonId"
                    >
                      <Select
                        refSelect={refInput}
                        data={listAllQuayTiepDon || []}
                        placeholder={t("danhMuc.quayTiepDon")}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.khoaChiDinh")}
                      name="khoaId"
                      rules={[
                        {
                          required: true,
                          message: t("khamSucKhoe.vuiLongChonKhoaChiDinh"),
                        },
                      ]}
                    >
                      <Select
                        data={listKhoaTheoTaiKhoan}
                        placeholder={t("khamSucKhoe.khoaChiDinh")}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row>
              <Col span={12}>
                <Form.Item
                  label={t("common.hoVaTen")}
                  name="tenNb"
                  rules={[
                    {
                      required: true,
                      message: t("khamSucKhoe.vuiLongNhapHoTen"),
                    },
                  ]}
                >
                  <Input
                    onInput={(e) =>
                      (e.target.value = e.target.value.toUpperCase())
                    }
                    placeholder={t("common.nhapHoVaTen")}
                    onChange={onChangeTenNb}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label={t("common.soDienThoai")} name="soDienThoai">
                  <Input placeholder={t("khamSucKhoe.nhapSoDienThoai")} />
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={12}>
                <Row>
                  <Col span={10} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.ngayThangNamSinh")}
                      name="ngaySinh"
                      rules={[
                        {
                          required: true,
                          message: t("khamSucKhoe.vuiLongNhapNgayThangNamSinh"),
                        },
                      ]}
                    >
                      <DOBInput
                        value="1970-01-01T00:00:00+07:00"
                        className="item-born"
                        placeholder={t("khamSucKhoe.nhapNgayThangNamSinh")}
                        onBlur={(e, nofi, ageStr, chiNamSinh) => {
                          form.setFields([{ name: "ngaySinh", value: e }]);
                          setState({ chiNamSinh });
                        }}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("common.gioiTinh")}
                      name="gioiTinh"
                      rules={[
                        {
                          required: true,
                          message: t("khamSucKhoe.vuiLongChonGioiTinh"),
                        },
                      ]}
                    >
                      <Select
                        placeholder={t("khamSucKhoe.chonGioiTinh")}
                        data={listgioiTinh || []}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.loaiDoiTuong")}
                      name="loaiDoiTuongId"
                      rules={[
                        {
                          required: true,
                          message: t("khamSucKhoe.vuiLongChonLoaiDoiTuong"),
                        },
                      ]}
                    >
                      <Select
                        className="item-type"
                        placeholder={t("khamSucKhoe.chonLoaiDoiTuong")}
                        data={state.listLoaiDoiTuongKsk || []}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>

              <Col span={12}>
                <Row>
                  <Col span={8} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.soNhaThonXom")}
                      name="soNha"
                    >
                      <Input placeholder={t("khamSucKhoe.soNhaThonXom")} />
                    </Form.Item>
                  </Col>
                  <Col span={16} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.phuongXaTinhThanh")}
                      name="diaChi"
                      rules={[
                        {
                          required: true,
                          message: t("khamSucKhoe.vuiLongNhapSoDiaChi"),
                        },
                      ]}
                    >
                      <AddressFull
                        onChangeAdrressText={onChangeAdrressText}
                        onBlur={(e) => {
                          form.setFields([
                            { name: "diaChi", value: e.target.value },
                          ]);
                        }}
                        value={state.diaChi}
                        placeholder={t("khamSucKhoe.phuongXaTinhThanh")}
                        onSelectAddress={selectAdress}
                        onError={onErrorAddress}
                        delayTyping={300}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.capNhapNb.hinhThucTtDvNgoaiHd")}
                  name="hinhThucTtDvNgoaiHd"
                  rules={[
                    {
                      required: true,
                      message: t("khamSucKhoe.capNhapNb.vuiLongChonHinhThucTt"),
                    },
                  ]}
                >
                  <Select
                    placeholder={t("khamSucKhoe.capNhapNb.chonHinhThucTt")}
                    data={listHinhThucTtDvHdKsk || []}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.capNhapNb.hinhThucTtDvTrongHd")}
                  name="hinhThucTtDvTrongHd"
                  rules={[
                    {
                      required: true,
                      message: t("khamSucKhoe.capNhapNb.vuiLongChonHinhThucTt"),
                    },
                  ]}
                >
                  <Select
                    placeholder={t("khamSucKhoe.capNhapNb.chonHinhThucTt")}
                    data={listHinhThucTtDvHdKsk || []}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.goiDichVuApDung")}
                  name="dsBoChiDinhId"
                >
                  <Select
                    mode="multiple"
                    placeholder={t("khamSucKhoe.chonGoiDichVu")}
                    data={listGoiMemo || []}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.dichVuLeApDung")}
                  name="dsDichVuId"
                >
                  <Select
                    mode="multiple"
                    placeholder={t("khamSucKhoe.chonDichVuLeApDung")}
                    data={dsDichVuLeMemo || []}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.bacSiChiDinh")}
                  name="bacSiChiDinhId"
                >
                  <Select
                    placeholder={t("khamSucKhoe.chonBacSiChiDinh")}
                    data={listAllNhanVien || []}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={t("khamSucKhoe.soCmtCanCuoc")} name="maSo">
                  <Input placeholder={t("khamSucKhoe.nhapSoCmtCanCuoc")} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Row>
                  <Col span={8} style={{ padding: 0 }}>
                    <Form.Item label={t("khamSucKhoe.ngayCap")} name="ngayCap">
                      <DatePicker
                        format="DD/MM/YYYY"
                        placeholder={t("common.chonNgay")}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={16} style={{ padding: 0 }}>
                    <Form.Item label={t("khamSucKhoe.noiCap")} name="noiCap">
                      <Input placeholder={t("khamSucKhoe.nhapNoiCap")} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <Row>
                  <Col span={8} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.moiQhVoiNb")}
                      name="moiQuanHeId"
                    >
                      <Select
                        placeholder={t("khamSucKhoe.chonMoiQuanHe")}
                        data={listMoiQuanHe || []}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={16} style={{ padding: 0 }}>
                    <Form.Item
                      label={t("khamSucKhoe.hoTenNguoiBaoLanh")}
                      name="hoTenNguoiBaoLanh"
                    >
                      <Input
                        placeholder={t("khamSucKhoe.nhapHoTenNguoiBaoLanh")}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <Form.Item
                  className="form-item"
                  style={{ flex: 1 }}
                  name={"sdtNguoiBaoLanh"}
                >
                  <div className="label">{t("common.sdtNguoiBaoLanh")}</div>
                  <Input
                    placeholder={t("common.nhapSdtNguoiBaoLanh")}
                    autoFocus
                    value={state.sdtNguoiBaoLanh}
                    onChange={(e) =>
                      setState({ sdtNguoiBaoLanh: e.target.value })
                    }
                  />
                  {isValidSdtBaoLanh ? (
                    <div className="error">
                      {t("khamSucKhoe.soDienThoaiSaiDinhDang")}
                    </div>
                  ) : null}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.maNhanVien")}
                  name="maNhanVien"
                >
                  <Input placeholder={t("khamSucKhoe.nhapMaNhanVien")} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item label={t("khamSucKhoe.chucDanh")} name="chucVu">
                  <Input placeholder={t("khamSucKhoe.nhapChucDanh")} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={t("khamSucKhoe.phongBan")} name="phongBan">
                  <Input placeholder={t("khamSucKhoe.nhapPhongBan")} />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.diemDiemLamViec")}
                  name="noiLamViec"
                >
                  <Input placeholder={t("khamSucKhoe.nhapDiaDiemLamViec")} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Email" name="email">
                  <Input placeholder={t("common.nhapEmail")} />
                </Form.Item>
              </Col>
            </Row>

            {!state.isThemMoi && (
              <>
                <Row>
                  <Col span={6}>
                    <Form.Item
                      label={t("khamSucKhoe.ngayLayMauTuNgay")}
                      name="tuThoiGianLayMau"
                    >
                      <DatePicker
                        showTime
                        placeholder={t("common.chonNgay")}
                        format="DD/MM/YYYY HH:mm:ss"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label={t("common.denNgay")}
                      name="denThoiGianLayMau"
                    >
                      <DatePicker
                        showTime
                        placeholder={t("common.chonNgay")}
                        format="DD/MM/YYYY HH:mm:ss"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item
                      label={t("khamSucKhoe.diaDiemLayMau")}
                      name="diaDiemLayMau"
                    >
                      <Input placeholder={t("khamSucKhoe.nhapDiaDiemLayMau")} />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {!state.isThemMoi && (
              <>
                <Row>
                  <Col span={6}>
                    <Form.Item
                      label={t("khamSucKhoe.lenLich.tuThoiGianKham")}
                      name="tuThoiGianKham"
                    >
                      <DatePicker
                        showTime
                        placeholder={t("common.chonNgay")}
                        format="DD/MM/YYYY HH:mm:ss"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label={t("common.denNgay")}
                      name="denThoiGianKham"
                    >
                      <DatePicker
                        showTime
                        placeholder={t("common.chonNgay")}
                        format="DD/MM/YYYY HH:mm:ss"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item
                      label={t("khamSucKhoe.lenLich.diaDiemKham")}
                      name="diaDiemKham"
                    >
                      <Input
                        placeholder={t("khamSucKhoe.dsNb.nhapDiaDiemKham")}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            <Row>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.nguoiGioiThieu")}
                  name="nguoiGioiThieuId"
                >
                  <Select
                    placeholder={t("khamSucKhoe.nguoiGioiThieu")}
                    data={listAllNguoiGioiThieu}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={t("khamSucKhoe.nguonNguoiBenh")}
                  name="nguonNbId"
                >
                  <Select
                    placeholder={t("khamSucKhoe.nguonNguoiBenh")}
                    data={listAllNguonNguoiBenh}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label=" "
                  labelAlign="right"
                  name="ngoaiVien"
                  valuePropName="checked"
                >
                  <Checkbox>{t("khamSucKhoe.capNhapNb.ngoaiVien")}</Checkbox>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label=" "
                  labelAlign="right"
                  name="uuTien"
                  valuePropName="checked"
                >
                  <Checkbox>{t("common.uuTien")}</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          </FormWraper>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalThemMoiNguoiBenh);
