import styled from "styled-components";
import { Collapse } from "antd";
export const Main = styled.div`
  width: 100%;
  height: 100%;
  overflow: auto;
  .ant-row {
    width: 100%;
  }

  .content-tab {
    .item-sub {
      margin-bottom: 10px;
      margin-left: 10px;
    }
  }
  .info {
    padding-bottom: 10px;
    .title {
      background: #e6effe;
      border-radius: 4px 4px 0px 0px;
      width: 100%;
      font-weight: 700;
      font-size: 16px;
      line-height: 22px;
      height: 40px;
      align-items: center;
      display: flex;
      flex: none;
      order: 0;
      align-self: stretch;
      flex-grow: 0;
      margin: -1px 0px;
      padding-left: 10px;
      img {
        padding-left: 10px;
      }
      svg {
        margin-left: 10px;
        path {
          fill: #0762f7;
        }
        :hover {
          cursor: pointer;
        }
      }
    }
    .content-tab {
      width: 100%;
      border: 1px solid #e6effe;
      padding: 10px 0px 0px 10px;

      .thong-tin-kham {
        .ant-row {
          align-items: flex-start;
          line-height: 25px;
          .ant-col {
            padding-left: 5px;
          }
        }
        .title-header {
          font-weight: 700;
          font-size: 16px;
          line-height: 25px;
          padding: 12px 0;
        }
        .title-tien-su-benh {
          margin-left: 10px;
          margin-bottom: 5px;
        }
        .item-sub {
          word-wrap: break-word;
        }
      }
    }
  }
`;

export const CollapseWrapper = styled(Collapse)`
  background-color: #fff !important;
  .ant-collapse-item {
    border-bottom: none !important;
    .ant-collapse-header {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-weight: 700;
      font-size: 16px;
      line-height: 25px;
      color: rgba(0, 0, 0, 0.85);

      .ant-collapse-arrow {
        position: initial !important;
        margin: 0 15px 0 0 !important;
        padding: 0 !important;
      }
    }

    .ant-collapse-content {
      margin-left: 13px;
      .ant-row {
        align-items: center;
      }
      .error {
        @media (max-width: 1537px) {
          font-size: 10px !important;
        }
      }
    }
  }

  .sub-label {
    margin-top: -10px;
  }
`;

export const SelectGroup = styled.div`
  line-height: 25px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 6 6"><circle cx="3" cy="3" r="0.4" fill="black" /></svg>')
    20px;
  background-position-y: 7px;
  background-size: 5px 28px;
  margin-top: ${(props) => props.marginTop ?? "10px"};
  display: flex;
  > span {
    display: inline-block;
    padding-right: 5px;
    background: #ffffff;
    vertical-align: sub;
    white-space: nowrap;
    /* flex: 1 0 auto; */
    /* height: ${(props) =>
      props.dataHeight ? props.dataHeight + "px" : "auto"}; */
  }
  .select-box {
    display: inline-block;
    .ant-select-selector {
      margin-top: -13px;
      background: none;
      border: 0;
    }
  }
  .red-text {
    color: #ef4066;
  }
  .select-box-chan-doan {
    display: inline-block;
    flex: 1;
    width: 100px;
    &
      .ant-select
      .ant-select-multiple
      .ant-select-allow-clear
      .ant-select-show-search {
      width: auto;
    }
    & .ant-select {
      width: 100%;
      &.ant-select-show-search {
        width: auto;
      }
      & .ant-select-selector {
        margin-top: -13px;
        background: none;
        border: 0;
        & .ant-select-selection-overflow {
          width: 380px;
        }
      }
    }
    .ant-select-item-option-active {
      background-color: #c7c7c7 !important;
      &:hover {
        background-color: #c7c7c7 !important;
      }
    }
    .ant-select-item-option-selected {
      background-color: #d1edfb !important;
    }
  }
`;

export const Title = styled.div`
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
`;
