import React, { useMemo, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { Row, Col, Collapse, Tooltip } from "antd";
import { useEnum, useListAll, useStore, useThietLap } from "hooks";
import { ENUM, THIET_LAP_CHUNG } from "constants/index";
import { Main, CollapseWrapper, Title } from "./styled";
import moment from "moment";
import { SVG } from "assets";
import ModalThongTinKetThucBA from "pages/dieuTriDaiHan/components/ModalThongTinKetThucBA";
import ThongTinCmu from "./ThongTinCmu";
import ThongTinKham from "./ThongTinKham";
import ModalThongTinKhamCmu from "pages/dieuTriDaiHan/components/ModalThongTinKhamCmu";

const { Panel } = Collapse;

const ThongTinBenhAn = () => {
  const { t } = useTranslation();
  const refModalThongTinKetThucBA = useRef(null);
  const refModalThongTinCmu = useRef(null);
  const { id: benhAnId } = useParams();

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const nbThongTinRaVien = useStore("nbDotDieuTri.nbThongTinRaVien");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const nbThongTinKham = useStore("dieuTriDaiHan.nbThongTinKham");
  const dsMaBADaiHan = useStore("nbMaBenhAn.dsMaBADaiHan", []);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);

  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listHuongDieuTriNoiTru] = useEnum(ENUM.HUONG_DIEU_TRI);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [dataKHOA_CMU] = useThietLap(THIET_LAP_CHUNG.KHOA_CMU);
  const [dataHIEN_THI_THONG_TIN_BENH_AN] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_THONG_TIN_BENH_AN
  );

  const isShowThongTinKhamCmu = useMemo(() => {
    return (
      !!dataKHOA_CMU &&
      dataKHOA_CMU
        .split(",")
        .some((thietLap) => thietLap.trim() === thongTinBenhNhan?.khoa?.ma)
    );
  }, [dataKHOA_CMU, thongTinBenhNhan?.khoa?.ma]);

  const currentBADaiHan = useMemo(() => {
    return dsMaBADaiHan.find((i) => i.id === Number(benhAnId));
  }, [dsMaBADaiHan, benhAnId]);

  return (
    <Main>
      <Row className="info">
        <div className="title">{t("dieuTriDaiHan.thongTinLapBenhAn")}</div>
        <div className="content-tab">
          <Row>
            <Col span={12}>
              <div className="item-sub">
                <span>{t("dieuTriDaiHan.thoiGianLapBenhAn")}:</span>{" "}
                <b>
                  {thongTinBenhNhan?.thoiGianLapBenhAn
                    ? moment(thongTinBenhNhan?.thoiGianLapBenhAn).format(
                        "DD/MM/YYYY HH:mm:ss"
                      )
                    : ""}
                </b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.nguoiLapBenhAn")}:</span>{" "}
                <b>
                  {
                    listAllNhanVien.find(
                      (i) => i.id === currentBADaiHan?.nguoiLapBenhAnId
                    )?.ten
                  }
                </b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.loaiBenhAn")}:</span>{" "}
                <b>{currentBADaiHan?.tenLoaiBenhAn}</b>
              </div>
              <div className="item-sub">
                <span>{t("khamBenh.khoaDieuTri")}:</span>{" "}
                <b>{chiTietNguoiBenhNoiTru?.tenKhoaNb}</b>
              </div>
              <div className="item-sub">
                <span>{t("hsba.trangThaiBenhAn")}:</span>{" "}
                <b>
                  {
                    (listTrangThaiNb || []).find(
                      (o) => o.id === currentBADaiHan?.trangThai
                    )?.ten
                  }
                </b>
              </div>
            </Col>
            <Col span={12}>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.doiTuongKcb")}:</span>{" "}
                <b>
                  {
                    (listDoiTuongKcb || []).find(
                      (x) => x.id === currentBADaiHan?.doiTuongKcb
                    )?.ten
                  }
                </b>
              </div>
              <div className="item-sub">
                <span>{t("dieuTriDaiHan.noiGioiThieu")}:</span>{" "}
                <b>{chiTietNguoiBenhNoiTru?.tenNoiGioiThieu}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.chanDoanChinhHienTai")}:</span>{" "}
                <b>
                  {chiTietNguoiBenhNoiTru?.dsCdVaoVien
                    ?.map((item) => `${item?.ma} - ${item?.ten}`)
                    ?.join("; ")}
                </b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.chanDoanKemTheo")}:</span>{" "}
                <b>
                  {chiTietNguoiBenhNoiTru?.dsCdVaoVienKemTheo
                    ?.map((item) => `${item?.ma} - ${item?.ten}`)
                    ?.join("; ")}
                </b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.chanDoanMoTaChiTiet")}:</span>{" "}
                <b>{chiTietNguoiBenhNoiTru?.moTaChiTiet}</b>
              </div>
            </Col>
          </Row>
        </div>
      </Row>

      {dataHIEN_THI_THONG_TIN_BENH_AN.toLowerCase() === "true" &&
        nbThongTinKham?.id && (
          <Row span={24} className="info">
            <div className="title">
              {t("quanLyNoiTru.thongTinBenhAn")}
              <Tooltip title={t("dieuTriDaiHan.suaThongTinBenhAn")}>
                <SVG.IcEdit
                  style={{ cursor: "pointer" }}
                  onClick={() => {
                    refModalThongTinCmu.current &&
                      refModalThongTinCmu.current.show();
                  }}
                />
              </Tooltip>
            </div>
            <div className="content-tab">
              <CollapseWrapper
                bordered={false}
                defaultActiveKey={["thong-tin-kham", "thong-tin-cmu"]}
                expandIcon={({ isActive }) =>
                  isActive ? <SVG.IcExpandDown /> : <SVG.IcExpandRight />
                }
                className="site-collapse-custom-collapse"
              >
                <Panel
                  key={"thong-tin-kham"}
                  header={
                    <Title>{`${t("common.a")}. ${t(
                      "dieuTriDaiHan.thongTinKham"
                    )}`}</Title>
                  }
                >
                  <ThongTinKham />
                </Panel>
                {isShowThongTinKhamCmu && (
                  <Panel
                    key={"thong-tin-cmu"}
                    header={
                      <Title>{`${t("common.b")}. ${t(
                        "dieuTriDaiHan.thongTinKhamCmu"
                      )}`}</Title>
                    }
                  >
                    <ThongTinCmu />
                  </Panel>
                )}
              </CollapseWrapper>
            </div>
          </Row>
        )}

      <Row span={24} className="info">
        <div className="title">
          {t("dieuTriDaiHan.thongTinKetThucBenhAn")}
          <SVG.IcEdit
            style={{ cursor: "pointer" }}
            onClick={() => {
              refModalThongTinKetThucBA.current &&
                refModalThongTinKetThucBA.current.show();
            }}
          />
        </div>

        <div className="content-tab">
          <Row>
            <Col span={24}>
              <div className="item-sub">
                <span>
                  {t("quanLyNoiTru.quaTrinhBenhLyVaDienBienLamSang")}:{" "}
                </span>{" "}
                <b>{nbThongTinRaVien?.quaTrinhBenhLy}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.tomTatKetQuaCls")}: </span>{" "}
                <b>{nbThongTinRaVien?.ketQuaCls}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.chanDoanRaVienChinh")}: </span>{" "}
                <b>{chiTietNguoiBenhNoiTru?.cdVaoVien}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.chanDoanRaVienKemTheo")}: </span>{" "}
                <b>{chiTietNguoiBenhNoiTru?.cdVaoVienKemTheo}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.chanDoanRaVienMoTaChiTiet")}: </span>{" "}
                <b>{nbThongTinRaVien?.moTa}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.phuongPhapDieuTri")}: </span>{" "}
                <b>{nbThongTinRaVien?.phuongPhapDieuTri}</b>
              </div>
              <div className="item-sub">
                <span>
                  {t("quanLyNoiTru.huongDieuTriVaCacCheDoTiepTheo")}:{" "}
                </span>{" "}
                <b>{nbThongTinRaVien?.cheDoTiepTheo}</b>
              </div>
              <div className="item-sub">
                <span>{t("quanLyNoiTru.tinhTrangRaVien")}: </span>{" "}
                <b>
                  {
                    (listHuongDieuTriNoiTru || []).find(
                      (x) => x.id === nbThongTinRaVien?.huongDieuTri
                    )?.ten
                  }
                </b>
              </div>
              <div className="item-sub">
                <span>{t("common.thoiGianKetThucBenhAn")}:</span>{" "}
                <b>
                  {nbThongTinRaVien?.thoiGianKetThucBenhAn
                    ? moment(nbThongTinRaVien?.thoiGianKetThucBenhAn).format(
                        "DD/MM/YYYY HH:mm:ss"
                      )
                    : ""}
                </b>
              </div>
            </Col>
          </Row>
        </div>
      </Row>
      <ModalThongTinKetThucBA ref={refModalThongTinKetThucBA} />
      <ModalThongTinKhamCmu ref={refModalThongTinCmu} />
    </Main>
  );
};

export default ThongTinBenhAn;
