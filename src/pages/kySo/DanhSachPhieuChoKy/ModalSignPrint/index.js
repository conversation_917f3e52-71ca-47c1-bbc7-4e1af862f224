import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import { Main } from "components/ModalSignPrint/styled";
import { Col } from "antd";
import DanhSachPhieu from "./DanhSachPhieu";
import { useTranslation } from "react-i18next";
import { ModalTemplate } from "components";
import fileUtils from "utils/file-utils";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import { useDispatch } from "react-redux";
import { cloneDeep } from "lodash";
import { useLoading } from "hooks";
import XemTruoc from "./XemTruoc";

const ModalSignPrint = (props, ref) => {
  const refModal = useRef(null);
  const refRefreshDsPhieu = useRef(null);

  const { t } = useTranslation();
  const { hideLoading, showLoading } = useLoading();

  const {
    danhSachPhieuChoKy: { getListPhieu },
  } = useDispatch();

  const [state, _setState] = useState({
    isKyPhieu: false,
    listPhieu: [],
    isLoadingPhieu: true,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useImperativeHandle(ref, () => ({
    show: async (payload, callback) => {
      setState({
        show: true,
        data: payload,
        dataSource: [payload],
      });
      const data = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
        id: payload?.id,
      });
      fileUtils
        .getFromUrl({ url: fileUtils.absoluteFileUrl(data?.data?.file?.pdf) })
        .then((s) => {
          const blob = new Blob([new Uint8Array(s)], {
            type: "application/pdf",
          });
          const blobUrl = window.URL.createObjectURL(blob);
          setState({ urlFileLocal: blobUrl, isLoadingPhieu: false });
        });

      refRefreshDsPhieu.current = callback;
    },
  }));

  const onRefreshPhieuKy = async () => {
    const data = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
      id: state?.data?.id,
    });
    fileUtils
      .getFromUrl({ url: fileUtils.absoluteFileUrl(data?.data?.file?.pdf) })
      .then((s) => {
        const blob = new Blob([new Uint8Array(s)], {
          type: "application/pdf",
        });
        const blobUrl = window.URL.createObjectURL(blob);
        setState({ urlFileLocal: blobUrl, isLoadingPhieu: false });
      });
  };

  const onCancel = () => {
    setState({
      show: false,
      urlFileLocal: "",
      data: {},
      dataSource: [],
    });
  };

  const onClose = () => {
    refRefreshDsPhieu.current && refRefreshDsPhieu.current();

    onCancel();
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onGetPhieuKy = async (record) => {
    showLoading();
    const data = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
      id: record?.id?.id || state?.data?.id,
      chuKySo: record?.id?.chuKySo,
    });
    fileUtils
      .getFromUrl({ url: fileUtils.absoluteFileUrl(data?.data?.file?.pdf) })
      .then((s) => {
        const blob = new Blob([new Uint8Array(s)], {
          type: "application/pdf",
        });
        const blobUrl = window.URL.createObjectURL(blob);
        setState({ urlFileLocal: blobUrl, isLoadingPhieu: false });
        hideLoading();
      })
      .finally(() => {
        hideLoading();
      });
  };

  const onXemLichSuKy = (record, index) => async (e) => {
    e.stopPropagation();
    const res = await getListPhieu({
      dsId: record?.id,
      size: "",
    });
    state.dataSource[index].lichSuKy = res;
    const listData = cloneDeep([state.data]);
    let _data = [];
    listData.forEach((item) => {
      _data.push(item);
      item?.lichSuKy?.forEach((x) => _data.push({ ...x, isLichSuKy: true }));
    });
    if (state.isViewLichSuKy) {
      _data = [state.data];
    }
    setState({ dataSource: _data, isViewLichSuKy: !state.isViewLichSuKy });
  };

  return (
    <ModalTemplate
      ref={refModal}
      onCancel={onCancel}
      title={t("phieuIn.kyVaIn")}
      centered={true}
    >
      <Main className="modal-content" gutter={[8, 8]}>
        <Col md={16} xl={16} xxl={16}>
          <XemTruoc
            urlFileLocal={state.urlFileLocal}
            isLoadingPhieu={state.isLoadingPhieu}
            refreshPhieuKy={onRefreshPhieuKy}
          />
        </Col>
        <Col md={8} xl={8} xxl={8}>
          <DanhSachPhieu
            data={state.data}
            refreshPhieuKy={onRefreshPhieuKy}
            onClose={onClose}
            onGetLichSuKy={onGetPhieuKy}
            urlFileLocal={state.urlFileLocal}
            onXemLichSuKy={onXemLichSuKy}
            dataSource={state.dataSource}
          />
        </Col>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSignPrint);
