import { useState, useCallback, useRef } from "react";
import moment from "moment";
import { isArray } from "utils";
import { getMonthDaysFromRange } from "../utils/dateUtils";

export const useScheduleData = () => {
  const [state, _setState] = useState({
    dataSource: [],
    listActiveKeys: [],
    renderReady: false,
    isProcessing: false,
  });

  // Lưu layout config để tránh re-init
  const layoutConfigRef = useRef({
    listAllPhong: null,
    listAllNhanVien: null,
    khoaId: null,
    phongId: null,
    dsNhanVienId: null,
    isLayoutInitialized: false,
  });

  const setState = useCallback((data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  }, []);

  // Khởi tạo layout (chỉ chạy 1 lần khi có đủ config data)
  const initLayout = useCallback((layoutData = {}) => {
    const { listAllPhong, listAll<PERSON>han<PERSON>ien, khoaId, phongId, dsNhanVienId } =
      layoutData;

    // Kiểm tra xem layout config có thay đổi không
    const currentConfig = layoutConfigRef.current;
    const configChanged =
      currentConfig.khoaId !== khoaId ||
      currentConfig.phongId !== phongId ||
      JSON.stringify(currentConfig.dsNhanVienId) !==
        JSON.stringify(dsNhanVienId) ||
      currentConfig.listAllPhong !== listAllPhong ||
      currentConfig.listAllNhanVien !== listAllNhanVien;

    if (!configChanged && currentConfig.isLayoutInitialized) {
      console.log("🔄 Layout config unchanged, skipping re-init");
      return;
    }

    console.log("🏗️ Initializing layout...");
    setState({ isProcessing: true, renderReady: false });

    // Lưu config mới
    layoutConfigRef.current = {
      listAllPhong,
      listAllNhanVien,
      khoaId,
      phongId,
      dsNhanVienId,
      isLayoutInitialized: true,
    };

    const defaultState = {
      dataSource: [],
      listActiveKeys: [],
      renderReady: true,
      isProcessing: false,
    };

    if (!isArray(listAllPhong, true) || !isArray(listAllNhanVien, true)) {
      if (isArray(listAllPhong, true) && !isArray(listAllNhanVien, true)) {
        setState({
          dataSource: [],
          listActiveKeys: [],
          renderReady: false,
          isProcessing: true,
        });
        return;
      }
      setState(defaultState);
      return;
    }

    // Tạo layout structure (không có data)
    const processLayout = () => {
      try {
        const nhanVienByPhongMap = new Map();
        if (isArray(listAllNhanVien, true)) {
          const filteredNhanVien = isArray(dsNhanVienId, true)
            ? listAllNhanVien.filter((nv) => dsNhanVienId.includes(nv.id))
            : listAllNhanVien;

          filteredNhanVien.forEach((nv) => {
            const khoaInfo = nv.dsKhoa?.find((k) => k.khoaId === khoaId);
            if (khoaInfo?.dsPhongId) {
              khoaInfo.dsPhongId.forEach((pId) => {
                if (!nhanVienByPhongMap.has(pId)) {
                  nhanVienByPhongMap.set(pId, []);
                }
                nhanVienByPhongMap.get(pId).push(nv);
              });
            }
          });
        }

        const filteredPhongList = phongId
          ? listAllPhong.filter((i) => i.id === phongId)
          : listAllPhong;

        const newData = [];
        const activeKeys = [];

        filteredPhongList.forEach((phong) => {
          const nhanVienInPhong = nhanVienByPhongMap.get(phong.id);
          if (!nhanVienInPhong?.length) return;

          const childrenNhanVien = nhanVienInPhong.map((nhanVien) => ({
            name: nhanVien.ten,
            key: nhanVien.id,
            children: [], // Empty initially, will be filled by updateData
          }));

          if (childrenNhanVien.length > 0) {
            newData.push({
              name: phong.ten,
              key: phong.id,
              children: childrenNhanVien,
            });
            activeKeys.push(phong.id);
          }
        });

        setState({
          dataSource: newData,
          listActiveKeys: activeKeys,
          renderReady: true,
          isProcessing: false,
        });

        console.log("✅ Layout initialized successfully");
      } catch (error) {
        console.error("Error processing layout:", error);
        setState({ ...defaultState, renderReady: true, isProcessing: false });
      }
    };

    if (window.requestIdleCallback) {
      window.requestIdleCallback(processLayout, { timeout: 100 });
    } else {
      setTimeout(processLayout, 0);
    }
  }, []);

  // Cập nhật data mới (không làm lại layout)
  const updateData = useCallback((listData = []) => {
    if (!layoutConfigRef.current.isLayoutInitialized) {
      console.log("⚠️ Layout not initialized, skipping data update");
      return;
    }

    console.log("📊 Updating data...", listData.length, "items");

    setState((prevState) => {
      try {
        const dataByNhanVienPhongMap = new Map();
        const hasListData = isArray(listData, true);

        if (hasListData) {
          listData.forEach((item) => {
            const key = `${item.nhanVienId}-${item.phongId}`;
            if (!dataByNhanVienPhongMap.has(key)) {
              dataByNhanVienPhongMap.set(key, []);
            }

            const processedItem = {
              ...item,
              thoiGian: moment(item.tuThoiGian).date(),
              listThoiGian: getMonthDaysFromRange(
                item.tuThoiGian,
                item.denThoiGian
              ),
            };

            dataByNhanVienPhongMap.get(key).push(processedItem);
          });
        }

        // Cập nhật data vào layout có sẵn
        const updatedDataSource = prevState.dataSource.map((phong) => ({
          ...phong,
          children: phong.children.map((nhanVien) => {
            const dataKey = `${nhanVien.key}-${phong.key}`;
            const nhanVienData = dataByNhanVienPhongMap.get(dataKey) || [];

            return {
              ...nhanVien,
              children: nhanVienData,
            };
          }),
        }));

        console.log("✅ Data updated successfully");
        return {
          ...prevState,
          dataSource: updatedDataSource,
        };
      } catch (error) {
        console.error("Error updating data:", error);
        return prevState;
      }
    });
  }, []);

  // Legacy function để tương thích ngược
  const initState = useCallback(
    (data = {}) => {
      const { listData, ...layoutData } = data;

      // Khởi tạo layout trước
      initLayout(layoutData);

      // Sau đó cập nhật data
      if (listData) {
        setTimeout(() => updateData(listData), 100);
      }
    },
    [initLayout, updateData]
  );

  return {
    state,
    setState,
    initState,
    initLayout,
    updateData,
  };
};
