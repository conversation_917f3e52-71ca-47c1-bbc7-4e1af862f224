import { useCallback, useRef, useEffect } from "react";
import { message } from "antd";
import { getNavigationParams } from "../utils/dateUtils";

export const useTableNavigation = ({
  isLoading,
  tuThoiGian,
  denThoiGian,
  setParentState,
  searchByParams,
  onLoadMoreData,
  page,
  listData,
  isLoadMore,
  hasNext,
}) => {
  // Lưu scroll position và data count trước khi load more
  const scrollPositionRef = useRef(0);
  const virtuosoRef = useRef(null);
  const previousDataCountRef = useRef(0);
  const isLoadingMoreRef = useRef(false);

  // Auto preload management
  const autoPreloadRef = useRef(false);
  const preloadTimeoutRef = useRef(null);
  const lastAutoLoadPageRef = useRef(0);

  const handleNavigate = useCallback(
    (isNext) => () => {
      if (isLoading) return;

      const params = getNavigationParams(tuT<PERSON><PERSON><PERSON><PERSON>, denThoi<PERSON>ian, isNext);
      setParentState(params);
      searchByParams({ ...params });
    },
    [isLoading, tuThoiGian, denThoiGian, setParentState, searchByParams]
  );

  // Auto preload next page ngầm
  const autoPreloadNextPage = useCallback(() => {
    if (
      !isLoading &&
      !isLoadMore &&
      hasNext &&
      !autoPreloadRef.current &&
      lastAutoLoadPageRef.current < page + 1
    ) {
      console.log(`🔄 Auto preloading page ${page + 1}...`);
      autoPreloadRef.current = true;
      lastAutoLoadPageRef.current = page + 1;

      onLoadMoreData({
        page: Number(page) + 1,
        loadMore: true,
        isAutoPreload: true, // Flag để biết đây là auto preload
      });
    }
  }, [isLoading, isLoadMore, hasNext, page, onLoadMoreData]);

  // Manual load more (khi user click button)
  const handleLoadMore = useCallback(() => {
    // Lưu data count hiện tại
    previousDataCountRef.current = listData?.length || 0;
    isLoadingMoreRef.current = true;

    // Lưu scroll position hiện tại
    if (virtuosoRef.current) {
      virtuosoRef.current.getState((state) => {
        scrollPositionRef.current = state.scrollTop;
      });
    }

    onLoadMoreData({
      page: Number(page) + 1,
      loadMore: true,
      preserveScrollPosition: true,
    });
  }, [onLoadMoreData, page, listData]);

  // Auto preload khi load page đầu tiên xong
  useEffect(() => {
    if (
      !isLoading &&
      !isLoadMore &&
      listData?.length > 0 &&
      hasNext &&
      page === 0
    ) {
      // Delay 1 giây sau khi load page đầu xong thì auto preload page 2
      preloadTimeoutRef.current = setTimeout(() => {
        autoPreloadNextPage();
      }, 1000);
    }

    return () => {
      if (preloadTimeoutRef.current) {
        clearTimeout(preloadTimeoutRef.current);
      }
    };
  }, [isLoading, isLoadMore, listData, hasNext, page, autoPreloadNextPage]);

  // Effect để xử lý sau khi load more hoàn thành
  useEffect(() => {
    if (!isLoadMore && autoPreloadRef.current) {
      // Auto preload hoàn thành
      autoPreloadRef.current = false;
      console.log(
        `✅ Auto preloaded page ${lastAutoLoadPageRef.current} completed`
      );

      // Tiếp tục auto preload page tiếp theo nếu còn
      if (hasNext) {
        preloadTimeoutRef.current = setTimeout(() => {
          autoPreloadNextPage();
        }, 2000); // Delay 2 giây giữa các lần auto preload
      }
    }

    if (
      !isLoadMore &&
      isLoadingMoreRef.current &&
      listData?.length > previousDataCountRef.current
    ) {
      // Manual load more đã hoàn thành và có data mới
      isLoadingMoreRef.current = false;

      // Đóng loading message
      message.destroy("loadMore");

      // Hiển thị thông báo thành công
      const newDataCount = listData.length - previousDataCountRef.current;
      message.success({
        content: `✅ Đã tải thêm ${newDataCount} bản ghi mới`,
        duration: 2,
      });

      // Smooth scroll để highlight vùng data mới
      setTimeout(() => {
        if (virtuosoRef.current) {
          // Scroll đến vị trí data mới (khoảng 80% của scroll position cũ)
          const targetPosition = Math.max(scrollPositionRef.current * 0.8, 0);
          virtuosoRef.current.scrollTo({
            top: targetPosition,
            behavior: "smooth",
          });
        }
      }, 300);
    }
  }, [isLoadMore, listData, hasNext, autoPreloadNextPage]);

  return {
    handleNavigate,
    handleLoadMore,
    virtuosoRef,
    autoPreloadNextPage,
  };
};
