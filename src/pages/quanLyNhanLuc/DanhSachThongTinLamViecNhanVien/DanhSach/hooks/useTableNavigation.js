import { useCallback, useRef, useEffect } from "react";
import { message } from "antd";
import { getNavigationParams } from "../utils/dateUtils";

export const useTableNavigation = ({
  isLoading,
  tuThoiGian,
  denThoiGian,
  setParentState,
  searchByParams,
  onLoadMoreData,
  page,
  listData,
  isLoadMore,
}) => {
  // Lưu scroll position và data count trước khi load more
  const scrollPositionRef = useRef(0);
  const virtuosoRef = useRef(null);
  const previousDataCountRef = useRef(0);
  const isLoadingMoreRef = useRef(false);

  const handleNavigate = useCallback(
    (isNext) => () => {
      if (isLoading) return;

      const params = getNavigationParams(tuThoiGian, denThoiGian, isNext);
      setParentState(params);
      searchByParams({ ...params });
    },
    [isLoading, tuThoi<PERSON><PERSON>, denThoi<PERSON>ian, setParentState, searchByParams]
  );

  const handleLoadMore = useCallback(() => {
    // Lưu data count hiện tại
    previousDataCountRef.current = listData?.length || 0;
    isLoadingMoreRef.current = true;

    // Lưu scroll position hiện tại
    if (virtuosoRef.current) {
      virtuosoRef.current.getState((state) => {
        scrollPositionRef.current = state.scrollTop;
      });
    }

    onLoadMoreData({
      page: Number(page) + 1,
      loadMore: true,
      preserveScrollPosition: true,
    });
  }, [onLoadMoreData, page, listData]);

  // Effect để xử lý sau khi load more hoàn thành
  useEffect(() => {
    if (
      !isLoadMore &&
      isLoadingMoreRef.current &&
      listData?.length > previousDataCountRef.current
    ) {
      // Load more đã hoàn thành và có data mới
      isLoadingMoreRef.current = false;

      // Đóng loading message
      message.destroy("loadMore");

      // Hiển thị thông báo thành công
      const newDataCount = listData.length - previousDataCountRef.current;
      message.success({
        content: `✅ Đã tải thêm ${newDataCount} bản ghi mới`,
        duration: 2,
      });

      // Smooth scroll để highlight vùng data mới
      setTimeout(() => {
        if (virtuosoRef.current) {
          // Scroll đến vị trí data mới (khoảng 80% của scroll position cũ)
          const targetPosition = Math.max(scrollPositionRef.current * 0.8, 0);
          virtuosoRef.current.scrollTo({
            top: targetPosition,
            behavior: "smooth",
          });
        }
      }, 300);
    }
  }, [isLoadMore, listData]);

  return {
    handleNavigate,
    handleLoadMore,
    virtuosoRef,
  };
};
