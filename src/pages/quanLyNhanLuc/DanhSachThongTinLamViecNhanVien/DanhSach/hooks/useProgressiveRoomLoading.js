import { useCallback, useRef, useEffect, useState } from "react";
import { message } from "antd";

export const useProgressiveRoomLoading = ({
  listAllPhong = [],
  khoaId,
  phongId,
  tuThoi<PERSON><PERSON>,
  denThoi<PERSON><PERSON>,
  onLoadRoomData, // Function để load data cho 1 phòng
  onUpdateRoomData, // Function để update data cho 1 phòng
}) => {
  const [loadingState, setLoadingState] = useState({
    currentRoomIndex: 0,
    loadedRooms: new Set(),
    isLoading: false,
    totalRooms: 0,
  });

  const loadingQueueRef = useRef([]);
  const isLoadingRef = useRef(false);
  const loadTimeoutRef = useRef(null);

  // Khởi tạo queue loading theo thứ tự phòng
  const initializeLoadingQueue = useCallback(() => {
    if (!listAllPhong?.length) {
      console.log("⚠️ No rooms available for loading queue");
      return;
    }

    const roomsToLoad = phongId
      ? listAllPhong.filter((p) => p.id === phongId)
      : listAllPhong;

    if (roomsToLoad.length === 0) {
      console.log("⚠️ No rooms match the filter criteria");
      return;
    }

    // Tránh re-initialize nếu queue đã có
    if (loadingQueueRef.current.length === roomsToLoad.length) {
      console.log("🔄 Loading queue already initialized, skipping...");
      return;
    }

    loadingQueueRef.current = roomsToLoad.map((phong, index) => ({
      phongId: phong.id,
      phongName: phong.ten,
      index,
      loaded: false,
    }));

    setLoadingState({
      currentRoomIndex: 0,
      loadedRooms: new Set(),
      isLoading: false,
      totalRooms: roomsToLoad.length,
    });

    console.log(
      `🏢 Initialized loading queue for ${roomsToLoad.length} rooms:`,
      roomsToLoad.map((r) => r.ten)
    );
  }, [listAllPhong, phongId]);

  // Load data cho 1 phòng cụ thể
  const loadRoomData = useCallback(
    async (roomInfo) => {
      if (isLoadingRef.current) return;

      isLoadingRef.current = true;
      setLoadingState((prev) => ({
        ...prev,
        isLoading: true,
        currentRoomIndex: roomInfo.index,
      }));

      console.log(
        `🔄 Loading data for room: ${roomInfo.phongName} (${
          roomInfo.index + 1
        }/${loadingQueueRef.current.length})`
      );

      try {
        const roomData = await onLoadRoomData({
          phongId: roomInfo.phongId,
          khoaId,
          tuThoiGian,
          denThoiGian,
          page: 0,
          size: 500,
        });

        // Update data cho phòng này
        onUpdateRoomData(roomInfo.phongId, roomData);

        // Mark room as loaded
        roomInfo.loaded = true;
        setLoadingState((prev) => ({
          ...prev,
          loadedRooms: new Set([...prev.loadedRooms, roomInfo.phongId]),
          isLoading: false,
        }));

        console.log(
          `✅ Loaded ${roomData?.length || 0} items for room: ${
            roomInfo.phongName
          }`
        );

        // Auto load next room after delay
        setTimeout(() => {
          loadNextRoom();
        }, 1000);
      } catch (error) {
        console.error(`❌ Error loading room ${roomInfo.phongName}:`, error);
        message.error(`Lỗi tải dữ liệu phòng ${roomInfo.phongName}`);

        // Continue with next room even if current fails
        setTimeout(() => {
          loadNextRoom();
        }, 2000);
      } finally {
        isLoadingRef.current = false;
      }
    },
    [onLoadRoomData, onUpdateRoomData, khoaId, tuThoiGian, denThoiGian]
  );

  // Load phòng tiếp theo trong queue
  const loadNextRoom = useCallback(() => {
    const queue = loadingQueueRef.current;
    const nextRoom = queue.find((room) => !room.loaded);

    if (nextRoom) {
      loadRoomData(nextRoom);
    } else {
      console.log("🎉 All rooms loaded successfully!");
      setLoadingState((prev) => ({ ...prev, isLoading: false }));
    }
  }, [loadRoomData]);

  // Bắt đầu progressive loading
  const startProgressiveLoading = useCallback(() => {
    if (loadingQueueRef.current.length === 0) {
      console.log("⚠️ No rooms in queue to load");
      return;
    }

    // Tránh start nhiều lần
    if (isLoadingRef.current) {
      console.log("🔄 Progressive loading already in progress, skipping...");
      return;
    }

    console.log("🚀 Starting progressive room loading...");

    // Clear any existing timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }

    // Start loading first room
    const firstRoom = loadingQueueRef.current[0];
    if (firstRoom && !firstRoom.loaded) {
      loadRoomData(firstRoom);
    }
  }, [loadRoomData]);

  // Reset loading state
  const resetLoading = useCallback(() => {
    console.log("🔄 Resetting progressive loading state...");

    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
      loadTimeoutRef.current = null;
    }

    isLoadingRef.current = false;
    loadingQueueRef.current = [];
    setLoadingState({
      currentRoomIndex: 0,
      loadedRooms: new Set(),
      isLoading: false,
      totalRooms: 0,
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, []);

  return {
    loadingState,
    initializeLoadingQueue,
    startProgressiveLoading,
    resetLoading,
    loadNextRoom,
  };
};
