import React, { useEffect, useRef, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { cloneDeep } from "lodash";

import { useConfirm, useLoading, useStore, useQueryAll } from "hooks";

import { query } from "redux-store/stores";
import { GlobalStyle, Main } from "./styled";
import { THIET_LAP_CHUNG } from "constants/index";
import ModalThemMoiNhanVien from "../components/ModalThemMoiNhanVien";
import {
  useScheduleData,
  useTableNavigation,
  useProgressiveRoomLoading,
} from "./hooks";
import { getMonthDaysFromRange } from "./utils";
import {
  LoadingSpinner,
  ContentItem,
  VirtualizedTable,
  PerformanceMonitor,
  ProgressiveLoadingIndicator,
} from "./components";

const DanhSach = ({ parentState, setParentState, khoaLamViec }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();

  const {
    listData,
    page,
    hasNext,
    isLoading,
    isLoadMore,
    isLoadFinish,
    dataSearch,
    isAutoPreloading,
  } = useSelector((state) => state.nvThoiGianLamViec);
  const {
    nvThoiGianLamViec: {
      onLoadMoreData,
      searchByParams,
      onDelete: onDeleteItem,
    },
  } = useDispatch();
  const listAllPhong = useStore("phong.listAllPhong", []);
  const { data: listAllNhanVien, isLoading: isFetchingNhanVien } = useQueryAll(
    query.nhanVien.queryNhanVienByKhoa({
      params: {
        dsKhoaId: [khoaLamViec?.id],
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
      enabled: !!khoaLamViec?.id,
    })
  );
  const { showLoading, hideLoading } = useLoading();
  const { tuThoiGian, denThoiGian } = parentState || {};
  const refModalThemMoiNhanVien = useRef(null);

  const { state, setState, initLayout, updateData, updateRoomData } =
    useScheduleData();
  const { handleNavigate, handleLoadMore, virtuosoRef } = useTableNavigation({
    isLoading,
    tuThoiGian,
    denThoiGian,
    setParentState,
    searchByParams,
    onLoadMoreData,
    page,
    listData,
    isLoadMore,
    hasNext,
  });

  // Progressive room loading
  const {
    loadingState,
    initializeLoadingQueue,
    startProgressiveLoading,
    resetLoading,
  } = useProgressiveRoomLoading({
    listAllPhong,
    khoaId: khoaLamViec?.id,
    phongId,
    tuThoiGian,
    denThoiGian,
    onLoadRoomData: nvThoiGianLamViec.loadRoomData,
    onUpdateRoomData: updateRoomData,
  });

  const { phongId, dsNhanVienId } = dataSearch || {};

  const rangeDates = useMemo(() => {
    try {
      const dates = getMonthDaysFromRange(tuThoiGian, denThoiGian);
      return dates || [];
    } catch (error) {
      return [];
    }
  }, [tuThoiGian, denThoiGian]);

  // Effect để khởi tạo layout (chỉ chạy khi config thay đổi)
  useEffect(() => {
    if (
      !isFetchingNhanVien &&
      listAllPhong?.length &&
      listAllNhanVien?.length
    ) {
      console.log("🏗️ Initializing layout with config...");
      initLayout({
        listAllPhong,
        listAllNhanVien,
        khoaId: khoaLamViec?.id,
        phongId,
        dsNhanVienId,
      });
    }
  }, [
    listAllPhong,
    listAllNhanVien,
    khoaLamViec?.id,
    phongId,
    dsNhanVienId,
    initLayout,
    isFetchingNhanVien,
  ]);

  // Effect để khởi tạo progressive loading queue
  useEffect(() => {
    if (listAllPhong?.length && khoaLamViec?.id) {
      console.log("🏗️ Initializing progressive loading queue...");
      initializeLoadingQueue();
    }
  }, [listAllPhong, khoaLamViec?.id, phongId, initializeLoadingQueue]);

  // Effect để bắt đầu progressive loading sau khi layout ready
  useEffect(() => {
    if (
      state.renderReady &&
      !state.isProcessing &&
      loadingState.totalRooms > 0
    ) {
      console.log("🚀 Starting progressive room loading...");
      startProgressiveLoading();
    }
  }, [
    state.renderReady,
    state.isProcessing,
    loadingState.totalRooms,
    startProgressiveLoading,
  ]);

  // Bỏ effect cũ để tránh conflict với progressive loading
  // useEffect(() => {
  //   if (isLoadFinish && listData?.length >= 0) {
  //     console.log("📊 Updating data...", listData.length, "items");
  //     updateData(listData);
  //   }
  // }, [isLoadFinish, listData, updateData]);

  const onAddNew = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
        });
    },
    [khoaLamViec?.id]
  );

  const onEdit = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
          isEdit: true,
        });
    },
    [khoaLamViec?.id]
  );

  const onDelete = useCallback(
    (item) => () => {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("quanLyNhanLuc.banCoChacMuonXoaDuLieuLichLamViec")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showImg: true,
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          showLoading();
          onDeleteItem(item?.id)
            .then((res) => {
              if (res && res.code === 0) {
                searchByParams({});
              }
            })
            .finally(() => {
              hideLoading();
            });
        }
      );
    },
    [t, onDeleteItem, searchByParams]
  );

  const onCollapse = useCallback(
    (key) => () => {
      let _listKeys = cloneDeep(state.listActiveKeys);
      if (_listKeys.includes(key)) {
        _listKeys = _listKeys.filter((i) => i !== key);
      } else {
        _listKeys.push(key);
      }
      setState({ listActiveKeys: _listKeys });
    },
    [state.listActiveKeys, setState]
  );

  const renderContentItem = useCallback(
    (item) => (
      <ContentItem
        key={item.id}
        item={item}
        onAddNew={onAddNew}
        onEdit={onEdit}
        onDelete={onDelete}
        t={t}
      />
    ),
    [onAddNew, onEdit, onDelete, t]
  );

  if (!rangeDates || rangeDates.length === 0) {
    return (
      <>
        <GlobalStyle />
        <Main noPadding={true}>
          <div>Loading dates...</div>
        </Main>
      </>
    );
  }

  return (
    <>
      <GlobalStyle />
      <Main noPadding={true}>
        <div className={"table-wrapper"}>
          <VirtualizedTable
            state={state}
            rangeDates={rangeDates}
            onCollapse={onCollapse}
            onAddNew={onAddNew}
            renderContentItem={renderContentItem}
            handleNavigate={handleNavigate}
            isLoading={isLoading}
            virtuosoRef={virtuosoRef}
          />
          {/* TableFooter removed - using progressive room loading instead */}
        </div>
        {(isLoading || !state.renderReady || state.isProcessing) &&
          !isLoadMore &&
          !isAutoPreloading && <LoadingSpinner />}
        <ModalThemMoiNhanVien
          ref={refModalThemMoiNhanVien}
          rangeDates={rangeDates}
        />

        {/* Progressive Loading Indicator */}
        <ProgressiveLoadingIndicator
          loadingState={loadingState}
          listAllPhong={listAllPhong}
        />

        {/* Performance Monitor - Press Ctrl+Shift+P to toggle */}
        <PerformanceMonitor
          listData={listData}
          isLoading={isLoading}
          isAutoPreloading={isAutoPreloading}
          renderReady={state.renderReady}
          isProcessing={state.isProcessing}
          page={page}
        />
      </Main>
    </>
  );
};

export default DanhSach;
