import React, { useEffect, useRef, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { cloneDeep } from "lodash";

import { useConfirm, useLoading, useStore, useQueryAll } from "hooks";

import { query } from "redux-store/stores";
import { GlobalStyle, Main } from "./styled";
import { THIET_LAP_CHUNG } from "constants/index";
import ModalThemMoiNhanVien from "../components/ModalThemMoiNhanVien";
import { useScheduleData, useTableNavigation } from "./hooks";
import { getMonthDaysFromRange } from "./utils";
import {
  TableFooter,
  LoadingSpinner,
  ContentItem,
  VirtualizedTable,
} from "./components";

const DanhSach = ({ parentState, setParentState, khoaLamViec }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();

  const {
    listData,
    page,
    hasNext,
    isLoading,
    isLoadMore,
    isLoadFinish,
    dataSearch,
    isAutoPreloading,
  } = useSelector((state) => state.nvThoiGianLamViec);
  const {
    nvThoiGianLamViec: {
      onLoadMoreData,
      searchByParams,
      onDelete: onDeleteItem,
    },
  } = useDispatch();
  const listAllPhong = useStore("phong.listAllPhong", []);
  const { data: listAllNhanVien, isLoading: isFetchingNhanVien } = useQueryAll(
    query.nhanVien.queryNhanVienByKhoa({
      params: {
        dsKhoaId: [khoaLamViec?.id],
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
      enabled: !!khoaLamViec?.id,
    })
  );
  const { showLoading, hideLoading } = useLoading();
  const { tuThoiGian, denThoiGian } = parentState || {};
  const refModalThemMoiNhanVien = useRef(null);

  const { state, setState, initState } = useScheduleData();
  const { handleNavigate, handleLoadMore, virtuosoRef } = useTableNavigation({
    isLoading,
    tuThoiGian,
    denThoiGian,
    setParentState,
    searchByParams,
    onLoadMoreData,
    page,
    listData,
    isLoadMore,
    hasNext,
  });

  const { phongId, dsNhanVienId } = dataSearch || {};

  const rangeDates = useMemo(() => {
    try {
      const dates = getMonthDaysFromRange(tuThoiGian, denThoiGian);
      return dates || [];
    } catch (error) {
      return [];
    }
  }, [tuThoiGian, denThoiGian]);

  useEffect(() => {
    if (isLoadFinish && !isFetchingNhanVien) {
      initState({
        listData,
        listAllPhong,
        listAllNhanVien,
        khoaId: khoaLamViec?.id,
        phongId,
        dsNhanVienId,
      });
    }
  }, [
    isLoadFinish,
    listData,
    listAllPhong,
    listAllNhanVien,
    khoaLamViec?.id,
    phongId,
    dsNhanVienId,
    initState,
    isFetchingNhanVien,
  ]);

  const onAddNew = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
        });
    },
    [khoaLamViec?.id]
  );

  const onEdit = useCallback(
    (item) => () => {
      refModalThemMoiNhanVien.current &&
        refModalThemMoiNhanVien.current.show({
          ...item,
          khoaId: khoaLamViec?.id,
          isEdit: true,
        });
    },
    [khoaLamViec?.id]
  );

  const onDelete = useCallback(
    (item) => () => {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("quanLyNhanLuc.banCoChacMuonXoaDuLieuLichLamViec")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showImg: true,
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          showLoading();
          onDeleteItem(item?.id)
            .then((res) => {
              if (res && res.code === 0) {
                searchByParams({});
              }
            })
            .finally(() => {
              hideLoading();
            });
        }
      );
    },
    [t, onDeleteItem, searchByParams]
  );

  const onCollapse = useCallback(
    (key) => () => {
      let _listKeys = cloneDeep(state.listActiveKeys);
      if (_listKeys.includes(key)) {
        _listKeys = _listKeys.filter((i) => i !== key);
      } else {
        _listKeys.push(key);
      }
      setState({ listActiveKeys: _listKeys });
    },
    [state.listActiveKeys, setState]
  );

  const renderContentItem = useCallback(
    (item) => (
      <ContentItem
        key={item.id}
        item={item}
        onAddNew={onAddNew}
        onEdit={onEdit}
        onDelete={onDelete}
        t={t}
      />
    ),
    [onAddNew, onEdit, onDelete, t]
  );

  if (!rangeDates || rangeDates.length === 0) {
    return (
      <>
        <GlobalStyle />
        <Main noPadding={true}>
          <div>Loading dates...</div>
        </Main>
      </>
    );
  }

  return (
    <>
      <GlobalStyle />
      <Main noPadding={true}>
        <div className={"table-wrapper"}>
          <VirtualizedTable
            state={state}
            rangeDates={rangeDates}
            onCollapse={onCollapse}
            onAddNew={onAddNew}
            renderContentItem={renderContentItem}
            handleNavigate={handleNavigate}
            isLoading={isLoading}
            virtuosoRef={virtuosoRef}
          />
          {state.renderReady && (
            <TableFooter
              hasNext={hasNext}
              isLoadMore={isLoadMore}
              handleLoadMore={handleLoadMore}
              t={t}
              currentPage={page}
              isAutoPreloading={isAutoPreloading}
            />
          )}
        </div>
        {(isLoading || !state.renderReady || state.isProcessing) &&
          !isLoadMore && <LoadingSpinner />}
        <ModalThemMoiNhanVien
          ref={refModalThemMoiNhanVien}
          rangeDates={rangeDates}
        />
      </Main>
    </>
  );
};

export default DanhSach;
