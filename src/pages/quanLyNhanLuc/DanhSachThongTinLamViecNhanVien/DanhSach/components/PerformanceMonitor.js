import React, { useState, useEffect } from "react";
import { Card, Statistic, Row, Col, Badge } from "antd";
import { 
  ClockCircleOutlined, 
  DatabaseOutlined, 
  EyeOutlined,
  ThunderboltOutlined 
} from "@ant-design/icons";

const PerformanceMonitor = ({ 
  listData = [], 
  isLoading, 
  isAutoPreloading,
  renderReady,
  isProcessing,
  page 
}) => {
  const [renderCount, setRenderCount] = useState(0);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [show, setShow] = useState(false);

  useEffect(() => {
    setRenderCount(prev => prev + 1);
    setLastUpdateTime(new Date().toLocaleTimeString());
  });

  // Toggle visibility với Ctrl + Shift + P
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setShow(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  if (!show) return null;

  const getStatusColor = () => {
    if (isLoading) return "processing";
    if (isAutoPreloading) return "warning";
    if (renderReady) return "success";
    return "default";
  };

  const getStatusText = () => {
    if (isLoading) return "Loading";
    if (isAutoPreloading) return "Auto Preloading";
    if (isProcessing) return "Processing";
    if (renderReady) return "Ready";
    return "Idle";
  };

  return (
    <div
      style={{
        position: "fixed",
        top: "10px",
        left: "10px",
        zIndex: 9999,
        width: "300px",
        opacity: 0.9,
      }}
    >
      <Card 
        size="small" 
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <ThunderboltOutlined />
            Performance Monitor
            <Badge status={getStatusColor()} text={getStatusText()} />
          </div>
        }
        style={{ fontSize: "12px" }}
      >
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <Statistic
              title="Data Count"
              value={listData.length}
              prefix={<DatabaseOutlined />}
              valueStyle={{ fontSize: "14px" }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="Current Page"
              value={page + 1}
              prefix={<EyeOutlined />}
              valueStyle={{ fontSize: "14px" }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="Renders"
              value={renderCount}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ fontSize: "14px", color: renderCount > 10 ? "#ff4d4f" : "#52c41a" }}
            />
          </Col>
          <Col span={12}>
            <div style={{ fontSize: "10px", color: "#666" }}>
              Last Update: {lastUpdateTime}
            </div>
          </Col>
        </Row>
        
        <div style={{ 
          marginTop: "8px", 
          fontSize: "10px", 
          color: "#999",
          borderTop: "1px solid #f0f0f0",
          paddingTop: "4px"
        }}>
          Press Ctrl+Shift+P to toggle
        </div>
      </Card>
    </div>
  );
};

export default React.memo(PerformanceMonitor);
