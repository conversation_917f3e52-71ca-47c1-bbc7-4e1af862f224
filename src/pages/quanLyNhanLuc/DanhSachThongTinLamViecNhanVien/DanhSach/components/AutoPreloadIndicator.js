import React, { useState, useEffect } from "react";
import { Progress, Badge } from "antd";
import { CloudDownloadOutlined } from "@ant-design/icons";

const AutoPreloadIndicator = ({ 
  isAutoPreloading, 
  currentPage, 
  totalPreloaded,
  hasNext 
}) => {
  const [show, setShow] = useState(false);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (isAutoPreloading) {
      setShow(true);
      setProgress(0);
      
      // Simulate progress
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(interval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      return () => clearInterval(interval);
    } else if (show) {
      setProgress(100);
      setTimeout(() => {
        setShow(false);
        setProgress(0);
      }, 1000);
    }
  }, [isAutoPreloading, show]);

  if (!show) return null;

  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        right: "20px",
        zIndex: 1000,
        backgroundColor: "#fff",
        border: "1px solid #d9d9d9",
        borderRadius: "8px",
        padding: "12px 16px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        minWidth: "280px",
        animation: "slideInUp 0.3s ease-out",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
        <CloudDownloadOutlined 
          style={{ 
            color: "#1890ff",
            animation: isAutoPreloading ? "spin 1s linear infinite" : "none"
          }} 
        />
        <span style={{ fontWeight: "500", color: "#1890ff" }}>
          {isAutoPreloading ? "Đang tải ngầm..." : "Hoàn thành"}
        </span>
        <Badge 
          count={totalPreloaded} 
          style={{ backgroundColor: "#52c41a" }}
          title={`Đã tải ${totalPreloaded} trang`}
        />
      </div>

      <div style={{ marginBottom: "4px", fontSize: "12px", color: "#666" }}>
        Trang {currentPage + 1} {hasNext ? "→ Tiếp tục tải..." : "→ Đã hết"}
      </div>

      <Progress 
        percent={progress} 
        size="small" 
        status={isAutoPreloading ? "active" : "success"}
        showInfo={false}
      />

      <style jsx>{`
        @keyframes slideInUp {
          from {
            transform: translateY(100%);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default React.memo(AutoPreloadIndicator);
