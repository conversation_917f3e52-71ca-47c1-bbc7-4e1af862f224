import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button } from "antd";
import { DownOutlined } from "@ant-design/icons";

const NewDataIndicator = ({ 
  newDataCount, 
  onScrollToNew, 
  isVisible,
  onDismiss 
}) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isVisible && newDataCount > 0) {
      setShow(true);
      // Tự động ẩn sau 5 giây
      const timer = setTimeout(() => {
        setShow(false);
        onDismiss?.();
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, newDataCount, onDismiss]);

  if (!show || newDataCount <= 0) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: "50%",
        right: "20px",
        transform: "translateY(-50%)",
        zIndex: 1000,
        backgroundColor: "#fff",
        border: "2px solid #52c41a",
        borderRadius: "8px",
        padding: "12px 16px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        animation: "slideInRight 0.3s ease-out",
      }}
    >
      <div style={{ marginBottom: "8px", color: "#52c41a", fontWeight: "bold" }}>
        <Badge count={newDataCount} style={{ backgroundColor: "#52c41a" }}>
          ✅ Đã tải thêm dữ liệu mới
        </Badge>
      </div>
      
      <div style={{ display: "flex", gap: "8px" }}>
        <Button 
          type="primary" 
          size="small" 
          icon={<DownOutlined />}
          onClick={() => {
            onScrollToNew?.();
            setShow(false);
          }}
        >
          Xem ngay
        </Button>
        
        <Button 
          size="small" 
          onClick={() => {
            setShow(false);
            onDismiss?.();
          }}
        >
          Đóng
        </Button>
      </div>

      <style jsx>{`
        @keyframes slideInRight {
          from {
            transform: translateX(100%) translateY(-50%);
            opacity: 0;
          }
          to {
            transform: translateX(0) translateY(-50%);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default React.memo(NewDataIndicator);
