import React, { useMemo } from "react";
import { TableVirtuoso } from "react-virtuoso";
import { Empty } from "antd";
import classNames from "classnames";

import TableHeader from "./TableHeader";
import { isArray } from "utils/index";
import PhongRow from "./PhongRow";
import NhanVienRow from "./NhanVienRow";

const VirtualizedTable = ({
  state,
  rangeDates = [],
  onAddNew,
  onCollapse,
  renderContentItem,
  isLoading,
  handleNavigate,
  virtuosoRef,
}) => {
  if (
    !state ||
    !rangeDates.length ||
    !onAddNew ||
    !onCollapse ||
    !state.renderReady
  ) {
    return null;
  }

  const flattenedData = useMemo(() => {
    if (!state.renderReady) return [];

    return state.dataSource.reduce((rows, phong) => {
      const isExpanded = state.listActiveKeys.includes(phong.key);

      // Row phòng
      rows.push({
        type: "phong",
        key: phong.key,
        name: phong.name,
        isExpanded,
      });

      // Row nhân viên
      if (isExpanded && isArray(phong.children, true)) {
        phong.children.forEach((nv) => {
          const sortedChildren = [...(nv.children || [])].sort((a, b) => {
            const startA = new Date(a.tuThoiGian).getTime();
            const startB = new Date(b.tuThoiGian).getTime();
            if (startA === startB) {
              const endA = new Date(a.denThoiGian).getTime();
              const endB = new Date(b.denThoiGian).getTime();
              return endA - endB;
            }
            return startA - startB;
          });

          rows.push({
            type: "nhanvien",
            phongKey: phong.key,
            key: nv.key,
            name: nv.name,
            children: sortedChildren,
          });
        });
      }

      return rows;
    }, []);
  }, [state.dataSource, state.listActiveKeys, state.renderReady]);

  if (!isArray(state.dataSource, true) && !isLoading) {
    return (
      <tbody>
        <tr>
          <td colSpan={rangeDates.length + 3} className="not-found">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          </td>
        </tr>
      </tbody>
    );
  }

  return (
    <TableVirtuoso
      ref={virtuosoRef}
      data={flattenedData}
      overscan={300}
      increaseViewportBy={{ top: 1000, bottom: 1000 }}
      fixedHeaderContent={() => (
        <TableHeader rangeDates={rangeDates} handleNavigate={handleNavigate} />
      )}
      computeItemKey={(_, row) => {
        if (row.type === "phong") {
          return `${row.type}_${row.key}`;
        }
        if (row.type === "nhanvien") {
          return `${row.type}_${row.phongKey}_${row.key}`;
        }
      }}
      itemContent={(_, row) => {
        if (!row) return null;
        if (row.type === "phong")
          return (
            <PhongRow
              phong={row}
              rangeDates={rangeDates}
              onAddNew={onAddNew}
              onCollapse={onCollapse}
            />
          );
        if (row.type === "nhanvien")
          return (
            <NhanVienRow
              nhanVien={row}
              phongKey={row.phongKey}
              rangeDates={rangeDates}
              onAddNew={onAddNew}
              renderContentItem={renderContentItem}
            />
          );
      }}
      components={{
        Table: (props) => (
          <table {...props}>
            <colgroup>
              <col
                className="date-col"
                style={{
                  width: `calc((100vw / ${rangeDates.length}) + 2px - 50px)`,
                }}
              />
              <col className="action-col" />
              {rangeDates.map((item) => (
                <col
                  className="date-col"
                  key={item.label}
                  style={{
                    width: `calc((100vw / ${rangeDates.length}) + 2px - 50px)`,
                  }}
                />
              ))}
              <col className="action-col" />
            </colgroup>
            {props.children}
          </table>
        ),
        TableRow: ({ item, ...props }) => (
          <>
            <tr
              {...props}
              className={classNames({
                "row-collapse": item.type === "phong",
              })}
            />
          </>
        ),
        TableHead: (props) => <thead {...props} style={{ zIndex: 4 }} />,
      }}
    />
  );
};

export default React.memo(VirtualizedTable);
