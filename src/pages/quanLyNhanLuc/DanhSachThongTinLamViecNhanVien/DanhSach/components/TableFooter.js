import React, { useMemo } from "react";
import { Spin, message } from "antd";
import { Button } from "components";

const TableFooter = ({
  hasNext,
  isLoadMore,
  handleLoadMore,
  t,
  currentPage,
}) => {
  const handleLoadMoreWithNotification = () => {
    handleLoadMore();
    // Hiển thị thông báo đang tải thêm dữ liệu
    message.loading({
      content: `${t("common.dangTaiDuLieu")} trang ${currentPage + 1}...`,
      key: "loadMore",
      duration: 0,
    });
  };

  const renderFooter = useMemo(() => {
    if (!hasNext && !isLoadMore) return null;

    return (
      <div
        className="footer"
        style={{
          textAlign: "center",
          padding: "20px",
          borderTop: "1px solid #f0f0f0",
          backgroundColor: "#fafafa",
        }}
      >
        {!isLoadMore && hasNext && (
          <div>
            <div style={{ marginBottom: "10px", color: "#666" }}>
              Trang {currentPage + 1} - Còn {hasNext ? "nhiều" : "ít"} dữ liệu
            </div>
            <Button
              type="primary"
              onClick={handleLoadMoreWithNotification}
              style={{ minWidth: "120px" }}
            >
              {t("common.xemThem")} →
            </Button>
          </div>
        )}
        {isLoadMore && (
          <div>
            <Spin />
            <div style={{ marginTop: "10px", color: "#1890ff" }}>
              Đang tải thêm dữ liệu...
            </div>
          </div>
        )}
      </div>
    );
  }, [hasNext, isLoadMore, handleLoadMoreWithNotification, t, currentPage]);

  return renderFooter;
};

export default React.memo(TableFooter);
