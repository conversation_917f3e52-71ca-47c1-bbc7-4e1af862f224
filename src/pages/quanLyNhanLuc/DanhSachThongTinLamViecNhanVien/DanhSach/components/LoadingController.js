import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Switch, Card } from "antd";
import { 
  PauseCircleOutlined, 
  PlayCircleOutlined,
  StopOutlined,
  SettingOutlined 
} from "@ant-design/icons";

const LoadingController = ({ 
  loadingState,
  onPause,
  onResume,
  onStop,
  onReset
}) => {
  const [show, setShow] = useState(false);
  const [autoMode, setAutoMode] = useState(true);

  // Toggle visibility với Ctrl + Shift + L
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'L') {
        setShow(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  if (!show) return null;

  const { isLoading, totalRooms, loadedRooms } = loadingState;

  return (
    <div
      style={{
        position: "fixed",
        top: "50%",
        right: "20px",
        transform: "translateY(-50%)",
        zIndex: 1001,
        width: "250px",
        opacity: 0.95,
      }}
    >
      <Card 
        size="small" 
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <SettingOutlined />
            Loading Controller
          </div>
        }
        style={{ fontSize: "12px" }}
      >
        <div style={{ marginBottom: "12px" }}>
          <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "8px" }}>
            <span>Auto Mode:</span>
            <Switch 
              size="small" 
              checked={autoMode} 
              onChange={setAutoMode}
            />
          </div>
          
          <div style={{ fontSize: "10px", color: "#666", marginBottom: "8px" }}>
            Progress: {loadedRooms.size}/{totalRooms} rooms
          </div>
        </div>

        <div style={{ display: "flex", gap: "4px", flexWrap: "wrap" }}>
          {!isLoading ? (
            <Button 
              size="small" 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={onResume}
              disabled={autoMode}
            >
              Start
            </Button>
          ) : (
            <Button 
              size="small" 
              icon={<PauseCircleOutlined />}
              onClick={onPause}
              disabled={autoMode}
            >
              Pause
            </Button>
          )}
          
          <Button 
            size="small" 
            danger
            icon={<StopOutlined />}
            onClick={onStop}
          >
            Stop
          </Button>
          
          <Button 
            size="small" 
            onClick={onReset}
          >
            Reset
          </Button>
        </div>
        
        <div style={{ 
          marginTop: "8px", 
          fontSize: "10px", 
          color: "#999",
          borderTop: "1px solid #f0f0f0",
          paddingTop: "4px"
        }}>
          Press Ctrl+Shift+L to toggle
        </div>
      </Card>
    </div>
  );
};

export default React.memo(LoadingController);
