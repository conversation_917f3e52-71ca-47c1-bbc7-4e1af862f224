import React from "react";
import { Progress, Card, Badge } from "antd";
import { 
  HomeOutlined, 
  LoadingOutlined,
  CheckCircleOutlined 
} from "@ant-design/icons";

const ProgressiveLoadingIndicator = ({ 
  loadingState,
  listAllPhong = []
}) => {
  const { currentRoomIndex, loadedRooms, isLoading, totalRooms } = loadingState;

  if (totalRooms === 0) return null;

  const progress = Math.round((loadedRooms.size / totalRooms) * 100);
  const currentRoom = listAllPhong[currentRoomIndex];

  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        left: "20px",
        zIndex: 1000,
        width: "320px",
        backgroundColor: "#fff",
        border: "1px solid #d9d9d9",
        borderRadius: "8px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        animation: "slideInLeft 0.3s ease-out",
      }}
    >
      <Card 
        size="small" 
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <HomeOutlined />
            <span><PERSON><PERSON> tải dữ liệu theo phòng</span>
            {isLoading && <LoadingOutlined spin />}
          </div>
        }
        style={{ fontSize: "12px" }}
      >
        <div style={{ marginBottom: "12px" }}>
          <Progress 
            percent={progress} 
            size="small" 
            status={progress === 100 ? "success" : "active"}
            format={() => `${loadedRooms.size}/${totalRooms}`}
          />
        </div>

        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <div>
            {isLoading ? (
              <Badge status="processing" text={`Đang tải: ${currentRoom?.ten || 'N/A'}`} />
            ) : progress === 100 ? (
              <Badge status="success" text="Hoàn thành tất cả phòng" />
            ) : (
              <Badge status="default" text="Đang chờ..." />
            )}
          </div>
          
          <div style={{ fontSize: "10px", color: "#666" }}>
            {progress === 100 && <CheckCircleOutlined style={{ color: "#52c41a" }} />}
          </div>
        </div>

        {/* Danh sách phòng đã load */}
        {loadedRooms.size > 0 && (
          <div style={{ 
            marginTop: "8px", 
            fontSize: "10px", 
            color: "#999",
            borderTop: "1px solid #f0f0f0",
            paddingTop: "4px",
            maxHeight: "60px",
            overflowY: "auto"
          }}>
            <div>Đã tải: {Array.from(loadedRooms).map(roomId => {
              const room = listAllPhong.find(p => p.id === roomId);
              return room?.ten;
            }).filter(Boolean).join(", ")}</div>
          </div>
        )}
      </Card>

      <style jsx>{`
        @keyframes slideInLeft {
          from {
            transform: translateX(-100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};

export default React.memo(ProgressiveLoadingIndicator);
