import React from "react";
import { Pagination, Select } from "antd";

const { Option } = Select;

const TablePagination = ({
  current,
  total,
  pageSize,
  onPageChange,
  onPageSizeChange,
  showSizeChanger = true,
  showQuickJumper = true,
  showTotal = true,
  t,
}) => {
  const pageSizeOptions = ["10", "20", "50", "100", "500"];

  const showTotalText = (total, range) => {
    return `${range[0]}-${range[1]} của ${total} bản ghi`;
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "16px 0",
        borderTop: "1px solid #f0f0f0",
        backgroundColor: "#fafafa",
      }}
    >
      <div style={{ color: "#666" }}>
        {showTotal && showTotalText(total, [
          (current - 1) * pageSize + 1,
          Math.min(current * pageSize, total)
        ])}
      </div>

      <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
        {showSizeChanger && (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <span>Hiển thị:</span>
            <Select
              value={pageSize}
              onChange={onPageSizeChange}
              style={{ width: 80 }}
            >
              {pageSizeOptions.map(size => (
                <Option key={size} value={parseInt(size)}>
                  {size}
                </Option>
              ))}
            </Select>
            <span>/ trang</span>
          </div>
        )}

        <Pagination
          current={current}
          total={total}
          pageSize={pageSize}
          onChange={onPageChange}
          showQuickJumper={showQuickJumper}
          showSizeChanger={false} // Đã có custom size changer
          size="default"
          showLessItems={true}
        />
      </div>
    </div>
  );
};

export default React.memo(TablePagination);
