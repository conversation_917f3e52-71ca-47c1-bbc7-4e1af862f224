import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { cloneDeep, debounce, groupBy, isNumber, sumBy } from "lodash";
import moment from "moment";
import { Menu, message, Row, Select as SelectAntd } from "antd";
import {
  Checkbox,
  Button,
  TableWrapper,
  Select,
  Pagination,
  HeaderSearch,
  Tooltip,
  SplitPanel,
  InputTimeout,
  Dropdown,
  TimeInput,
} from "components";
import CircleCheck from "assets/images/khamBenh/circle-check.png";
import {
  containText,
  isArray,
  openInNewTab,
  roundNumberPoint,
  sortString,
} from "utils";
import ModalThemLieuDung from "../../DonThuoc/ModalThemLieuDung";
import lieuDungProvider from "data-access/categories/dm-lieu-dung-provider";
import { useConfirm, useEnum, useLazyKVMap, useListAll, useStore } from "hooks";
import stringUtils from "mainam-react-native-string-utils";
import {
  DOI_TUONG,
  LOAI_DON_THUOC,
  LIST_THOI_DIEM_DUNG,
  HOTKEY,
  ENUM,
  CO_CHE_DUYET_PHAT,
} from "constants/index";
import { DEFAULT_POPUP_CHI_DINH_THUOC } from "pages/application/TuyChinhGiaoDienPhamMem/KhamBenh/config";
import chiDinhThuocUtils, {
  evalString,
  gopThuocKhacLo,
} from "utils/chi-dinh-thuoc-utils";
import { SVG } from "assets";
import fileUtils from "utils/file-utils";
import { getCachDung } from "pages/khamBenh/utils";
import { Main } from "../TableThuocKeNgoai/styled";
import { GlobalStyle, WrapperInput, WrapperSelect } from "./styled";
import IcXemHdsd from "../Thuoc/IcXemHdsd";
import classNames from "classnames";
import { RIGHT_COLUMNS } from "../TableDonThuoc/hooks/useColumns";
import useThietLapChung from "./hooks/useThietLapChung";
import { VN_SEVERITY_RANKING } from "pages/khamBenh/DonThuoc/ModalChiDinhThuoc/vnConstants";

const { Setting } = TableWrapper;

const { Option } = SelectAntd;

const TableThuocDaKe = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const {
    onSelected,
    thanhTien,
    toDieuTriId,
    loaiDonThuoc,
    visible,
    activeHotKey = true,
    layerId,
    keyword,
    nbDotDieuTriId,
    onSetData,
    phanNhomDvKhoId,
    splitCacheCustomize,
    onResizeSplit = () => {},
    dsKho,
    isTuTruc = false,
    rawMimsData,
    getColorTuongTacThuoc,
  } = props;
  const refModalThemLieuDung = useRef(null);
  const refInput = useRef(null);
  const refSettings = useRef(null);
  const refSettingsLeft = useRef(null);
  const refSelectLieuDung = useRef(null);
  const refBtnThemNhanh = useRef(null);
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const refLieuDung = useRef({});
  const refSelectRow = useRef(null);
  const refAddService = useRef(null);
  const refSplitPanelLeft = useRef(null);
  const refSplitPanelRight = useRef(null);
  const showMessLessThan0Ref = useRef(null);

  const {
    pageThuocDaKe,
    sizeThuocDaKe,
    totalElementsThuocDaKe,
    listThuocDaKeToDieuTri,
  } = useSelector((state) => state.chiDinhDichVuKho);
  const {
    lieuDung: { createOrEdit: createOrEditLieuDung },
    lieuDungThuoc: { create: createOrEditLieuDungThuoc },
    chiDinhThuocDaKe: { getListThuocDaKeToDieuTri },
    phimTat: { onRegisterHotkey },
  } = useDispatch();
  const [listAllDuongDung] = useListAll("duongDung", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listAllLieuDung, reloadListAllLieuDung] = useListAll(
    "lieuDung",
    {},
    true
  );

  const {
    BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT,
    TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC,
    dataThoiGianVaTenBuoiSang,
    dataThoiGianVaTenBuoiChieu,
    dataThoiGianVaTenBuoiToi,
    dataThoiGianVaTenBuoiDem,
    isKhongHienThiCheckBoxDotXuatKhiKeTuTruc,
  } = useThietLapChung();

  const configData = useStore("chiDinhKhamBenh.configData", {});
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const { nbKetLuan } = thongTinChiTiet || {};
  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const [getDonViTocDoTruyen] = useLazyKVMap(listDonViTocDoTruyen);

  const isValidateLieuDungCachDung =
    thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
    BAT_BUOC_LIEU_DUNG_CACH_DUNG_THUOC_BHYT.toLowerCase() === "true" &&
    loaiDonThuoc === LOAI_DON_THUOC.THUOC_KHO;

  const [state, _setState] = useState({
    listServiceSelected: [],
    elementKey: 1,
    keySelected: [],
    dataSelected: [],
    msgWarned: [],
    openLieuDung: {},
    expandedKeys: [],
    leftExpandedKeys: [],
  });
  const setState = (data) => {
    _setState((pre) => ({
      ...pre,
      ...data,
    }));
  };

  const isNoiTru = useMemo(() => {
    return (
      window.location.pathname.indexOf(
        "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri"
      ) >= 0
    );
  }, []);
  const isPttt = useMemo(() => {
    return window.location.pathname.indexOf("/phau-thuat-thu-thuat") >= 0;
  }, []);
  const isCDHA = useMemo(() => {
    return window.location.pathname.indexOf("/chan-doan-hinh-anh") >= 0;
  }, []);

  useEffect(() => {
    //khi tắt hoặc bật thì mặc đinh reset popup
    setState({
      selectedRowKeys: [],
      listServiceSelected: [],
      dataSelected: [],
      msgWarned: [],
      keySelected: [],
      keyword: "",
      phanNhomDvKhoId: null,
      ma: null,
      dichVuCha: null,
    });
  }, [loaiDonThuoc, visible]);

  const getDataThuoc = (value, phanNhomDvKhoId, page = 0, size = 50) => {
    let objSearch = {
      tenDichVu: value,
      page,
      size,
      nbDotDieuTriId: nbDotDieuTriId,
      chiDinhTuDichVuId: toDieuTriId,
      phanNhomDvKhoId,
      thuocDaChiDinh: true,
    };

    getListThuocDaKeToDieuTri(objSearch);
  };

  const debounceFunc = useCallback(debounce(getDataThuoc, 500), [toDieuTriId]);

  const dataKey = (() => {
    switch (true) {
      case isNoiTru:
        return "noiTru.popupChiDinhThuocNoiTru";
      case isCDHA:
        return "cdha.popupChiDinhThuocCdha";
      case isPttt:
        return "phauThuat.popupChiDinhThuocPttt";
      default:
        break;
    }
  })();
  const dataThietLap = useStore(
    `thietLap.thietLapGiaoDien.${dataKey}`,
    DEFAULT_POPUP_CHI_DINH_THUOC
  );

  const isFocusSelect = () => {
    let activeEl = document.activeElement?.className;
    if (activeEl == "ant-select-selection-search-input") return true;

    return false;
  };

  useEffect(() => {
    if (visible && activeHotKey) {
      onRegisterHotkey({
        layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.ENTER, //key enter
            onEvent: (e) => {
              if (!isFocusSelect() && refAddService.current) {
                refAddService.current();
              }
            },
          },
          {
            keyCode: HOTKEY.DOWN, // down
            onEvent: () => {
              if (!isFocusSelect() && refSelectRow.current)
                refSelectRow.current(1);
            },
          },
          {
            keyCode: HOTKEY.UP, //up
            onEvent: () => {
              if (!isFocusSelect() && refSelectRow.current)
                refSelectRow.current(-1);
            },
          },
        ],
      });
      setTimeout(() => {
        refInput.current && refInput.current.focus();
      }, 500);
    }
  }, [visible, activeHotKey]);

  const showLuuY = useMemo(() => {
    const isTuTra = (state.dataSelected || []).some((x) => x.tuTra);
    const isThuocNhaThuoc = loaiDonThuoc === LOAI_DON_THUOC.NHA_THUOC;

    return isTuTra || isThuocNhaThuoc;
  }, [state.dataSelected, loaiDonThuoc]);

  useEffect(() => {
    if (visible) {
      debounceFunc(keyword, phanNhomDvKhoId);
    }
  }, [keyword, visible, toDieuTriId, phanNhomDvKhoId]);

  const { isCheckAllDotXuat, isCheckAllBoSung } = useMemo(() => {
    if (!isArray(state.dataSelected, true)) {
      return { isCheckAllDotXuat: false, isCheckAllBoSung: false };
    }
    return {
      isCheckAllDotXuat: state.dataSelected.every((i) => i.dotXuat),
      isCheckAllBoSung: state.dataSelected.every((i) => i.boSung),
    };
  }, [state.dataSelected]);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (dataSource?.findIndex((item) => item.key === state?.key) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < dataSource.length) {
      setState({ key: dataSource[indexNextItem]?.key });
      document
        .getElementsByClassName(
          "table-row-odd " + dataSource[indexNextItem]?.key
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const onSelectChangeLeft = async (selectedRowKeys, data, msg) => {
    let selectedRowKeysService = data;
    // khi search thì selected data bị mất giá trị soLuong
    // => xử lý giữ lại giá trị số lượng đã nhập
    data.forEach((item1) => {
      state.dataSelected.forEach((item2) => {
        if (
          item1.dichVuId &&
          item2.dichVuId &&
          item1.dichVuId == item2.dichVuId &&
          item1.key === item2.key
        ) {
          item1.soLuong = item2.soLuong;
          item1.lieuDungId = item2.lieuDungId;
        }
      });
    });
    Promise.all(
      data.map((item) => {
        return new Promise(async (resolve, reject) => {
          if (!item.listLieuDung) {
            if (refLieuDung.current[item.dichVuId]) {
              //check trong ref có liều dùng cho dv này chưa, nếu có rồi thì lôi ra dùng
              //còn chưa có thì gọi api
              item.listLieuDung = refLieuDung.current[item.dichVuId];
            } else {
              const listLieuDung = await lieuDungProvider
                .searchAll({
                  bacSiId: nhanVienId,
                  dichVuId: item.dichVuId,
                  page: "",
                  size: "",
                })
                .then((s) => {
                  return s?.data || [];
                });
              refLieuDung.current[item.dichVuId] = listLieuDung;
              item.listLieuDung = listLieuDung;
            }
            resolve(item);
          } else resolve(item);
        });
      })
    ).then((data) => {
      setState({
        selectedRowKeys: selectedRowKeys,
        listServiceSelected: data,
        keySelected: selectedRowKeys,
        dataSelected: data,
        msgWarned: msg ? [...state.msgWarned, msg] : state.msgWarned,
      });
    });
    onSelected(selectedRowKeysService, true);
  };

  const onSelectChangeRight = (selectedRowKeys, data, item) => {
    setState({
      listServiceSelected: data,
      keySelected: selectedRowKeys,
      dataSelected: data,
    });
    onSelected(data, true);
  };

  const onCheckAllLoaiChiDinh = (type, isCheck) => (e) => {
    let data = cloneDeep(state.dataSelected);
    let otherKey = type === "dotXuat" ? "boSung" : "dotXuat";
    if (isCheck) {
      data = data.map((item) => ({
        ...item,
        [type]: true,
        [otherKey]: false,
      }));
    } else {
      data = data.map((item) => ({
        ...item,
        [type]: false,
      }));
    }
    setState({ dataSelected: data });
    onSelected(data);
  };

  const onChangePage = (page) => {
    getDataThuoc(keyword, phanNhomDvKhoId, page - 1, size);
  };

  const onSizeChange = (value) => {
    getDataThuoc(keyword, phanNhomDvKhoId, 0, value);
  };

  const onSelectLeft = (record, isSelected) => {
    record = cloneDeep(record);

    let listServiceSelected = [];

    function onSelectLeftContinue() {
      let selectedRowKeys = [];
      listServiceSelected.forEach((item) => {
        selectedRowKeys.push(item.key);
        item?.children?.forEach((x) => {
          selectedRowKeys.push(x.key);
        });
      });

      onSelectChangeLeft(selectedRowKeys, listServiceSelected);
    }
    if (isSelected) {
      //kiểm tra thuốc trùng với thuốc đã chọn
      let dupplicateDaChon = state.dataSelected.filter(
        (item1) => item1.dichVuId === record.dichVuId
      );

      let dupplicateDaChonHoatChat = state.dataSelected.filter(
        (item1) => record.maHoatChat && item1.maHoatChat === record.maHoatChat
      );
      function onCheckDuplicate() {
        if (
          !isNoiTru &&
          ((dupplicateDaChon && dupplicateDaChon.length > 0) ||
            dupplicateDaChonHoatChat.length)
        ) {
          const duplicateSoLuong = dupplicateDaChon.reduce(
            (a, b) =>
              a + Number(b.heSoDinhMuc != 1 ? b.soLuongSoCap : b.soLuong),
            0
          );
          let mes = null;
          //check trùng theo tên hoạt chất
          if (dupplicateDaChon.length) {
            mes = t(
              "khamBenh.{{tenHoatChat}}dangDuocKeChiDinh{{soLuong}}{{tenDvtSoCap}}",
              {
                tenHoatChat: dupplicateDaChon[0].ten,
                soLuong: duplicateSoLuong,
                tenDvtSoCap: dupplicateDaChon[0].tenDvtSoCap,
              }
            );
          } else {
            mes = `${t("khamBenh.hoatChat{{tenHoatChat}}dangDuocChon", {
              tenHoatChat: dupplicateDaChonHoatChat[0].tenHoatChat,
            })}. ${t("common.thuoc")}:${dupplicateDaChonHoatChat[0].ten}`;
          }
          showConfirm(
            {
              title: t("common.canhBao"),
              content: `${mes}. <br/> ${t("khamBenh.chiDinh.tiepTuc")}`,
              cancelText: t("common.huy"),
              okText: t("common.xacNhan"),
              showImg: false,
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              let dataSelected = [...state.dataSelected, record];
              let selectedRowKeys = [...state.selectedRowKeys, record.key];

              if ((record?.children || []).length > 0) {
                setState({
                  expandedKeys: [...state.expandedKeys, record.key],
                });
              }
              onSelectChangeLeft(selectedRowKeys, dataSelected);
            },
            () => {}
          );
        } else {
          record.key = record.key + stringUtils.guid();
          let dataSelected = [
            ...state.dataSelected,
            {
              ...record,
              soNgay: nbKetLuan?.soNgayChoDon ? nbKetLuan?.soNgayChoDon : 1,
            },
          ];
          listServiceSelected = dataSelected;
          if ((record?.children || []).length > 0) {
            setState({
              expandedKeys: [...state.expandedKeys, record.key],
            });
          }

          onSelectLeftContinue();
        }
      }

      //kiểm tra thuốc dấu sao
      if (record?.thuocDauSao && dupplicateDaChon?.length == 0) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: `${t("khamBenh.{{tenThuoc}}laThuocDauSao", {
              tenThuoc: record?.tenDichVu,
            })}.`,
            okText: t("common.dong"),
            showImg: false,
            showBtnCancel: false,
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {},
          () => {},
          () => {
            onCheckDuplicate();
          }
        );
      } else {
        onCheckDuplicate();
      }
    } else {
      listServiceSelected = state.dataSelected.filter(
        (item) => record.key !== item.key
      );

      onSelectLeftContinue();
    }
  };

  const onCheckAll = async (e) => {
    setState({
      selectedRowKeys: [],
      listServiceSelected: [],
      keySelected: [],
      dataSelected: [],
      isCheckAll: false,
    });
  };

  const onSelectRight = (record) => {
    let listServiceSelected = state.dataSelected.filter(
      (item) => record.key !== item.key
    );
    let selectedRowKeys = state.selectedRowKeys.filter(
      (item) => item !== record.key
    );

    onSelectChangeRight(selectedRowKeys, listServiceSelected);
  };

  const onViewHdsd = (data) => async (e) => {
    e.stopPropagation();
    const byteArray = await fileUtils.getFromUrl({
      url: fileUtils.absoluteFileUrl(data.dsTaiLieuHdsd[0]),
    });
    const blob = new Blob([new Uint8Array(byteArray)], {
      type: "application/pdf",
    });
    const blobUrl = window.URL.createObjectURL(blob);

    window.open(blobUrl, "_blank").focus();
  };

  const rowSelectionLeft = {
    columnTitle: (
      <HeaderSearch title={<Setting refTable={refSettingsLeft} />} />
    ),
    columnWidth: 40,
    selectedRowKeys: [],
    preserveSelectedRowKeys: true,
    onSelect: onSelectLeft,
    getCheckboxProps: (record) => {
      if (record.dungKemId) {
        return {
          style: { display: "none" },
        };
      }
      return {};
    },
  };

  const rowSelectionRight = {
    columnTitle: (
      <HeaderSearch
        title={<Checkbox onChange={onCheckAll} checked={true} />}
        isTitleCenter={true}
      />
    ),
    columnWidth: 40,
    // onChange: onSelectChangeRight,
    selectedRowKeys: state.keySelected,
    preserveSelectedRowKeys: true,
    onSelect: onSelectRight,
    getCheckboxProps: (record) => {
      if (record.dungKemId) {
        return {
          style: { display: "none" },
        };
      }
      return {};
    },
  };

  const showMessLessThan0 = (index, type) => {
    let tenCot =
      type === "soLan1Ngay" ? t("common.lan/ngay") : t("common.sl/lan");

    let value = state.dataSelected[index][type];
    if (value <= 0) {
      message.error(`${t("common.cot")} ${tenCot} ${t("common.phaiLonHon0")}`);
    }
  };

  showMessLessThan0Ref.current = showMessLessThan0;
  //xử lý tính lại số lượng + SL sơ cấp khi các field số ngày, số lần / ngày, số lượng / lần thay đổi
  const genarateSoLuong = (record, soLuong) => {
    if (record.heSoDinhMuc != 1) {
      const soLuongSoCap = roundNumberPoint(soLuong / record.heSoDinhMuc, 6); // nếu các màn khác thì lấy đến số thập phân thứ 2

      if (record.dvtSuDungId == record.donViTinhId) {
        record.soLuong = soLuong;
        record.soLuongSoCap = soLuongSoCap;
      }
    } else {
      if (record.dvtSuDungId == record.donViTinhId) {
        record.soLuong = soLuong;
        record.soLuongSoCap = soLuong;
      }
    }

    record.soLuong = roundNumberPoint(record.soLuong, 6);
    record.soLuongSoCap = roundNumberPoint(record.soLuongSoCap, 6);
  };
  const onChangeInput = (type, index, data) => (e, list) => {
    let value = "";
    if (e?.target) {
      value = e.target.value;
    } else if (e?._d) value = e._d.format("MM/dd/yyyy");
    else value = e;
    if (["tuTra", "khongTinhTien", "dotXuat", "boSung"].includes(type)) {
      value = e.target.checked;
    }

    let dataSelect = cloneDeep(state.dataSelected);
    let record = dataSelect[index];

    //convert soLuong, soLuongSoCap về dạng số
    if ((type === "soLuong" || type === "soLuongSoCap") && value) {
      value = evalString(value);
      if (isNumber(value)) {
        value = roundNumberPoint(value, 6);
      }
    }
    if (type === "soLuong") {
      if (Number(value) <= 0 && value) {
        record.soLuong = 0;
        message.error(t("khamBenh.donThuoc.nhapSoLuongLonHon0"));
      } else {
        record[type] = value;
        list?.callback && list?.callback(value);
        if (record.heSoDinhMuc == 1) {
          record.soLuongSoCap = value;
        }
      }
    } else {
      record[type] = value;
    }

    if (type === "tuTra" && value) {
      record["khongTinhTien"] = false;
    }

    if (type === "khongTinhTien" && value) {
      record["tuTra"] = false;
    }

    if (type === "lieuDungId" && list) {
      const soLuong1Lan = list?.soLuong1Lan || record.soLuong1Lan;
      const soLan1Ngay = list?.soLan1Ngay || record.soLan1Ngay;

      if (soLuong1Lan && soLan1Ngay) {
        const tenDuongDung = `${list?.tenDuongDung ? list?.tenDuongDung : ""}`;
        record.cachDung = getCachDung({
          t,
          tenDuongDung,
          soLan1Ngay,
          soLuong1Lan,
          tenDvtSuDung: record.tenDvtSuDung,
          thoiDiem: record.thoiDiem,
          tocDoTruyen: record.tocDoTruyen,
          tenDonViTocDoTruyen: getDonViTocDoTruyen(record.tocDoTruyen)?.ten,
        });
      }
      record.soLuong1Lan = soLuong1Lan;
      record.soLan1Ngay = soLan1Ngay;
      record.tenDuongDung = list?.tenDuongDung || record?.tenDuongDung;
      if (record.lieuDungId && soLuong1Lan && soLan1Ngay) {
        if (record.soNgay) {
          genarateSoLuong(
            record,
            chiDinhThuocUtils.tinhSoLuong({
              soNgay: record.soNgay,
              soLuong1Lan: soLuong1Lan,
              soLan1Ngay: soLan1Ngay,
            })
          );
        } else if (record.soLuong) {
          record.soNgay = chiDinhThuocUtils.tinhSoNgay({
            soLuong: record.soLuong,
            soLuong1Lan: soLuong1Lan,
            soLan1Ngay: soLan1Ngay,
            soLuongHuy: record.soLuongHuy,
          });
        }
      }
    }

    if (type === "tocDoTruyen" || type === "donViTocDoTruyen") {
      record.cachDung = getCachDung({
        t,
        tenDuongDung: record.tenDuongDung,
        soLan1Ngay: record.soLan1Ngay,
        soLuong1Lan: record.soLuong1Lan,
        tenDvtSuDung: record.tenDvtSuDung,
        thoiDiem: record.thoiDiem,
        tocDoTruyen: record.tocDoTruyen,
        tenDonViTocDoTruyen: getDonViTocDoTruyen(record.donViTocDoTruyen)?.ten,
      });
    }

    if (type === "soNgay" && value) {
      if (Number(value) <= 0) {
        record.soNgay = 0;
        message.error(
          t("khamBenh.soNgayPhaiLonHon", {
            soNgay: 0,
          })
        );
      } else if (record.soLan1Ngay && record.soLuong1Lan) {
        genarateSoLuong(
          record,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: value,
            soLan1Ngay: record.soLan1Ngay,
            soLuong1Lan: record.soLuong1Lan,
            soLuongHuy: record.soLuongHuy,
          })
        );
      }
    } //

    if (
      type === "soLuong" &&
      value &&
      record.soLan1Ngay &&
      record.soLuong1Lan
    ) {
      //Trường hợp thuốc có hệ số định mức # 1: Trường số ngày kê đơn cho tự động tính lại/không tự tính lại khi có thay đổi trường Số lượng sơ cấp dựa vào thiết lập chung có mã: TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
      //Trường hợp thuốc có hệ số định mức = 1: khi có thay đổi trường số lượng → tự động tính lại trường Số ngày kê đơn theo công thức
      if (
        (record.heSoDinhMuc == 1 && record.dvtSuDungId == record.donViTinhId) ||
        (record.heSoDinhMuc != 1 &&
          TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC.toLowerCase() == "true")
      ) {
        record.soNgay = chiDinhThuocUtils.tinhSoNgay({
          soLuong: value,
          soLan1Ngay: record.soLan1Ngay,
          soLuong1Lan: record.soLuong1Lan,
          soLuongHuy: record.soLuongHuy,
        });
      }
    }

    if (type == "soLuong" && value && record.heSoDinhMuc != 1) {
      let soLuongSoCap = (Number(value) || 0) / record.heSoDinhMuc;

      record.soLuongSoCap = roundNumberPoint(soLuongSoCap, 6);
    }
    if (type === "soLuongHuy") {
      if (record.soLan1Ngay && record.soLuong1Lan) {
        genarateSoLuong(
          record,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: record.soNgay,
            soLan1Ngay: record.soLan1Ngay,
            soLuong1Lan: record.soLuong1Lan,
            soLuongHuy: value || 0,
          })
        );
      }
    } else if ((type === "soLan1Ngay" || type === "soLuong1Lan") && value) {
      if (Number(value) <= 0 && value) {
        record[type] = 0;

        setTimeout(() => {
          showMessLessThan0Ref.current &&
            showMessLessThan0Ref.current(index, type);
        }, 1000);
      } else {
        if (
          record.soNgay &&
          ((type === "soLan1Ngay" && record.soLuong1Lan) ||
            (type === "soLuong1Lan" && record.soLan1Ngay))
        ) {
          genarateSoLuong(
            record,
            chiDinhThuocUtils.tinhSoLuong({
              soNgay: record.soNgay,
              soLan1Ngay: type === "soLan1Ngay" ? value : record.soLan1Ngay,
              soLuong1Lan: type === "soLuong1Lan" ? value : record.soLuong1Lan,
              soLuongHuy: record.soLuongHuy,
            })
          );
        } else if (record.soLuong) {
          record.soNgay = chiDinhThuocUtils.tinhSoNgay({
            soLuong: record.soLuong,
            soLan1Ngay: type === "soLan1Ngay" ? value : record.soLan1Ngay,
            soLuong1Lan: type === "soLuong1Lan" ? value : record.soLuong1Lan,
            soLuongHuy: record.soLuongHuy,
          });
        }
      }
    }

    if (
      ((type === "soLan1Ngay" && record.soLuong1Lan) ||
        (type === "soLuong1Lan" && record.soLan1Ngay)) &&
      value
    ) {
      record.cachDung = getCachDung({
        t,
        tenDuongDung: record.tenDuongDung,
        soLan1Ngay: type === "soLuong1Lan" ? record.soLan1Ngay : value,
        soLuong1Lan: type === "soLan1Ngay" ? record.soLuong1Lan : value,
        tenDvtSuDung: record.tenDvtSuDung,
        thoiDiem: record.thoiDiem,
        tocDoTruyen: record.tocDoTruyen,
        tenDonViTocDoTruyen: getDonViTocDoTruyen(record.tocDoTruyen)?.ten,
      });
    }

    if (type === "thoiDiem") {
      record.thoiDiem = value;
      if (record.soLuong1Lan || record.soLan1Ngay) {
        record.cachDung = getCachDung({
          t,
          tenDuongDung: record.tenDuongDung,
          soLan1Ngay: record.soLan1Ngay,
          soLuong1Lan: record.soLuong1Lan,
          tenDvtSuDung: record.tenDvtSuDung,
          thoiDiem: record.thoiDiem,
          tocDoTruyen: record.tocDoTruyen,
          tenDonViTocDoTruyen: getDonViTocDoTruyen(record.tocDoTruyen)?.ten,
        });
      }
    }

    if (type === "soLuongSoCap") {
      if (Number(value) <= 0 && value) {
        record.soLuongSoCap = 0;
        message.error(
          `${t("common.cot")} ${t("common.slSoCap")} ${t("common.phaiLonHon0")}`
        );
      } else {
        const soLuong = record.soLuongSoCap * record.heSoDinhMuc;
        record.soLuong = roundNumberPoint(soLuong, 6);

        //Trường hợp thuốc có hệ số định mức # 1: Trường số ngày kê đơn cho tự động tính lại/không tự tính lại khi có thay đổi trường Số lượng sơ cấp dựa vào thiết lập chung có mã: TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC
        //Trường hợp thuốc có hệ số định mức = 1: khi có thay đổi trường số lượng → tự động tính lại trường Số ngày kê đơn theo công thức
        if (
          (record.heSoDinhMuc == 1 &&
            record.dvtSuDungId == record.donViTinhId) ||
          (record.heSoDinhMuc != 1 &&
            TU_DONG_CAP_NHAT_SO_NGAY_CHO_DON_THUOC.toLowerCase() == "true")
        ) {
          record.soNgay = chiDinhThuocUtils.tinhSoNgay({
            soLuong,
            soLan1Ngay: record.soLan1Ngay,
            soLuong1Lan: record.soLuong1Lan,
            soLuongHuy: record.soLuongHuy,
          });
        }
      }
    }
    if (type === "ghiChu" || type === "soLuongHuy" || type === "lyDoHuy") {
      record[type] = value;
      onSetData(dataSelect);
      setState({ dataSelected: dataSelect });
      return;
    }

    if (type === "duongDungId") {
      record.tenDuongDung = list?.ten || "";

      if (record.soLuong1Lan && record.soLan1Ngay && !record.lieuDungId) {
        record.cachDung = getCachDung({
          t,
          tenDuongDung: record.tenDuongDung,
          soLan1Ngay: record.soLan1Ngay,
          soLuong1Lan: record.soLuong1Lan,
          tenDvtSuDung: record.tenDvtSuDung,
          thoiDiem: record.thoiDiem,
          tocDoTruyen: record.tocDoTruyen,
          tenDonViTocDoTruyen: getDonViTocDoTruyen(record.tocDoTruyen)?.ten,
        });
      }
    }

    if (record.soLuong > record.soLuongKhaDungConHsd && loaiDonThuoc === 10) {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("khamBenh.vuotQuaSoLuongTon")}:  ${t(
            "khamBenh.soLuongTon{{soLuongTon}}",
            {
              soLuongTon: record.soLuongKhaDungConHsd,
            }
          )}, ${t("khamBenh.soLuongKe{{soLuongKe}}", {
            soLuongKe: record.soLuong,
          })}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          setState({ dataSelected: dataSelect });
          onSelected(dataSelect, true);
        },
        () => {
          record.soLuong = 0;
          record.soLuongSoCap = 0;
          setState({ dataSelected: dataSelect });
          onSelected(dataSelect, true);
        }
      );
    } else {
      setState({ dataSelected: dataSelect });
      onSelected(dataSelect, true);
    }
  };

  const blockInvalidChar = (e) =>
    ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

  const columnsTableLeft = [
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.donThuoc.tenThuocHamLuong")}
        />
      ),
      dataIndex: "",
      key: "",
      width: "50%",
      i18Name: "khamBenh.donThuoc.tenThuocHamLuong",
      show: true,
      render: (item, record) => {
        const ten =
          record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "";
        const tenLieuDung = `${
          record?.tenLieuDung || record?.lieuDung?.ten || ""
        }`;
        const tenDuongDung = `${
          record?.tenDuongDung ? " - " + record?.tenDuongDung : ""
        }`;
        const tenCachDung = `${
          record?.cachDung ? " - " + record?.cachDung : ""
        }`;
        const content1 = `${tenLieuDung}${tenDuongDung}${tenCachDung}${
          tenLieuDung || tenDuongDung || tenCachDung ? `. ` : ""
        }`;
        return (
          <span style={record.dungKemId ? { fontStyle: "italic" } : {}}>
            <span>
              <b>{`${ten} ${
                record.tenHoatChat ? " (" + record.tenHoatChat + ")" : " "
              } ${record.hamLuong ? " - " + record.hamLuong : ""}`}</b>
            </span>
            <br />
            <span style={{ fontSize: "12px" }}>
              <i>
                {`${content1} `}
                {record.ghiChu ? `Lưu ý: ${record.ghiChu}` : ""}
              </i>
            </span>
          </span>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("quanLyNoiTru.toDieuTri.ngayYLenh")}
        />
      ),
      dataIndex: "thoiGianYLenh",
      key: "thoiGianYLenh",
      width: "30%",
      align: "right",
      i18Name: "quanLyNoiTru.toDieuTri.ngayYLenh",
      show: true,
      render: (item) => {
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
      },
    },
    {
      title: <HeaderSearch isTitleCenter={true} title={t("hsba.slDung")} />,
      dataIndex: "slDung",
      key: "slDung",
      width: "20%",
      align: "right",
      i18Name: "hsba.slDung",
      show: true,
      render: (item, data) => {
        return item + " " + (data.tenDonViTinh ? data.tenDonViTinh : "");
      },
    },
    {
      title: <HeaderSearch isTitleCenter={true} title={"HDSD"} />,
      dataIndex: "HDSD",
      key: "HDSD",
      width: 50,
      i18Name: "HDSD",
      show: false,
      render: (_, data) => {
        return <IcXemHdsd record={data} />;
      },
    },
  ];

  const onSearchLieuDung = (data) => async (e) => {
    if (e) {
      const listLieuDung = await lieuDungProvider
        .searchAll({
          ten: e,
          page: "",
          size: "",
        })
        .then((s) => {
          return s?.data || [];
        });
      data.listLieuDung = listLieuDung;
    } else {
      const listLieuDung = await lieuDungProvider
        .searchAll({
          bacSiId: nhanVienId,
          dichVuId: data.id || data.dichVuId,
          page: "",
          size: "",
        })
        .then((s) => {
          return s?.data || [];
        });
      data.listLieuDung = listLieuDung;
    }
    setState({ searchLieuDungWord: e || "" });
  };

  const filterOption = (input = "", option) => {
    return containText(option?.children, input);
  };

  const renderListThoiDiemDung = (listThoiDiemDung, index, data) =>
    listThoiDiemDung.map((item) => ({
      key: item.key,
      label: (
        <a
          href={() => false}
          onClick={() => onChangeInput("thoiDiem", index, data)(item.label)}
        >
          {item.label}
        </a>
      ),
    }));

  const columnsTableRight = {
    ten: {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={
            <div
              className="pointer"
              onClick={() => openInNewTab("/danh-muc/thuoc")}
            >
              {t("khamBenh.donThuoc.tenThuocHamLuong")}
            </div>
          }
        />
      ),
      show: true,
      dataIndex: "",
      key: "",
      width: 200,
      i18Name: "khamBenh.donThuoc.tenThuocHamLuong",
      render: (item, data) => {
        let text = `${data.tenDichVu} ${
          data.tenHoatChat ? " (" + data.tenHoatChat + ")" : " "
        } ${data.hamLuong ? " - " + data.hamLuong : ""}`;

        if (!data.thuocDungKem) {
          return <span clickcheckbox="true">{text}</span>;
        } else {
          return <i clickcheckbox="true">{text}</i>;
        }
      },
    },
    soLuong: {
      title: <HeaderSearch title={t("common.soLuong")} isTitleCenter={true} />,
      dataIndex: "soLuong",
      key: "soLuong",
      width: 100,
      align: "right",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item flex flex-center">
            <InputTimeout
              id={`${data.dichVuId}-sl-input`}
              value={item}
              style={{
                border: !data.soLuong ? "1px solid red" : "unset",
                marginRight: 2,
              }}
              onChange={onChangeInput("soLuong", index, data)}
              onKeyDown={blockInvalidChar}
            ></InputTimeout>
            <Tooltip title={data.tenDonViTinh}>
              <span className="span-ellipsis">{data.tenDonViTinh}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
      i18Name: "common.soLuong",
    },
    soLuongTheoNgay: {
      title: (
        <HeaderSearch title={t("common.slTheoNgay")} isTitleCenter={true} />
      ),
      dataIndex: "soLuong",
      key: "soLuongTheoNgay",
      width: 100,
      align: "right",
      show: true,
      render: (item, data, index) => {
        const _value =
          isNumber(data.soNgay) && data.soNgay > 0 ? item / data.soNgay : null;

        return (
          <WrapperInput className="form-item flex flex-center">
            <InputTimeout
              value={_value || ""}
              style={{
                border: !data.soLuong ? "1px solid red" : "unset",
                marginRight: 2,
              }}
              disabled
            />
            <Tooltip title={data.tenDonViTinh}>
              <span className="span-ellipsis">{data.tenDonViTinh}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
      i18Name: "common.soLuong",
    },
    soLuongSoCap: {
      title: <HeaderSearch title={t("common.slSoCap")} isTitleCenter={true} />,
      dataIndex: "soLuongSoCap",
      key: "soLuongSoCap",
      width: 100,
      align: "right",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item flex flex-center">
            <InputTimeout
              value={item}
              // type="number"
              onChange={onChangeInput("soLuongSoCap", index, data)}
              onKeyDown={blockInvalidChar}
              style={{
                marginRight: 2,
              }}
              // disabled={data.heSoDinhMuc <= 1}
            ></InputTimeout>
            <Tooltip title={data.tenDvtSoCap}>
              <span className="span-ellipsis">{data.tenDvtSoCap}</span>
            </Tooltip>
          </WrapperInput>
        );
      },
      i18Name: "common.slSoCap",
    },
    tuTra: {
      title: <HeaderSearch title={t("common.tuTra")} isTitleCenter={true} />,
      width: 50,
      align: "center",
      dataIndex: "tuTra",
      hidden: loaiDonThuoc !== 20,
      show: true,
      i18Name: "common.tuTra",
      render: (item, data, index) => {
        return (
          <Checkbox
            defaultChecked={false}
            checked={item}
            onChange={onChangeInput("tuTra", index, data)}
            disabled={data?.dsMucDich?.length}
          />
        );
      },
    },
    khongTinhTien: {
      title: (
        <HeaderSearch title={t("common.khongTinhTien")} isTitleCenter={true} />
      ),
      width: 80,
      align: "center",
      dataIndex: "khongTinhTien",
      show: true,
      i18Name: "common.khongTinhTien",
      hidden: loaiDonThuoc !== 20,
      render: (item, data, index) => {
        return (
          <Checkbox
            defaultChecked={false}
            checked={item}
            onChange={onChangeInput("khongTinhTien", index, data)}
            disabled={data?.dsMucDich?.length}
          />
        );
      },
    },
    lieuDungId: {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={
            <div
              className="pointer"
              onClick={() => openInNewTab("/danh-muc/lieu-dung")}
            >
              {t("common.lieuDung")}
            </div>
          }
        />
      ),
      dataIndex: "lieuDungId",
      key: "lieuDungId",
      width: 150,
      show: true,
      i18Name: "common.lieuDung",
      render: (item, data, index) => {
        let newDataRender = [];
        if (item) {
          newDataRender = listAllLieuDung;
        } else {
          newDataRender = Object.assign([], data?.listLieuDung);
          newDataRender?.sort(sortString("ten", 1));
        }
        return (
          <WrapperSelect
            style={{
              border:
                !data.lieuDungId && !data.cachDung && isValidateLieuDungCachDung
                  ? "1px solid red"
                  : "unset",
            }}
          >
            <ModalThemLieuDung ref={refModalThemLieuDung} />
            <SelectAntd
              ref={refSelectLieuDung}
              showSearch={true}
              open={state.openLieuDung[index]}
              onDropdownVisibleChange={(open) =>
                setState({
                  openLieuDung: { ...state.openLieuDung, [index]: open },
                })
              }
              value={item}
              onChange={onChangeInput("lieuDungId", index, data)}
              onSearch={onSearchLieuDung(data)}
              onFocus={() => {
                setState({
                  openLieuDung: { ...state.openLieuDung, [index]: true },
                });
              }}
              filterOption={filterOption}
              dropdownClassName="table-thuoc-right-lieu-dung"
              notFoundContent={
                <div>
                  <div style={{ color: "#7A869A", textAlign: "center" }}>
                    <small>{t("common.khongCoDuLieuPhuHop")}</small>
                  </div>
                  <Row justify="center" ref={refBtnThemNhanh}>
                    <Button
                      style={{
                        border: "1px solid",
                        borderRadius: "10px",
                        margin: "auto",
                        lineHeight: 0,
                        // boxShadow: "-1px 3px 1px 1px #d9d9d9",
                        cursor: "pointer",
                      }}
                      onClick={async (e) => {
                        refSelectLieuDung.current.blur();
                        return (
                          refModalThemLieuDung &&
                          refModalThemLieuDung.current.show(
                            {
                              visible: true,
                              data,
                              tenLieuDung: state.searchLieuDungWord,
                            },
                            (res) => {
                              const { values } = res;
                              values.bacSiId = nhanVienId;
                              createOrEditLieuDung(values).then(async (s) => {
                                const dataCustom = {
                                  lieuDung: {
                                    ...s,
                                  },
                                  lieuDungId: s.id,
                                  dichVuId: data?.id || data?.dichVuId,
                                };
                                await createOrEditLieuDungThuoc(dataCustom);
                                // await reRenderListLieuDungDependDichVu();
                                const listLieuDung = await lieuDungProvider
                                  .searchAll({
                                    bacSiId: nhanVienId,
                                    dichVuId: data?.id || data?.dichVuId,
                                    page: "",
                                    size: "",
                                  })
                                  .then((s) => {
                                    return s?.data || [];
                                  });
                                setTimeout(() => {
                                  reloadListAllLieuDung();
                                }, 1000);
                                let updateDataSelected = cloneDeep(
                                  state.dataSelected
                                );
                                let _row = updateDataSelected.find(
                                  (x) =>
                                    x.dichVuId == (data?.id || data?.dichVuId)
                                );
                                if (_row) {
                                  _row.listLieuDung = listLieuDung;
                                  _row.lieuDungId = dataCustom?.lieuDungId;
                                  setState({
                                    dataSelected: updateDataSelected,
                                  });
                                  onSelected(updateDataSelected, true);
                                }
                              });
                            },
                            (err) => {
                              // setState({...state})
                            }
                          )
                        );
                      }}
                    >
                      {t("khamBenh.donThuoc.themNhanhLieuDungBS")}
                    </Button>
                  </Row>
                </div>
              }
            >
              {newDataRender?.map((option) => {
                return (
                  <Option
                    lists={option}
                    key={
                      option[`${props.id}`] ? option[`${props.id}`] : option.id
                    }
                    value={
                      option[`${props.id}`] ? option[`${props.id}`] : option.id
                    }
                    ref={option}
                  >
                    {option[`${props.ten}`]
                      ? option[`${props.ten}`]
                      : option.ten}
                  </Option>
                );
              })}
            </SelectAntd>
          </WrapperSelect>
        );
      },
    },
    cachDung: {
      title: <HeaderSearch title={t("common.cachDung")} isTitleCenter={true} />,
      width: 320,
      align: "center",
      dataIndex: "cachDung",
      i18Name: "common.cachDung",
      show: true,
      render: (item, data, index) => {
        return (
          <InputTimeout
            onChange={onChangeInput("cachDung", index, data)}
            value={item}
            style={{
              border:
                !data.cachDung && !data.lieuDungId && isValidateLieuDungCachDung
                  ? "1px solid red"
                  : "unset",
            }}
          ></InputTimeout>
        );
      },
    },
    soNgay: {
      title: <HeaderSearch title={t("common.soNgay")} isTitleCenter={true} />,
      width: 60,
      align: "center",
      dataIndex: "soNgay",
      i18Name: "common.soNgay",
      show: true,
      render: (item, data, index) => {
        //
        return (
          <WrapperInput>
            <InputTimeout
              onChange={onChangeInput("soNgay", index, data)}
              value={item}
              type="number"
              style={{ width: "100%" }}
            ></InputTimeout>
          </WrapperInput>
        );
      },
    },
    thoiDiem: {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.thoiDiemDung")}
          isTitleCenter={true}
        />
      ),
      width: 120,
      align: "center",
      dataIndex: "thoiDiem",
      i18Name: "khamBenh.donThuoc.thoiDiemDung",
      show: true,
      render: (item, data, index) => {
        const _listThoiDiemDung = item
          ? LIST_THOI_DIEM_DUNG.filter(
              (x) => x.label.toLowerCase().indexOf(item.toLowerCase()) > -1
            )
          : LIST_THOI_DIEM_DUNG;

        return (
          <Dropdown
            overlayStyle={
              _listThoiDiemDung.length == 0 ? { display: "none" } : {}
            }
            overlay={
              <Menu
                items={renderListThoiDiemDung(_listThoiDiemDung, index, data)}
              />
            }
            trigger={["click"]}
          >
            <InputTimeout
              onChange={onChangeInput("thoiDiem", index, data)}
              value={item}
            />
          </Dropdown>
        );
      },
    },
    tenDvtSuDung: {
      title: (
        <HeaderSearch title={t("common.dvtSuDung")} isTitleCenter={true} />
      ),
      width: 60,
      align: "center",
      dataIndex: "tenDvtSuDung",
      i18Name: "common.dvtSuDung",
      show: true,
    },
    soLan1Ngay: {
      title: <HeaderSearch title={t("common.lan/ngay")} isTitleCenter={true} />,
      width: 60,
      align: "center",
      dataIndex: "soLan1Ngay",
      i18Name: "common.lan/ngay",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <InputTimeout
              onChange={onChangeInput("soLan1Ngay", index, data)}
              value={item}
              type="number"
              timeDelay={100}
            ></InputTimeout>
          </WrapperInput>
        );
      },
    },
    soLuong1Lan: {
      title: <HeaderSearch title={t("common.sl/lan")} isTitleCenter={true} />,
      width: 120,
      align: "center",
      dataIndex: "soLuong1Lan",
      i18Name: "common.sl/lan",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item flex gap-5">
            <InputTimeout
              onChange={onChangeInput("soLuong1Lan", index, data)}
              value={item}
              timeDelay={100}
              className="flex1"
            ></InputTimeout>
            {data?.tenDvtSuDung}
          </WrapperInput>
        );
      },
    },
    ghiChu: {
      title: <HeaderSearch title={t("common.luuY")} isTitleCenter={true} />,
      width: 150,
      align: "center",
      dataIndex: "ghiChu",
      i18Name: "common.luuY",
      show: true,
      render: (item, data, index) => {
        return (
          <InputTimeout
            onChange={onChangeInput("ghiChu", index, data)}
            value={item}
          ></InputTimeout>
        );
      },
    },
    soLuongHuy: {
      title: <HeaderSearch title={t("common.slHuy")} isTitleCenter={true} />,
      width: 80,
      align: "center",
      dataIndex: "soLuongHuy",
      i18Name: "common.slHuy",
      show: true,
      render: (item, data, index) => {
        return (
          <WrapperInput className="form-item">
            <InputTimeout
              onChange={onChangeInput("soLuongHuy", index, data)}
              value={item}
              style={{ width: "100%" }}
              timeDelay={100}
            ></InputTimeout>
          </WrapperInput>
        );
      },
    },
    lyDoHuy: {
      title: <HeaderSearch title={t("common.lyDoHuy")} isTitleCenter={true} />,
      width: 180,
      align: "center",
      dataIndex: "lyDoHuy",
      i18Name: "common.lyDoHuy",
      show: true,
      render: (item, data, index) => {
        return (
          <InputTimeout
            onChange={onChangeInput("lyDoHuy", index, data)}
            value={item}
          ></InputTimeout>
        );
      },
    },
    duongDung: {
      title: (
        <HeaderSearch title={t("common.duongDung")} isTitleCenter={true} />
      ),
      width: 160,
      dataIndex: "duongDungId",
      i18Name: "common.duongDung",
      show: true,
      render: (item, data, index) => (
        <Select
          defaultValue={item}
          showAction={["focus"]}
          data={listAllDuongDung}
          onChange={onChangeInput("duongDungId", index, data)}
        />
      ),
    },
    dotXuat: {
      title: (
        <HeaderSearch
          title={
            <Checkbox
              checked={isCheckAllDotXuat}
              onChange={onCheckAllLoaiChiDinh("dotXuat", !isCheckAllDotXuat)}
            >
              {t("kho.dotXuat")}
            </Checkbox>
          }
          isTitleCenter={true}
        />
      ),
      width: 100,
      dataIndex: "dotXuat",
      key: "dotXuat",
      i18Name: "kho.dotXuat",
      align: "center",
      show: true,
      render: (item, data, index) => {
        const currentKho = (dsKho || []).find((i) => i.id == data?.khoId);
        const isKhoTuTruc = currentKho?.dsCoCheDuyetPhat?.includes(
          CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE
        );
        if (
          isKhongHienThiCheckBoxDotXuatKhiKeTuTruc &&
          (isKhoTuTruc || isTuTruc)
        ) {
          return null;
        }

        return (
          <Checkbox
            checked={item}
            onChange={onChangeInput("dotXuat", index, data)}
          />
        );
      },
    },
    boSung: {
      title: (
        <HeaderSearch
          title={
            <Checkbox
              checked={isCheckAllBoSung}
              onChange={onCheckAllLoaiChiDinh("boSung", !isCheckAllBoSung)}
            >
              {t("kho.boSung")}
            </Checkbox>
          }
          isTitleCenter={true}
        />
      ),
      width: 100,
      dataIndex: "boSung",
      key: "boSung",
      i18Name: "kho.boSung",
      align: "center",
      show: true,
      render: (item, data, index) => {
        const currentKho = (dsKho || []).find((i) => i.id == data?.khoId);
        const isKhoTuTruc = currentKho?.dsCoCheDuyetPhat?.includes(
          CO_CHE_DUYET_PHAT.DUYET_PHAT_NGAY_KHI_KE
        );
        if (
          isKhongHienThiCheckBoxDotXuatKhiKeTuTruc &&
          (isKhoTuTruc || isTuTruc)
        ) {
          return null;
        }

        return (
          <Checkbox
            checked={item}
            onChange={onChangeInput("boSung", index, data)}
          />
        );
      },
    },
    slSang: RIGHT_COLUMNS.SL_SANG({ dataThoiGianVaTenBuoiSang, onChangeInput }),
    slChieu: RIGHT_COLUMNS.SL_CHIEU({
      dataThoiGianVaTenBuoiChieu,
      onChangeInput,
    }),
    slToi: RIGHT_COLUMNS.SL_TOI({ dataThoiGianVaTenBuoiToi, onChangeInput }),
    slDem: RIGHT_COLUMNS.SL_DEM({ dataThoiGianVaTenBuoiDem, onChangeInput }),
    nguonKhacId: {
      title: (
        <HeaderSearch title={t("danhMuc.nguonKhac")} isTitleCenter={true} />
      ),
      width: 160,
      dataIndex: "nguonKhacId",
      i18Name: "danhMuc.nguonKhac",
      show: true,
      render: (item, data, index) => {
        return (
          <Select
            defaultValue={item}
            showAction={["focus"]}
            data={listAllNguonKhacChiTra}
            onChange={onChangeInput("nguonKhacId", index, data)}
          />
        );
      },
    },
    tocDoTruyen: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.tocDoTruyen")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "tocDoTruyen",
      key: "tocDoTruyen",
      i18Name: "quanLyNoiTru.toDieuTri.tocDoTruyen",
      show: true,
      render: (item, data, index) => (
        <InputTimeout
          type="number"
          onChange={onChangeInput("tocDoTruyen", index, data)}
          value={item}
          timeDelay={300}
        />
      ),
    },
    donViTocDoTruyen: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.dvTocDoTruyen")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "donViTocDoTruyen",
      key: "donViTocDoTruyen",
      i18Name: "quanLyNoiTru.toDieuTri.dvTocDoTruyen",
      show: true,
      render: (item, data, index) => (
        <Select
          value={item}
          data={listDonViTocDoTruyen}
          showAction={["focus"]}
          onChange={onChangeInput("donViTocDoTruyen", index, data)}
        />
      ),
    },
    soGiot: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.soGiot/ml")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "soGiot",
      key: "soGiot",
      i18Name: "quanLyNoiTru.toDieuTri.soGiot/ml",
      show: true,
      render: (item, data, index) => (
        <InputTimeout
          onChange={onChangeInput("soGiot", index, data)}
          value={item}
          timeDelay={300}
        />
      ),
    },
    cachGio: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.cachGio")}
          isTitleCenter={true}
        />
      ),
      width: 160,
      dataIndex: "cachGio",
      key: "cachGio",
      i18Name: "quanLyNoiTru.toDieuTri.cachGio",
      show: true,
      render: (item, data, index) => (
        <InputTimeout
          onChange={onChangeInput("cachGio", index, data)}
          value={item}
          timeDelay={300}
        />
      ),
    },
    thoiGianBatDau: {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.gioBatDau")}
          isTitleCenter={true}
        />
      ),
      width: 120,
      dataIndex: "thoiGianBatDau",
      key: "thoiGianBatDau",
      i18Name: "quanLyNoiTru.toDieuTri.gioBatDau",
      show: true,
      render: (item, data, index) => (
        <TimeInput
          value={item}
          onChange={onChangeInput("thoiGianBatDau", index, data)}
          placeholder={t("common.chonThoiGian")}
          timeDelay={0}
        />
      ),
    },
  };

  const columnsTableRightMemo =
    dataThietLap?.map((key) => columnsTableRight[key]) ||
    Object.values(columnsTableRight || {});

  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  const dataSource = useMemo(() => {
    let thuocChaIdCoDungKem = [];
    //lọc thuốc cha => hiển thị thuốc dùng kèm
    const _listThuocDaKeToDieuTri = (listThuocDaKeToDieuTri || []).map(
      (item) => {
        genarateSoLuong(item, item.soLuong);
        return item;
      }
    );
    let _dataSource = _listThuocDaKeToDieuTri.filter((item) => !item.dungKemId);
    _dataSource = gopThuocKhacLo(_dataSource);

    _dataSource = _dataSource.map((item, index) => {
      item.key = item.id || index;

      item.children = _listThuocDaKeToDieuTri.filter(
        (item2) =>
          item2.dungKemId === item.id ||
          (item.dsThuocGopId || []).includes(item2.dungKemId)
      );

      item.children = Object.entries(groupBy(item.children, "chiDinhId")).map(
        ([chiDinhId, items]) => {
          return {
            ...items[0],
            soLuong: sumBy(items, "soLuong"),
            soLuongHuy: sumBy(items, "soLuongHuy"),
            soLuongYeuCau: sumBy(items, "soLuongYeuCau"),
            soLuongTra: sumBy(items, "soLuongTra"),
            soLuongSoCap: sumBy(items, "soLuongSoCap"),
            slDung: sumBy(items, "slDung"),
          };
        }
      );

      if (item.children.length > 0) {
        thuocChaIdCoDungKem.push(item.key);
      }
      return item;
    });

    setState({ leftExpandedKeys: thuocChaIdCoDungKem });
    return _dataSource;
  }, [listThuocDaKeToDieuTri]);

  const onEnterAddService = () => {
    let data = dataSource.find((x) => x.key === state?.key);
    if (data) onSelectLeft(data, true);
  };

  refAddService.current = onEnterAddService;

  useEffect(() => {
    if (state.keySelected.length) {
      const cellToFocus = document.querySelector(
        `.table-right tr[data-row-key*='${
          state.keySelected[state.keySelected.length - 1]
        }'] input:not([disabled]):not(.ant-checkbox-input) `
      );
      cellToFocus && cellToFocus.focus();
    }
  }, [state.keySelected]);

  const handleResizeSplit = async () => {
    const widthSplitLeft =
      refSplitPanelLeft?.current?.getBoundingClientRect()?.width;
    const widthSplitRight =
      refSplitPanelRight?.current?.getBoundingClientRect()?.width;
    onResizeSplit(
      "widthCacheThuocDaKe",
      "DATA_CUSTOMIZE_COLUMN_split_TODIEUTRI_KeDonThuoc_DaKe",
      [widthSplitLeft, widthSplitRight]
    );
  };

  const listAllDsThuoc = useMemo(() => {
    return [...(rawMimsData?.dsThuoc || []), ...state.dataSelected];
  }, [rawMimsData, state.dataSelected]);

  return (
    <Main className="content-chi-dinh-thuoc-table">
      <GlobalStyle />
      <SplitPanel onDragEnd={() => handleResizeSplit()}>
        <div
          className="content-left"
          ref={refSplitPanelLeft}
          style={splitCacheCustomize && { width: splitCacheCustomize[0] }}
        >
          <div className="content-left-header-table">
            <TableWrapper
              rowKey={(record) => {
                return record?.key;
              }}
              columns={columnsTableLeft}
              dataSource={dataSource}
              rowSelection={rowSelectionLeft}
              // showHeader={false}
              rowClassName={(record, index) =>
                state?.key === record.key
                  ? "row-actived table-row-odd " + record.key
                  : "table-row-odd " + record.key
              }
              expandedRowKeys={state?.leftExpandedKeys}
              expandable={{
                expandIcon: ({ expanded, onExpand, record }) =>
                  record?.children?.length ? (
                    expanded ? (
                      <SVG.IcExpandDown
                        onClick={(e) => {
                          onExpand(record, e);
                          e.stopPropagation();
                        }}
                      />
                    ) : (
                      <SVG.IcExpandRight
                        onClick={(e) => {
                          onExpand(record, e);
                          e.stopPropagation();
                        }}
                      />
                    )
                  ) : null,
              }}
              onRow={(record) => {
                return {
                  onClick: (row) => {
                    row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                  },
                };
              }}
              locale={{
                emptyText: renderEmptyTextLeftTable(),
              }}
              scroll={{ x: 400 }}
              tableName="table_KHAMBENH_KeDonThuocDaKe_DsThuoc"
              ref={refSettingsLeft}
            />
            {!!dataSource.length && (
              <Pagination
                listData={dataSource}
                onChange={onChangePage}
                current={pageThuocDaKe + 1}
                pageSize={sizeThuocDaKe}
                total={totalElementsThuocDaKe}
                onShowSizeChange={onSizeChange}
                stylePagination={{ justifyContent: "flex-start" }}
                showSplash={true}
              />
            )}
          </div>
        </div>
        <div
          className="content-right"
          ref={refSplitPanelRight}
          style={splitCacheCustomize && { width: splitCacheCustomize[1] }}
        >
          <div className="title">
            <div className="title__left">
              <img src={CircleCheck} alt="" /> {t("common.daChon")}
            </div>
            <div className="title__right">
              <div className="title__right_left">
                <div className="title__right_left_soTien">
                  {`${t(
                    "khamBenh.donThuoc.tongTienCanTra"
                  )}: ${thanhTien?.formatPrice()} vnđ`}
                </div>
                {showLuuY && (
                  <div className="title__right_left_note">
                    <Tooltip
                      title={t(
                        "khamBenh.luuYSoTienChinhXacDuocXacDinhTaiQuayThuNgan"
                      )}
                    >
                      {t(
                        "khamBenh.luuYSoTienChinhXacDuocXacDinhTaiQuayThuNgan"
                      )}
                    </Tooltip>
                  </div>
                )}
              </div>
              <Setting refTable={refSettings} />
            </div>
          </div>
          <div className="content-right_table">
            <TableWrapper
              rowKey={(record) => {
                return record?.key;
              }}
              rowSelection={rowSelectionRight}
              className="table-right"
              columnResizable={true}
              columns={columnsTableRightMemo || []}
              dataSource={state.dataSelected}
              tableName="table_KHAMBENH_KeDonThuoc"
              ref={refSettings}
              expandIconColumnIndex={3}
              expandedRowKeys={state?.expandedKeys}
              expandable={{
                expandIcon: ({ expanded, onExpand, record }) =>
                  record?.children?.length ? (
                    expanded ? (
                      <SVG.IcExpandDown
                        onClick={(e) => {
                          onExpand(record, e);
                          setState({
                            expandedKeys: state?.expandedKeys.filter(
                              (x) => x !== record.key
                            ),
                          });
                          e.stopPropagation();
                        }}
                      />
                    ) : (
                      <SVG.IcExpandRight
                        onClick={(e) => {
                          onExpand(record, e);
                          setState({
                            expandedKeys: [...state?.expandedKeys, record.key],
                          });
                          e.stopPropagation();
                        }}
                      />
                    )
                  ) : null,
              }}
              rowClassName={(record, index) => {
                const colorTuongTacThuoc = getColorTuongTacThuoc(
                  record.dichVuId,
                  listAllDsThuoc
                );

                let isSeverityRed = colorTuongTacThuoc === "red";
                let isSeverityOrange = colorTuongTacThuoc === "orange";

                return classNames({
                  "table-row-even": index % 2 === 0,
                  "table-row-odd": index % 2 !== 0,
                  "add-border":
                    index == state.dataSelected?.length - 1 ? "add-border" : "",
                  "table-row-severity-red": isSeverityRed,
                  "table-row-severity-orange": isSeverityOrange,
                });
              }}
              onRow={() => {
                return {
                  onClick: (row) => {
                    if (
                      row?.target?.firstElementChild?.hasAttribute(
                        "clickcheckbox"
                      ) ||
                      row?.target?.hasAttribute("clickcheckbox")
                    ) {
                      row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                    }
                  },
                };
              }}
              locale={{
                emptyText: (
                  <div style={{ height: 297 }}>
                    <div style={{ color: "#c3c3c3", lineHeight: "297px" }}>
                      {t("khamBenh.donThuoc.khongCoDuLieuThuocDaChon")}
                    </div>
                  </div>
                ),
              }}
            />
          </div>
        </div>
      </SplitPanel>
    </Main>
  );
};

export default TableThuocDaKe;
