import { useMemo } from "react";
import { useListAll, useQueryAll, useThietLap } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";
import thietLapQuyenKyProvider from "data-access/kySo/thiet-lap-quyen-ky-provider.js";
import { isArray } from "utils/index";
import { query } from "redux-store/stores";

function useListAllNhanVienQuyenKy({ viTri }) {
  const [dataMA_QUYEN_KY_HEN_KHAM] = useThietLap(
    THIET_LAP_CHUNG.MA_QUYEN_KY_HEN_KHAM
  );
  const [dataMA_QUYEN_KY_CHUYEN_VIEN] = useThietLap(
    THIET_LAP_CHUNG.MA_QUYEN_KY_CHUYEN_VIEN
  );
  const [dataMA_QUYEN_KY_TTDV] = useThietLap(THIET_LAP_CHUNG.MA_QUYEN_KY_TTDV);

  const [listAllQuyenKy] = useListAll(
    "quyenKy",
    {
      active: true,
      size: "",
      page: "",
    },
    true
  );

  const { quyenKyId, maQuyenKy } = useMemo(() => {
    let maQuyenKy;

    switch (viTri) {
      case "khamBenhFormHenKham":
        maQuyenKy = dataMA_QUYEN_KY_HEN_KHAM;
        break;
      case "khamBenhFormChuyenVien":
        maQuyenKy = dataMA_QUYEN_KY_CHUYEN_VIEN;
        break;
      case "noiTruThongTinRaVien":
        maQuyenKy = dataMA_QUYEN_KY_TTDV;
        break;
      default:
        break;
    }
    return {
      quyenKyId: listAllQuyenKy.find((i) => i.ma === maQuyenKy)?.id,
      maQuyenKy,
    };
  }, [
    viTri,
    dataMA_QUYEN_KY_HEN_KHAM,
    dataMA_QUYEN_KY_CHUYEN_VIEN,
    dataMA_QUYEN_KY_TTDV,
    listAllQuyenKy,
  ]);

  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      enabled: !quyenKyId,
    })
  );

  const { data: listAllNhanVienQuyenKy } = useQueryAll({
    queryKey: ["listAllNhanVienQuyenKy"],
    queryFn: thietLapQuyenKyProvider.searchNhanVienQuyenKyByNhanVien,
    params: {
      dsQuyenKyId: quyenKyId,
    },
    enabled: !!quyenKyId,
  });

  const listAllNhanVienQuyenKyMemo = useMemo(() => {
    let result = [];
    if (isArray(listAllNhanVienQuyenKy, true) && !!maQuyenKy) {
      result = listAllNhanVienQuyenKy;
    } else if (isArray(listAllNhanVien, true) && !maQuyenKy) {
      result = listAllNhanVien;
    }
    return result.sort((a, b) => a.ten.localeCompare(b.ten));
  }, [listAllNhanVien, listAllNhanVienQuyenKy, maQuyenKy]);

  return [listAllNhanVienQuyenKyMemo];
}

export default useListAllNhanVienQuyenKy;
