import React, { useState, useEffect, useMemo, useRef } from "react";
import { Collapse } from "antd";
import Header from "./Header";
import DanhSachDichVu from "./DanhSachDichVu";
import { groupBy, orderBy } from "lodash";
import { CollapseWrapper } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import printProvider from "data-access/print-provider";
import { isArray, locPhieuLisPacs, openInNewTab } from "utils";
import { useLoading, useStore, useThietLap, useConfirm } from "hooks";
import { LOAI_IN, THIET_LAP_CHUNG, LOAI_DICH_VU, ROLES } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { Select } from "components";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import ModalScanPrint from "pages/hoSoBenhAn/components/ModalScanPrint";
import stringUtils from "mainam-react-native-string-utils";

const { Panel } = Collapse;

const KetQua = ({ isTiepDon }) => {
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refModalSignPrint = useRef(null);

  const chiDinhTuDichVuId = useStore("khamBenh.thongTinChiTiet.id", null);
  const listThietLapTrangThai = useStore("khamBenh.listThietLapTrangThai", []);
  const { dsKetQuaXN, dsKetQuaDichVuCLS } = useSelector(
    (state) => state.ketQuaKham
  );

  const [MAC_DINH_HIEN_THI_DICH_VU_THEO_NHOM] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_HIEN_THI_DICH_VU_THEO_NHOM
  );
  const [dataHIEN_THI_DICH_VU_CHI_DINH_TU_TIEP_DON, isFinish] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_DICH_VU_CHI_DINH_TU_TIEP_DON
  );
  const [LOAI_DICH_VU_NB_KSK] = useThietLap(
    THIET_LAP_CHUNG.LOAI_DICH_VU_NB_KSK
  );
  const [dataBAT_POPUP_KY_SO_KHI_IN_KET_QUA] = useThietLap(
    THIET_LAP_CHUNG.BAT_POPUP_KY_SO_KHI_IN_KET_QUA
  );

  const [coCanhBao, setCoCanhBao] = useState(null);

  const infoNb = useStore("khamBenh.infoNb", {});
  const { tienChuaThanhToan, tienTamUng, tienConLai } = useStore(
    "nbDotDieuTri.thongTinCoBan",
    {},
    { field: "tienChuaThanhToan,tienTamUng,tienConLai" }
  );

  const isKhamBenh = useMemo(() => {
    return window.location.pathname.includes("/kham-benh");
  }, []);

  const isNbThieuTien = useMemo(() => {
    // Nếu bệnh nhân không thiếu tiền → luôn được xem kết quả
    const isKhongThieuTien = tienChuaThanhToan < tienTamUng || tienConLai >= 0;
    if (isKhongThieuTien) return false;

    // Nếu bệnh nhân thiếu tiền và tài khoản không có quyền → không được xem kết quả
    if (isKhamBenh && !checkRole([ROLES["KHAM_BENH"].CHAN_XEM_KQ_KHAM])) {
      return true;
    }

    return false;
  }, [tienChuaThanhToan, tienTamUng, tienConLai, isKhamBenh]);

  const {
    pacs: { getUrl },
    nbXetNghiem: { getKetQuaXNPdf },
    choTiepDonDV: { getPhieuKetQua },
    ketQuaKham: { getDsKetQuaXN, getDsKetQuaDichVuCLS },
    nbDotDieuTri: { getThongTinCoBan },
    chiDinhKhamBenh: { inPhieuKetQua },
  } = useDispatch();

  const [state, _setState] = useState({
    collapsedKey: [],
    dataSortColumn: {},
  });

  useEffect(() => {
    if (chiDinhTuDichVuId && isFinish && !isTiepDon) {
      const dsLoaiDichVu = LOAI_DICH_VU_NB_KSK
        ? LOAI_DICH_VU_NB_KSK?.split(",")
        : [];
      const _params = {
        page: "",
        size: "",
        dsTrangThaiHoan: [0, 10, 20],
        ...(dataHIEN_THI_DICH_VU_CHI_DINH_TU_TIEP_DON?.eval()
          ? {
              dsChiDinhTuDichVuId: [chiDinhTuDichVuId, infoNb?.id],
              dsChiDinhTuLoaiDichVu: dsLoaiDichVu?.length
                ? dsLoaiDichVu
                : [LOAI_DICH_VU.KHAM, LOAI_DICH_VU.TIEP_DON],
              chiDinhTuLoaiDichVu: undefined,
            }
          : {}),
        coCanhBao,
      };

      getDsKetQuaXN(_params);
      getDsKetQuaDichVuCLS(_params);
    }
  }, [chiDinhTuDichVuId, isFinish, isTiepDon, coCanhBao, LOAI_DICH_VU_NB_KSK]);

  useEffect(() => {
    if (isTiepDon) {
      const _params = {
        page: "",
        size: "",
        dsTrangThaiHoan: [0, 10, 20],
        dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.TIEP_DON],
        dsChiDinhTuDichVuId: undefined,
      };
      getDsKetQuaXN(_params);
      getDsKetQuaDichVuCLS(_params);
    }
  }, [isTiepDon, coCanhBao]);

  useEffect(() => {
    if (infoNb?.id) {
      getThongTinCoBan(infoNb?.id);
    }
  }, [infoNb?.id]);

  const setState = (data) => {
    _setState((state) => {
      return {
        ...state,
        ...data,
      };
    });
  };

  const onCollapsed = (value) => {
    setState({
      collapsedKey: value,
    });
  };

  const groupAndOrderItem = (items, groupkey, orderKey) => {
    const groupData = groupBy(items, "trangChiDinh");
    const data = {};
    Object.keys(groupData).forEach((trangChiDinh) => {
      data[`${trangChiDinh}-${groupkey}`] = orderBy(
        groupData[trangChiDinh],
        orderKey,
        "asc"
      );
    });

    return data;
  };

  const dataKetQuaXN = useMemo(() => {
    return groupAndOrderItem(dsKetQuaXN, "xn", [
      "phieuChiDinh",
      "benhPham",
      "phongThucHien",
    ]);
  }, [dsKetQuaXN]);

  const dataKetQuaDichVuCLS = useMemo(() => {
    return groupAndOrderItem(dsKetQuaDichVuCLS, "cls", [
      "phieuChiDinh",
      "benhPham",
      "phongThucHien",
    ]);
  }, [dsKetQuaDichVuCLS]);

  const dataSource = useMemo(() => {
    return { ...dataKetQuaXN, ...dataKetQuaDichVuCLS };
  }, [dataKetQuaXN, dataKetQuaDichVuCLS]);

  const onViewPacs = (e, data) => {
    e.stopPropagation();
    e.preventDefault();

    if (isNbThieuTien) {
      showConfirm({
        title: t("common.canhBao"),
        content: t("khamBenh.nguoiBenhThieuTienCanDiThanhToan"),
        cancelText: t("common.quayLai"),
        typeModal: "warning",
      });
      return;
    }

    getUrl({ id: data[0]?.id }).then((res) => {
      if (res) {
        window.open(res, "_blank")?.focus();
      }
    });
  };

  const onPrintPdfLis = async (data) => {
    try {
      showLoading();
      let payload = {
        nbDotDieuTriId: data[0]?.nbDotDieuTriId,
        dsSoPhieuId: data[0]?.soPhieuId,
      };
      const s = await getKetQuaXNPdf(payload);
      const dsPhieu = locPhieuLisPacs(s, {
        allData: false,
        isLocPhieu: true,
        isXetNghiem: true,
      });
      const dsPhieuFull = locPhieuLisPacs(s, {
        allData: true,
        isLocPhieu: true,
        isXetNghiem: true,
      });
      if (
        isArray(dsPhieuFull, true) &&
        dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
      ) {
        const finalFile = await printProvider.getMergePdf(dsPhieu);
        openInNewTab(finalFile);
      } else {
        printProvider.printPdf(dsPhieuFull, { alwayMerge: true });
      }
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onPrintPdfCdha = async (data) => {
    const dsSoKetNoi = data.map((item) => item.soKetNoi);
    try {
      showLoading();
      let payload = {
        nbDotDieuTriId: data[0]?.nbDotDieuTriId,
        dsSoKetNoi: dsSoKetNoi,
      };
      const s = await getPhieuKetQua(payload);
      const dsPhieu = locPhieuLisPacs(s, {
        allData: false,
        isLocPhieu: true,
        isXetNghiem: false,
      });
      const dsPhieuFull = locPhieuLisPacs(s, {
        allData: true,
        isLocPhieu: true,
        isXetNghiem: false,
      });
      if (
        isArray(dsPhieuFull, true) &&
        dsPhieuFull.every((x) => x?.loaiIn == LOAI_IN.MO_TAB)
      ) {
        const finalFile = await printProvider.getMergePdf(dsPhieu);
        openInNewTab(finalFile);
      } else {
        printProvider.printPdf(dsPhieuFull);
      }
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onPrintKetQua = (loaiDichVu, data) => async () => {
    if (isNbThieuTien) {
      showConfirm({
        title: t("common.canhBao"),
        content: t("khamBenh.nguoiBenhThieuTienCanDiThanhToan"),
        cancelText: t("common.quayLai"),
        typeModal: "warning",
      });
      return;
    }

    if (!dataBAT_POPUP_KY_SO_KHI_IN_KET_QUA?.eval()) {
      if (loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
        onPrintPdfLis(data);
      } else if (
        loaiDichVu === LOAI_DICH_VU.CDHA ||
        loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
      ) {
        onPrintPdfCdha(data);
      }

      return;
    }

    //Xử lý hiển thị ký số
    try {
      const dsSoKetNoi = data.map((item) => item.soKetNoi);
      showLoading();

      const s = await inPhieuKetQua({
        nbDotDieuTriId: data[0]?.nbDotDieuTriId,
        dsSoPhieuId: data[0]?.soPhieuId,
        loaiDichVu,
        dsSoKetNoi: dsSoKetNoi,
        returnData: true,
      });

      if (!s.length) return;
      const _listPhieu = s.map((item) => {
        return {
          ...item,
          key: stringUtils.guid(),
          data: {
            filePdf: item.dsDuongDan
              ? item.dsDuongDan.length
                ? item.dsDuongDan[0]
                : null
              : item.file.pdf,
          },
          ten: `Kết quả pdf`,
          nbDotDieuTriId: data[0]?.nbDotDieuTriId,
          trangThai: item?.lichSuKy?.trangThai,
          baoCaoId: item.baoCaoId,
          soPhieu: item.soPhieu,
          lichSuKy: item.lichSuKy,
        };
      });

      const listPhieu = await Promise.all(
        _listPhieu.map(async (item) => {
          if (item.lichSuKy) {
            const itemDaKy = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
              id: item.lichSuKy?.id,
              chuKySo: item.lichSuKy?.chuKySo,
            });

            return {
              ...item,
              data: { filePdf: itemDaKy.data?.file?.pdf },
            };
          }
          return item;
        })
      );

      refModalSignPrint.current &&
        refModalSignPrint.current.show(
          {
            dsPhieu: listPhieu,
            isSignPdf: true,
            nbDotDieuTriId: data[0]?.nbDotDieuTriId,
            baoCaoId: listPhieu[0].baoCaoId,
          },
          async () => {
            const s = await inPhieuKetQua({
              nbDotDieuTriId: data[0]?.nbDotDieuTriId,
              dsSoPhieuId: data[0]?.soPhieuId,
              loaiDichVu,
              dsSoKetNoi: dsSoKetNoi,
              returnData: true,
            });
            const _listPhieu = s.map((item) => {
              return {
                ...item,
                key: stringUtils.guid(),
                data: {
                  filePdf: item.dsDuongDan
                    ? item.dsDuongDan.length
                      ? item.dsDuongDan[0]
                      : null
                    : item.file.pdf,
                },
                ten: `Kết quả pdf`,
                nbDotDieuTriId: data[0]?.nbDotDieuTriId,
                trangThai: item?.lichSuKy?.trangThai,
                baoCaoId: item.baoCaoId,
                soPhieu: item.soPhieu,
                lichSuKy: item.lichSuKy,
              };
            });

            const listPhieu = await Promise.all(
              _listPhieu.map(async (item) => {
                if (item.lichSuKy) {
                  const itemDaKy =
                    await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                      id: item.lichSuKy?.id,
                      chuKySo: item.lichSuKy?.chuKySo,
                    });

                  return {
                    ...item,
                    data: { filePdf: itemDaKy.data?.file?.pdf },
                  };
                }
                return item;
              })
            );
            return listPhieu;
          }
        );
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const listPanel = useMemo(() => {
    return Object.keys(dataSource)
      .map((key, index) => {
        const {
          soPhieu,
          tenNhomDichVuCap1,
          tenNhomDichVuCap2,
          tenNhomDichVuCap3,
          tenBacSiChiDinh,
          tenKhoaThucHien,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          trangThai,
          loaiDichVu,
        } = dataSource[key][0] || {};
        let hasThietLapTrangThai = listThietLapTrangThai?.filter(
          (item) =>
            item.nhomDichVuCap3Id == nhomDichVuCap3Id ||
            (item.nhomDichVuCap2Id == nhomDichVuCap2Id &&
              item.trangThaiHoanThanh == trangThai)
        );
        const title = `${t("khamBenh.ketQua.ketQua")} ${
          tenNhomDichVuCap1 || ""
        } ${tenNhomDichVuCap2 || ""} ${tenNhomDichVuCap3 || ""}`;

        if (hasThietLapTrangThai.length > 0) {
          return {
            header: (
              <Header
                title={title}
                isCollapsed={(state.collapsedKey || []).includes(key)}
                key={key}
                dataSource={dataSource[key]}
                onViewPacs={(e) => onViewPacs(e, dataSource[key])}
                onPrint={onPrintKetQua(loaiDichVu, dataSource[key])}
              />
            ),
            content: (
              <DanhSachDichVu
                loaiDichVu={loaiDichVu}
                dataGroup={orderBy(dataSource[key], "thoiGianCoKetQua", "desc")}
                dataSortColumn={state.dataSortColumn}
                soPhieu={soPhieu}
                tenBacSiChiDinh={tenBacSiChiDinh}
                tenKhoaThucHien={tenKhoaThucHien}
                nhomDichVuCap1Id={nhomDichVuCap1Id}
                isNbThieuTien={isNbThieuTien}
              />
            ),
            key,
          };
        }
      })
      .filter((item) => item);
  }, [dataSource, state.dataSortColumn, state.collapsedKey]);

  useEffect(() => {
    if (
      MAC_DINH_HIEN_THI_DICH_VU_THEO_NHOM &&
      MAC_DINH_HIEN_THI_DICH_VU_THEO_NHOM.toLowerCase() == "true"
    ) {
      setState({
        collapsedKey: Object.keys(dataSource),
      });
    }
  }, [MAC_DINH_HIEN_THI_DICH_VU_THEO_NHOM, dataSource]);

  return (
    <div className="collapse-content fadeIn">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          padding: 12,
        }}
      >
        <div className="label" style={{ fontWeight: 600 }}>
          {t("quanLyNoiTru.dvCoKetQuaBatThuong")}:&nbsp;
        </div>
        <div style={{ width: 200 }}>
          <Select
            style={{ width: "100%" }}
            data={[
              { ten: t("quanLyNoiTru.coBatThuong"), id: true },
              { ten: t("quanLyNoiTru.khongBatThuong"), id: false },
            ]}
            hasAllOption={true}
            value={coCanhBao}
            onChange={(value) => {
              setCoCanhBao(value);
            }}
            placeholder={t("common.tuyChon")}
          />
        </div>
      </div>
      <CollapseWrapper
        bordered={false}
        onChange={onCollapsed}
        activeKey={state.collapsedKey}
      >
        {listPanel.map((panel, idx) => (
          <Panel showArrow={false} key={panel.key} header={panel.header}>
            {panel.content}
          </Panel>
        ))}
      </CollapseWrapper>

      <ModalScanPrint ref={refModalSignPrint} />
    </div>
  );
};
export default KetQua;
