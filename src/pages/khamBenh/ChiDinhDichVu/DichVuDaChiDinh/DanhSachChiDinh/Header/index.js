import React, { useMemo, useRef, useState } from "react";
import { CaretRightOutlined } from "@ant-design/icons";
import { HeaderWrapper } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import {
  TRANG_THAI_DICH_VU,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import {
  Checkbox,
  Tooltip,
  Dropdown,
  Select as SelectAnt,
  Select,
  Button,
} from "components";
import { Menu, Form, Row, Col, Input, message } from "antd";
import { SVG } from "assets";
import CustomPopover from "pages/khamBenh/components/CustomPopover";
import benhPhamProvider from "data-access/categories/dm-benh-pham-provider";
import { t } from "i18next";
import { useConfirm, useLoading, useStore, useThietLap } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { useDonViKetNoiPacs } from "pages/hoSoBenhAn/ChiTietNguoiBenh/hooks/useDonViKetNoiPacs";
import ModalSapXepThuoc from "pages/khamBenh/DonThuoc/ModalSapXepThuoc";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import ModalScanPrint from "pages/hoSoBenhAn/components/ModalScanPrint";
import stringUtils from "mainam-react-native-string-utils";

const Header = ({
  keyDefine,
  isCollapsed,
  title,
  toolTipInPhieu = t("common.inPhieu"),
  toolTipXoaPhieu = t("common.xoaPhieu"),
  isShowChuyenThuoc,
  nbDotDieuTriId,
  loaiDichVu,
  dsSoKetNoi,
  chiDinhTuLoaiDichVu,
  dsChiDinhTuLoaiDichVu,
  chiDinhTuDichVuId,
  dsChiDinhTuDichVuId,
  isDisplayIconHoan,
  dataSource = [],
  phieuChiDinhId,
  isReadonly,
  disabledAll,
  dsSoPhieuId,
  soPhieu,

  onHoanDichVu,
  onViewPacs,
  onDelete,
  onTraMau,
  onHuyTraMau,
  getDsDichVu,
  listPhieu,
  isDisplayPrintPhieu = true,
  isDisplayTraMau,
  onChuyenThuoc = () => {},
  onInPhieu, // ko để default func vì if(func) luôn true
  isThuocDaKe = false,
  listDvThuoc = [],
  isShowSetGhiChu = false,
  setGhiChuValue = () => {},
  isNbThieuTien = false,
  isTiepDon = false,
  isHideBtnInDonThuoc = false,
  listData,
  isDisplaySapXep = false,
  onXemKetQua,
  thuocNhaThuoc,
  thuocKeNgoai,
  onHuyXemKetQua,
  diplayIconKetQua,
}) => {
  const { showConfirm } = useConfirm();
  const { checkDonViKetNoi } = useDonViKetNoiPacs();

  const [form] = Form.useForm();
  const [dataBAT_POPUP_KY_SO_KHI_IN_KET_QUA] = useThietLap(
    THIET_LAP_CHUNG.BAT_POPUP_KY_SO_KHI_IN_KET_QUA
  );

  const listPhongThucHien = useSelector(
    (state) => state.phongThucHien.listData
  );
  const soPhieuCls = useSelector((state) => state.chiDinhKhamBenh.soPhieuCls);
  const listAllBenhPham = useSelector(
    (state) => state.benhPham.listAllBenhPham
  );
  const refMoDalSapXepThuoc = useRef(null);
  const refModalSignPrint = useRef(null);

  const nhanVienId = useStore("auth.auth.nhanVienId");
  const {
    chiDinhKhamBenh: {
      getNBSoPhieuCLS,
      themThongTinPhieu,
      inPhieu,
      inPhieuKetQua,
      inPhieuKhamChuyenKhoa,
    },
    phongThucHien: { getData: searchPhongThucHien },
    benhPham: { getListAllBenhPham },
    chiDinhDichVuKho: {
      getListDichVuThuoc,
      getListDichVuThuocKeNgoai,
      getListThuocNhaThuoc,
    },
  } = useDispatch();

  const { showLoading, hideLoading } = useLoading();
  const [visible, setVisible] = useState([]);

  const onChangeBenhPham = (values) => {
    try {
      if (values.length > 1) {
        form.setFieldsValue({
          benhPhamId: values[values.length - 1] || "",
        });
      }
    } catch (err) {
      message.error(err.message);
    }
  };

  const onPrint = async (e) => {
    e.stopPropagation();
    showLoading();
    try {
      if (onInPhieu) {
        await onInPhieu(e);
      } else {
        await inPhieu({
          nbDotDieuTriId,
          dsSoPhieuId,
          loaiDichVu,
          phieuChiDinhId,
          chiDinhTuLoaiDichVu: isTiepDon ? undefined : chiDinhTuLoaiDichVu,
          dsChiDinhTuLoaiDichVu: isTiepDon ? undefined : dsChiDinhTuLoaiDichVu,
          chiDinhTuDichVuId: isTiepDon ? undefined : chiDinhTuDichVuId,
          loai: chiDinhTuLoaiDichVu == LOAI_DICH_VU.KHAM ? 1 : "",
        });
      }
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onPrintKetQua = async (e) => {
    try {
      e.stopPropagation();

      if (isNbThieuTien) {
        showConfirm({
          title: t("common.canhBao"),
          content: t("khamBenh.nguoiBenhThieuTienCanDiThanhToan"),
          cancelText: t("common.quayLai"),
          typeModal: "warning",
        });
        return;
      }

      showLoading();

      if (!dataBAT_POPUP_KY_SO_KHI_IN_KET_QUA?.eval()) {
        await inPhieuKetQua({
          nbDotDieuTriId,
          dsSoPhieuId,
          loaiDichVu,
          dsSoKetNoi,
          chiDinhTuLoaiDichVu: isTiepDon ? undefined : chiDinhTuLoaiDichVu,
          chiDinhTuDichVuId: isTiepDon ? undefined : chiDinhTuDichVuId,
        });
        return;
      }

      const s = await inPhieuKetQua({
        nbDotDieuTriId,
        dsSoPhieuId,
        loaiDichVu,
        dsSoKetNoi,
        chiDinhTuLoaiDichVu: isTiepDon ? undefined : chiDinhTuLoaiDichVu,
        chiDinhTuDichVuId: isTiepDon ? undefined : chiDinhTuDichVuId,
        returnData: true,
      });

      if (!s.length) return;
      const _listPhieu = s.map((item) => {
        return {
          ...item,
          key: stringUtils.guid(),
          data: {
            filePdf: item.dsDuongDan
              ? item.dsDuongDan.length
                ? item.dsDuongDan[0]
                : null
              : item.file.pdf,
          },
          ten: `Kết quả pdf`,
          nbDotDieuTriId: item.nbDotDieuTriId || nbDotDieuTriId,
          trangThai: item?.lichSuKy?.trangThai,
          baoCaoId: item.baoCaoId,
          soPhieu: item.soPhieu,
          lichSuKy: item.lichSuKy,
        };
      });

      const listPhieu = await Promise.all(
        _listPhieu.map(async (item) => {
          if (item.lichSuKy) {
            const itemDaKy = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
              id: item.lichSuKy?.id,
              chuKySo: item.lichSuKy?.chuKySo,
            });

            return {
              ...item,
              data: { filePdf: itemDaKy.data?.file?.pdf },
            };
          }
          return item;
        })
      );

      refModalSignPrint.current &&
        refModalSignPrint.current.show(
          {
            dsPhieu: listPhieu,
            isSignPdf: true,
            nbDotDieuTriId,
            baoCaoId: listPhieu[0].baoCaoId,
          },
          async () => {
            const s = await inPhieuKetQua({
              nbDotDieuTriId,
              dsSoPhieuId,
              loaiDichVu,
              dsSoKetNoi,
              chiDinhTuLoaiDichVu: isTiepDon ? undefined : chiDinhTuLoaiDichVu,
              chiDinhTuDichVuId: isTiepDon ? undefined : chiDinhTuDichVuId,
              returnData: true,
            });
            const _listPhieu = s.map((item) => {
              return {
                ...item,
                key: stringUtils.guid(),
                data: {
                  filePdf: item.dsDuongDan
                    ? item.dsDuongDan.length
                      ? item.dsDuongDan[0]
                      : null
                    : item.file.pdf,
                },
                ten: `Kết quả pdf`,
                nbDotDieuTriId: item.nbDotDieuTriId || nbDotDieuTriId,
                trangThai: item?.lichSuKy?.trangThai,
                baoCaoId: item.baoCaoId,
                soPhieu: item.soPhieu,
                lichSuKy: item.lichSuKy,
              };
            });

            const listPhieu = await Promise.all(
              _listPhieu.map(async (item) => {
                if (item.lichSuKy) {
                  const itemDaKy =
                    await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                      id: item.lichSuKy?.id,
                      chuKySo: item.lichSuKy?.chuKySo,
                    });

                  return {
                    ...item,
                    data: { filePdf: itemDaKy.data?.file?.pdf },
                  };
                }
                return item;
              })
            );
            return listPhieu;
          }
        );
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintKCK = async (e) => {
    try {
      showLoading();
      await inPhieuKhamChuyenKhoa({
        nbDotDieuTriId,
        dsChiDinhTuLoaiDichVu: isTiepDon ? undefined : dsChiDinhTuLoaiDichVu,
        dsChiDinhTuDichVuId: isTiepDon ? undefined : dsChiDinhTuDichVuId,
      });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const isShowPacs = useMemo(
    () =>
      dataSource &&
      dataSource.find((x) => {
        return (
          (x.loaiDichVu === LOAI_DICH_VU.CDHA || checkDonViKetNoi(x)) &&
          x.trangThai >= TRANG_THAI_DICH_VU.DA_CO_KET_QUA
        );
      }),
    [dataSource]
  );

  const isDaCoKetQua = useMemo(() => {
    return dataSource.some(
      (x) => x.trangThai === TRANG_THAI_DICH_VU.DA_CO_KET_QUA
    );
  }, [dataSource]);

  const optionsChild = useMemo(() => {
    return (listPhongThucHien || [])
      .map((item) => ({
        id: item.phongId,
        ten: `${item?.ma} - ${item?.ten}`,
        // dichVuId: item.dichVuId,
      }))
      .map((item, index) => {
        return (
          <Option
            disabled={item.id == dataSource[0]?.phongThucHienId}
            key={index}
            value={item?.id + ""}
          >{`${item?.ten}`}</Option>
        );
      });
  }, [listPhongThucHien]);

  const onCancel = () => {
    setVisible([]);
  };

  const handleVisible =
    (key, data = []) =>
    (e) => {
      const dataPhieuChung = data[0] || {};
      form.setFieldsValue({
        benhPhamId: dataPhieuChung.benhPhamId || "",
        ghiChu: dataPhieuChung.ghiChu,
        soPhieuId: dataPhieuChung.soPhieuId,
        tuTra: dataPhieuChung.tuTra,
        // phongThucHienId: dataPhieuChung.phongThucHienId,
      });
      searchPhongThucHien({
        soPhieuId: dataPhieuChung.soPhieuId,
        nbDotDieuTriId: nbDotDieuTriId,
        dichVuId: dataPhieuChung.dichVuId,
        page: 0,
        size: 500,
      });
      getNBSoPhieuCLS({ loaiDichVu: dataPhieuChung.loaiDichVu });
      setVisible([...visible, key]);
    };

  const getIdBenhPham = (values) => {
    return new Promise(async (resolve, reject) => {
      if (Array.isArray(values)) {
        // nếu value is array lấy item cuối cùng
        let newItem = values[values.length - 1];

        if (typeof newItem === "string") {
          //nếu item là string thì tiến hành gọi api để tạo bệnh phẩm
          const response = await benhPhamProvider.post({
            ten: newItem,
          });
          if (response.code === 0) {
            getListAllBenhPham({}); //tạo xong bệnh phẩm thì gọi api search bệnh phẩm
            resolve(response.data.id); //tạo xong thì resolve về id bệnh phẩn
          } else reject();
        } else {
          resolve(newItem); //nếu không phải string thì resolve luôn id bệnh phẩm
        }
      } else resolve(values); //nếu không phải array thì resolve luôn id bênh phẩm
    });
  };

  const handleSubmit = (data) => () => {
    form.validateFields().then(async (values) => {
      let benhPhamId = await getIdBenhPham(values.benhPhamId);
      values.benhPhamId = benhPhamId;
      const soPhieuId = data[0]?.soPhieuId;
      const loaiDichVu = data[0]?.loaiDichVu;
      const obj = {
        benhPhamId: values.benhPhamId,
        nbDvKyThuat: {
          phongThucHienId: values.phongThucHienId,
          soPhieuId: values.soPhieuId,
        },
        nbDichVu: {
          tuTra: values.tuTra,
          ghiChu: values.ghiChu,
        },
      };
      themThongTinPhieu({ body: obj, id: soPhieuId, loaiDichVu }).then((s) => {
        if (s.code === 0) {
          setVisible([]);
          form.resetFields();
          getDsDichVu(loaiDichVu);
        }
      });
    });
  };

  const renderInfo = (loaiDichVu) => {
    return (
      <Form form={form} layout="vertical" className="form-custom">
        <Row gutter={8}>
          {![LOAI_DICH_VU.CDHA, LOAI_DICH_VU.KHAM].includes(loaiDichVu) && (
            <Col span={12}>
              <Form.Item
                label={t("khamBenh.chiDinh.benhPham")}
                name="benhPhamId"
                rules={[
                  {
                    required: true,
                    message: t("khamBenh.chiDinh.vuiLongNhapBenhPham"),
                  },
                ]}
              >
                <Select
                  allowClear
                  data={listAllBenhPham}
                  placeholder={t("khamBenh.chiDinh.chonTenBenhPham")}
                  mode="tags"
                  removeIcon={() => null}
                  onChange={onChangeBenhPham}
                />
              </Form.Item>
            </Col>
          )}
          {![LOAI_DICH_VU.CDHA, LOAI_DICH_VU.KHAM].includes(loaiDichVu) && (
            <Col span={12}>
              <Form.Item
                label={t("common.phong")}
                name="phongThucHienId"
                rules={[
                  {
                    required: true,
                    message: t("common.vuiLongNhapPhong"),
                  },
                ]}
              >
                <SelectAnt
                  allowClear
                  data={listPhongThucHien}
                  placeholder={t("common.chonTenPhong")}
                >
                  {optionsChild}
                </SelectAnt>
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item label={t("common.luuY")} name="ghiChu">
              <Input
                className="input-option"
                placeholder={t("common.vuiLongNhapLuuY")}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t("common.soPhieu")}
              name="soPhieuId"
              rules={[
                {
                  required: true,
                  message: t("khamBenh.chiDinh.vuiLongNhapSoPhieu"),
                },
              ]}
            >
              <SelectAnt
                data={soPhieuCls || []}
                placeholder={t("khamBenh.chiDinh.chonSoPhieu")}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="" name="tuTra" valuePropName="checked">
              <Checkbox>{t("khamBenh.chiDinh.tuTra")}</Checkbox>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  const renderBtnXoa = () => {
    if (isThuocDaKe) {
      //đã group theo bacSiChiDinhId nên chỉ cần check với bản ghi đầu tiên
      const isShowBtn =
        checkRole([ROLES["QUAN_LY_NOI_TRU"].XOA_CHI_DINH_DV_VA_THUOC]) ||
        nhanVienId == listDvThuoc[0].bacSiChiDinhId;

      return !isReadonly && !isShowBtn ? null : (
        <Tooltip title={toolTipXoaPhieu} placement="bottom">
          <SVG.IcDelete
            className="icon"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          />
        </Tooltip>
      );
    }

    if (!isReadonly) {
      return (
        <Tooltip title={toolTipXoaPhieu} placement="bottom">
          <SVG.IcDelete
            className="icon"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          />
        </Tooltip>
      );
    }

    return null;
  };

  const onSetGhiChuValue = (e = {}) => {
    e.preventDefault && e.preventDefault();
    e.stopPropagation && e.stopPropagation();

    showConfirm(
      {
        title: "",
        content: t(
          "khamBenh.xacNhanDayDonThuocBHNguoiBenhKhongLayLenPhanGhiChu"
        ),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showBtnOk: true,
      },
      () => {
        setGhiChuValue();
      },
      () => {}
    );
  };

  const listPhieuMemo = useMemo(() => {
    if (listPhieu) return listPhieu;
    if (
      chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI &&
      loaiDichVu === LOAI_DICH_VU.KHAM
    ) {
      return [
        {
          key: 2,
          ten: t("phieuIn.inPhieuKhamChuyenKhoa"),
          api: onPrintKCK,
        },
      ];
    }
    return [];
  }, [listPhieu]);

  const onSaveModalCustomizeColumn = () => {
    if (thuocNhaThuoc) {
      getListThuocNhaThuoc({
        nbDotDieuTriId: nbDotDieuTriId,
        chiDinhTuDichVuId,
        dsChiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20, 30],
      });
    } else if (thuocKeNgoai) {
      getListDichVuThuocKeNgoai({
        nbDotDieuTriId: nbDotDieuTriId,
        chiDinhTuDichVuId,
        dsChiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20, 30],
      });
    } else {
      getListDichVuThuoc({
        nbDotDieuTriId: nbDotDieuTriId,
        chiDinhTuDichVuId,
        dsChiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20, 30, 42],
      });
    }
  };
  const isShowPhieuChiDinh = !listPhieu; // Nếu có truyền list phiếu thì chỉ hiển thị những phiếu từ bên ngoài truyền vào
  return (
    <HeaderWrapper isCollapsed={isCollapsed}>
      <div className="header-info">
        <CaretRightOutlined className="collapse-arrow" />
        <span className="header-info__name">{title}</span>
        {!disabledAll && (
          <>
            {isDisplayPrintPhieu &&
              (onInPhieu ? (
                isHideBtnInDonThuoc ? (
                  <></>
                ) : (
                  <Tooltip title={toolTipInPhieu}>
                    <SVG.IcPrint className="icon" onClick={onPrint} />
                  </Tooltip>
                )
              ) : (
                <>
                  {!!listPhieuMemo.length && (
                    <Dropdown
                      menu={{
                        items: listPhieuMemo.map((item, index) => ({
                          key: item.key ?? index,
                          label: item.ten,
                          onClick: (e) => {
                            item?.api?.(e.domEvent);
                          },
                        })),
                      }}
                      placement="topCenter"
                    >
                      <SVG.IcPrint className="icon" />
                    </Dropdown>
                  )}

                  {isShowPhieuChiDinh && (
                    <Tooltip
                      title={t("phieuIn.inPhieuChiDinh")}
                      placement="bottom"
                    >
                      <SVG.IcPrint
                        onClick={onPrint}
                        className="icon cursor-pointer"
                      />
                    </Tooltip>
                  )}

                  {isDaCoKetQua && isShowPhieuChiDinh && (
                    <Tooltip title={t("phieuIn.inKetQua")} placement="bottom">
                      <SVG.IcPrint
                        onClick={onPrintKetQua}
                        className="icon cursor-pointer"
                      />
                    </Tooltip>
                  )}
                </>
              ))}

            {renderBtnXoa()}

            {!isReadonly && (
              <>
                {isDisplayTraMau && (
                  <Tooltip title={t("common.traMau")} placement="bottom">
                    <SVG.IcHoanDv onClick={onTraMau} />
                  </Tooltip>
                )}
                {isDisplayTraMau && (
                  <Tooltip title={t("common.huyTraMau")} placement="bottom">
                    <SVG.IcHuyHoanDv onClick={onHuyTraMau} />
                  </Tooltip>
                )}
                {isShowChuyenThuoc && (
                  <Tooltip title={t("pttt.chuyenDichVu")} placement="bottom">
                    <SVG.IcChuyenDichVu onClick={onChuyenThuoc} />
                  </Tooltip>
                )}
              </>
            )}
            {isDisplayIconHoan && !isReadonly && (
              <Tooltip title={t("common.hoanPhieu")} placement="bottom">
                <SVG.IcHoanDv onClick={onHoanDichVu} />
              </Tooltip>
            )}
            {diplayIconKetQua &&
              onXemKetQua &&
              (!isReadonly || !disabledAll) && (
                <Tooltip
                  title={t("khamBenh.xacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcTick onClick={onXemKetQua} />
                </Tooltip>
              )}
            {diplayIconKetQua &&
              onHuyXemKetQua &&
              (!isReadonly || !disabledAll) && (
                <Tooltip
                  title={t("common.huyXacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcCloseCircle
                    color={"var(--color-red-primary)"}
                    onClick={onHuyXemKetQua}
                  />
                </Tooltip>
              )}

            {isShowPacs && (
              <Tooltip
                title={t("khamBenh.ketQua.xemKQPacs")}
                placement="bottom"
              >
                <SVG.IcViewImagePacs className="icon" onClick={onViewPacs} />
              </Tooltip>
            )}

            {isShowSetGhiChu &&
              checkRole([ROLES["KHAM_BENH"].GHI_CHU_THUOC_BH_NB_KHONG_LAY]) && (
                <Tooltip
                  title={t("khamBenh.dayDonThuocBHLenGhiChu")}
                  placement="bottom"
                >
                  <SVG.IcAddFile
                    color={"var(--color-green-primary)"}
                    onClick={onSetGhiChuValue}
                  />
                </Tooltip>
              )}

            {isDisplaySapXep && (
              <Tooltip
                title={t("common.sapXepThongTinThuoc")}
                placement="bottom"
              >
                <SVG.IcSetting
                  className="icon"
                  onClick={() =>
                    refMoDalSapXepThuoc.current &&
                    refMoDalSapXepThuoc.current.show({})
                  }
                />
              </Tooltip>
            )}
          </>
        )}
      </div>
      {loaiDichVu && dataSource[0] && (
        <div className="header-soPhieu">
          {![LOAI_DICH_VU.KHAM, LOAI_DICH_VU.NGOAI_DIEU_TRI].includes(
            loaiDichVu
          ) && (
            <span className="soPhieu">
              <CustomPopover
                icon={null}
                onSubmit={handleSubmit(dataSource)}
                onCancel={onCancel}
                text={`${t("khamBenh.chiDinh.soPhieu")}: ` + soPhieu}
                visible={visible.includes(keyDefine)}
                handleVisible={handleVisible(keyDefine, dataSource)}
                placement="bottom"
              >
                {renderInfo(loaiDichVu)}
              </CustomPopover>
            </span>
          )}
        </div>
      )}
      <ModalSapXepThuoc
        ref={refMoDalSapXepThuoc}
        listData={listData}
        onSaveModalCustomizeColumn={onSaveModalCustomizeColumn}
        thuocNhaThuoc={thuocNhaThuoc}
        thuocKeNgoai={thuocKeNgoai}
      />
      <ModalScanPrint ref={refModalSignPrint} />
    </HeaderWrapper>
  );
};

export default Header;
