import React, {
  forwardRef,
  useEffect,
  useImper<PERSON><PERSON><PERSON><PERSON>,
  useState,
  useMemo,
} from "react";
import {
  Select,
  DateTimePicker,
  Checkbox,
  InputTimeout,
  TextField,
} from "components";
import { t } from "i18next";
import { Col, Row, Form, Collapse, Input, Radio, Space } from "antd";
import { SVG } from "assets";
import { useLazyKVMap, useLoading, useQueryAll, useStore } from "hooks";
import { LOAI_SANG_LOC_NGUY_CO_SDD } from "constants/index";
import { useDispatch } from "react-redux";
import moment from "moment";
import { isNil, maxBy } from "lodash";
import { calculateTiLeGiamCan, tinhBMI } from "../../utils";
import {
  fillNullPayload,
  isNumber,
  openInNewTab,
  parseFloatNumber,
  safeConvertToArray,
} from "utils";
import {
  CHAN_DOAN_SDD_GRID_CONFIG,
  TIEU_CHI_NGUYEN_NHAN_GRID_CONFIG,
  <PERSON><PERSON>_HOACH_CHAM_SOC_GRID_CONFIG,
  FIELD_OPTIONS_MAP,
  AN_UONG_2,
} from "./config";
import { NhomRadioDinhDuong, GridForm } from "./components";
import TongDiemDisplay from "../PhieuSLNBNgoaiTru/components/TongDiemDisplay";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import nbChiSoSongProvider from "data-access/nb-chi-so-song-provider";
import { Main, CollapseWrapper } from "./styled";
import { toSafePromise } from "lib-utils";

const { Panel } = Collapse;

const createTieuChiNguyenNhanValidator = (currentField, otherField) => {
  const errorMessage = t("quanLyNoiTru.vuiLongChonItNhat1TieuChiNguyenNhan");

  return ({ getFieldValue, setFields }) => ({
    validator(_, value) {
      const hasCurrentValue = value;
      const hasOtherValue = getFieldValue(otherField);

      if (hasCurrentValue || hasOtherValue) {
        setFields([
          { name: currentField, errors: [] },
          { name: otherField, errors: [] },
        ]);
        return Promise.resolve();
      }

      setFields([
        { name: currentField, errors: [errorMessage] },
        { name: otherField, errors: [errorMessage] },
      ]);

      return Promise.reject(new Error(errorMessage));
    },
  });
};

const useFormCalculations = () => {
  const calculateGiamCan4 = ({ tiLeGiamCan, giamCan, giamCan2 }) => {
    return giamCan2 === 30 ? (tiLeGiamCan < 5 ? 0 : giamCan) : 0;
  };

  const calculateDanhGiaBmi = ({ bmi }) => {
    return isNil(bmi)
      ? null
      : bmi >= 18.5
      ? 0
      : bmi >= 16 && bmi < 18.5
      ? 10
      : 20;
  };

  const calculateKetQua = ({ danhGiaBmi, giamCan4, teoCo }) => {
    let ketQua = null;
    if (danhGiaBmi === 0 && giamCan4 === 0 && teoCo === 0) {
      ketQua = 10;
    } else {
      if (danhGiaBmi === 10 || [20, 30].includes(giamCan4) || teoCo === 10) {
        ketQua = 20;
      }
      if (danhGiaBmi === 20 || giamCan4 === 10 || teoCo === 20) {
        ketQua = 30;
      }
    }

    return ketQua;
  };

  const calculateKetLuan2 = ({
    giamCan4,
    danhGiaBmi,
    teoCo,
    phu,
    anUong,
    tieuHoa,
  }) => {
    if (
      isNil(giamCan4) ||
      isNil(danhGiaBmi) ||
      isNil(teoCo) ||
      isNil(phu) ||
      isNil(anUong) ||
      isNil(tieuHoa)
    ) {
      return null;
    }

    const isSDD =
      [giamCan4, danhGiaBmi, teoCo, phu].some((item) => item > 0) &&
      [anUong, tieuHoa].some((item) => item > 0);

    return isSDD ? 1 : 0;
  };

  const calculateSuyGiamScore = (bmi, tiLeGiamCan, giamCan, anUong2) => {
    let bmiScore = 0;
    if (bmi !== null && bmi !== undefined) {
      if (bmi <= 18.5) bmiScore = 3;
      else if (bmi >= 18.5 && bmi <= 20.5) bmiScore = 2;
      else if (bmi < 20.5) bmiScore = 1;
      else bmiScore = 0;
    }

    let sutCanScore = 0;
    if (tiLeGiamCan !== null && tiLeGiamCan !== undefined && tiLeGiamCan >= 5) {
      if (giamCan === 30) sutCanScore = 1;
      else if (giamCan === 20) sutCanScore = 2;
      else if (giamCan === 10) sutCanScore = 3;
    }

    let anUongScore = 0;
    if (anUong2 === 0) anUongScore = 0;
    else if (anUong2 === 2) anUongScore = 1;
    else if (anUong2 === 12) anUongScore = 2;
    else if (anUong2 === 22) anUongScore = 3;

    const highestScore = Math.max(bmiScore, sutCanScore, anUongScore);

    return highestScore * 10;
  };

  return {
    calculateDanhGiaBmi,
    calculateGiamCan4,
    calculateKetQua,
    calculateKetLuan2,
    calculateSuyGiamScore,
  };
};

const useFormData = ({
  form,
  data,
  thongTinBenhNhanTongHop,
  nhanVienId,
  calculateGiamCan4,
  calculateKetQua,
  calculateDanhGiaBmi,
}) => {
  const setDataForm = (data) => {
    const giamCan4 = calculateGiamCan4({
      tiLeGiamCan: data?.tiLeGiamCan,
      giamCan: data?.giamCan,
      giamCan2: data?.giamCan2,
    });

    const tinhTrangSuyDinhDuong = calculateKetQua({
      danhGiaBmi: data?.danhGiaBmi,
      giamCan4,
      teoCo: data?.teoCo,
    });

    form.setFieldsValue({
      thoiGianThucHien: data?.thoiGianThucHien
        ? moment(data?.thoiGianThucHien)
        : null,
      nguoiThucHienId: data?.nguoiThucHienId,
      tonTaiSga: data?.tonTaiSga,
      chieuCao: data?.chieuCao,
      canNang: data?.canNang,
      bmi: data?.bmi,
      cdChinh: data?.cdChinh,
      tiLeGiamCan: data?.tiLeGiamCan,
      tinhTrang: data?.tinhTrang,
      mucDo: data?.mucDo,
      doTuoi: data?.doTuoi,
      tongDiem: data?.tongDiem,
      ketLuan: data?.ketLuan,
      giamCan: data?.giamCan,
      giamCan2: data?.giamCan2,
      giamCan3: data?.giamCan3,

      teoCo: data?.teoCo,
      phu: data?.phu,
      anUong: data?.anUong,
      anUong2: data?.anUong2,
      tieuHoa: data?.tieuHoa,
      ketLuan2: data?.ketLuan2,
      dsDuongNuoiAn: data?.dsDuongNuoiAn,
      moiHoiChan: data?.moiHoiChan,
      danhGiaBmi: data?.danhGiaBmi,
      giamCan4,
      tinhTrangSuyDinhDuong,
    });
  };

  useEffect(() => {
    if (data?.id) {
      setDataForm({
        ...data,
      });
    } else {
      const handleGetList = async () => {
        const [_, res] = await toSafePromise(
          nbChiSoSongProvider.getChiSoSongByNbDotDieuTriId({
            nbDotDieuTriId: data?.nbDotDieuTriId,
          })
        );

        const listChiSoSong = res?.data || [];

        const chieuCaoRecord = maxBy(
          listChiSoSong.filter(
            (item) => item.chieuCao != null && item.thoiGianThucHien
          ),
          (item) => new Date(item.thoiGianThucHien).getTime()
        );

        const canNangRecord = maxBy(
          listChiSoSong.filter(
            (item) => item.canNang != null && item.thoiGianThucHien
          ),
          (item) => new Date(item.thoiGianThucHien).getTime()
        );

        const thongTinChiSoSong = {
          chieuCao: chieuCaoRecord?.chieuCao,
          canNang: canNangRecord?.canNang,
          bmi:
            chieuCaoRecord?.chieuCao && canNangRecord?.canNang
              ? tinhBMI(canNangRecord?.canNang, chieuCaoRecord?.chieuCao)
              : null,
        };

        setDataForm({
          thoiGianThucHien: moment(),
          nguoiThucHienId: nhanVienId,
          cdChinh: thongTinBenhNhanTongHop?.cdChinh,
          chieuCao: thongTinChiSoSong?.chieuCao,
          canNang: thongTinChiSoSong?.canNang,
          bmi: thongTinChiSoSong?.bmi,
          danhGiaBmi: calculateDanhGiaBmi({ bmi: thongTinChiSoSong?.bmi }),
          doTuoi: thongTinBenhNhanTongHop?.tuoi
            ? thongTinBenhNhanTongHop?.tuoi >= 70
              ? 10
              : 0
            : 0,
        });
      };
      handleGetList();
    }
  }, [data?.id, thongTinBenhNhanTongHop, form, nhanVienId]);

  return { setDataForm };
};

const HeaderSection = ({ data, listAllNhanVien }) => (
  <Row gutter={[8, 8]}>
    <Col span={data?.id ? 4 : 4}>
      <Form.Item
        label={t("quanLyDinhDuong.ngayThucHien")}
        name="thoiGianThucHien"
        rules={[
          {
            required: true,
            message: t("quanLyDinhDuong.mess.vuiLongChonNgayThucHien"),
          },
        ]}
        className="label-8"
      >
        <DateTimePicker
          format={"DD/MM/YYYY"}
          showTime={false}
          placeholder={t("common.chonThoiGian")}
        />
      </Form.Item>
    </Col>

    <Col span={data?.id ? 8 : 8}>
      <Form.Item
        label={
          <div
            className="pointer"
            onClick={() => openInNewTab("/quan-tri/nhan-vien")}
          >
            {t("khamBenh.chiDinh.nguoiThucHien")}
          </div>
        }
        name="nguoiThucHienId"
        className="label-8"
      >
        <Select
          data={listAllNhanVien}
          placeholder={t("common.timKiem")}
          getLabel={selectMaTen}
        />
      </Form.Item>
    </Col>

    {data?.id && (
      <Col span={4}>
        <div className="form-text-item">
          <div className="form-text-item-label">
            {t("quanLyDinhDuong.nbNoiTru.soPhieuSangLoc")}:&nbsp;
          </div>
          <div className="form-text-item-content">{data?.soPhieu}</div>
        </div>
      </Col>
    )}

    {data?.id && (
      <Col span={4} offset={1}>
        <Form.Item
          name="tonTaiSga"
          valuePropName="checked"
          className="label-16 ton-tai-phieu-danh-gia-sga"
        >
          <Checkbox disabled>
            {t("quanLyDinhDuong.tonTaiPhieuDanhGiaSGA")}
          </Checkbox>
        </Form.Item>
      </Col>
    )}

    {data?.id && (
      <Col span={3}>
        <div className="form-text-item">
          <div className="form-text-item-label">
            {t("quanLyDinhDuong.sangLoc.lanSangLoc")}:&nbsp;
          </div>
          <div className="form-text-item-content">{data?.stt}</div>
        </div>
      </Col>
    )}
  </Row>
);

const PhanThongTinCoBan = ({ data }) => (
  <Row gutter={[8, 8]}>
    <Col span={data?.id ? 4 : 4}>
      <Form.Item
        label={t("khamBenh.chanDoan.chieuCao")}
        name="chieuCao"
        className="label-8"
        rules={[
          {
            required: true,
            message: t("quanLyDinhDuong.mess.vuiLongNhapChieuCao"),
          },
        ]}
      >
        <InputTimeout type="number" style={{ width: "100%" }} timeDelay={0} />
      </Form.Item>
    </Col>
    <Col span={data?.id ? 4 : 4}>
      <Form.Item
        label={t("common.canNang")}
        name="canNang"
        className="label-8"
        rules={[
          {
            required: true,
            message: t("lapBenhAn.vuiLongNhapCanNang"),
          },
        ]}
      >
        <InputTimeout type="number" style={{ width: "100%" }} timeDelay={0} />
      </Form.Item>
    </Col>
    <Col span={4}>
      <Form.Item label={t("sinhHieu.bmi")} name="bmi">
        <InputTimeout disabled style={{ width: "100%" }} />
      </Form.Item>
    </Col>
    <Col span={12}>
      <Form.Item label={t("common.chanDoan")} name="cdChinh">
        <InputTimeout
          isTextArea
          rows={1}
          disabled
          style={{ width: "100%" }}
          timeDelay={0}
        />
      </Form.Item>
    </Col>
  </Row>
);

const PhanDanhGiaSutCan = ({ form }) => {
  const giamCan2 = Form.useWatch("giamCan2", form);
  const tiLeGiamCan = Form.useWatch("tiLeGiamCan", form);
  const [getAnUong2] = useLazyKVMap(AN_UONG_2, "value");

  useEffect(() => {
    if (!isNil(tiLeGiamCan) && tiLeGiamCan < 5) {
      form.setFields([
        {
          name: "giamCan",
          errors: [],
        },
      ]);
    }
  }, [tiLeGiamCan]);

  return (
    <Row gutter={[8, 8]}>
      <Col>
        <Space size={"large"} align="start" className="giam-can-2-container">
          <Space style={{ width: 1077 }} size="large">
            <div className="label-8">
              {t("quanLyDinhDuong.sangLoc.sutCanTrongVong3Thang")}
            </div>
            <Form.Item
              className="label-8"
              name="giamCan2"
              style={{ marginBottom: 0 }}
              rules={[
                {
                  required: true,
                  message: t(
                    "quanLyDinhDuong.nbNoiTru.vuiLongChon1TrongNhungGiaTriTren"
                  ),
                },
              ]}
            >
              <Radio.Group>
                <Space direction="vertical" className="giam-can-2-group">
                  <Radio key="1" value={0}>
                    {t("common.khong")}
                  </Radio>
                  <Space className="giam-can-2-group-item-co">
                    <Radio key="2" value={30}>
                      {t("common.co")}
                    </Radio>
                    {giamCan2 === 30 && (
                      <>
                        <Form.Item
                          name="giamCan3"
                          valuePropName="html"
                          style={{ marginBottom: 0 }}
                          rules={[
                            {
                              required: true,
                              message: t(
                                "quanLyDinhDuong.nbNoiTru.vuiLongNhapGiaTri"
                              ),
                            },
                          ]}
                        >
                          <TextField
                            type="numberFormat"
                            suffix={"(kg)"}
                            timeDelay={0}
                            haveOtherProps={true}
                          />
                        </Form.Item>
                        <Form.Item
                          name="tiLeGiamCan"
                          label={t(
                            "quanLyDinhDuong.nbNoiTru.tyLePhanTramMatCan"
                          )}
                          style={{ marginBottom: 0, marginLeft: 20 }}
                        >
                          <div className="ti-le-giam-can-input">
                            {tiLeGiamCan !== null && tiLeGiamCan !== undefined
                              ? `${tiLeGiamCan}%`
                              : ""}
                          </div>
                        </Form.Item>
                        {tiLeGiamCan && tiLeGiamCan >= 5 && (
                          <Form.Item
                            name="giamCan"
                            style={{ marginBottom: 0, marginLeft: 20 }}
                            rules={[
                              {
                                required: true,
                                message: t(
                                  "quanLyDinhDuong.nbNoiTru.vuiLongChon1TrongNhungGiaTriTren"
                                ),
                              },
                            ]}
                          >
                            <Radio.Group>
                              <Space className="giam-can-group">
                                {FIELD_OPTIONS_MAP.giamCan.map((item, idx) => (
                                  <Radio key={idx} value={item.value}>
                                    {t(item.i18n)}
                                  </Radio>
                                ))}
                              </Space>
                            </Radio.Group>
                          </Form.Item>
                        )}
                      </>
                    )}
                  </Space>
                  <Radio key="3" value={20}>
                    {t("quanLyDinhDuong.nbNoiTru.coNhungKhongBiet")}
                  </Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Space>
          <Form.Item noStyle>
            <Col className="an-sut-giam-1-tuan-truoc-day-container">
              <Space align="start" size="large">
                <div className="title">
                  {t("quanLyDinhDuong.nbNoiTru.anSutGiam1TuanTruocDay")}
                </div>
                <Form.Item
                  name="anUong2"
                  style={{ marginBottom: 0 }}
                  rules={[
                    {
                      required: true,
                      message: t(
                        "quanLyDinhDuong.nbNoiTru.vuiLongChon1TrongNhungGiaTriTren"
                      ),
                    },
                  ]}
                >
                  <Radio.Group>
                    <Space className="an-uong-2-group" align="start">
                      <Radio value={getAnUong2(0).value}>
                        {t(getAnUong2(0).i18n)}
                      </Radio>
                      <Space direction="vertical" size={0}>
                        <Radio value={getAnUong2(2).value}>
                          {t(getAnUong2(2).i18n)}
                        </Radio>
                        <Radio value={getAnUong2(12).value}>
                          {t(getAnUong2(12).i18n)}
                        </Radio>
                        <Radio value={getAnUong2(22).value}>
                          {t(getAnUong2(22).i18n)}
                        </Radio>
                      </Space>
                    </Space>
                  </Radio.Group>
                </Form.Item>
              </Space>
            </Col>
          </Form.Item>
        </Space>
      </Col>
    </Row>
  );
};

const PhanSangLocDinhDuong = () => ({
  header: (
    <div className="header-panel">
      {t("quanLyDinhDuong.nbNoiTru.sangLocNguyCoSuyDinhDuongSDD")}{" "}
    </div>
  ),
  content: (
    <>
      <Row>
        <Col span={24}>
          <Form.Item
            name="tinhTrang"
            className="label-uppercase"
            rules={[
              {
                required: true,
                message: t(
                  "quanLyDinhDuong.nbNoiTru.vuiLongChon1TrongNhungGiaTriTren"
                ),
              },
            ]}
          >
            <NhomRadioDinhDuong
              title={t(
                "quanLyDinhDuong.nguoiBenhNoiTru2.suyGiamTinhTrangDinhDuong.title"
              )}
              name="tinhTrang"
              options={FIELD_OPTIONS_MAP.tinhTrang}
              editable={false}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item
            name="mucDo"
            className="label-uppercase"
            rules={[
              {
                required: true,
                message: t(
                  "quanLyDinhDuong.nbNoiTru.vuiLongChon1TrongNhungGiaTriTren"
                ),
              },
            ]}
          >
            <NhomRadioDinhDuong
              title={"Ảnh hưởng của Bệnh lý"}
              name="mucDo"
              options={FIELD_OPTIONS_MAP.mucDo}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item name="doTuoi" className="label-uppercase">
            <NhomRadioDinhDuong
              title={t("quanLyDinhDuong.sangLoc.doTuoi")}
              name="doTuoi"
              options={FIELD_OPTIONS_MAP.doTuoi}
              editable={false}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Form.Item name="ketLuan" hidden>
          <Input disabled={true} />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, curr) =>
            prev.ketLuan !== curr.ketLuan || prev.tongDiem !== curr.tongDiem
          }
        >
          {({ getFieldValue }) => {
            const ketLuan = getFieldValue("ketLuan");
            const tongDiem = getFieldValue("tongDiem");
            return (
              <TongDiemDisplay
                value={isNumber(ketLuan) ? ketLuan + 1 : null}
                tongDiem={tongDiem}
                listKetQua={[
                  {
                    id: 1,
                    mucDoNguyCo: 1,
                    ten: t("quanLyDinhDuong.khongNguyCoSDD"),
                  },
                  {
                    mucDoNguyCo: 3,
                    id: 3,
                    ten: t("quanLyDinhDuong.coNguyCoSDD"),
                  },
                  {
                    mucDoNguyCo: 4,
                    id: 4,
                    ten: t("quanLyDinhDuong.coNguyCoCao"),
                  },
                ]}
                scoringGuide={
                  <>
                    <div className="guide-green">
                      {t("quanLyDinhDuong.nbNoiTru.khongNguyCoSDD")}
                    </div>
                    <div className="guide-yellow">
                      {t("quanLyDinhDuong.nbNoiTru.coNguyCoSDD")}
                    </div>
                    <div className="guide-red">
                      {t("quanLyDinhDuong.nbNoiTru.coNguyCoCao")}
                    </div>
                  </>
                }
              />
            );
          }}
        </Form.Item>
      </Row>
    </>
  ),
  key: "1",
});

const PhanChanDoanDinhDuong = () => ({
  header: (
    <div className="header-panel">
      {t("quanLyDinhDuong.nbNoiTru.chanDoanSuyDinhDuong")}{" "}
    </div>
  ),
  content: (
    <>
      <Row className="tieu-chi">
        <Col>
          <div className="criteria-section">
            <div className="tieu-chi__title">
              {t("quanLyDinhDuong.nbNoiTru.tieuChiKieuHinh")}
            </div>
            <GridForm
              dataSource={CHAN_DOAN_SDD_GRID_CONFIG}
              gridTemplateColumns="220px 100px 150px 150px 150px"
              isRequired={true}
            />
          </div>
        </Col>
        <Col>
          <div className="criteria-section">
            <div className="tieu-chi__title">
              {t("quanLyDinhDuong.nbNoiTru.tieuChiNguyenNhan")}
            </div>
            <GridForm
              dataSource={TIEU_CHI_NGUYEN_NHAN_GRID_CONFIG}
              gridTemplateColumns="220px 100px 150px"
              isRequired={true}
              rulesByKey={{
                anUong: [createTieuChiNguyenNhanValidator("anUong", "tieuHoa")],
                tieuHoa: [
                  createTieuChiNguyenNhanValidator("tieuHoa", "anUong"),
                ],
              }}
            />
          </div>
        </Col>
      </Row>

      <Row>
        <Form.Item name="ketLuan2" hidden>
          <Input hidden />
        </Form.Item>
      </Row>

      <Row>
        <Form.Item name="tinhTrangSuyDinhDuong" hidden>
          <Input disabled={true} />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prev, curr) =>
            prev.tinhTrangSuyDinhDuong !== curr.tinhTrangSuyDinhDuong
          }
        >
          {({ getFieldValue }) => {
            const ketQua = getFieldValue("tinhTrangSuyDinhDuong");
            const value = isNumber(ketQua) ? ketQua / 10 : null;
            return (
              <TongDiemDisplay
                showTongDiem={false}
                value={value}
                listKetQua={[
                  {
                    id: 1,
                    mucDoNguyCo: 1,
                    ten: t("quanLyDinhDuong.nguoiBenhNoiTru2.ketLuan2.1"),
                  },
                  {
                    id: 2,
                    mucDoNguyCo: 3,
                    ten: t("quanLyDinhDuong.nguoiBenhNoiTru2.ketLuan2.2"),
                  },
                  {
                    id: 3,
                    mucDoNguyCo: 4,
                    ten: t("quanLyDinhDuong.nguoiBenhNoiTru2.ketLuan2.3"),
                  },
                ]}
                scoringGuide={
                  <>
                    <div className="guide-green">
                      {t("quanLyDinhDuong.nguoiBenhNoiTru2.ketQua.binhThuong")}
                    </div>
                    <div className="guide-yellow">
                      {t("quanLyDinhDuong.nguoiBenhNoiTru2.ketQua.sddNhe")}
                    </div>
                    <div className="guide-red">
                      {t("quanLyDinhDuong.nguoiBenhNoiTru2.ketQua.sddNang")}
                    </div>
                  </>
                }
              />
            );
          }}
        </Form.Item>
      </Row>
    </>
  ),
  key: "2",
});

const PhanKeHoachChamSoc = () => ({
  header: t("quanLyDinhDuong.nbNoiTru.keHoachChamSocDinhDuong"),
  content: (
    <GridForm
      dataSource={KE_HOACH_CHAM_SOC_GRID_CONFIG}
      gridTemplateColumns="220px 100px 140px 200px 200px"
      isRequired={true}
    />
  ),
  key: "3",
});

const PhieuSLNBNoiTru2 = (props, ref) => {
  const [state, _setState] = useState({
    show: false,
    activeKey: ["1", "2", "3"],
  });

  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  const {
    sangLocSuyDd: { themMoiSangLoc, capNhatSangLoc },
    nbDotDieuTri: { getByTongHopId },
  } = useDispatch();

  const [form] = Form.useForm();

  const { isPhieuCu = false, data, refreshInfo, refetchData } = props;
  const { showLoading, hideLoading } = useLoading();
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const thongTinBenhNhanTongHop = useStore(
    "nbDotDieuTri.thongTinBenhNhanTongHop"
  );

  const {
    calculateGiamCan4,
    calculateDanhGiaBmi,
    calculateKetQua,
    calculateKetLuan2,
    calculateSuyGiamScore,
  } = useFormCalculations();

  const { setDataForm } = useFormData({
    form,
    data,
    thongTinBenhNhanTongHop,
    nhanVienId,
    calculateGiamCan4,
    calculateKetQua,
    calculateDanhGiaBmi,
  });

  const disabledForm = useMemo(() => {
    return data?.tonTaiSga || isPhieuCu;
  }, [data?.tonTaiSga, isPhieuCu]);

  useEffect(() => {
    if (data?.nbDotDieuTriId && !thongTinBenhNhanTongHop?.id) {
      getByTongHopId(data?.nbDotDieuTriId);
    }
  }, [data?.nbDotDieuTriId, thongTinBenhNhanTongHop, getByTongHopId]);

  useImperativeHandle(ref, () => ({
    handleClickNext,
    handleClickBack,
  }));

  const handleClickBack = () => {
    form.resetFields();
  };

  const handleClickNext = () => {
    form.submit();
  };

  const onValuesChange = (changedValues) => {
    let updateFields = {};

    const allValues = form.getFieldsValue(true);

    if (
      ["canNang", "chieuCao"].some((key) => changedValues.hasOwnProperty(key))
    ) {
      let canNang =
        typeof allValues?.canNang === "string"
          ? parseFloatNumber(allValues?.canNang || 0)
          : allValues?.canNang || 0;
      let chieuCao =
        typeof allValues?.chieuCao === "string"
          ? parseFloatNumber(allValues?.chieuCao || 0)
          : allValues?.chieuCao || 0;
      let bmi = tinhBMI(canNang, chieuCao);

      updateFields.bmi = bmi;
    }

    if (
      ["canNang", "giamCan3"].some((key) => changedValues.hasOwnProperty(key))
    ) {
      const tiLeGiamCan = calculateTiLeGiamCan({
        canNang: allValues?.canNang,
        soCanSut: allValues?.giamCan3,
      });
      updateFields.tiLeGiamCan = tiLeGiamCan;
    }

    if (
      ["bmi", "tiLeGiamCan", "anUong2", "giamCan"].some(
        (key) =>
          changedValues.hasOwnProperty(key) || updateFields.hasOwnProperty(key)
      )
    ) {
      const bmi = updateFields.bmi ?? allValues?.bmi;
      const tiLeGiamCan = updateFields.tiLeGiamCan ?? allValues?.tiLeGiamCan;
      const anUong2 = allValues?.anUong2;

      updateFields.tinhTrang = calculateSuyGiamScore(
        bmi,
        tiLeGiamCan,
        allValues?.giamCan,
        anUong2
      );
    }

    if (
      ["tinhTrang", "mucDo", "doTuoi"].some(
        (key) =>
          changedValues.hasOwnProperty(key) || updateFields.hasOwnProperty(key)
      )
    ) {
      let tongDiem = ["tinhTrang", "mucDo", "doTuoi"].reduce((prev, cur) => {
        const value = updateFields.hasOwnProperty(cur)
          ? updateFields[cur]
          : allValues[cur];
        return prev + (value ? Math.floor(parseInt(value) / 10) : 0);
      }, 0);
      updateFields.tongDiem = tongDiem;

      updateFields.ketLuan = isNil(tongDiem)
        ? null
        : tongDiem < 3
        ? 0
        : tongDiem >= 5
        ? 3
        : 2;
    }

    const getFieldValues = (keys) => {
      return keys.reduce((prev, cur) => {
        prev[cur] = updateFields.hasOwnProperty(cur)
          ? updateFields[cur]
          : allValues[cur];
        return prev;
      }, {});
    };

    updateFields.giamCan4 = calculateGiamCan4(
      getFieldValues(["tiLeGiamCan", "giamCan", "giamCan2"])
    );

    updateFields.danhGiaBmi = calculateDanhGiaBmi(getFieldValues(["bmi"]));

    updateFields.ketLuan2 = calculateKetLuan2(
      getFieldValues([
        "giamCan4",
        "danhGiaBmi",
        "teoCo",
        "phu",
        "anUong",
        "tieuHoa",
      ])
    );

    updateFields.tinhTrangSuyDinhDuong = calculateKetQua(
      getFieldValues(["danhGiaBmi", "giamCan4", "teoCo"])
    );

    form.setFieldsValue(updateFields);
  };

  const onHandleSubmit = async (values) => {
    let payload = fillNullPayload({
      nbDotDieuTriId: data?.nbDotDieuTriId,
      loai: LOAI_SANG_LOC_NGUY_CO_SDD.NOI_TRU_2,
      thoiGianThucHien: values?.thoiGianThucHien.format("YYYY-MM-DD HH:mm:ss"),
      nguoiThucHienId: values?.nguoiThucHienId,
      canNang: values?.canNang,
      chieuCao: values?.chieuCao,
      bmi: values?.bmi,
      giamCan: values?.giamCan,
      giamCan2: values?.giamCan2,
      giamCan3: values?.giamCan3,
      giamCan4: values?.giamCan4,
      tiLeGiamCan: values?.tiLeGiamCan,
      tinhTrang: values?.tinhTrang,
      mucDo: values?.mucDo,
      doTuoi: values?.doTuoi,
      tongDiem: values?.tongDiem,
      ketLuan: values?.ketLuan,
      giamCan2: values?.giamCan2,
      danhGiaBmi: values?.danhGiaBmi,
      teoCo: values?.teoCo,
      phu: values?.phu,
      anUong: values?.anUong || 0,
      anUong2: values?.anUong2,
      tieuHoa: values?.tieuHoa || 0,
      ketLuan2: values?.ketLuan2,
      dsDuongNuoiAn: values?.dsDuongNuoiAn,
      moiHoiChan: values?.moiHoiChan,
      dsTinhTrangSuyDinhDuong: safeConvertToArray(
        values?.tinhTrangSuyDinhDuong
      ),
      cdChinh: values?.cdChinh,
    });

    let api = themMoiSangLoc;
    if (data?.id) {
      payload.id = data?.id;
      api = capNhatSangLoc;
    }

    try {
      showLoading();
      const res = await api(payload);

      if (data?.id) {
        const newData = await refetchData();
        setDataForm(newData);
      }

      refreshInfo(res?.data);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onCollapsed = (value) => {
    setState({
      activeKey: value,
    });
  };

  const listPanel = [
    PhanSangLocDinhDuong(),
    PhanChanDoanDinhDuong(),
    PhanKeHoachChamSoc(),
  ];

  return (
    <Main>
      <Form
        form={form}
        labelAlign="left"
        onFinish={onHandleSubmit}
        onValuesChange={onValuesChange}
        disabled={disabledForm}
        layout="horizontal"
      >
        <HeaderSection data={data} listAllNhanVien={listAllNhanVien} />

        <PhanThongTinCoBan data={data} />

        <PhanDanhGiaSutCan form={form} />

        <CollapseWrapper
          bordered={false}
          expandIcon={({ isActive }) =>
            isActive ? <SVG.IcDown /> : <SVG.IcUp />
          }
          className="site-collapse-custom-collapse"
          activeKey={state.activeKey}
          onChange={onCollapsed}
        >
          {(listPanel || []).map((panel) => (
            <Panel key={panel.key} header={panel.header}>
              {panel.content}
            </Panel>
          ))}
        </CollapseWrapper>
      </Form>
    </Main>
  );
};

export default forwardRef(PhieuSLNBNoiTru2);
