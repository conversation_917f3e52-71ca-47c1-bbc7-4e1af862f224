import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import { Main } from "./styled";
import { Col, message, Spin } from "antd";
import { useDispatch } from "react-redux";
import DanhSachPhieu from "./DanhSachPhieu";
import { useTranslation } from "react-i18next";
import { ModalTemplate, IframeResizer } from "components";
import fileUtils from "utils/file-utils";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import PhieuIn from "./PhieuIn";
import { Element } from "react-scroll";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { combineUrlParams, openInNewTab } from "utils";
import { useWindowSize } from "hooks";

const ModalSignPrint = (props, ref) => {
  const refModal = useRef(null);
  const { t } = useTranslation();
  const size = useWindowSize();

  const { lichSuKyId, ...queries } = getAllQueryString();
  const [state, _setState] = useState({
    isKyPhieu: false,
    listPhieu: [],
    isLoadingPhieu: true,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    danhSachPhieuChoKy: { getListPhieu },
  } = useDispatch();
  useImperativeHandle(ref, () => ({
    show: async ({ lichSuKyId: id, linkEditor }) => {
      setState({
        show: true,
        linkEditor,
      });
      const res = await getListPhieu({
        dsId: id || lichSuKyId,
        size: "",
      });
      if (!linkEditor) {
        if (res?.length) {
          const data = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
            id: res[0].id?.id,
            chuKySo: res[0]?.id?.chuKySo,
          });
          fileUtils
            .getFromUrl({
              url: fileUtils.absoluteFileUrl(data?.data?.file?.pdf),
            })
            .then((s) => {
              const blob = new Blob([new Uint8Array(s)], {
                type: "application/pdf",
              });
              const blobUrl = window.URL.createObjectURL(blob);
              setState({ urlFileLocal: blobUrl, isLoadingPhieu: false });
            });
        }
      }
    },
  }));

  const onViewBaoCaoDaKy = async (item) => {
    if (state.linkEditor) {
      const url = state.linkEditor.substring(
        0,
        state.linkEditor.lastIndexOf("?")
      );
      const queries = state.linkEditor.substring(
        state.linkEditor.lastIndexOf("?") + 1,
        state.linkEditor.length
      );
      const queriesObject = JSON.parse(
        '{"' +
          decodeURI(queries.replace(/&/g, '","').replace(/=/g, '":"')) +
          '"}'
      );
      const linkEditor = combineUrlParams(url, {
        ...queriesObject,
        chuKySo: item?.id?.chuKySo,
      });
      setState({ linkEditor, isLoadingPhieu: true });
    } else {
      const data = await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
        id: item?.id?.id,
        chuKySo: item.id?.chuKySo,
      });
      fileUtils
        .getFromUrl({ url: fileUtils.absoluteFileUrl(data?.data?.file?.pdf) })
        .then((s) => {
          const blob = new Blob([new Uint8Array(s)], {
            type: "application/pdf",
          });
          const blobUrl = window.URL.createObjectURL(blob);
          setState({ urlFileLocal: blobUrl, isLoadingPhieu: false });
        });
    }
  };

  const onCancel = () => {
    setState({
      show: false,
      urlFileLocal: "",
      isLoadingPhieu: true,
      linkEditor: "",
    });
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onLoad = () => {
    setState({ isLoadingPhieu: false });
  };
  return (
    <ModalTemplate
      ref={refModal}
      onCancel={onCancel}
      title={t("phieuIn.kyVaIn")}
      centered={true}
    >
      <Main className="modal-content" gutter={[8, 8]}>
        <Col md={16} xl={16} xxl={16} style={{ overflow: "auto" }}>
          <Element
            className="element element-page"
            style={{ height: state.linkEditor ? "100%" : "" }}
          >
            <Spin spinning={state.isLoadingPhieu}>
              {state.linkEditor ? (
                <IframeResizer
                  src={state.linkEditor}
                  style={{
                    width: "1px",
                    minWidth: "100%",
                    height: "100%",
                    paddingLeft: size.width <= 1368 ? "" : "150px",
                  }}
                  scrolling="yes"
                  onLoad={onLoad}
                />
              ) : (
                <PhieuIn urlFileLocal={state.urlFileLocal} />
              )}
            </Spin>
          </Element>
        </Col>
        <Col md={8} xl={8} xxl={8}>
          <DanhSachPhieu onViewBaoCaoDaKy={onViewBaoCaoDaKy} />
        </Col>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSignPrint);
