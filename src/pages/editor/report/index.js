import React, {
  useEffect,
  useRef,
  useState,
  useMemo,
  useContext,
  useCallback,
} from "react";
import { useParams, useLocation } from "react-router-dom";
import { useDispatch } from "react-redux";
import { Spin } from "antd";
// import HtmlView from "components/HtmlView";
import File from "./components/File";
import { Main } from "./styled";
import { getLayout } from "utils/editor-utils";
// import fileUtils from "utils/file-utils";
// import pdfUtils from "utils/pdf-utils";
import { checkComponentDisable } from "utils/editor-utils";
import { EMRProvider, useEditor, withEditor } from "components/editor/config";
import { EMR2Context } from "components/editor/config";
import { useLoading, useStore } from "hooks";
import { getThongTinKySo } from "utils/phieu-utils";
import { isObject } from "lodash";
import { LazyLoad } from "components";
export const refOnsaveData = React.createRef();
const EditorPage = (props) => {
  const { showLoading, hideLoading } = useLoading();
  const { editorId } = useContext(EMR2Context);
  const location = useLocation().pathname;
  const headless = location.indexOf("headless-editor") != -1;
  const file = useEditor(editorId, "file", {});
  const auth = useStore("auth.auth", {});

  const refContextValue = useRef({
    isDisable: ({ itemProps, signStatus, props, option }) => {
      const disabled = checkComponentDisable({
        auth,
        itemProps,
        signStatus,
        props,
        option,
      });

      return disabled;
    },
    fillDataToParams: (api = "") => {
      return api;
    },
    headless,
  });

  const {
    application: { onGetThongTinBenhVien },
    files: { getBaoCaoByMaBaoCao, getTemplateBieuMau },
    phieuIn: { getListPhieu, updateData },
    danhSachPhieuChoKy: { getById },
    phimTat: { onAddLayer, onRemoveLayer },
  } = useDispatch();

  const [state, _setState] = useState({
    fileOnShow: {},
    zoomValue: 100,
  });
  useEffect(() => {
    if (auth) {
      onGetThongTinBenhVien({ benhVienId: auth?.benhVien?.id });
    }
  }, [auth]);

  useEffect(() => {
    onAddLayer({ layerId: editorId });
    return () => {
      onRemoveLayer({ layerId: editorId });
    };
  }, [editorId]);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refFile = useRef(null);
  const refForm = useRef(null);
  const { maBaoCao, id } = useParams();
  const { search } = useLocation();
  const layout = useMemo(
    () => getLayout(file?.cauHinh?.layoutType, file.cauHinh?.pageType),
    [file]
  );
  const queries = useMemo(() => {
    const queries = {};
    search
      .substring("1")
      .split("&")
      .forEach((item) => {
        const arr = item.split("=");
        queries[arr[0]] = arr[1];
      });
    return queries;
  }, [search]);

  const getData = async (maBaoCao, queries) => {
    try {
      const baoCao = await getBaoCaoByMaBaoCao(editorId, {
        maBaoCao,
        id,
        queries,
      });
      refFile.current && refFile.current.setBaoCaoId(baoCao?.id);
      if (!headless) {
        await getTemplateBieuMau(editorId, {
          baoCaoId: baoCao?.id,
          api: baoCao.apiTemplate || baoCao?.api,
        });
      }
    } catch (error) {
      if (headless) {
        document.querySelector("html").innerHTML =
          "<div class='editor-loaded-data'></div>";
      }
    }
  };

  const handleGetData = useCallback(() => {
    if (maBaoCao) {
      const { printNewTab, ...rest } = queries;
      getData(maBaoCao, rest);
    }
  }, [maBaoCao, queries]);

  useEffect(() => {
    handleGetData();
  }, [handleGetData]);

  const refreshPhieuKy = async () => {
    try {
      const { kySo, maPhieuKy, ...rest } = queries || {};
      let phieuKy = null;
      if (kySo && (maPhieuKy || rest.lichSuKyId)) {
        let _listPhieu = [];
        if (!rest.lichSuKyId) {
          _listPhieu = await getListPhieu({
            ...rest,
          });
          phieuKy = (_listPhieu || []).find((x) => x.ma === maPhieuKy);
        } else {
          _listPhieu = await getById({
            id: rest.lichSuKyId,
            isAlwayResolve: true,
          });
          // Nếu k tồn tại lịch sử ký id thì gọi lại lấy danh sách phiếu
          phieuKy = {
            dsSoPhieu: _listPhieu,
          };
          if (isObject(_listPhieu) && _listPhieu.code === 602) {
            _listPhieu = await getListPhieu({
              ...rest,
            });
            phieuKy = (_listPhieu || []).find((x) => x.ma === maPhieuKy);
          }
        }
        if (phieuKy) {
          let dsPhieuKy = [];
          (phieuKy.dsSoPhieu || []).forEach((element) => {
            let _phieu = { ...phieuKy, dsSoPhieu: [element] };

            dsPhieuKy.push(getThongTinKySo(_phieu));
          });
          updateData({ dsPhieuKy });
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    refreshPhieuKy();
  }, [queries]);
  useEffect(() => {
    refContextValue.current.auth = auth;
  }, [auth]);

  const eventMessage = (event = {}) => {
    if (event.data?.TYPE === "EDITOR-SIGNED") {
      onSaveData();
    }
    if (event.data?.TYPE === "EDITOR-SIGNED-ASYNC") {
      onSaveDataDebounce();
    }
    if (event.data?.TYPE === "REFRESH-PHIEU-KY") {
      refreshPhieuKy();
    }
    if (event.data?.TYPE === "RELOAD-PHIEU-EDITOR") {
      handleGetData();
    }
  };

  useEffect(() => {
    window.addEventListener("message", eventMessage, false); //khi thay đổi fileOnShow thì thực hiện register lại event
    return () => {
      window.removeEventListener("message", eventMessage);
    };
  }, [file]);

  const onSaveData = () => {
    return new Promise(async (resolve, reject) => {
      try {
        if (!refFile.current && file && file.sign && !file.editMode) {
          file.editMode = true;
        } else if (refFile.current) {
          const res = await refFile.current.handleSubmit({
            file,
            id,
            queries,
          });
          if (["EMR_BA049", "EMR_BA093"].includes(maBaoCao)) {
            handleGetData();
          }

          resolve(res);
        }
      } catch (error) {
        reject();
      }
    });
  };
  refOnsaveData.current = onSaveData;
  const onSaveDataDebounce = async () => {
    try {
      showLoading();

      await onSaveData();
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };
  const setZoomValue = (zoom) => {
    setState({
      zoomValue: zoom,
    });
  };
  const editorComponent = (
    <div
      className={"form-content"}
      data-type={state.fileOnShow?.type}
      width={layout.width}
      style={{ width: layout.width }}
      data-type-not-edit-html={file?.cauHinh?.notEditHtml}
    >
      <File ref={refFile} config={file?.cauHinh || file?.config} />
    </div>
  );
  return (
    <EMRProvider value={refContextValue.current}>
      {headless ? (
        editorComponent
      ) : (
        <Main
          layoutType={file?.cauHinh?.layoutType || "default"}
          width={layout.width}
          height={layout.height}
          pageType={file?.cauHinh?.pageType || "A4"}
          zoomValue={state.zoomValue}
        >
          <div className="layout-body">
            {!headless && (
              <LazyLoad
                component={() => import("./components/Toolbar")}
                layoutType={
                  // isCheckShowPdf()
                  //   ? state?.pdfLayoutHorizontal
                  // :
                  file?.cauHinh?.layoutType || "default"
                } //truyền layout type vào để xác định khổ giấy khi nhấn in
                pageType={file?.cauHinh?.pageType || "A4"}
                fileOnShow={file}
                onSaveData={onSaveData}
                // changeFileOnshow={changeFile}
                // patientDocument={params.patientDocument}
                zoomValue={state.zoomValue}
                setZoomValue={setZoomValue}
                printNewTab={queries.printNewTab}
                // onChangeNameDocument={onChangeNameDocument}
              />
            )}

            <div className="layout-middle">
              <div className={"editing-contain"} id={"main-contain"}>
                <div className="editor-layout">
                  <div className={"editing-box"} id="scrollBox" ref={refForm}>
                    <Spin spinning={false}>{editorComponent}</Spin>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Main>
      )}
    </EMRProvider>
  );
};

export default withEditor(EditorPage);
