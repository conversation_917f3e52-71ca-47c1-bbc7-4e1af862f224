import React, { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Col, message, Row } from "antd";
import { DateTimePicker, Select, SelectLoadMore } from "components";
import moment from "moment";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import ChonNhomBaoCao from "pages/baocao/BaseBaoCao/components/ChonNhomBaoCao";
import {
  useStore,
  useListAll,
  useQueryAll,
  useEnum,
  useCache,
  useThietLap,
  useLazyKVMap,
} from "hooks";
import dichVuProvider from "data-access/categories/dm-dich-vu-provider";
import {
  CACHE_KEY,
  ENUM,
  PHAN_LOAI_DOI_TUONG_KCB,
  THIET_LAP_CHUNG,
  TAI_TRO_BHBL,
  ROLES,
} from "constants/index";
import { selectMaTen } from "redux-store/selectors";
import { concatString, isNumber } from "utils/index";
import { query } from "redux-store/stores";
import { Main } from "./styled";
import {
  LOAI_PHIEU_THU_TRONG_VIEN,
  DOI_TUONG_THANH_TOAN_DICH_VU,
} from "pages/baocao/utils";
import { checkRole } from "lib-utils/role-utils";

/**
 * BC10.3 Báo cáo chi tiết dịch vụ theo ngày
 *
 */

const params = { page: "", size: "", active: true };

const LIST_MAU_BAO_CAO = [
  { id: 10, i18n: "baoCao.mauTongTienTheoNgay" },
  { id: 20, i18n: "baoCao.mauGopTheoKhoaChiDinh" },
  { id: 30, i18n: "baoCao.mauChiTietBenhNhan" },
];
const LIST_SO_LUONG_DICH_VU = [
  { id: 10, i18n: "baoCao.thanhTienDichVu" },
  { id: 20, i18n: "baoCao.soLuongDichVu" },
]
const Index = () => {
  const { t } = useTranslation();

  const [dataMA_THU_NGAN, finishGetMaThuNgan] = useThietLap(
    THIET_LAP_CHUNG.MA_THU_NGAN
  );
  const isHienThiSoLuong = checkRole([
    ROLES["BAO_CAO"].LOC_XEM_SO_LUONG_DICH_VU,
  ]);
  const isHienThiThanhTien = checkRole([
    ROLES["BAO_CAO"].LOC_XEM_THANH_TIEN_DICH_VU,
  ]);
  const isHienThiSoLuongThanhTien = checkRole([
    ROLES["BAO_CAO"].LOC_XEM_SO_LUONG_THANH_TIEN_DICH_VU,
  ]);

  const {
    baoCaoDaIn: { getBc10_3 },
    dichVu: { getAllDichVu },
    loaiHinhThanhToan: { getListAllLoaiHinhThanhToan },
    doiTac: { getListAllDoiTacThanhToan },
  } = useDispatch();
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listLoaiPhieuThu] = useEnum(ENUM.LOAI_PHIEU_THU);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listAllQuayTiepDon] = useListAll("quayTiepDon", { dsLoai: 20 }, true);
  const listAllDoiTacThanhToan = useStore("doiTac.listAllDoiTacThanhToan", []);
  const [listAllPhuongThucTT] = useListAll("phuongThucTT", {}, true);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllToaNha } = useQueryAll(query.toaNha.queryAllToaNha);
  const { data: listThuNgan } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaVaiTro: dataMA_THU_NGAN,
      },
      enabled: finishGetMaThuNgan,
    })
  );
  const listAllLoaiHinhThanhToan = useStore(
    "loaiHinhThanhToan.listAllLoaiHinhThanhToan",
    []
  );
  const [listAllCaLamViec] = useListAll("caLamViec", {}, true);
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const [loaiHinhThanhToan, setLoaiHinhThanhToan] = useCache(
    "",
    CACHE_KEY.DATA_LOAI_HINH_THANH_TOAN_BC10_3,
    [""],
    false
  );
  const listEnumLoaiPhieuThuTrongVien = useMemo(() => {
    return listLoaiPhieuThu.map((x) => x.id).filter((x) => ![5, 6].includes(x));
  }, [listLoaiPhieuThu]);

  const listLoaiPhieuThuEnumCustom = useMemo(() => {
    return [
      { id: 6, ten: t("baoCao.thuNhaThuoc") },
      {
        id: LOAI_PHIEU_THU_TRONG_VIEN,
        ten: t("baoCao.thuTrongVien"),
      },
      { id: 5, ten: t("baoCao.thuNgoai") },
    ];
  }, [listLoaiPhieuThu]);

  const listLoaiXemSoLuongDVu = useMemo(() => {
    if (isHienThiSoLuong)
      return [
        { id: 20, ten: t("baoCao.soLuongDichVu") }
      ]
    if (isHienThiThanhTien)
      return [
        { id: 10, ten: t("baoCao.thanhTienDichVu") }
      ]
    if (isHienThiSoLuongThanhTien)
      return LIST_SO_LUONG_DICH_VU;
  }, [isHienThiSoLuong, isHienThiSoLuongThanhTien, isHienThiThanhTien, LIST_SO_LUONG_DICH_VU]);

  const listLoaiDoiTuongCustom = useMemo(() => {
    return (listAllLoaiDoiTuong || []).map((item) => ({
      id: item?.id,
      ten: `${item?.ma} - ${item?.ten}`,
    }));
  }, [listAllLoaiDoiTuong]);

  useEffect(() => {
    getAllDichVu(params);
    getListAllLoaiHinhThanhToan(params);
    getListAllDoiTacThanhToan(params);
  }, []);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuThoiGian}
              onChange={onChange("tuThoiGian")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.tuThoiGian && (
              <div className="error">{t("baoCao.chonTuNgay")}!</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denThoiGian}
              onChange={onChange("denThoiGian")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.denThoiGian && (
              <div className="error">{t("baoCao.chonDenNgay")}!</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaThucHien")}</label>
            <Select
              onChange={onChange("dsKhoaThucHienId")}
              value={_state.dsKhoaThucHienId}
              className="input-filter"
              placeholder={t("baoCao.chonKhoaThucHien")}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              onChange={onChange("dsKhoaChiDinhId")}
              className="input-filter"
              value={_state.dsKhoaChiDinhId}
              placeholder={t("baoCao.chonKhoaChiDinh")}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              onChange={onChange("dsNhaThuNganId")}
              value={_state.dsNhaThuNganId}
              data={listAllToaNha}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.quayThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsQuayId", true)}
              value={_state.dsQuayId}
              data={listAllQuayTiepDon}
              mode="multiple"
              showArrow
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuong")}
              value={_state.doiTuong}
              data={listDoiTuong}
              onChange={onChange("doiTuong")}
              hasAllOption={true}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
            <Select
              className="input-filter"
              placeholder={concatString(
                t("common.chon"),
                t("baoCao.loaiDoiTuong")
              )}
              data={listLoaiDoiTuongCustom}
              hasAllOption
              onChange={onChange("dsLoaiDoiTuongId")}
              value={_state.dsLoaiDoiTuongId}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              className="input-filter"
              mode="multiple"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              data={PHAN_LOAI_DOI_TUONG_KCB}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.loaiHinhThanhToan")}
            </label>
            <Select
              onChange={onChange("dsLoaiHinhThanhToanId", true)}
              value={_state.dsLoaiHinhThanhToanId}
              className="select input-filter"
              placeholder={t("baoCao.chonLoaiHinhThanhToan")}
              data={listAllLoaiHinhThanhToan}
              hasAllOption
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.caLamViec")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsCaLamViecId")}
              value={_state.dsCaLamViecId}
              data={listAllCaLamViec}
              mode="multiple"
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenDichVu")}</label>
            <SelectLoadMore
              className="input-filter"
              placeholder={t("baoCao.chonDichVu")}
              onChange={onChange("dsDichVuId", true)}
              value={_state.dsDichVuId}
              api={dichVuProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: i.ten,
              })}
              keySearch={"timKiem"}
              addParam={{ dsNhomDichVuCap3Id: _state.dsNhomDichVuCap3Id }}
              blurReset={true}
              mode="multiple"
              hasAll={true}
              showArrow
            />
          </div>
        </Col>
        <ChonNhomBaoCao
          onChange={onChange}
          _state={_state}
          keyValue="dsMaNhomBaoCao"
          mode={"multiple"}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("thuNgan.tenNganHang")}</label>
            <Select
              className="input-filter"
              placeholder={t("thuNgan.chonNganHang")}
              data={listAllDoiTacThanhToan}
              onChange={onChange("dsNganHangId", true)}
              value={_state.dsNganHangId}
              defaultValue={_state.dsNganHangId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("danhMuc.phuongThucThanhToan")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhuongThucThanhToan")}
              onChange={onChange("dsPhuongThucTtId")}
              value={_state.dsPhuongThucTtId}
              data={listAllPhuongThucTT}
              mode="multiple"
              showArrow
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              onChange={onChange("dsThuNganId", true)}
              value={_state.dsThuNganId}
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              data={listThuNgan}
              getLabel={(item) =>
                `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
              }
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiPhieuThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiPhieuThu")}
              onChange={onChange("dsLoaiPhieuThu")}
              value={_state.dsLoaiPhieuThu}
              data={listLoaiPhieuThuEnumCustom}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.doiTuongThanhToanDichVu")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongThanhToanDichVu")}
              onChange={onChange("dsNhomLoaiDichVu")}
              value={_state.dsNhomLoaiDichVu}
              data={DOI_TUONG_THANH_TOAN_DICH_VU}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.taiTroBHBL")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonTaiTroBHBL")}
              onChange={onChange("hienThiTienTaiTro")}
              value={_state.hienThiTienTaiTro}
              data={TAI_TRO_BHBL}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.mauBaoCao")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonMauBaoCao")}
              onChange={onChange("mauBaoCao")}
              value={_state.mauBaoCao}
              data={LIST_MAU_BAO_CAO}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiXemSoLuongDichVu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiXemSoLuongDichVu")}
              onChange={onChange("hienThi")}
              value={_state.hienThi}
              data={listLoaiXemSoLuongDVu}
            />
          </div>
        </Col>
      </Row>
    );
  };

  const convertDSLoaiPhieuThu = (data) => {
    if (!Array.isArray(data)) data = [data];

    return data
      .flatMap((item) => {
        if (item === LOAI_PHIEU_THU_TRONG_VIEN) {
          return listEnumLoaiPhieuThuTrongVien;
        }
        return item;
      })
      .filter((x) => isNumber(x));
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuThoiGian).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denThoiGian).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoaThucHienId: _state.dsKhoaThucHienId,
    dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
    dsNhaThuNganId: _state.dsNhaThuNganId,
    dsQuayId: _state.dsQuayId,
    doiTuong: _state.doiTuong,
    dsLoaiDoiTuongId: _state.dsLoaiDoiTuongId,
    dsDoiTuongKcb:
      _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
        ? _state.dsDoiTuongKcb.flatMap(
          (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
        )
        : null,
    hienThiTienTaiTro: _state.hienThiTienTaiTro,
    dsLoaiHinhThanhToanId: _state.dsLoaiHinhThanhToanId,
    dsCaLamViecId: _state.dsCaLamViecId,
    dsDichVuId: _state.dsDichVuId,
    dsMaNhomBaoCao: _state.dsMaNhomBaoCao,
    dsNganHangId: _state.dsNganHangId,
    dsPhuongThucTtId: _state.dsPhuongThucTtId,
    dsThuNganId: _state.dsThuNganId,
    dsLoaiPhieuThu: convertDSLoaiPhieuThu(_state.dsLoaiPhieuThu),
    dsNhomLoaiDichVu: _state.dsNhomLoaiDichVu,
    mauBaoCao: _state.mauBaoCao,
    hienThi: _state.hienThi
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.tuNgay) {
          message.error(t("baoCao.chonTuNgay"));
          return false;
        }
        if (!_state.denNgay) {
          message.error(t("baoCao.chonDenNgay"));
          return false;
        }
        return _beforeOk();
      };
  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.bc10_3")}
        renderFilter={renderFilter}
        getBc={getBc10_3}
        handleDataSearch={handleDataSearch}
        beforeOk={beforeOk}
        initState={{
          dsQuayId: [""],
          dsDichVuId: [""],
          dsCaLamViecId: [""],
          dsLoaiHinhThanhToanId: loaiHinhThanhToan,
          dsNganHangId: [""],
          dsPhuongThucTtId: [""],
          dsLoaiPhieuThu: [LOAI_PHIEU_THU_TRONG_VIEN],
          dsNhomLoaiDichVu: [""],
          hienThiTienTaiTro: "",
          mauBaoCao: 10,
        }}
        breadcrumb={[{ title: "BC10.3", link: "/bao-cao/bc-10_3" }]}
      />
    </Main>
  );
};

export default Index;
