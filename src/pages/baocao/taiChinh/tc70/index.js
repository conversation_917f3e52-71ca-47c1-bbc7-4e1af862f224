import React from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, message, Row } from "antd";
import { useCache, useEnum, useLazyKVMap, useListAll } from "hooks";
import { Select, DateTimePicker } from "components";
import BaseBaoCao from "../../BaseBaoCao";
import {
  ENUM,
  CACHE_KEY,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_THOI_GIAN,
} from "constants/index";
import { selectMaTen } from "redux-store/selectors";
import { isArray } from "utils/index";
import { ChonCoSoChiNhanh } from "../../BaseBaoCao/components";

const TRANG_THAI_CHOT_SO = [
  {
    id: true,
    i18n: "baoCao.daChotSo",
  },
  {
    id: false,
    i18n: "baoCao.chuaChotSo",
  },
];

const TC70 = () => {
  const { t } = useTranslation();
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const [cacheDoiTuongKcb, setCacheDoiTuongKcb] = useCache(
    "",
    CACHE_KEY.DOI_TUONG_KCB_TC67_1,
    null,
    false
  );
  const {
    baoCaoDaIn: { getTc70 },
  } = useDispatch();

  const handleChange = (key, onChange) => (e) => {
    if (key === "loaiThoiGian") {
      onChange("trangThaiChotSo")(false);
    }
    if (key === "dsDoiTuongKcb") {
      setCacheDoiTuongKcb(e, false);
    }
    onChange(key)(e);
  };

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.loaiThoiGian")}{" "}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={handleChange("loaiThoiGian", onChange)}
              value={_state.loaiThoiGian}
              data={listLoaiThoiGian.filter((loai) =>
                [
                  LOAI_THOI_GIAN.THEO_THOI_GIAN_CHOT_SO,
                  LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
                  LOAI_THOI_GIAN.THEO_THOI_GIAO_RA_VIEN,
                ].some((item) => item === loai.id)
              )}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonLoaiThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
              disabledDate={(current) =>
                current && _state.denNgay && current > _state.denNgay
              }
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
              disabledDate={(current) =>
                current && _state.tuNgay && current < _state.tuNgay
              }
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <ChonCoSoChiNhanh onChange={onChange} _state={_state} />
        {_state.loaiThoiGian === LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.trangThaiChotSo")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTrangThaiChotSo")}
                onChange={onChange("trangThaiChotSo")}
                value={_state.trangThaiChotSo}
                data={TRANG_THAI_CHOT_SO}
                hasAllOption={true}
              />
            </div>
          </Col>
        )}
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongNb")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongNb")}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              data={listDoiTuong}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              onChange={handleChange("dsDoiTuongKcb", onChange)}
              value={_state.dsDoiTuongKcb}
              data={PHAN_LOAI_DOI_TUONG_KCB}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onChange("dsKhoaChiDinhId")}
              value={_state.dsKhoaChiDinhId}
              data={listAllKhoa}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    loaiThoiGian: _state.loaiThoiGian,
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsCoSoKcbId: _state.dsCoSoKcbId,
    trangThaiChotSo:
      _state.loaiThoiGian === LOAI_THOI_GIAN.THEO_THOI_GIAN_CHOT_SO
        ? true
        : _state.trangThaiChotSo,
    doiTuong: _state.doiTuong,
    dsDoiTuongKcb: _state.dsDoiTuongKcb
      ? _state.dsDoiTuongKcb.flatMap(
          (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
        )
      : null,
    dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonLoaiThoiGian"));
        return false;
      }
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      if (!isArray(_state.dsCoSoKcbId, 1)) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("baoCao.coSoChiNhanh"),
          })
        );
        return false;
      }
      return _beforeOk();
    };

  return (
    <BaseBaoCao
      title={t("baoCao.tc70")}
      breadcrumb={[{ title: "TC67.1", link: "/bao-cao/tc70" }]}
      renderFilter={renderFilter}
      getBc={getTc70}
      beforeOk={beforeOk}
      handleDataSearch={handleDataSearch}
      initState={{
        loaiThoiGian: LOAI_THOI_GIAN.THEO_THOI_GIAN_CHOT_SO,
        doiTuong: [""],
        dsDoiTuongKcb: cacheDoiTuongKcb,
        dsKhoaChiDinhId: [""],
      }}
    />
  );
};

export default TC70;
