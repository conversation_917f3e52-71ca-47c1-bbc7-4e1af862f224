import React, { useMemo, useState } from "react";
import { Col, Row, message } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useLazyKVMap, useQueryAll, useStore } from "hooks";
import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "../../BaseBaoCao";
import {
  TRANG_THAI_NB_DONG_HO_SO,
  DS_DOI_TUONG_BAO_HIEM,
  ENUM,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_PHONG,
} from "constants/index";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { ChonCoSoChiNhanh } from "../../BaseBaoCao/components";
import { isArray } from "utils/index";
import { Main } from "./styled";

/**
 * TC29. Danh sách người bệnh nội trú chưa thanh toán
 *
 */

const TRANG_THAI_XAC_NHAN_BHYT = [
  {
    id: "true",
    i18n: "baoCao.daXacNhanBhyt",
  },
  {
    id: "false",
    i18n: "baoCao.chuaXacNhanBhyt",
  },
];

const Index = () => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    dsDoiTuong: null,
  });

  const setState = (data) => {
    _setState((prev) => ({ ...prev, ...data }));
  };

  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN, []);
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  const { data: listAllPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsLoaiPhong: [LOAI_PHONG.PHONG_KHAM],
      },
    })
  );
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllLoaiDoiTuong } = useQueryAll(
    query.loaiDoiTuong.queryAllLoaiDoiTuong({
      params: {
        doiTuong: state.dsDoiTuong?.length === 1 ? state.dsDoiTuong[0] : null,
      },
    })
  );

  const {
    baoCaoDaIn: { getTc29 },
  } = useDispatch();

  const listLoaiThoiGianCustom = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      [10, 70, 130].some((item) => item === loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <Row>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.theoThoiGian")}
                <span className="icon-required"> *</span>
              </label>
              <Select
                className="select input-filter"
                placeholder={t("baoCao.chonLoaiThoiGian")}
                onChange={(value) => {
                  onChange("loaiThoiGian")(value);
                  if (value == 70) {
                    onChange("trangThaiIn")(50);
                  }
                }}
                value={_state.loaiThoiGian}
                data={listLoaiThoiGianCustom}
              />
              {!_state.isValidData && !_state.loaiThoiGian && (
                <div className="error">
                  {t("baoCao.vuiLongChonLoaiThoiGian")}
                </div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.tuNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().startOf("day") }}
                value={_state.tuNgay}
                onChange={onChange("tuNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
                onKeyDown={onKeyDownDate("tuNgay")}
              />
              {!_state.isValidData && !_state.tuNgay && (
                <div className="error">{t("baoCao.chonTuNgay")}!</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.denNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().startOf("day") }}
                value={_state.denNgay}
                onChange={onChange("denNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
                onKeyDown={onKeyDownDate("denNgay")}
              />
              {!_state.isValidData && !_state.denNgay && (
                <div className="error">{t("baoCao.chonDenNgay")}!</div>
              )}
            </div>
          </Col>
          <ChonCoSoChiNhanh onChange={onChange} _state={_state} />
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.doiTuongNguoiBenh")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongNguoiBenh")}
                value={_state.dsDoiTuong}
                onChange={(e) => {
                  setState({ dsDoiTuong: e });
                  onChange("dsDoiTuong")(e);
                }}
                data={DS_DOI_TUONG_BAO_HIEM}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongKcb")}
                onChange={onChange("dsDoiTuongKcb", true)}
                value={_state.dsDoiTuongKcb}
                data={PHAN_LOAI_DOI_TUONG_KCB}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.khoaNguoiBenh")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKhoaNguoiBenh")}
                onChange={onChange("dsKhoaNbId", true)}
                value={_state.dsKhoaNbId}
                data={listAllKhoa}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("hsba.trangThaiNb")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTrangThaiNb")}
                onChange={onChange("trangThaiIn")}
                value={_state.trangThaiIn}
                data={TRANG_THAI_NB_DONG_HO_SO}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("dashboard.phongKham")}</label>
              <Select
                className="input-filter"
                placeholder={t("common.chonPhongKham")}
                onChange={onChange("dsPhongKhamId")}
                value={_state.dsPhongKhamId}
                data={listAllPhong}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("thuNgan.trangThaiXacNhanBHYT")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("thuNgan.chonTrangThaiXacNhanBHYT")}
                onChange={onChange("xacNhanBh")}
                value={_state.xacNhanBh}
                data={TRANG_THAI_XAC_NHAN_BHYT}
                hasAllOption
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiDoiTuong")}
                data={listAllLoaiDoiTuong}
                onChange={onChange("dsLoaiDoiTuongId")}
                value={_state.dsLoaiDoiTuongId}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.inChiTiet}
                onChange={onChange("inChiTiet")}
              >
                {t("baoCao.inChiTiet")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.loaiLienKet}
                onChange={onChange("loaiLienKet")}
              >
                {t("baoCao.chiLocHoSoCon")}
              </Checkbox>
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => {
    return {
      loaiThoiGian: _state.loaiThoiGian,
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsCoSoKcbId: _state.dsCoSoKcbId,
      dsDoiTuongKcb:
        _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
          ? _state.dsDoiTuongKcb.flatMap(
              (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
            )
          : null,
      dsKhoaNbId: _state.dsKhoaNbId,
      dsDoiTuong: _state.dsDoiTuong,
      trangThaiIn: _state.trangThaiIn,
      dsPhongKhamId: _state.dsPhongKhamId,
      xacNhanBh: _state.xacNhanBh,
      dsLoaiDoiTuongId: _state.dsLoaiDoiTuongId,
      inChiTiet: _state.inChiTiet,
      ...(_state.loaiLienKet ? { loaiLienKet: 10 } : {}),
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonTheoThoiGian"));
        return false;
      }
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      if (!isArray(_state.dsCoSoKcbId, 1)) {
        message.error(
          t("danhMuc.vuiLongChonTitle", {
            title: t("baoCao.coSoChiNhanh"),
          })
        );
        return false;
      }
      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.tc29")}
        breadcrumb={[{ title: "TC29", link: "/bao-cao/tc29" }]}
        renderFilter={renderFilter}
        getBc={getTc29}
        initState={{
          dsDoiTuongKcb: [""],
          dsKhoaNbId: [""],
          dsDoiTuong: [""],
          trangThaiIn: 10,
          loaiThoiGian: 10,
          xacNhanBh: "",
          inChiTiet: false,
        }}
        handleDataSearch={handleDataSearch}
        beforeOk={beforeOk}
      />
    </Main>
  );
};

export default Index;
