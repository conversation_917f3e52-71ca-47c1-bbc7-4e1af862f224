import React, { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, message, Row } from "antd";
import { useListAll, useCache, useLazyKVMap, useEnum, useStore } from "hooks";
import { Select, DateTimePicker } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import {
  ENUM,
  CACHE_KEY,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_THU_NHAP_VAO,
  LOAI_THOI_GIAN,
} from "constants/index";
import { openInNewTab } from "utils/index";

const NHOM_CHI_PHI = [
  { id: 1, i18n: "khamBenh.khamBenh" },
  { id: 2, i18n: "kpis.ngayGiuong" },
  { id: 3, i18n: "danhMuc.xetNghiem" },
  { id: 4, i18n: "baoCao.CDHA_TDCN" },
  { id: 5, i18n: "baoCao.TT_PT" },
  { id: 6, i18n: "editor.mau" },
  { id: 7, i18n: "baoCao.thuocDich" },
  { id: 8, i18n: "kho.vtyt" },
  { id: 9, i18n: "baoCao.tienAn" },
  { id: 10, i18n: "common.khac" },
];

const params = { page: "", size: "", active: true };

const TC82_1 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc82_1 },
    phong: { getListAllPhong },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
  } = useDispatch();

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const listAllPhongTheoKhoa = useStore("phong.listAllPhongTheoKhoa", []);
  const listAllPhong = useStore("phong.listAllPhong", []);
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );

  const [cacheDoiTuongKcb, setCacheDoiTuongKcb] = useCache(
    "",
    CACHE_KEY.DATA_DOI_TUONG_KCB_TC82_1,
    "",
    false
  );

  const listLoaiThoiGianFiltered = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      [
        LOAI_THOI_GIAN.THEO_THOI_GIAN_CHI_DINH,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_THUC_HIEN,
        LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
      ].some((item) => item === loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  useEffect(() => {
    getListAllPhong(params, { storeKey: "listAllPhongTheoKhoa" });
    getListAllPhong(params);
    getAllTongHopDichVuCap1(params);
    getAllTongHopDichVuCap2(params);
  }, []);

  const onHandleChange = (key, onChange) => (e) => {
    if (key === "dsKhoaThucHienId") {
      getListAllPhong(
        {
          ...params,
          dsKhoaId: e,
        },
        { storeKey: "listAllPhongTheoKhoa" }
      );
      onChange("dsPhongThucHienId")([""]);
    }
    if (key === "dsKhoaChiDinhId") {
      getListAllPhong({
        ...params,
        dsKhoaId: e,
      });
      onChange("dsPhongChiDinhId")([""]);
    }
    if (key === "dsNhomDichVuCap1Id") {
      getAllTongHopDichVuCap2({
        ...params,
        dsNhomDichVuCap1Id: e,
      });
      onChange("dsNhomDichVuCap2Id")([""]);
    }
    onChange(key)(e);
  };

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter pointer">
              {t("baoCao.theoThoiGian")}{" "}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              defaultValue={_state.loaiThoiGian}
              data={listLoaiThoiGianFiltered}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonLoaiThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              onChange={(e) => {
                setCacheDoiTuongKcb(e, false);
                onChange("dsDoiTuongKcb")(e);
              }}
              value={_state.dsDoiTuongKcb}
              data={PHAN_LOAI_DOI_TUONG_KCB}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongNb")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongNb")}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              data={listDoiTuong}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiPhieuThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiPhieuThu")}
              onChange={onChange("nhomLoaiThu")}
              value={_state.nhomLoaiThu}
              data={LOAI_THU_NHAP_VAO}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.nhomChiPhi")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomChiPhi")}
              onChange={onChange("dsLoaiNhomCap1")}
              value={_state.dsLoaiNhomCap1}
              data={NHOM_CHI_PHI}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/khoa")}
            >
              {t("baoCao.khoaThucHien")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaThucHien")}
              data={listAllKhoa}
              onChange={onHandleChange("dsKhoaThucHienId", onChange)}
              value={_state.dsKhoaThucHienId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/phong")}
            >
              {t("baoCao.phongThucHien")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhongThucHien")}
              onChange={onChange("dsPhongThucHienId")}
              value={_state.dsPhongThucHienId}
              data={listAllPhongTheoKhoa}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/khoa")}
            >
              {t("baoCao.khoaChiDinh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              value={_state.dsKhoaChiDinhId}
              onChange={onHandleChange("dsKhoaChiDinhId", onChange)}
              data={listAllKhoa}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label
              className="label-filter cursor-pointer"
              onClick={() => openInNewTab("/danh-muc/phong")}
            >
              {t("baoCao.phongChiDinh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhongChiDinh")}
              onChange={onChange("dsPhongChiDinhId")}
              value={_state.dsPhongChiDinhId}
              data={listAllPhong}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              onChange={onHandleChange("dsNhomDichVuCap1Id", onChange, _state)}
              data={listAllNhomDichVuCap1}
              value={_state.dsNhomDichVuCap1Id}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              onChange={onChange("dsNhomDichVuCap2Id")}
              data={listAllNhomDichVuCap2}
              value={_state.dsNhomDichVuCap2Id}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonTheoThoiGian"));
        return false;
      }
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      return _beforeOk();
    };

  const handleDataSearch = ({ _state }) => {
    const filterList = (list, fullList) =>
      list?.length !== fullList?.length ? list : null;

    return {
      loaiThoiGian: _state.loaiThoiGian,
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsDoiTuongKcb: _state.dsDoiTuongKcb
        ? getPhanLoaiDoiTuongKcb(_state.dsDoiTuongKcb).referIds
        : null,
      doiTuong: _state.doiTuong,
      nhomLoaiThu: _state.nhomLoaiThu,
      dsLoaiNhomCap1: _state.dsLoaiNhomCap1,
      dsKhoaThucHienId: filterList(_state.dsKhoaThucHienId, listAllKhoa),
      dsPhongThucHienId: filterList(
        _state.dsPhongThucHienId,
        listAllPhongTheoKhoa
      ),
      dsKhoaChiDinhId: filterList(_state.dsKhoaChiDinhId, listAllKhoa),
      dsPhongChiDinhId: filterList(_state.dsPhongChiDinhId, listAllPhong),
      dsNhomDichVuCap1Id: filterList(
        _state.dsNhomDichVuCap1Id,
        listAllNhomDichVuCap1
      ),
      dsNhomDichVuCap2Id: filterList(
        _state.dsNhomDichVuCap2Id,
        listAllNhomDichVuCap2
      ),
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc82_1")}
      breadcrumb={[{ title: "TC82.1", link: "/bao-cao/tc82_1" }]}
      renderFilter={renderFilter}
      getBc={getTc82_1}
      initState={{
        loaiThoiGian: LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
        dsDoiTuongKcb: cacheDoiTuongKcb,
        doiTuong: "",
        nhomLoaiThu: "",
        dsLoaiNhomCap1: NHOM_CHI_PHI.map(({ id }) => id),
        dsKhoaChiDinhId: [""],
        dsPhongChiDinhId: [""],
        dsKhoaThucHienId: [""],
        dsPhongThucHienId: [""],
        dsNhomDichVuCap1Id: [""],
        dsNhomDichVuCap2Id: [""],
      }}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
    />
  );
};

export default TC82_1;
