import { Col, message, Row } from "antd";
import { Select, SelectLoadMore, Checkbox, DateTimePicker } from "components";
import moment from "moment";
import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { useTranslation } from "react-i18next";
import {
  useCache,
  useListAll,
  useQueryAll,
  useStore,
  useThietLap,
} from "hooks";
import {
  LOAI_DICH_VU,
  LOAI_DOI_TAC,
  LOAI_VAT_TU,
  LOAI_THOI_GIAN_DUYET_PHAT,
  CACHE_KEY,
  THIET_LAP_CHUNG,
} from "constants/index";
import nhomDichVuKhoCap2Provider from "data-access/categories/nhom-dich-vu-kho-cap-2-provider";
import FilterThoiGian from "pages/baocao/BaseBaoCao/components/FilterThoiGian";
import { query } from "redux-store/stores";

/**
 * Báo cáo kho: K02. Báo cáo xuất nhập tồn kho
 *
 */

const listMauBaoCao = [
  {
    id: "1",
    ten: "K02_M01",
    i18n: "baoCao.m01MauChung",
  },
  {
    id: "2",
    ten: "K02_M02",
    i18n: "baoCao.m02dktd",
  },
  {
    id: "3",
    ten: "K02_M03",
    i18n: "baoCao.m03DanhChoKhoTong",
  },
  {
    id: "4",
    ten: "K02_M04",
    i18n: "baoCao.m04DanhSachTongKeDuoc",
  },
  {
    id: "5",
    ten: "K02_M05",
    i18n: "baoCao.m05DanhChoKhoNoiTru",
  },
  {
    id: "6",
    ten: "K02_M06",
    i18n: "baoCao.m02dktdNhaThuoc",
  },
  {
    id: "7",
    ten: "K02_M07",
    i18n: "baoCao.m02pstwXNTChiTiet",
  },
  {
    id: "8",
    ten: "K02_M08",
    i18n: "baoCao.m08pshnXNT",
  },
  {
    id: "9",
    ten: "K02_BVP",
    i18n: "baoCao.k02PVP",
  },
];

const K02 = () => {
  const { t } = useTranslation();
  const listKhoUser = useStore("kho.listKhoUser", []);
  const listNuocSanXuat = useStore("xuatXu.listAllXuatXu", []);

  const listHangSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);
  const listAllNhomDichVuKho = useStore(
    "phanNhomDichVuKho.listAllNhomDichVuKho",
    []
  );
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );

  const [mauBaoCao, setMauBaoCao] = useCache(
    "",
    CACHE_KEY.DATA_MAU_BAO_CAO_K02,
    null,
    false
  );

  const [MAU_BAO_CAO_K02] = useThietLap(THIET_LAP_CHUNG.MAU_BAO_CAO_K02);
  const [dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KHO_HIEN_THI_THEM_HAM_LUONG
  );
  const {
    baoCaoDaIn: { getK02 },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
    xuatXu: { getListAllXuatXu },
    doiTac: { getListAllNhaSanXuat, getListAllNhaCungCap },
    nhomDichVuKho: { getListAllNhomDichVuKhoCap1 },
    phanNhomDichVuKho: { getListAllNhomDichVuKho },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
  } = useDispatch();
  const listNhomDichVuKhoCap1 = useStore(
    "nhomDichVuKho.listAllNhomDichVuKhoCap1",
    []
  );
  const [listAllQuyetDinhThau] = useListAll("quyetDinhThau", {}, true);
  const [listAllNguonNhapKho] = useListAll("nguonNhapKho", {}, true);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);

  const { data: listAllPhanLoaiDichVu } = useQueryAll(
    query.phanLoaiThuoc.queryAllPhanLoaiThuoc({
      params: {
        dsLoaiDichVu: [
          LOAI_DICH_VU.THUOC,
          LOAI_DICH_VU.VAT_TU,
          LOAI_DICH_VU.HOA_CHAT,
        ],
      },
    })
  );

  const [state, _setState] = useState({
    paramDichVuCap2: {
      loaiDichVu: LOAI_DICH_VU.THUOC,
    },
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  useEffect(() => {
    getKhoTheoTaiKhoan({ page: "", page: "", active: true });
    getListAllXuatXu({ page: "", size: "", active: true });
    getListAllNhaSanXuat({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: [LOAI_DOI_TAC.NHA_SAN_XUAT],
    });
    getListAllNhaCungCap({ page: "", size: "", active: true });
    getListAllNhomDichVuKhoCap1({
      active: true,
      page: "",
      size: "",
      loaiDichVu: LOAI_DICH_VU.THUOC,
    });
    getListAllNhomDichVuKho({
      page: "",
      size: "",
      active: true,
      loaiDichVu: LOAI_DICH_VU.THUOC,
    });
    getAllTongHopDichVuCap1({ page: "", size: "", active: true });
    getAllTongHopDichVuCap2({ page: "", size: "", active: true });
  }, []);

  const customChange = (name, onChange) => (e) => {
    if (name === "dsKhoId") {
      let paramHangHoa = { active: true };
      if (Array.isArray(e) && e.length > 0 && e[0] != "") {
        paramHangHoa = { ...paramHangHoa, dsKhoId: e };
      }
      setState({
        paramHangHoa,
      });
      onChange("dichVuId")();
    }
    if (name === "dsNhomDichVuCap1Id") {
      getAllTongHopDichVuCap2({
        page: "",
        size: "",
        active: true,
        dsNhomDichVuCap1Id: e,
      });
      onChange("dsNhomDichVuCap2Id")();
    }
    onChange(name, true)(e);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <FilterThoiGian
          t={t}
          onChange={onChange}
          _state={_state}
          onKeyDownDate={onKeyDownDate}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">{t("baoCao.ngayIn")}</label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("baoCao.chonNgay")}
              value={_state.thoiGianIn}
              format="DD/MM/YYYY HH:mm:ss"
              onChange={onChange("thoiGianIn")}
              onKeyDown={onKeyDownDate("thoiGianIn")}
              showTime={{ defaultValue: moment().endOf("day") }}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("phaCheThuoc.nguoiLap")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.tenNguoiLapBaoCao")}
              onChange={onChange("nguoiLapId")}
              value={_state.nguoiLapId}
              data={listAllNhanVien}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")}
              <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={listKhoUser}
              onChange={customChange("dsKhoId", onChange)}
              value={_state.dsKhoId}
              defaultValue={_state.dsKhoId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i, idx) => {
                return {
                  value: `${i.dichVuId}-${i.khoId}-${i.ten}-${idx}`,
                  label: `${i.ma} - ${i.ten}${
                    dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG?.eval()
                      ? i.hamLuong
                        ? " - " + i.hamLuong
                        : ""
                      : ""
                  }`,
                };
              }}
              onChange={onChange("dichVuId")}
              keySearch={"timKiem"}
              value={_state.dichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramHangHoa}
              hasAll={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiNuocSanXuat}
              onChange={onChange("hienThiNuocSanXuat")}
            >
              {t("baoCao.hienThiNuocSanXuat")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiHangSanXuat}
              onChange={onChange("hienThiHangSanXuat")}
            >
              {t("baoCao.hienThiHangSanXuat")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiNhaCungCap}
              onChange={onChange("hienThiNhaCungCap")}
            >
              {t("baoCao.hienThiNhaCungCap")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiPhanLoai}
              onChange={onChange("hienThiPhanLoai")}
            >
              {t("baoCao.hienThiPhanLoaiHangHoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.hienThiNuocSanXuat ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("danhMuc.nuocSanXuat")}</label>
            <Select
              onChange={onChange("xuatXuId")}
              value={_state.xuatXuId}
              className="input-filter"
              placeholder={t("danhMuc.chonNuocSanXuat")}
              data={listNuocSanXuat}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.hienThiHangSanXuat ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("baoCao.hangSanXuat")}</label>
            <Select
              onChange={onChange("nhaSanXuatId")}
              value={_state.nhaSanXuatId}
              className="input-filter"
              placeholder={t("baoCao.chonHangSanXuat")}
              data={listHangSanXuat}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.hienThiNhaCungCap ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("danhMuc.nhaCungCap")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhaCungCap")}
              data={listAllNhaCungCap}
              onChange={onChange("dsNhaCungCapId")}
              value={_state.dsNhaCungCapId || []}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.hienThiPhanLoai ? {} : { display: "none" }}
          >
            <label className="label-filter">
              {t("baoCao.phanLoaiHangHoa")}
            </label>
            <Select
              onChange={onChange("dsPhanLoaiDvKhoId", true)}
              value={_state.dsPhanLoaiDvKhoId}
              className="input-filter"
              mode="multiple"
              placeholder={t("baoCao.chonPhanLoaiHangHoa")}
              data={listAllPhanLoaiDichVu}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiNhomThuoc}
              onChange={onChange("hienThiNhomThuoc")}
            >
              {t("baoCao.hienThiNhomThuoc")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}></Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox checkbox-pl">
            <Checkbox
              checked={_state.hienThiQuyetDinhThau}
              onChange={onChange("hienThiQuyetDinhThau")}
            >
              {t("baoCao.hienThiQuyetDinhThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.khoTaiKhoa}
              onChange={onChange("khoTaiKhoa")}
            >
              {t("baoCao.khoTaiKhoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.hienThiNhomThuoc ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("danhMuc.nhomThuoc")}</label>
            <Select
              onChange={onChange("dsNhomDvKhoCap1Id")}
              value={_state.dsNhomDvKhoCap1Id}
              className="input-filter"
              placeholder={t("danhMuc.chonNhomThuoc")}
              mode="multiple"
              showArrow
              data={listNhomDichVuKhoCap1}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.phanNhomThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhanLoaiHangHoa")}
              onChange={onChange("dsPhanNhomDvKhoId", true)}
              value={_state.dsPhanNhomDvKhoId}
              data={listAllNhomDichVuKho}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.hienThiQuyetDinhThau ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("baoCao.quyetDinhThau")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonQuyetDinhThau")}
              onChange={onChange("dsQuyetDinhThauId", true)}
              value={_state.dsQuyetDinhThauId}
              defaultValue={_state.dsQuyetDinhThauId}
              data={[
                ...(listAllQuyetDinhThau || []).map((item) => ({
                  ...item,
                  ten: item.quyetDinhThau,
                })),
              ]}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            // style={_state.khoTaiKhoa ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("baoCao.tenKhoTaiKhoa")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoa")}
              data={(listAllKhoa || []).map((item) => ({
                ...item,
                ten: `${item.ma} - ${item.ten}`,
              }))}
              onChange={onChange("dsKhoTaiKhoaId")}
              value={_state.dsKhoTaiKhoaId}
              mode="multiple"
              defaultValue={[]}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter pointer">
              {t("baoCao.loaiThoiGian")}
              <span className="icon-required"> *</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              defaultValue={_state.loaiThoiGian}
              data={LOAI_THOI_GIAN_DUYET_PHAT}
            />
            {!_state.isValidData && !_state.loaiThoiGian && (
              <div className="error">{t("baoCao.vuiLongChonLoaiThoiGian")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiVatTu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiVatTu")}
              mode="multiple"
              data={LOAI_VAT_TU}
              onChange={onChange("chayMay")}
              value={_state.chayMay}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.nguonNhapKho")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.nguonNhapKho")}
              data={listAllNguonNhapKho}
              hasAllOption={true}
              onChange={onChange("nguonNhapKhoId")}
              value={_state.nguonNhapKhoId}
              defaultValue=""
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.nhomThuocCap2")}</label>
            <SelectLoadMore
              api={nhomDichVuKhoCap2Provider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dsNhomDvKhoCap2Id")}
              keySearch={"ten"}
              value={_state.dsNhomDvKhoCap2Id}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramDichVuCap2}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.mauBaoCao")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonMauBaoCao")}
              data={
                MAU_BAO_CAO_K02
                  ? listMauBaoCao?.filter((x) =>
                      MAU_BAO_CAO_K02?.split(",")?.includes(x.ten)
                    )
                  : listMauBaoCao
              }
              onChange={(e) => {
                setMauBaoCao(e, false);
                onChange("mauBaoCao")(e);
                onChange("hienThiSoLo")(false);
              }}
              value={_state.mauBaoCao}
              allowClear
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap1")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
              onChange={customChange("dsNhomDichVuCap1Id", onChange)}
              value={_state.dsNhomDichVuCap1Id}
              data={listAllNhomDichVuCap1}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhomDichVuCap2")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
              onChange={onChange("dsNhomDichVuCap2Id")}
              value={_state.dsNhomDichVuCap2Id}
              data={listAllNhomDichVuCap2}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiMaQuanLy}
              onChange={onChange("hienThiMaQuanLy")}
            >
              {t("kho.quyetDinhThau.maHangHoaTrungThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoSlThuCap}
              onChange={onChange("theoSlThuCap")}
            >
              {t("baoCao.hienThiTheoSLThuCap")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiTenTrungThau}
              onChange={onChange("hienThiTenTrungThau")}
            >
              {t("baoCao.hienThiTenHangHoaTheoThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiTien}
              onChange={onChange("hienThiTien")}
            >
              {t("baoCao.hienThiGiaNhapThanhTien")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiGiaBan}
              onChange={onChange("hienThiGiaBan")}
            >
              {t("baoCao.hienThiGiaBan")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTheoMaDichVu}
              onChange={onChange("sapXepTheoMaDichVu")}
            >
              {t("baoCao.sapXepTheoMaHangHoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTheoTenMoiThau}
              onChange={onChange("sapXepTheoTenMoiThau")}
            >
              {t("baoCao.sapXepTheoTenMoiThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTheoDvt}
              onChange={onChange("sapXepTheoDvt")}
            >
              {t("baoCao.sapXepTheoDvt")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiGiaTruocVat}
              onChange={onChange("hienThiGiaTruocVat")}
            >
              {t("baoCao.hienThiGiaTruocVat")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoNgayHoaDon}
              onChange={onChange("theoNgayHoaDon")}
            >
              {t("baoCao.theoNgayHoaDon")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.kichCoVatTu}
              onChange={onChange("kichCoVatTu")}
            >
              {t("danhMuc.kichCoVatTu")}
            </Checkbox>
          </div>
        </Col>
        {_state.mauBaoCao == 4 && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.hienThiSoLo}
                onChange={onChange("hienThiSoLo")}
              >
                {t("baoCao.hienThiSoLoHSD")}
              </Checkbox>
            </div>
          </Col>
        )}
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    let dichVuId = _state?.dichVuId?.split("-")[0] || null;
    const chayMay = !_state.chayMay
      ? null
      : _state.chayMay.length == 1
      ? _state.chayMay[0]
      : null;
    const dsKhoId =
      _state.dsKhoId[0] == ""
        ? listKhoUser.map((item) => item.id)
        : _state.dsKhoId;
    return {
      loaiThoiGian: _state.loaiThoiGian,
      tuNgay: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denNgay: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      thoiGianIn: _state.thoiGianIn
        ? moment(_state.thoiGianIn).format("DD-MM-YYYY HH:mm:ss")
        : null,
      nguoiLapId: _state.nguoiLapId,
      dsKhoId: dsKhoId,
      dichVuId: dichVuId,
      hienThiNuocSanXuat: _state.hienThiNuocSanXuat,
      hienThiHangSanXuat: _state.hienThiHangSanXuat,
      hienThiTien: _state.hienThiTien,
      hienThiPhanLoai: _state.hienThiPhanLoai,
      hienThiMaQuanLy: _state.hienThiMaQuanLy,
      hienThiNhaCungCap: _state.hienThiNhaCungCap,
      xuatXuId: _state.xuatXuId,
      nhaSanXuatId: _state.nhaSanXuatId,
      dsPhanLoaiDvKhoId: _state.dsPhanLoaiDvKhoId,
      hienThiTenTrungThau: _state.hienThiTenTrungThau,
      dsNhomDvKhoCap1Id: _state.dsNhomDvKhoCap1Id,
      hienThiNhomThuoc: _state.hienThiNhomThuoc,
      chayMay: chayMay,
      nguonNhapKhoId: _state.nguonNhapKhoId,
      theoNgayHoaDon: _state.theoNgayHoaDon,
      hienThiQuyetDinhThau: _state.hienThiQuyetDinhThau,
      dsQuyetDinhThauId: _state.hienThiQuyetDinhThau
        ? _state.dsQuyetDinhThauId
        : null,
      khoTaiKhoa: _state.khoTaiKhoa,
      dsKhoTaiKhoaId: _state.khoTaiKhoa ? _state.dsKhoTaiKhoaId : [],
      dsNhaCungCapId: _state.hienThiNhaCungCap ? _state.dsNhaCungCapId : [],
      theoSlThuCap: _state.theoSlThuCap,
      hienThiGiaBan: _state.hienThiGiaBan,
      dsPhanNhomDvKhoId: _state.dsPhanNhomDvKhoId,
      dsNhomDvKhoCap2Id: _state.dsNhomDvKhoCap2Id,
      mauBaoCao: _state.mauBaoCao,
      dsNhomDichVuCap1Id: _state.dsNhomDichVuCap1Id,
      dsNhomDichVuCap2Id: _state.dsNhomDichVuCap2Id,
      sapXepTheoMaDichVu: _state.sapXepTheoMaDichVu,
      sapXepTheoTenMoiThau: _state.sapXepTheoTenMoiThau,
      sapXepTheoDvt: _state.sapXepTheoDvt,
      hienThiGiaTruocVat: _state.hienThiGiaTruocVat,
      kichCoVatTu: _state.kichCoVatTu,
      ...(_state.mauBaoCao == 4 && {
        hienThiSoLo: _state.hienThiSoLo,
      }),
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.dsKhoId || _state.dsKhoId.length === 0) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      if (!_state.loaiThoiGian) {
        message.error(t("baoCao.vuiLongChonLoaiThoiGian"));
        return false;
      }
      return _beforeOk();
    };
  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k02")}
        renderFilter={renderFilter}
        beforeOk={beforeOk}
        getBc={getK02}
        handleDataSearch={handleDataSearch}
        initState={{
          thoiGianIn: moment().endOf("day"),
          loaiThoiGian: 10,
          dsKhoId: [""],
          hienThiNuocSanXuat: true,
          hienThiHangSanXuat: true,
          hienThiPhanLoai: true,
          hienThiTien: true,
          hienThiGiaBan: true,
          hienThiNhomThuoc: false,
          theoNgayHoaDon: false,
          hienThiQuyetDinhThau: false,
          dsPhanLoaiDvKhoId: [""],
          hienThiNhaCungCap: false,
          theoSlThuCap: false,
          kichCoVatTu: false,
          dsPhanNhomDvKhoId: [""],
          mauBaoCao:
            MAU_BAO_CAO_K02 && MAU_BAO_CAO_K02?.split(",")?.length === 1
              ? listMauBaoCao?.find(
                  (x) => x.ten == MAU_BAO_CAO_K02?.split(",")?.[0]
                )?.id
              : mauBaoCao,
          sapXepTheoMaDichVu: false,
          sapXepTheoTenMoiThau: false,
          hienThiGiaTruocVat: false,
        }}
        breadcrumb={[{ title: "K02", link: "/bao-cao/k02" }]}
      />
    </Main>
  );
};

export default K02;
