import React, { useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Col, Row, message } from "antd";
import moment from "moment";
import { useEnum, useListAll } from "hooks";
import { LOAI_THOI_GIAN, ENUM, MAN_HINH_AP_DUNG } from "constants/index";
import { DateTimePicker, Select } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { isArray } from "utils/index";

const ALLOWED_TIME_TYPES = [
  LOAI_THOI_GIAN.THE0_THOI_GIAO_VAO_VIEN,
  LOAI_THOI_GIAN.THEO_THOI_GIAO_RA_VIEN,
];

const KHTH45 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getKhth45 },
  } = useDispatch();

  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listHuongDieuTri] = useEnum(ENUM.HUONG_DIEU_TRI);
  const [listKetQuaDieuTri] = useEnum(ENUM.KET_QUA_DIEU_TRI);
  const [listAllKetQuaDieuTri] = useListAll("ketQuaDieuTri", {}, true);

  const listKetQuaDieuTriMap = useMemo(() => {
    const resultMap = new Map();

    listAllKetQuaDieuTri
      .filter(
        (x) =>
          isArray(x.manHinhApDung, true) &&
          x.manHinhApDung.includes(MAN_HINH_AP_DUNG.NOI_TRU)
      )
      .forEach((item) => {
        const kq = listKetQuaDieuTri.find((k) => k.id === item.ketQuaDieuTri);
        const newItem = {
          ...item,
          id: kq?.id ?? item.ketQuaDieuTri,
          ten: kq?.ten ?? null,
        };
        resultMap.set(newItem.id, newItem);
      });

    return Array.from(resultMap.values());
  }, [listAllKetQuaDieuTri, listKetQuaDieuTri]);

  const listLoaiThoiGianMemo = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      ALLOWED_TIME_TYPES.includes(loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  const getFilteredKetQuaDieuTri = useCallback(
    (dsHuongDieuTri) => {
      if (!isArray(dsHuongDieuTri, true)) {
        return listKetQuaDieuTriMap;
      }

      return listKetQuaDieuTriMap.filter((item) => {
        if (isArray(item.huongDieuTri, true)) {
          return item.huongDieuTri.some((h) => dsHuongDieuTri.includes(h));
        }
        return true;
      });
    },
    [listKetQuaDieuTriMap]
  );

  const renderFilter = useCallback(
    ({ onChange, _state }) => {
      const listKetQuaDieuTriMeMo = getFilteredKetQuaDieuTri(
        _state.dsHuongDieuTri
      );

      return (
        <Row>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.loaiThoiGian")}
                <span className="icon-required">*</span>
              </label>
              <Select
                className="input-filter"
                placeholder={t("common.chonThoiGian")}
                data={listLoaiThoiGianMemo}
                onChange={onChange("loaiThoiGian")}
                value={_state.loaiThoiGian}
              />
              {!_state.isValidData && !_state.loaiThoiGian && (
                <div className="error">
                  {t("baoCao.vuiLongChonLoaiThoiGian")}
                </div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.tuNgay")}
                <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                className="input-filter"
                placeholder={t("common.chonNgay")}
                onChange={onChange("tuNgay")}
                value={_state.tuNgay}
                format="DD/MM/YYYY HH:mm:ss"
                showTime={{ defaultValue: moment().startOf("day") }}
                disabledDate={(current) =>
                  current && _state.denNgay && current > _state.denNgay
                }
              />
              {!_state.isValidData && !_state.tuNgay && (
                <div className="error">{t("baoCao.chonTuNgay")}</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.denNgay")}
                <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                className="input-filter"
                placeholder={t("common.chonNgay")}
                onChange={onChange("denNgay")}
                value={_state.denNgay}
                format="DD/MM/YYYY HH:mm:ss"
                showTime={{ defaultValue: moment().endOf("day") }}
                disabledDate={(current) =>
                  current && _state.tuNgay && current < _state.tuNgay
                }
              />
              {!_state.isValidData && !_state.denNgay && (
                <div className="error">{t("baoCao.chonDenNgay")}</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongKcb")}
                onChange={onChange("dsDoiTuongKcb")}
                value={_state.dsDoiTuongKcb}
                data={listDoiTuongKcb}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("danhMuc.huongDieuTri")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonHuongDieuTri")}
                onChange={onChange("dsHuongDieuTri")}
                value={_state.dsHuongDieuTri}
                data={listHuongDieuTri}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("hsba.ketQuaDieuTri")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKetQuaDieuTri")}
                onChange={onChange("dsKetQuaDieuTri", true)}
                value={_state.dsKetQuaDieuTri}
                defaultValue={_state.dsKetQuaDieuTri}
                data={listKetQuaDieuTriMeMo}
                mode="multiple"
              />
            </div>
          </Col>
        </Row>
      );
    },
    [
      getFilteredKetQuaDieuTri,
      listLoaiThoiGianMemo,
      listDoiTuongKcb,
      listHuongDieuTri,
      t,
    ]
  );

  const handleDataSearch = useCallback(
    ({ _state }) => ({
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      loaiThoiGian: _state.loaiThoiGian,
      dsDoiTuongKcb: _state.dsDoiTuongKcb,
      dsHuongDieuTri: _state.dsHuongDieuTri,
      dsKetQuaDieuTri: _state.dsKetQuaDieuTri,
    }),
    []
  );

  const beforeOk = useCallback(
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.loaiThoiGian) {
          message.error(t("baoCao.vuiLongChonLoaiThoiGian"));
          return false;
        }
        return _beforeOk();
      },
    [t]
  );

  return (
    <BaseBaoCao
      title={t("baoCao.khth45")}
      renderFilter={renderFilter}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      getBc={getKhth45}
      initState={{ loaiThoiGian: LOAI_THOI_GIAN.THE0_THOI_GIAO_VAO_VIEN }}
      breadcrumb={[{ title: "KHTH45", link: "/bao-cao/khth-45" }]}
    />
  );
};

export default KHTH45;
