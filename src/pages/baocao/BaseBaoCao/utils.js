import { t } from "i18next";
import { message } from "antd";
import { useLoading } from "hooks";
import { toSafePromise } from "lib-utils";
import fileUtils from "utils/file-utils";
import { printJS } from "data-access/print-provider";
import { LOAI_IN } from "constants/index";

const getExtension = (path) => path.split(".").pop();

const { showLoading, hideLoading } = useLoading();

const onDownLoadBaoCao = async ({ src, fileName, type, loaiIn }) => {
  if (!src) return;

  if (loaiIn === LOAI_IN.IN_NHANH) {
    printJS({
      printable: fileUtils.absoluteFileUrl(src),
      type: "pdf",
    });
    return;
  }

  const s = await fileUtils.getFromUrl({
    url: fileUtils.absoluteFileUrl(src),
  });

  const mimeType =
    type === 20
      ? "application/pdf"
      : type === 10
      ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      : "application/pdf";

  const blob = new Blob([new Uint8Array(s)], { type: mimeType });
  fileUtils.downloadBlob(blob, fileName);
};

const xemBaoCao = async (baoCao) => {
  const toBlobUrl = async (url, type) => {
    if (!url) return null;
    const byteArray = await fileUtils.getFromUrl({
      url: fileUtils.absoluteFileUrl(url),
    });
    return window.URL.createObjectURL(
      new Blob([new Uint8Array(byteArray)], { type })
    );
  };

  const { file, dinhDang, tenBaoCao } = baoCao || {};
  if (!file) {
    message.error(t("baoCao.khongCoTapTinBaoCao"));
    return;
  }

  const pdfUrl = file.pdf ? await toBlobUrl(file.pdf, "application/pdf") : null;
  const excelUrl = file.doc
    ? await toBlobUrl(
        file.doc,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      )
    : null;

  if (dinhDang === 20 && pdfUrl) {
    fileUtils.viewPdf({ pdf: pdfUrl, tenBaoCao });
  } else if (dinhDang === 10 && (excelUrl || pdfUrl)) {
    if (excelUrl) {
      fileUtils.viewPdf({ excel: excelUrl, pdf: pdfUrl, tenBaoCao });
    } else {
      onDownLoadBaoCao({
        src: file.doc,
        fileName: `${tenBaoCao}.${getExtension(file.doc)}`,
        type: 10,
      });
    }
  } else if (!dinhDang && (pdfUrl || excelUrl)) {
    fileUtils.viewPdf({ pdf: pdfUrl, excel: excelUrl, tenBaoCao });
  } else {
    message.error(t("baoCao.khongCoTapTinBaoCao"));
  }
};

export async function onXemBaoCao(isXemBaoCao, payload, getBc, noLoading) {
  if (!noLoading) showLoading();

  try {
    const [err, data] = await toSafePromise(getBc(payload));
    if (err?.code === 0 && err?.data === null && payload.thongBaoKetQua) {
      message.success(t("baoCao.dangTaiXuongTapTin"));
      return;
    } else if (err) {
      message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
      return;
    }

    if (isXemBaoCao) {
      await xemBaoCao(data);
    } else {
      const { dinhDang, file, loaiIn, tenBaoCao } = data || {};
      if ((dinhDang === 20 || !dinhDang) && file?.pdf) {
        onDownLoadBaoCao({
          src: file.pdf,
          type: 20,
          loaiIn,
          fileName: `${tenBaoCao}.${getExtension(file.pdf)}`,
        });
      } else if (file?.doc) {
        onDownLoadBaoCao({
          src: file.doc,
          fileName: `${tenBaoCao}.${getExtension(file.doc)}`,
          type: 10,
        });
      }
    }
  } catch (e) {
    message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
  } finally {
    if (!noLoading) hideLoading();
  }
}
