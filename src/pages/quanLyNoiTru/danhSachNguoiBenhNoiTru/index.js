import React, { useEffect, useRef, useState, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import DanhSach from "./DanhSach";
import TimKiemNb from "./TimKiemNb";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>hu<PERSON><PERSON><PERSON>, AuthWrapper } from "components";
import ModalSoDoPhongGiuong from "../ModalSoDoPhongGiuong";
import { DS_TINH_CHAT_KHOA, ROLES } from "constants/index";
import { useStore } from "hooks";
import { Main, WrapButtonRight, MainPage } from "./styled";
import ModalCapPhatSdThuoc from "./ModalCapPhatSdThuoc";
import ButtonTaoPhieuLinhTra from "../components/ButtonTaoPhieuLinhTra";
import ModalQLDieuDuongPhuTrachPhongDuong from "./ModalQLDieuDuongPhuTrachPhongDuong";
import { withPersistor } from "hoc/withPersistor";
import { PersistorUpdate } from "../components/PersistorUpdate";
import { getState } from "redux-store/stores";

const DanhSachNguoiBenhNoiTru = ({ history }) => {
  const { t } = useTranslation();
  const refModalSoDoPhongGiuong = useRef();
  const refModalCapPhatSdThuoc = useRef(null);
  const refModalQuanLyDieuDuong = useRef(null);

  const {
    danhSachNguoiBenhNoiTru: { onSearch, updateData, clearData },
  } = useDispatch();
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);

  const [state, _setState] = useState({
    firstLoadKhoaLamViec: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    return () => {
      clearData();
    };
  }, []);

  const isKhoaNbNgatDieuTriNoiTru = useMemo(() => {
    if (state.khoaLamViec?.id) {
      const _khoaNb = listKhoaTheoTaiKhoan.find(
        (x) => x.id === state.khoaLamViec?.id
      );
      if (_khoaNb) {
        return (_khoaNb?.dsTinhChatKhoa || []).includes(
          DS_TINH_CHAT_KHOA.NGAT_DIEU_TRI_NOI_TRU
        );
      }
    }

    return false;
  }, [listKhoaTheoTaiKhoan, state.khoaLamViec?.id]);

  const onClickPhongGiuong = () => {
    refModalSoDoPhongGiuong.current &&
      refModalSoDoPhongGiuong.current.show({
        nbDotDieuTriId: null,
        khoaId: state.khoaLamViec?.id || listKhoaTheoTaiKhoan[0]?.id,
      });
  };

  const onClickCapPhatSuDungThuoc = () => {
    refModalCapPhatSdThuoc.current &&
      refModalCapPhatSdThuoc.current.show({
        dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
      });
  };
  const onClickDanhSachNbSuDungDVKT = () => {
    history.push("/quan-ly-noi-tru/danh-sach-nguoi-benh-su-dung-dvkt");
  };

  const onClickQuanLyDieuDuong = () => {
    refModalQuanLyDieuDuong.current && refModalQuanLyDieuDuong.current.show();
  };

  const onChangeKhoaThucHien = (khoaLamViec, options) => {
    if (khoaLamViec) {
      updateData({
        dataSearch: {
          ...getState()?.danhSachNguoiBenhNoiTru?.dataSearch,
          dsKhoaNbId: khoaLamViec?.id,
        },
        ...(options?.type === "userChange" && {
          page: 0,
        }),
      });
      onSearch({});
      setState({ khoaLamViec });
    }
  };

  return (
    <MainPage
      breadcrumb={[
        { link: "/quan-ly-noi-tru", title: t("quanLyNoiTru.quanLyNoiTru") },
        {
          link: "/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru",
          title: t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
        },
      ]}
      title={
        <div className="title">
          <label>{t("quanLyNoiTru.danhSachNguoiBenhNoiTru")}</label>
          <div className="khoaLamViec flex">
            <span style={{ color: "#2d3540" }}>
              {t("quanLyNoiTru.khoaLamViec")}:
            </span>
            &nbsp;
            <KhoaThucHien
              dsTinhChatKhoa={DS_TINH_CHAT_KHOA.NOI_TRU}
              onChange={onChangeKhoaThucHien}
              type={1}
            />
          </div>
        </div>
      }
      titleRight={
        <WrapButtonRight>
          <AuthWrapper
            accessRoles={[
              ROLES["QUAN_LY_NOI_TRU"]
                .HIEN_THI_POPUP_QL_DIEU_DUONG_PHU_TRACH_GIUONG,
            ]}
          >
            <Button onClick={onClickQuanLyDieuDuong}>
              {t("quanLyNoiTru.bangPhanCongDieuDuong")}
            </Button>
          </AuthWrapper>
          <Button onClick={onClickDanhSachNbSuDungDVKT}>
            {t("quanLyNoiTru.danhSachNbSuDungDVKT")}
          </Button>
          <AuthWrapper
            accessRoles={[
              ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_QUAN_LY_CAP_PHAT_THUOC,
            ]}
          >
            <Button onClick={onClickCapPhatSuDungThuoc}>
              {t("quanLyNoiTru.capPhatThuoc.capPhatVaSuDungThuoc")}
            </Button>
          </AuthWrapper>
          <Button onClick={onClickPhongGiuong}>
            {t("quanLyNoiTru.dvNoiTru.soDoPhongGiuong")}
          </Button>
          <ButtonTaoPhieuLinhTra khoaLamViec={state?.khoaLamViec} />
        </WrapButtonRight>
      }
    >
      <Main>
        <TimKiemNb khoaLamViec={state?.khoaLamViec} />
        <DanhSach
          khoaLamViec={state?.khoaLamViec}
          isKhoaNbNgatDieuTriNoiTru={isKhoaNbNgatDieuTriNoiTru}
        />
        <PersistorUpdate />

        <ModalSoDoPhongGiuong
          ref={refModalSoDoPhongGiuong}
          khoaLamViec={state?.khoaLamViec}
        />
        <ModalCapPhatSdThuoc ref={refModalCapPhatSdThuoc} />
        <ModalQLDieuDuongPhuTrachPhongDuong
          khoaLamViec={state?.khoaLamViec}
          ref={refModalQuanLyDieuDuong}
        />
      </Main>
    </MainPage>
  );
};

export default withPersistor(DanhSachNguoiBenhNoiTru);
