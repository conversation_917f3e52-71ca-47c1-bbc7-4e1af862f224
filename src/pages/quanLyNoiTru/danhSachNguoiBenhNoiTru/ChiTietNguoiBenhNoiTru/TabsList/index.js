import React, { memo, useEffect, useMemo, useCallback } from "react";
import { Checkbox, Tabs, withSuspense } from "components";
import { CaretDownOutlined } from "@ant-design/icons";
import { SVG } from "assets";
import {
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
} from "constants/index";
import { useQueryString, useStore, useThietLap } from "hooks";
import { useSelector, useDispatch } from "react-redux";
import { checkRole } from "lib-utils/role-utils";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import { useTranslation } from "react-i18next";
import { isArray } from "utils/index";

const ThongTinVaoVien = withSuspense(() => import("../ThongTinVaoVien"));
const ToDieuTri = withSuspense(() => import("../ToDieuTri"));
const PhongGiuong = withSuspense(() => import("../PhongGiuong"));
const DieuTriSoKet = withSuspense(() => import("../DieuTriSoKet"));
const DvNoiTru = withSuspense(() => import("../DvNoiTru"));
const GoiPtTt = withSuspense(() => import("../GoiPtTt"));
const VitalSigns = withSuspense(() => import("components/VitalSigns"));
const DvNgoaiTru = withSuspense(() => import("../DvNgoaiTru"));
const DonThuocRaVien = withSuspense(() => import("../DonThuocRaVien"));
const ThongTinCon = withSuspense(() => import("../ThongTinCon"));
const HoiChan = withSuspense(() => import("../HoiChan"));
const DvNgoaiDieuTri = withSuspense(() => import("../DvNgoaiDieuTri"));
const TongKetKhoaDe = withSuspense(() => import("../TongKetKhoaDe"));
const ChiTietNbNhiemKhuanBody = withSuspense(() =>
  import("pages/kiemSoatNhiemKhuan/ChiTietNbNhiemKhuan").then((module) => ({
    default: module.default.Body,
  }))
);
const ChiTietNbDieuTriLaoBody = withSuspense(() =>
  import("pages/quanLyDieuTriLao/ChiTietNbDieuTriLao").then((module) => ({
    default: module.default.Body,
  }))
);
const DSPhieuThuChotDotDieuTri = withSuspense(() =>
  import("../DSPhieuThuChotDotDieuTri")
);
const DonThuocLonHon7Ngay = withSuspense(() =>
  import("../DonThuocLonHon7Ngay")
);

export const RightToolBarSinhHieu = ({ onRefetchData }) => {
  const { t } = useTranslation();
  const configData = useStore("vitalSigns.configData", {});
  const {
    vitalSigns: { updateData: updateDataVitalSigns },
  } = useDispatch();

  return (
    <div className="flex-center">
      <Checkbox
        checked={configData?.hienThiTheoKhoa}
        onChange={() => {
          updateDataVitalSigns({
            configData: {
              ...configData,
              hienThiTheoKhoa: !configData?.hienThiTheoKhoa,
            },
          });
          onRefetchData();
        }}
      >
        {t("quanLyNoiTru.sinhHieuTaiKhoa")}
      </Checkbox>
    </div>
  );
};

const TabsList = ({
  khoaLamViec,
  nbDotDieuTriId,
  onKiemTraHoSo,
  isBatBuocPhanPhongGiuong,
  checkChongChiDinh,
}) => {
  const { t } = useTranslation();
  const [tab] = useQueryString("tab", 0);
  const activeKey = useStore("toDieuTri.activeKey", null);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const nbThongTinCon = useStore("nbThongTinSanPhu.nbThongTinCon", {});
  const { dsKhoaNb } = useSelector((state) => state.soDoPhongGiuong);
  const { khoaNbId } = chiTietNguoiBenhNoiTru || {};

  const [dataKHOA_DE, isLoadFinish] = useThietLap(THIET_LAP_CHUNG.KHOA_DE, "");
  const [
    dataHIEN_THI_SINH_HIEU_THEO_KHOA,
    loadFinishHIEN_THI_SINH_HIEU_THEO_KHOA,
  ] = useThietLap(THIET_LAP_CHUNG.HIEN_THI_SINH_HIEU_THEO_KHOA);
  const [dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU] =
    useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU,
      ""
    );
  const [TRANG_THAI_NB_CHO_DON_30_NGAY] = useThietLap(
    THIET_LAP_CHUNG.TRANG_THAI_NB_CHO_DON_30_NGAY,
    ""
  );

  const isShowTabDonThuoc30Ngay = TRANG_THAI_NB_CHO_DON_30_NGAY?.split(",")
    ?.map((x) => parseInt(x, 10))
    ?.includes(chiTietNguoiBenhNoiTru.trangThai);

  const {
    toDieuTri: { updateData: updateDataToDieuTri },
    vitalSigns: { updateData: updateDataVitalSigns },
  } = useDispatch();

  useEffect(() => {
    if (tab) {
      updateDataToDieuTri({ activeKey: tab });
    }
  }, [tab]);

  useEffect(() => {
    if (
      activeKey == "5" &&
      chiTietNguoiBenhNoiTru.id &&
      loadFinishHIEN_THI_SINH_HIEU_THEO_KHOA
    ) {
      const dsKhoa = dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU
        .split(",")
        .map((x) => x.trim())
        .filter(Boolean);

      const allowShowTiepDonKham = dsKhoa.includes(
        chiTietNguoiBenhNoiTru?.maKhoaNb
      );

      updateDataVitalSigns({
        configData: {
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU, //Hiển thị danh sách sinh hiệu của NB loại = nội trú, phẫu thuật
          chiDinhTuDichVuId: chiTietNguoiBenhNoiTru.id,
          dsChiDinhTuLoaiDichVu: allowShowTiepDonKham
            ? [
                LOAI_DICH_VU.NOI_TRU,
                LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                LOAI_DICH_VU.KHAM,
                LOAI_DICH_VU.TIEP_DON,
              ]
            : [LOAI_DICH_VU.NOI_TRU, LOAI_DICH_VU.PHAU_THUAT_THU_THUAT],
          hienThiTheoKhoa: !!dataHIEN_THI_SINH_HIEU_THEO_KHOA.eval(),
        },
      });
    }
  }, [
    activeKey,
    chiTietNguoiBenhNoiTru.id,
    dataHIEN_THI_SINH_HIEU_THEO_KHOA,
    dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VA_KHAM_BENH_VAO_NOI_TRU,
    loadFinishHIEN_THI_SINH_HIEU_THEO_KHOA,
  ]);

  useEffect(() => {
    return () => {
      updateDataVitalSigns({
        configData: {},
      });
    };
  }, []);

  const isShowTabThongTinCon = useMemo(() => {
    let hasThongTinCon = isArray(nbThongTinCon?.dsThongTinCon, true);
    if (dataKHOA_DE && isLoadFinish && chiTietNguoiBenhNoiTru?.maKhoaNb) {
      let listKhoaDeThietLap = dataKHOA_DE.split(",").map((i) => i.trim());
      if (isArray(listKhoaDeThietLap, true)) {
        return (
          listKhoaDeThietLap.includes(chiTietNguoiBenhNoiTru?.maKhoaNb) ||
          hasThongTinCon
        );
      }
      return hasThongTinCon;
    }
    return hasThongTinCon;
  }, [dataKHOA_DE, isLoadFinish, chiTietNguoiBenhNoiTru, nbThongTinCon]);

  const isReadonly = useMemo(() => {
    return (
      (khoaNbId !== khoaLamViec?.id &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_KHAC_KHOA])) ||
      ([
        TRANG_THAI_NB.DANG_CHUYEN_KHOA,
        TRANG_THAI_NB.HEN_DIEU_TRI,
        TRANG_THAI_NB.DA_RA_VIEN,
        TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
        TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
      ].includes(chiTietNguoiBenhNoiTru.trangThai) &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN]))
    );
  }, [chiTietNguoiBenhNoiTru, khoaLamViec, dsKhoaNb]);

  // -------------------------------------------------------------------------
  const listTabs = [
    {
      name: t("tuyChinhGiaoDien.thongTinChung"),
      component: ThongTinVaoVien,
      props: { khoaLamViec },
      iconTab: <SVG.IcThongTinChung />,
      key: "0",
    },
    {
      name: t("quanLyNoiTru.phongGiuong.title"),
      component: PhongGiuong,
      props: { isReadonly, onKiemTraHoSo, khoaLamViec, nbDotDieuTriId },
      iconTab: <SVG.IcPhongGiuong />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].PHONG_GIUONG],
      key: "1",
    },
    {
      name: t("quanLyNoiTru.toDieuTriChiDinh"),
      component: ToDieuTri,
      props: {
        khoaLamViec,
        isReadonly,
        isBatBuocPhanPhongGiuong,
        checkChongChiDinh,
      },
      iconTab: <SVG.IcToDieuTri />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].TO_DIEU_TRI],
      key: "2",
    },
    {
      name: t("quanLyNoiTru.dvNoiTru.title"),
      component: DvNoiTru,
      props: {
        khoaLamViec,
        isBatBuocPhanPhongGiuong,
        isReadonly,
        checkChongChiDinh,
      },
      iconTab: <SVG.IcDichVu />,
      accessRoles: [],
      key: "3",
    },
    {
      name: t("quanLyNoiTru.goiMo10Ngay"),
      component: GoiPtTt,
      iconTab: <SVG.IcThongTinPttt />,
      accessRoles: [],
      rolesDisplayTab: [ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_TAB_GOI_MO_10_NGAY],
      key: "4",
    },
    {
      name: t("title.sinhHieu"),
      component: VitalSigns,
      props: {
        isReadonly,
        isEdit:
          chiTietNguoiBenhNoiTru.trangThai !==
          TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA,
        khoaLamViec,
        rightToolbar: <RightToolBarSinhHieu />,
        nbDotDieuTriId: nbDotDieuTriId,
      },
      iconTab: <SVG.IcChiSoSong />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DO_CHI_SO_SONG],
      key: "5",
    },
    {
      name: t("quanLyNoiTru.soKetNgayDieuTri"),
      component: DieuTriSoKet,
      props: { khoaLamViec, isBatBuocPhanPhongGiuong, isReadonly },
      iconTab: <SVG.IcCalendar />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].SO_KET_15_NGAY],
      key: "6",
    },
    {
      name: t("khamBenh.hoiChan"),
      component: HoiChan,
      props: { khoaLamViec },
      iconTab: <SVG.IcHoiChan />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].HOI_CHAN],
      key: "7",
    },
    {
      name: t("quanLyNoiTru.dvNgoaiDieuTri"),
      component: DvNgoaiDieuTri,
      props: {
        isReadonly,
        khoaLamViec,
        isBatBuocPhanPhongGiuong,
      },
      iconTab: <SVG.IcDichVu />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DV_NGOAI_DIEU_TRI],
      key: "8",
    },
    {
      name: t("quanLyNoiTru.donThuocRaVien"),
      component: DonThuocRaVien,
      props: { khoaLamViec, isBatBuocPhanPhongGiuong },
      iconTab: <SVG.IcDonThuoc className={"icon-fill-000"} />,
      key: "9",
    },
    ...(isShowTabDonThuoc30Ngay
      ? [
          {
            name: t("quanLyNoiTru.donThuocLonHon7Ngay"),
            component: DonThuocLonHon7Ngay,
            props: { khoaLamViec, isBatBuocPhanPhongGiuong },
            iconTab: <SVG.IcDonThuoc className={"icon-fill-000"} />,
            key: "16",
          },
        ]
      : []),
    {
      name: t("quanLyNoiTru.dvNgoaiTru"),
      component: DvNgoaiTru,
      props: { isReadonly },
      iconTab: <SVG.IcDichVu />,
      accessRoles: [ROLES["QUAN_LY_NOI_TRU"].DV_NGOAI_TRU],
      key: "10",
    },
    {
      name: t("xetNghiem.nhiemKhuan"),
      component: ChiTietNbNhiemKhuanBody,
      props: { nbDotDieuTriId, fromNoiTru: true },
      iconTab: <SVG.IcToDieuTri />,
      accessRoles: [],
      paddingCollapseHeader: "12px 16px",
      key: "11",
    },
    {
      name: t("quanLyNoiTru.dieuTriLao"),
      component: ChiTietNbDieuTriLaoBody,
      props: { khoaLamViec, nbDotDieuTriId, fromNoiTru: true, isReadonly },
      iconTab: <SVG.IcDieuTriLaoNoiTru />,
      accessRoles: [],
      rolesDisplayTab: [ROLES["QUAN_LY_NOI_TRU"].DIEU_TRI_LAO],
      key: "12",
    },
    ...(isShowTabThongTinCon
      ? [
          {
            name: t("quanLyNoiTru.thongTinCon.title"),
            component: ThongTinCon,
            props: { khoaLamViec },
            iconTab: <SVG.IcThongTinConNoiTru />,
            key: "13",
          },
          {
            name: t("quanLyNoiTru.tongKetKhoaDe.title"),
            component: TongKetKhoaDe,
            props: {
              khoaLamViec,
              nbDotDieuTriId,
              fromNoiTru: true,
              isReadonly,
            },
            iconTab: <SVG.IcTongKetKhoaDeNoiTru />,
            accessRoles: [],
            rolesDisplayTab: [
              ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_TONG_KET_KHOA_DE,
            ],
            key: "14",
          },
        ]
      : []),
    {
      name: t("quanLyNoiTru.phieuThuChotDot.dsPhieuThuChotDotDieuTri"),
      component: DSPhieuThuChotDotDieuTri,
      props: { isReadonly },
      iconTab: <SVG.IcThuTamUng />,
      rolesDisplayTab: [
        ROLES["QUAN_LY_NOI_TRU"].DS_PHIEU_THU_CHOT_DOT_DIEU_TRI,
      ],
      key: "15",
    },
  ].filter((x) => checkRole(x?.rolesDisplayTab));

  const onChange = (e) => {
    updateDataToDieuTri({ activeKey: e });
    setQueryStringValue("tab", e);
  };

  const renderContentTab = (obj) => {
    const Component = obj?.component;
    const content = checkRole(obj.accessRoles) && <Component {...obj.props} />;

    if (
      ["1", "3", "6", "7", "8", "9", "12", "13", "14", "15", "16"].includes(
        obj.key
      )
    ) {
      return content;
    }

    return (
      <Tabs.TabBox
        fixHeight={true}
        title={obj.name}
        showHeader={obj.key === "2" ? false : undefined}
        paddingCollapseHeader={
          obj.key !== "2" ? obj.paddingCollapseHeader : undefined
        }
      >
        {content}
      </Tabs.TabBox>
    );
  };

  const items = listTabs.map((obj, i) => ({
    key: obj.key,
    label: (
      <div onMouseEnter={() => obj.component.prefetch()}>
        {obj?.iconTab}
        {obj?.name}
      </div>
    ),
    children: renderContentTab(obj, i),
  }));

  return (
    <Tabs.Left
      activeKey={activeKey || "0"}
      tabPosition={"left"}
      tabWidth={185}
      type="card"
      moreIcon={<CaretDownOutlined />}
      onChange={onChange}
      className="tab-main"
      items={items}
    />
  );
};

export default memo(TabsList);
