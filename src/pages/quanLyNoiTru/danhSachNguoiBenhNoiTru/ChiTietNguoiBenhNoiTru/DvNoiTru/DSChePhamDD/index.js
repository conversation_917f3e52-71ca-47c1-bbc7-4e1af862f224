import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import Icon from "@ant-design/icons";
import { useParams } from "react-router-dom";
import {
  Checkbox,
  Pagination,
  TableWrapper,
  Tooltip,
  HeaderSearch,
  Select,
  InputTimeout,
  DatePicker,
} from "components";
import useColumns from "../useColumns";
import { InputNumber } from "antd";
import moment from "moment";
import { LOAI_DICH_VU, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { useConfirm, useListAll, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import ChinhSuaChePhamDD from "pages/chiDinhDichVu/DichVuChePhamDinhDuong/ChinhSuaChePhamDD";
import { checkRole } from "lib-utils/role-utils";
import { checkBatBuocPhanPhongGiuong } from "utils";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";
const { Column, Setting } = TableWrapper;

const DSChePhamDD = (
  {
    isEdit = false,
    khoaLamViecId,
    isReadonlyDvNoiTru,
    isColumnToDieuTri,
    isBatBuocPhanPhongGiuong,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const [NOITRU_COLUMNS] = useColumns();
  const refSuaChePhamDD = useRef(null);
  const refSettings = useRef(null);
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const [state, _setState] = useState({
    show: false,
    currentItem: {},
    currentIndex: -1,
    isCheckedAll: false,
    dataSource: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const dataSortColumnChePhamDD = useStore(
    "dvNoiTru.dataSortColumnChePhamDD",
    {}
  );
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const [dataSearch, setDataSearch] = useState({
    page: 0,
    size: 30,
    totalElements: 0,
    dsDoiTuongKcb: [2, 3, 4],
    nbDotDieuTriId: id,
    dsTrangThaiHoan: [0, 10, 20],
  });

  const dsChePhamDD = useSelector((state) => state.dvNoiTru.dsChePhamDD);
  const {
    auth: { nhanVienId },
  } = useSelector((state) => state.auth);
  const {
    dvNoiTru: { onSearchDvChePhamDD, updateData, onSortChangeChePhamDD },
    chiDinhChePhamDinhDuong: { onDeleteDichVu },
    mucDichSuDung: { onSearch: getListTT20 },
    lieuDung: { getListAllLieuDung },
    chiDinhKhamBenh: { updateConfigData },
  } = useDispatch();

  const onSearchByParams = (params) => {
    onSearchDvChePhamDD(params).then((s) => {
      const { pageNumber, pageSize, totalElements } = s || {};
      setDataSearch({
        ...dataSearch,
        ...params,
        page: pageNumber,
        size: pageSize,
        totalElements: totalElements,
      });
    });
  };

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru.id) {
      updateConfigData({
        configData: {
          thongTinNguoiBenh: chiTietNguoiBenhNoiTru,
        },
      });
    }
  }, [chiTietNguoiBenhNoiTru.id]);

  useEffect(() => {
    if (id && isFinish) {
      onSearchByParams({
        ...dataSearch,
        dsKhoaChiDinhId: khoaLamViecId ? [khoaLamViecId] : [],
        size: dataPageSize,
      });
    }
  }, [id, khoaLamViecId, isFinish, dataPageSize]);

  useEffect(() => {
    if (dsChePhamDD) {
      setState({
        dataSource: dsChePhamDD.map((item) => ({
          ...item,
          loaiDichVu: LOAI_DICH_VU.CHE_PHAM_DINH_DUONG,
        })),
      });
    }
  }, [dsChePhamDD]);

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onSearchByParams({ ...dataSearch });
    },
    getEditList: () => {
      return state.dataSource.filter((x) => x.isEditing);
    },
    clearSelect: () => {
      setState({ currentItem: {}, currentIndex: -1 });
    },
    onSettings: onSettings,
  }));

  const onChange = (key) => (e) => {
    let value = e;
    if (["khongTinhTien", "tuTra", "trangThaiThanhToan"].includes(key)) {
      value = e?.target?.checked;
    }

    if (["thoiGianThucHien", "thoiGianChiDinh"].includes(key)) {
      value = e instanceof moment ? e.format() : null;
    }

    let dataSource = state.dataSource;

    dataSource[state.currentIndex][key] = value;
    if (key === "khongTinhTien" && value) {
      dataSource[state.currentIndex].tuTra = !value;
    }
    if (key === "tuTra" && value) {
      dataSource[state.currentIndex].khongTinhTien = !value;
    }
    dataSource[state.currentIndex].isEditing = true;

    setState({ dataSource });
  };

  const onEdit = (record) => () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refSuaChePhamDD.current &&
        refSuaChePhamDD.current.show({ data: record }, () => {
          onSearchByParams({ ...dataSearch });
        });
  };

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + (record?.tenDichVu || "") + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        onDeleteDichVu(record.id).then((s) => refreshData());
      }
    );
  };

  const refreshData = () => {
    onSearchByParams({ ...dataSearch });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onSearch = (key, value) => {
    const newSearch = { ...dataSearch, [key]: value };

    onSearchByParams(newSearch);
  };

  const onSearchDate = (key, value) => {
    const newSearch = {
      ...dataSearch,
      [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
      [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
    };

    onSearchByParams(newSearch);
  };

  const onClickSort = (key, value) => {
    onSortChangeChePhamDD({
      [key]: value,
      dataSearch,
    });
  };

  const dvNoiTruColumns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.MA_CHE_PHAM_DINH_DUONG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.TEN_CHE_PHAM_DINH_DUONG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.CACH_DUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.slChiDinh"),
      width: 100,
      dataIndex: "soLuongYeuCau",
      key: "soLuongYeuCau",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.slChiDinh",
      sort_key: "soLuongYeuCau",
      dataSort: dataSortColumnChePhamDD["soLuongYeuCau"] || "",
      onClickSort: onClickSort,
      render: (item, list, index) => {
        if (index === state.currentIndex && !list?.phieuLinhId) {
          return (
            <InputNumber
              defaultValue={item}
              onChange={onChange("soLuongYeuCau")}
            />
          );
        } else {
          return item;
        }
      },
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.slTra"),
      width: 100,
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.slTra",
      sort_key: "soLuongTra",
      dataSort: dataSortColumnChePhamDD["soLuongTra"] || "",
      onClickSort: onClickSort,
      render: (item, list, index) => {
        if (index === state.currentIndex && !list?.phieuLinhId) {
          return (
            <InputNumber
              defaultValue={item}
              onChange={onChange("soLuongTra")}
            />
          );
        } else {
          return item;
        }
      },
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.slThucDung"),
      width: 80,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.slThucDung",
      sort_key: "soLuong",
      dataSort: dataSortColumnChePhamDD["soLuong"] || "",
      onClickSort: onClickSort,
      render: (item, list, index) =>
        Number(list?.soLuongYeuCau - list?.soLuongTra || 0),
    }),
    NOITRU_COLUMNS.DON_VI_TINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.THOI_GIAN_CHI_DINH({
      // onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.THOI_GIAN_THUC_HIEN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnChePhamDD,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianThucHien")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.TEN_KHO({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.NGUOI_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.DA_PHAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.KHONG_TINH_TIEN({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.TU_TRA({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.THANH_TIEN({
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.TEN_KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
      value: dataSearch?.dsKhoaChiDinhId,
      isReadonlyDvNoiTru,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.soPhieuLinh"),
      sort_key: "soPhieuLinh",
      width: 160,
      dataIndex: "soPhieuLinh",
      key: "soPhieuLinh",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.soPhieuLinh",
      dataSort: dataSortColumnChePhamDD["soPhieuLinh"] || "",
      onClickSort: onClickSort,
      renderSearch: (
        <InputTimeout
          placeholder={t("quanLyNoiTru.dvNoiTru.nhapSoPhieuLinh")}
          onChange={(e) => {
            onSearch("soPhieuLinh", e);
          }}
        />
      ),
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.thoiGianPhat"),
      sort_key: "thoiGianDuyet",
      width: 160,
      dataIndex: "thoiGianDuyet",
      key: "thoiGianDuyet",
      i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhat",
      dataSort: dataSortColumnChePhamDD["thoiGianDuyet"] || "",
      onClickSort: onClickSort,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      selectSearch: true,
      renderSearch: (
        <DatePicker
          format="DD/MM/YYYY"
          placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
          onChange={(e) => {
            onSearchDate("thoiGianDuyet", e);
          }}
        />
      ),
    }),
    NOITRU_COLUMNS.LUU_Y({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.DON_GIA_KHONG_BH({
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.DOT_XUAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.BO_SUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 100,
      align: "center",
      fixed: "right",
      ignore: true,
      hidden: isEdit,
      render: (list, item, index) => {
        const checkQuyenXoa = () => {
          if (
            nhanVienId === item?.bacSiChiDinhId &&
            checkRole([
              ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_CHE_PHAM_DINH_DUONG,
            ])
          ) {
            return true;
          }

          if (
            checkRole([
              ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_CHE_PHAM_DINH_DUONG,
              ROLES["QUAN_LY_NOI_TRU"].XOA_CHI_DINH_DV_VA_THUOC,
            ])
          ) {
            return true;
          }

          return false;
        };

        const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien }, listAllPhieuThuChotDot
        );

        return (
          <div className="ic-action">
            {isChotThoiGianDotDieuTri && (
              <>
                {checkRole([
                  ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_CHE_PHAM_DINH_DUONG,
                ]) && (
                    <Tooltip
                      title={t("quanLyNoiTru.chinhSuaDichVu")}
                      placement="bottom"
                    >
                      <Icon
                        className="ic-action"
                        component={SVG.IcEdit}
                        onClick={onEdit(item)}
                      ></Icon>
                    </Tooltip>
                  )}
                {checkQuyenXoa() && !isReadonlyDvNoiTru && (
                  <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                    <Icon
                      className="ic-action"
                      component={SVG.IcDelete}
                      onClick={() => onDelete(item)}
                    ></Icon>
                  </Tooltip>
                )}
              </>
            )}
          </div>
        );
      },
    }),
  ];

  const toDieuTriColumns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.TEN_CHE_PHAM_DINH_DUONG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.CACH_DUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.SO_LUONG_KE({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    Column({
      title: t("quanLyNoiTru.toDieuTri.soLuongDung"),
      width: 120,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "quanLyNoiTru.toDieuTri.soLuongDung",
      sort_key: "soLuong",
      dataSort: dataSortColumnChePhamDD["soLuong"] || "",
      onClickSort: onClickSort,
      render: (item, list, index) =>
        Number(list?.soLuongYeuCau - list?.soLuongTra || 0),
    }),
    NOITRU_COLUMNS.CACH_DUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.THANH_TIEN({
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.TEN_KHO({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.DA_PHAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.TRANG_THAI_THANH_TOAN({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.BAC_SY_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.THOI_GIAN_CHI_DINH({
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.thoiGianPhat"),
      sort_key: "thoiGianDuyet",
      width: 160,
      dataIndex: "thoiGianDuyet",
      key: "thoiGianDuyet",
      i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhat",
      dataSort: dataSortColumnChePhamDD["thoiGianDuyet"] || "",
      onClickSort: onClickSort,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      selectSearch: true,
      renderSearch: (
        <DatePicker
          format="DD/MM/YYYY"
          placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
          onChange={(e) => {
            onSearchDate("thoiGianDuyet", e);
          }}
        />
      ),
    }),
    NOITRU_COLUMNS.TU_TRA({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
    NOITRU_COLUMNS.KHONG_TINH_TIEN({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnChePhamDD,
    }),
  ];

  const columns = isColumnToDieuTri ? toDieuTriColumns : dvNoiTruColumns;

  const onRow = (record, index) => {
    return {
      onClick: () => {
        if (!isEdit) return;

        if (index !== state.currentIndex) {
          setState({
            currentItem: record,
            currentIndex: index,
          });

          getListTT20({ dataSearch: { dichVuId: record.dichVuId } });
          getListAllLieuDung(
            {
              page: "",
              size: "",
              active: true,
              bacSiId: nhanVienId,
              dichVuId: record.dichVuId,
            },
            nhanVienId + "_" + record.dichVuId
          );
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newSearch = { ...dataSearch, page: page - 1 };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const handleSizeChange = (size) => {
    const newSearch = { ...dataSearch, page: 0, size };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
      isCheckedAll: selectedRowKeys.length == dsChePhamDD.length,
    });

    updateData({
      listDeleteDv: data,
    });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (dsChePhamDD || []).map((x) => x.id)
        : [],
      isCheckedAll: e.target?.checked,
    });

    updateData({
      listDeleteDv: e.target?.checked ? dsChePhamDD : [],
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <Main noPadding={true}>
      <div className="table-content">
        <TableWrapper
          columns={columns}
          dataSource={state.dataSource || []}
          onRow={onRow}
          rowKey={(record) => record.id}
          rowSelection={isEdit && rowSelection}
          tableName="table_DVNOITRU_DSChePhamDD"
          ref={refSettings}
          columnResizable={true}
        />

        <Pagination
          onChange={onChangePage}
          current={dataSearch?.page + 1}
          pageSize={dataSearch?.size}
          listData={dsChePhamDD || []}
          total={dataSearch?.totalElements}
          onShowSizeChange={handleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
        <ChinhSuaChePhamDD
          ref={refSuaChePhamDD}
          isReadonlyDvNoiTru={isReadonlyDvNoiTru}
          refreshList={onSearchByParams}
        />
      </div>
    </Main>
  );
};

export default forwardRef(DSChePhamDD);
