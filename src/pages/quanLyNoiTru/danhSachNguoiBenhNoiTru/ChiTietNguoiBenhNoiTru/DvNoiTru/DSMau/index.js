import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperative<PERSON>andle,
  useMemo,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { message } from "antd";
import { useParams } from "react-router-dom";
import {
  Checkbox,
  Tooltip,
  Pagination,
  TableWrapper,
  HeaderSearch,
  Select,
  DatePicker,
  AuthWrapper,
} from "components";
import ChinhSuaMau from "pages/chiDinhDichVu/DichVuMau/ChinhSuaMau";
import useColumns from "../useColumns";
import {
  useConfirm,
  useEnum,
  useListAll,
  useStore,
  useThietLap,
  useLoading,
} from "hooks";
import {
  ENUM,
  GIOI_TINH_BY_VALUE,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  TRANG_THAI_MAU,
  ROLES,
} from "constants/index";
import moment from "moment";
import { SVG } from "assets";
import ModalYeuCauTraDV from "../../ToDieuTri/ChiDinhDichVu/Mau/ModalYeuCauTraDV";
import { checkBatBuocPhanPhongGiuong } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";

const { Column, Setting } = TableWrapper;

const DSMau = (
  {
    isEdit = false,
    khoaLamViecId,
    isReadonlyDvNoiTru,
    isBatBuocPhanPhongGiuong,
    khongBatBuocKhoa,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const [NOITRU_COLUMNS] = useColumns();
  const refSuaThongTin = useRef(null);
  const refSettings = useRef(null);
  const refModalHoanDichVu = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  const [listKetQuaXetNghiem] = useEnum(ENUM.KET_QUA_XET_NGHIEM);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const [dataTHOI_GIAN_DUOC_TRA_MAU] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_DUOC_TRA_MAU
  );
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);

  const [state, _setState] = useState({
    show: false,
    currentItem: {},
    currentIndex: -1,
    isCheckedAll: false,
    dataSource: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const dataSortColumnMau = useStore("dvNoiTru.dataSortColumnMau", {});
  const listAllDuongDung = useStore("duongDung.listAllDuongDung", []);

  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  const [dataSearch, setDataSearch] = useState({
    page: 0,
    size: 10,
    totalElements: 0,
    dsDoiTuongKcb: [2, 3, 4],
    nbDotDieuTriId: id,
    dsTrangThaiHoan: [0, 10, 20],
  });
  const { mau } = useSelector((state) => state.dvNoiTru);
  const {
    dvNoiTru: { onSearchDvMau, updateData, onSortChangeMau },
    chiDinhMau: { onDeleteDichVu },
    truyenPhatMau: { xacNhanTruyenMau, huyTruyenMau },
    duongDung: { getListAllDuongDung },
  } = useDispatch();

  const [dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU
  );

  const onSearchByParams = (params) => {
    onSearchDvMau(params).then((s) => {
      const { pageNumber, pageSize, totalElements } = s || {};
      setDataSearch({
        ...dataSearch,
        ...params,
        page: pageNumber || 0,
        size: pageSize || 10,
        totalElements: totalElements || 0,
      });
    });
  };

  useEffect(() => {
    getListAllDuongDung({
      page: "",
      size: "",
      active: true,
      sort: "ten,asc",
    });
  }, []);

  useEffect(() => {
    if (id && isFinish) {
      onSearchByParams({
        ...dataSearch,
        nbDotDieuTriId: id,
        size: dataPageSize,
        ...(!khongBatBuocKhoa && {
          khoaChiDinhId: khoaLamViecId,
        }),
      });
    }
  }, [id, khongBatBuocKhoa, khoaLamViecId && isFinish && dataPageSize]);

  useEffect(() => {
    if (mau) {
      setState({
        dataSource: mau.map((item) => ({
          ...item,
          loaiDichVu: LOAI_DICH_VU.CHE_PHAM_MAU,
        })),
      });
    }
  }, [mau]);

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onSearchByParams({ ...dataSearch });
    },
    getEditList: () => {
      return state.dataSource.filter((x) => x.isEditing);
    },
    clearSelect: () => {
      setState({ currentItem: {}, currentIndex: -1 });
    },
    onSettings: onSettings,
  }));

  const onChange = (key) => (e) => {
    let value = e;
    if (["khongTinhTien", "tuTra"].includes(key)) {
      value = e?.target?.checked;
    }

    if (key == "thoiGianThucHien") {
      value = e instanceof moment ? e.format() : null;
    }

    let dataSource = state.dataSource;

    dataSource[state.currentIndex][key] = value;
    if (key == "khongTinhTien" && value) {
      dataSource[state.currentIndex].tuTra = !value;
    }
    if (key == "tuTra" && value) {
      dataSource[state.currentIndex].khongTinhTien = !value;
    }
    dataSource[state.currentIndex].isEditing = true;

    setState({ dataSource });
  };

  const onEdit = (record) => () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refSuaThongTin.current &&
        refSuaThongTin.current.show(record, () => {
          onSearchByParams({ ...dataSearch });
        });
  };

  const onDelete = (record) => {
    if (
      dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU?.eval() &&
      record?.maTuiMau &&
      record?.nguoiPhat1Id
    ) {
      return message.error(
        t("quanLyNoiTru.mau.xoaKhongThanhCong_MauDaĐuocienThongMaTuiMau")
      );
    }
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content:
          t("common.banCoChacMuonXoa") +
          (record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "") +
          "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        onDeleteDichVu(record.id).then((s) => refreshData());
      }
    );
  };
  const refreshData = () => {
    onSearchByParams({ ...dataSearch });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onSearch = (key, value) => {
    const newSearch = { ...dataSearch, [key]: value };

    onSearchByParams(newSearch);
  };

  const onSearchDate = (key, value) => {
    const newSearch = {
      ...dataSearch,
      [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
      [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
    };

    onSearchByParams(newSearch);
  };

  const onClickSort = (key, value) => {
    onSortChangeMau({
      [key]: value,
      dataSearch,
    });
  };

  const dataNb = useMemo(() => {
    let gender = chiTietNguoiBenhNoiTru?.gioiTinh
      ? GIOI_TINH_BY_VALUE[chiTietNguoiBenhNoiTru?.gioiTinh]
      : "";

    let age =
      chiTietNguoiBenhNoiTru?.thangTuoi > 36 || chiTietNguoiBenhNoiTru?.tuoi
        ? `${chiTietNguoiBenhNoiTru?.tuoi} ${t("common.tuoi")}`
        : `${chiTietNguoiBenhNoiTru?.thangTuoi} ${t("common.thang")}`;
    return { gender, age };
  }, []);

  const onHoanDv = (record) => {
    const data = Array(record);
    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = dataNb?.gender;
        itemLoop.tuoi = dataNb?.age;
      });
      const thoiGianDuocTra = dataTHOI_GIAN_DUOC_TRA_MAU || 0;
      if (record.thoiGianDuyet && dataTHOI_GIAN_DUOC_TRA_MAU) {
        const thoiGianDuyet = moment(record.thoiGianDuyet)
          .add(thoiGianDuocTra, "minutes")
          .valueOf();
        const thoiGianHienTai = moment().valueOf();
        if (thoiGianDuyet < thoiGianHienTai) {
          message.error(
            t("quanLyNoiTru.yeuCauTraMauErr", { num: thoiGianDuocTra })
          );
        } else {
          refModalHoanDichVu.current &&
            refModalHoanDichVu.current.show({ data }, () => {
              refreshData();
            });
        }
      } else {
        refModalHoanDichVu.current &&
          refModalHoanDichVu.current.show({ data }, () => {
            refreshData();
          });
      }
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onXacNhanTruyenMau = async (item) => {
    try {
      showLoading();
      await xacNhanTruyenMau([item.id]);
      message.success(t("khoMau.xacNhanTruyenMauThanhCong"));
      refreshData();
    } catch (error) {
      message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    } finally {
      hideLoading();
    }
  };

  const onHuyTruyenMau = async (item) => {
    try {
      showLoading();
      await huyTruyenMau([item.id]);
      message.success(t("khoMau.huyXacNhanTruyenMauThanhCong"));
      refreshData();
    } catch (error) {
      message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    } finally {
      hideLoading();
    }
  };

  const columns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.CHE_PHAM_MAU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.THOI_GIAN_THUC_HIEN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnMau,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("quanLyNoiTru.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianThucHien")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    Column({
      title: t("hsba.slKe"),
      width: 80,
      dataIndex: "soLuongYeuCau",
      key: "soLuongYeuCau",
      align: "center",
      i18Name: "hsba.slKe",
      sort_key: "soLuongYeuCau",
      dataSort: dataSortColumnMau["soLuongYeuCau"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("hsba.slDung"),
      width: 80,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "hsba.slDung",
      sort_key: "soLuong",
      dataSort: dataSortColumnMau["soLuong"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.soLuongTra"),
      width: 80,
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.soLuongTra",
      sort_key: "soLuongTra",
      dataSort: dataSortColumnMau["soLuongTra"] || "",
      onClickSort: onClickSort,
    }),
    NOITRU_COLUMNS.TRANG_THAI_MAU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.NGUON_KHAC(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnMau,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex && isEdit) {
            return (
              <Select
                defaultValue={state.currentItem?.nguonKhacId}
                data={listAllNguonKhacChiTra}
                placeholder={t("danhMuc.nguonKhac")}
                onChange={onChange("nguonKhacId")}
              />
            );
          } else
            return (
              item && listAllNguonKhacChiTra?.find((o) => o.id === item)?.ten
            );
        },
      }
    ),
    NOITRU_COLUMNS.KHONG_TINH_TIEN(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnMau },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Checkbox
                checked={item}
                onChange={onChange("khongTinhTien")}
                disabled={
                  !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN])
                }
              />
            );
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TU_TRA(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnMau },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return <Checkbox checked={item} onChange={onChange("tuTra")} />;
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.THANH_TIEN({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.NHOM_MAU_KE(
      { onClickSort, dataSortColumn: dataSortColumnMau },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Select
                data={listNhomMau}
                placeholder={t("quanLyNoiTru.chonNhomMau")}
                onChange={onChange("nhomMauNb")}
                defaultValue={list?.nhomMauNb}
              />
            );
          } else {
            return listNhomMau.find((x) => x.id == item)?.ten || "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.NHOM_MAU_PHAT({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.MA_TUI_MAU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.theTich"),
      width: 80,
      dataIndex: "theTich",
      key: "theTich",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.theTich",
      sort_key: "theTich",
      dataSort: dataSortColumnMau["theTich"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.tenDonViTinh"),
      width: 100,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.tenDonViTinh",
      sort_key: "tenDonViTinh",
      dataSort: dataSortColumnMau["tenDonViTinh"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.thoiGianPhatMau"),
      width: 160,
      dataIndex: "thoiGianDuyet",
      key: "thoiGianDuyet",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhatMau",
      sort_key: "thoiGianDuyet",
      dataSort: dataSortColumnMau["thoiGianDuyet"] || "",
      onClickSort: onClickSort,
      selectSearch: true,
      renderSearch: (
        <DatePicker
          format="DD/MM/YYYY"
          placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
          onChange={(e) => {
            onSearch("thoiGianDuyet", e);
          }}
        />
      ),
      render: (value) =>
        value ? moment(value).format("DD/MM/YYYY HH:mm:ss") : null,
    }),
    NOITRU_COLUMNS.THOI_GIAN_DUNG({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.DUONG_DUNG(
      { onClickSort, dataSortColumn: dataSortColumnMau },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Select
                data={listAllDuongDung}
                placeholder={t("quanLyNoiTru.chonDuongDung")}
                onChange={onChange("duongDungId")}
                defaultValue={list?.duongDungId}
              />
            );
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnMau,
      value: khongBatBuocKhoa ? undefined : dataSearch?.khoaChiDinhId,
      isDislaySearch: checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN,
      ]),
    }),
    NOITRU_COLUMNS.BAC_SY_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.nguoiDuyetMau"),
      width: 180,
      dataIndex: "tenNguoiDuyet",
      key: "tenNguoiDuyet",
      i18Name: "quanLyNoiTru.dvNoiTru.nguoiDuyetMau",
      sort_key: "tenNguoiDuyet",
      dataSort: dataSortColumnMau["tenNguoiDuyet"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.nguoiPhatMau"),
      width: 180,
      dataIndex: "tenNguoiPhat1",
      key: "tenNguoiPhat1",
      i18Name: "quanLyNoiTru.dvNoiTru.nguoiPhatMau",
      sort_key: "tenNguoiPhat1",
      dataSort: dataSortColumnMau["tenNguoiPhat1"] || "",
      onClickSort: onClickSort,
    }),
    NOITRU_COLUMNS.NGAY_SAN_XUAT({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.HAN_SU_DUNG({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.GHI_CHU({ onClickSort, dataSortColumn: dataSortColumnMau }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.sotRet"),
      width: 100,
      dataIndex: "sotRet",
      key: "sotRet",
      i18Name: "quanLyNoiTru.dvNoiTru.sotRet",
      sort_key: "sotRet",
      dataSort: dataSortColumnMau["sotRet"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.giangMai"),
      width: 100,
      dataIndex: "giangMai",
      key: "giangMai",
      i18Name: "quanLyNoiTru.dvNoiTru.giangMai",
      sort_key: "giangMai",
      dataSort: dataSortColumnMau["giangMai"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: "HCV",
      width: 100,
      dataIndex: "hcv",
      key: "hcv",
      sort_key: "hcv",
      dataSort: dataSortColumnMau["hcv"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: "HBV",
      width: 100,
      dataIndex: "hbv",
      key: "hbv",
      sort_key: "hbv",
      dataSort: dataSortColumnMau["hbv"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: "HIV",
      width: 100,
      dataIndex: "hiv",
      key: "hiv",
      sort_key: "hiv",
      dataSort: dataSortColumnMau["hiv"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.ong1MoiTruongMuoi22oC"),
      width: 160,
      dataIndex: "muoi1",
      key: "muoi1",
      i18Name: "quanLyNoiTru.dvNoiTru.ong1MoiTruongMuoi22oC",
      sort_key: "muoi1",
      dataSort: dataSortColumnMau["muoi1"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.ong2MoiTruongMuoi22oC"),
      width: 160,
      dataIndex: "muoi2",
      key: "muoi2",
      i18Name: "quanLyNoiTru.dvNoiTru.ong2MoiTruongMuoi22oC",
      sort_key: "muoi2",
      dataSort: dataSortColumnMau["muoi2"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng1"),
      width: 160,
      dataIndex: "globulin1",
      key: "globulin1",
      i18Name: "quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng1",
      sort_key: "globulin1",
      dataSort: dataSortColumnMau["globulin1"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng2"),
      width: 160,
      dataIndex: "globulin2",
      key: "globulin2",
      i18Name: "quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng2",
      sort_key: "globulin2",
      dataSort: dataSortColumnMau["globulin2"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng1"),
      width: 160,
      dataIndex: "phanUngCheo1",
      key: "phanUngCheo1",
      i18Name: "quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng1",
      sort_key: "phanUngCheo1",
      dataSort: dataSortColumnMau["phanUngCheo1"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng2"),
      width: 160,
      dataIndex: "phanUngCheo2",
      key: "phanUngCheo2",
      i18Name: "quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng2",
      sort_key: "phanUngCheo2",
      dataSort: dataSortColumnMau["phanUngCheo2"] || "",
      onClickSort: onClickSort,
      render: (value) =>
        (listKetQuaXetNghiem || []).find((e) => e.id === value)?.ten,
    }),
    Column({
      title: t("khoMau.khoaSuDung"),
      width: 160,
      dataIndex: "tenKhoaThucHien",
      key: "tenKhoaThucHien",
      i18Name: "khoMau.khoaSuDung",
      sort_key: "tenKhoaThucHien",
      dataSort: dataSortColumnMau["tenKhoaThucHien"] || "",
      onClickSort: onClickSort,
      show: false,
    }),
    NOITRU_COLUMNS.MAN_HINH_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.DON_GIA_BH({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    NOITRU_COLUMNS.DON_GIA_KHONG_BH({
      onClickSort,
      dataSortColumn: dataSortColumnMau,
    }),
    Column({
      title: t("quanLyNoiTru.suatAn.soPhieuLinh"),
      width: 100,
      dataIndex: "soPhieu",
      key: "soPhieu",
      i18Name: "quanLyNoiTru.suatAn.soPhieuLinh",
      sort_key: "soPhieu",
      dataSort: dataSortColumnMau["soPhieu"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 120,
      align: "center",
      fixed: "right",
      ignore: true,
      hidden: isEdit,
      render: (data, item, index) => {
        const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien },
          listAllPhieuThuChotDot
        );
        return (
          <div className="flex">
            {isChotThoiGianDotDieuTri && (
              <>
                <Tooltip
                  title={t("quanLyNoiTru.chinhSuaDichVu")}
                  placement="bottom"
                >
                  <SVG.IcEdit
                    className="ic-action"
                    onClick={onEdit(item)}
                  ></SVG.IcEdit>
                </Tooltip>
                {!isReadonlyDvNoiTru && (
                  <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                    <SVG.IcDelete
                      className="ic-action"
                      onClick={() => onDelete(item)}
                    ></SVG.IcDelete>
                  </Tooltip>
                )}
              </>
            )}

            {item.trangThai === TRANG_THAI_MAU.DA_PHAT &&
              !isReadonlyDvNoiTru && (
                <Tooltip title={t("common.traMau")}>
                  <SVG.IcHoanDv
                    onClick={() => onHoanDv(item)}
                    className="ic-action"
                  />
                </Tooltip>
              )}
            {item.trangThai === TRANG_THAI_MAU.DA_LINH &&
              !isReadonlyDvNoiTru && (
                <AuthWrapper
                  accessRoles={[ROLES["KHO_MAU"].XAC_NHAN_TRUYEN_PHAT_MAU]}
                >
                  <Tooltip title={t("khoMau.xacNhanTruyenMau")}>
                    <SVG.IcTick
                      onClick={() => onXacNhanTruyenMau(item)}
                      className="ic-action"
                      color="var(--color-green-primary)"
                    />
                  </Tooltip>
                </AuthWrapper>
              )}
            {item.trangThai === TRANG_THAI_MAU.DA_TRUYEN &&
              !isReadonlyDvNoiTru && (
                <AuthWrapper
                  accessRoles={[ROLES["KHO_MAU"].XAC_NHAN_TRUYEN_PHAT_MAU]}
                >
                  <Tooltip title={t("khoMau.huyXacNhanTruyenMau")}>
                    <SVG.IcHuyHoanDv
                      onClick={() => onHuyTruyenMau(item)}
                      className="ic-action"
                    />
                  </Tooltip>
                </AuthWrapper>
              )}
          </div>
        );
      },
    }),
  ];

  const onRow = (record, index) => {
    return {
      onClick: () => {
        if (!isEdit) return;

        if (index !== state.currentIndex) {
          setState({
            currentItem: record,
            currentIndex: index,
          });
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newSearch = { ...dataSearch, page: page - 1 };

    onSearchByParams(newSearch);
  };

  const handleSizeChange = (size) => {
    const newSearch = { ...dataSearch, page: 0, size };

    onSearchByParams(newSearch);
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
      isCheckedAll: selectedRowKeys.length == mau.length,
    });

    updateData({
      listDeleteDv: data,
    });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked ? (mau || []).map((x) => x.id) : [],
      isCheckedAll: e.target?.checked,
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <Main noPadding={true}>
      <div className="table-content">
        <TableWrapper
          columns={columns}
          dataSource={state.dataSource || []}
          onRow={onRow}
          rowSelection={isEdit && rowSelection}
          rowKey={(record) => record.id}
          tableName="table_DVNOITRU_DSMau"
          ref={refSettings}
          columnResizable={true}
        />

        <Pagination
          onChange={onChangePage}
          current={dataSearch?.page + 1}
          pageSize={dataSearch?.size}
          listData={mau || []}
          total={dataSearch?.totalElements}
          onShowSizeChange={handleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
      </div>

      <ChinhSuaMau
        ref={refSuaThongTin}
        isReadonlyDvNoiTru={isReadonlyDvNoiTru}
      />
      <ModalYeuCauTraDV ref={refModalHoanDichVu} />
    </Main>
  );
};

export default forwardRef(DSMau);
