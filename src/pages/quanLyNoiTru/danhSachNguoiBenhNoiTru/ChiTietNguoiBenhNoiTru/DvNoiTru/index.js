import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { uniqBy } from "lodash";
import { useTranslation } from "react-i18next";

import { useStore, useThietLap } from "hooks";
import useLayer from "hooks/useLayer";
import useHotKey from "hooks/useHotKey";

import { Main } from "./styled";
import DSDichVu from "./DSDichVu";
import DSThuoc from "./DSThuoc";
import DSChePhamDD from "./DSChePhamDD";
import DSVacxin from "./DSVacxin";
import DSVatTu from "./DSVatTu";
import DSSuatAn from "./DSSuatAn";
import DSMau from "./DSMau";
import HoaChat from "./HoaChat";
import DVGiuong from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/DichVuGiuong";

import { Ta<PERSON>, Button } from "components";
import ModalChinhSua from "./ModalChinhSua";
import { SVG } from "assets";
import ModalDienBienChiSoXetNghiem from "../Modal/ModalDienBienChiSoXetNghiem";
import { checkBatBuocPhanPhongGiuong } from "utils/index";
import { THIET_LAP_CHUNG, LOAI_DICH_VU, HOTKEY } from "constants/index";
import ChiDinhDichVuVatTu from "pages/chiDinhDichVu/DichVuVatTu";

const DvNoiTru = ({
  khoaLamViec,
  isBatBuocPhanPhongGiuong,
  isReadonly,
  checkChongChiDinh,
}) => {
  const { t } = useTranslation();
  const refModalChinhSua = useRef(null);
  const refModalChiDinhVatTu = useRef(null);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const parentActiveKey = useStore("toDieuTri.activeKey", null);
  const refModalDienBienChiSoXetNghiem = useRef(null);
  const [dataPageSize] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [state, _setState] = useState({ activeKey: "1" });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [dataAN_KHOA_CHI_DINH_DV_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.AN_KHOA_CHI_DINH_DV_NOI_TRU
  );
  const [dataAN_CHI_DINH_VAT_TU_DV_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.AN_CHI_DINH_VAT_TU_DV_NOI_TRU
  );

  const layerId = useLayer();
  const refF4 = useRef(null);

  const {
    dvNoiTru: { onSearchDvVatTu },
    chiDinhKhamBenh: { updateConfigData },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
  } = useDispatch();

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru?.id && state.activeKey == "3") {
      let maBenhId =
        chiTietNguoiBenhNoiTru?.dsCdChinhId &&
          chiTietNguoiBenhNoiTru?.dsCdChinhId.length > 0
          ? chiTietNguoiBenhNoiTru?.dsCdChinhId[0]
          : null;

      updateConfigData({
        configData: {
          chiDinhTuDichVuId: chiTietNguoiBenhNoiTru.id,
          dsChiDinhTuDichVuId: chiTietNguoiBenhNoiTru.id,
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          nbThongTinId: chiTietNguoiBenhNoiTru.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.NOI_TRU,
          khoaChiDinhId: khoaLamViec?.id,
          thongTinNguoiBenh: chiTietNguoiBenhNoiTru,
          isPhauThuat: chiTietNguoiBenhNoiTru.phauThuat,
          trangThaiKham: "",
          phongThucHienId: chiTietNguoiBenhNoiTru.phongId,
          doiTuongKcb: chiTietNguoiBenhNoiTru.doiTuongKcb,
          maBenhId,
          isNoiTru: true,
          canLamSang: false,
        },
      });
    }
  }, [chiTietNguoiBenhNoiTru, state.activeKey]);

  useEffect(() => {
    return () => {
      updateConfigData({
        configData: {},
      });
    };
  }, []);

  const DV_NOITRU_COLUMNS = {};

  const onEdit = () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refModalChinhSua.current &&
        refModalChinhSua.current.show(state.activeKey);
  };

  const onShowDienBienChiSoXetNghiem = (e) => {
    refModalDienBienChiSoXetNghiem.current &&
      refModalDienBienChiSoXetNghiem.current.show({ dichVuId: e?.dichVuId });
  };

  refF4.current = () => onShowDienBienChiSoXetNghiem();
  useHotKey(
    layerId,
    [
      {
        keyCode: HOTKEY.F4,
        onEvent: () => {
          if (parentActiveKey === "3") {
            refF4.current && refF4.current();
          }
        },
      },
    ],
    [layerId, parentActiveKey]
  );

  const getListKhoTheoTaiKhoan = async (loaiDichVu) => {
    const listThietLapChonKho = await getListThietLapChonKhoTheoTaiKhoan({
      khoaNbId: chiTietNguoiBenhNoiTru?.khoaNbId,
      khoaChiDinhId: khoaLamViec?.id,
      doiTuong: chiTietNguoiBenhNoiTru?.doiTuong,
      loaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
      capCuu: chiTietNguoiBenhNoiTru?.capCuu,
      phongId: chiTietNguoiBenhNoiTru?.phongId,
      noiTru: true,
      canLamSang: false,
      loaiDichVu: loaiDichVu,
    });
    return listThietLapChonKho?.payload?.listThietLapChonKho || [];
  };

  const onChiDinhVatTu = async (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    if (checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong)) return;

    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.VAT_TU
    );

    const options = {
      dataKho: uniqBy(listThietLapChonKho || [], "id"),
      khoId:
        listThietLapChonKho?.length === 1 ? listThietLapChonKho[0]?.id : null,
    };
    const callbackFunc = () => {
      onSearchDvVatTu({
        page: 0,
        size: dataPageSize || 10,
        totalElements: 0,
        dsDoiTuongKcb: [2, 3, 4],
        nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
        dsTrangThaiHoan: [0, 10, 20],
        dsKhoaChiDinhId:
          khoaLamViec && !dataAN_KHOA_CHI_DINH_DV_NOI_TRU?.eval()
            ? [khoaLamViec.id]
            : [],
      });
    };
    refModalChiDinhVatTu.current &&
      refModalChiDinhVatTu.current.show(options, callbackFunc);
  };

  const operations = (
    <>
      {state.activeKey === "3" &&
        !isReadonly &&
        khoaLamViec?.id === chiTietNguoiBenhNoiTru?.khoaNbId &&
        !dataAN_CHI_DINH_VAT_TU_DV_NOI_TRU?.eval() && (
          <Button
            minWidth={"100px"}
            rightIcon={<SVG.IcAdd />}
            type="success"
            onClick={onChiDinhVatTu}
          >
            {t("common.themMoi")}
          </Button>
        )}
      <Button onClick={onShowDienBienChiSoXetNghiem}>
        {t("quanLyNoiTru.bieuDoBienDongChiSoXetNghiem")}
      </Button>

      <Button rightIcon={<SVG.IcEdit />} onClick={onEdit} type="success">
        {t("common.chinhSua")}
      </Button>
    </>
  );

  const onChangeTab = (activeKey) => {
    setState({ activeKey });
  };

  return (
    <Main>
      <Tabs
        className="noi-tru-tab"
        defaultActiveKey="1"
        activeKey={state.activeKey}
        onChange={onChangeTab}
        tabBarExtraContent={operations}
      >
        <Tabs.TabPane tab={t("quanLyNoiTru.dvNoiTru.dvkt")} key="1">
          <DSDichVu
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            onShowDienBienChiSoXetNghiem={onShowDienBienChiSoXetNghiem}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("common.thuoc")} key="2">
          <DSThuoc
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
            khongBatBuocKhoa={dataAN_KHOA_CHI_DINH_DV_NOI_TRU?.eval()}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("common.vatTu")} key="3">
          <DSVatTu
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
            khongBatBuocKhoa={dataAN_KHOA_CHI_DINH_DV_NOI_TRU?.eval()}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("common.suatAn")} key="4">
          <DSSuatAn
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
            khongBatBuocKhoa={dataAN_KHOA_CHI_DINH_DV_NOI_TRU?.eval()}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("quanLyNoiTru.dvNoiTru.mau")} key="5">
          <DSMau
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
            khongBatBuocKhoa={dataAN_KHOA_CHI_DINH_DV_NOI_TRU?.eval()}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("danhMuc.hoaChat")} key="6">
          <HoaChat
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
            khongBatBuocKhoa={dataAN_KHOA_CHI_DINH_DV_NOI_TRU?.eval()}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("quanLyNoiTru.dvNoiTru.dvGiuong")} key="7">
          <DVGiuong
            khoaLamViecId={khoaLamViec?.id}
            tableName="table_DVNOITRU_DVGiuong"
            nbDotDieuTriId={chiTietNguoiBenhNoiTru?.id}
            dataSize={dataPageSize}
            isSearchHeader={true}
            dataSort={{ tenKhoaChiDinh: 1, thoiGianThucHien: 2 }}
            columnResizable={true}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("quanLyNoiTru.dvNoiTru.vacxin")} key="8">
          <DSVacxin
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab={t("quanLyNoiTru.dvNoiTru.chePhamDD")} key="9">
          <DSChePhamDD
            DV_NOITRU_COLUMNS={DV_NOITRU_COLUMNS}
            khoaLamViecId={khoaLamViec?.id}
            isReadonlyDvNoiTru={isReadonly}
            isBatBuocPhanPhongGiuong={isBatBuocPhanPhongGiuong}
          />
        </Tabs.TabPane>
      </Tabs>

      <ModalChinhSua
        ref={refModalChinhSua}
        khoaLamViec={khoaLamViec}
        isReadonlyDvNoiTru={isReadonly}
      />
      <ModalDienBienChiSoXetNghiem ref={refModalDienBienChiSoXetNghiem} />
      <ChiDinhDichVuVatTu ref={refModalChiDinhVatTu} />
    </Main>
  );
};

export default DvNoiTru;
