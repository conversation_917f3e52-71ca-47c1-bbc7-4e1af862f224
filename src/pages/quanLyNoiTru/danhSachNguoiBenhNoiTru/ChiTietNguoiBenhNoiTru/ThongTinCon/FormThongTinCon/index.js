import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
  useMemo,
} from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, Form, message, Row, Tooltip } from "antd";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import {
  useConfirm,
  useListAll,
  useLoading,
  useStore,
  useThietLap,
  useQueryAll,
} from "hooks";
import { Button } from "components";
import { cleanMaThe } from "../utils/index";
import { SVG } from "assets";
import { isBoolean, isNumber, sleep } from "utils/index";
import { THIET_LAP_CHUNG } from "constants/index";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import FormThongTinConCustomized from "pages/application/TuyChinhGiaoDienPhamMem/NoiTru/SapXepTruongThongTinNoiTru/FormThongTinCon/FormThongTinCon";

import ModalTaoBenhAnCon from "../ModalTaoBenhAnCon";
import { query } from "redux-store/stores";

const parseBoolean = (value) => {
  if (value === true || value === false) return value;
  if (typeof value === "string") {
    if (value.trim().toLowerCase() === "true") return true;
    if (value.trim().toLowerCase() === "false") return false;
  }
  return undefined;
};

function FormThongTinCon(
  { dataForm, thongTinConIndex, loaiHoSoSinh, hinhThucSinhId, formThongTinMe },
  ref
) {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const [isDisabled, setIsDisabled] = useState(false);
  const { id } = useParams();
  const sinhPhauThuatForm = Form.useWatch("sinhPhauThuat", form);
  const sinhDuoi32TuanForm = Form.useWatch("sinhDuoi32Tuan", form);
  const diTatForm = Form.useWatch("diTat", form);
  const [listAllCachThucDe] = useListAll("cachThucDe", {}, false);

  const customizedFormRef = useRef(null);
  const refModalTaoBenhAnCon = useRef(null);

  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const [listAllBaoCao] = useListAll("baoCao", {}, true);
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const [MA_CACH_THUC_DE_MO] = useThietLap(THIET_LAP_CHUNG.MA_CACH_THUC_DE_MO);
  const [dataCHAN_DOAN_BENH_TAO_BENH_AN_CON] = useThietLap(
    THIET_LAP_CHUNG.CHAN_DOAN_BENH_TAO_BENH_AN_CON
  );
  const {
    nbThongTinSanPhu: { getById },
  } = useDispatch();

  const { data: listAllMaBenh } = useQueryAll(query.maBenh.queryAllMabenh);

  const idChanDoanBenh = useMemo(() => {
    if (
      dataCHAN_DOAN_BENH_TAO_BENH_AN_CON &&
      Array.isArray(listAllMaBenh) &&
      listAllMaBenh.length > 0
    ) {
      return listAllMaBenh.find((item) => {
        return item.ma === dataCHAN_DOAN_BENH_TAO_BENH_AN_CON;
      })?.id;
    }
    return null;
  }, [dataCHAN_DOAN_BENH_TAO_BENH_AN_CON, listAllMaBenh]);

  const setFormData = (data, thongTinCoBan) => {
    const listHinhThucSinhId = listAllCachThucDe
      ?.filter((x) => MA_CACH_THUC_DE_MO?.split(",")?.includes(x.ma))
      .map((item) => item.id);
    const _hinhThucSinhId = data?.hinhThucSinhId || hinhThucSinhId;
    let ngayThai = data?.ngayThai || null;
    let tuan = Math.floor(ngayThai / 7);
    tuan = tuan > 0 ? tuan : null;
    let ngay = ngayThai % 7;
    ngay = ngay > 0 ? ngay : null;
    let canNang = data?.canNang || null;
    canNang = isNumber(canNang) ? canNang * 1000 : canNang;
    let formData = {
      // Thông tin con
      maNb: data?.maNb || null,
      maHoSo: data?.maHoSo || null,
      maBenhAn: data?.maBenhAn || null,
      maThe: data?.maTheBhyt
        ? cleanMaThe(
            data?.maTheBhyt,
            `TE1${thongTinCoBan?.maTinhThanhPho || ""}`
          )
        : "",
      maTheBhyt: data?.maTheBhyt || "",
      hoTen: data?.hoTen?.toUpperCase() || null,
      ngaySinh: data?.ngaySinh ? moment(data?.ngaySinh) : null,
      gioiTinh: data?.gioiTinh || null,
      noiSinh: data?.noiSinh || null,
      soGiayChungSinh: data?.soGiayChungSinh || null,
      apgar1Phut: data?.apgar1Phut || null,
      apgar5Phut: data?.apgar5Phut || null,
      apgar10Phut: data?.apgar10Phut || null,
      canNang,
      chieuCao: data?.chieuCao || null,
      vongDau: data?.vongDau || null,
      ngayThai: ngayThai || null,
      ngay,
      tuan,
      tinhTrangThai: data?.tinhTrangThai || null,
      diTat: typeof data?.diTat === "boolean" ? String(data?.diTat) : null,
      diTatBamSinhId: data?.diTatBamSinhId || null,
      diTatBamSinhChiTiet: data?.diTatBamSinhChiTiet || null,
      conThu: data?.conThu || thongTinConIndex + 1 || null,
      tinhTrang:
        typeof data?.tinhTrang === "boolean" ? String(data?.tinhTrang) : null,
      sinhPhauThuat: listHinhThucSinhId?.includes(_hinhThucSinhId)
        ? true
        : data?.sinhPhauThuat || false,
      sinhDuoi32Tuan: data?.sinhDuoi32Tuan || false,
      ghiChu: data?.ghiChu || null,
      maSo: data?.maSo || null,
      nbDotDieuTriId: data?.nbDotDieuTriId || null,
      diUng: data?.diUng || null,
      benhKhac: data?.benhKhac ?? null,
    };

    let arr = [formData.maBenhAn, formData.maHoSo, formData.maNb];
    setIsDisabled(arr.every((i) => i !== null));
    form.setFieldsValue({ ...formData });
  };

  useEffect(() => {
    if (thongTinCoBan?.id) {
      setFormData(dataForm, thongTinCoBan);
    }
  }, [dataForm, thongTinCoBan, MA_CACH_THUC_DE_MO]);

  useImperativeHandle(ref, () => {
    return {
      submit: onHandleSubmit,
      setFieldsValue: (data = {}) => {
        form.setFieldsValue(data);
      },

      form: form,
    };
  });

  useEffect(() => {
    if (
      !sinhPhauThuatForm &&
      !sinhDuoi32TuanForm &&
      form.getFieldError("ghiChu").length
    ) {
      form.setFields([{ name: "ghiChu", errors: [] }]);
    }

    if (
      !parseBoolean(diTatForm) &&
      form.getFieldError("diTatBamSinhId").length
    ) {
      form.setFields([{ name: "diTatBamSinhId", errors: [] }]);
    }
  }, [sinhPhauThuatForm, sinhDuoi32TuanForm, diTatForm, form]);

  const onFormValuesChange = (values, allValue) => {
    let ghiChu = allValue.ghiChu;
    let hoTen = allValue.hoTen;
    if (isBoolean(values.sinhPhauThuat) || isBoolean(values.sinhDuoi32Tuan)) {
      const textPhauThuat = "Sinh con phải phẫu thuật";
      const text32TuanTuoi = "Sinh con dưới 32 tuần tuổi";
      const textPhauThuat32Tuan = "Phẫu thuật, sinh con dưới 32 tuần tuổi";

      const normalizedGhiChu = (ghiChu || "").toLowerCase();
      const contains = (text) => normalizedGhiChu.includes(text.toLowerCase());
      const isEqual = (text) => normalizedGhiChu === text.toLowerCase();

      if (allValue.sinhPhauThuat && !allValue.sinhDuoi32Tuan) {
        if (
          !ghiChu ||
          isEqual(textPhauThuat32Tuan) ||
          isEqual(text32TuanTuoi)
        ) {
          ghiChu = textPhauThuat;
        } else if (!contains(textPhauThuat)) {
          ghiChu += ` ${textPhauThuat}`;
        }
      } else if (allValue.sinhDuoi32Tuan && !allValue.sinhPhauThuat) {
        if (!ghiChu || isEqual(textPhauThuat32Tuan) || isEqual(textPhauThuat)) {
          ghiChu = text32TuanTuoi;
        } else if (!contains(text32TuanTuoi)) {
          ghiChu += ` ${text32TuanTuoi}`;
        }
      } else if (allValue.sinhPhauThuat && allValue.sinhDuoi32Tuan) {
        if (isEqual(textPhauThuat) || isEqual(text32TuanTuoi)) {
          ghiChu = textPhauThuat32Tuan;
        } else if (!contains(textPhauThuat32Tuan)) {
          ghiChu += ` ${textPhauThuat32Tuan}`;
        }
      }
      // Update ghiChu in main form
      form.setFieldValue("ghiChu", ghiChu);
      if (ghiChu && form.getFieldError("ghiChu").length) {
        form.setFields([{ name: "ghiChu", errors: [] }]);
      }
    }
    if (hoTen !== undefined) {
      const upperValue = (hoTen || "").toUpperCase();
      form.setFieldValue("hoTen", upperValue);
    }
  };

  const onHandleSubmit = async () => {
    const values = await form.validateFields();

    const { ngay, tuan, canNang, ..._values } = values || {};
    const { active, createdAt, createdBy, updatedBy, updatedAt, id, ..._data } =
      dataForm || {};
    let ngayThai = parseInt(ngay || 0) + (parseInt(tuan || 0) * 7 || 0) || null;
    let payload = {
      ..._data,
      ..._values,
      ngaySinh: values.ngaySinh
        ? moment(values.ngaySinh).format("YYYY-MM-DD HH:mm:ss")
        : null,
      ngayThai,
      canNang: canNang ? canNang / 1000 : null,
      maThe: values.maThe
        ? `TE1${thongTinCoBan?.maTinhThanhPho || ""}${values.maThe}`
        : null,
      hinhThucSinhId: hinhThucSinhId,
    };
    return payload;
  };

  const onTaoGiayChungSinh = (e) => {
    e.stopPropagation();
    const _baoCaoItem = listAllBaoCao.find((item) => item.ma === "EMR_BA256");

    window.open(
      `/editor/bao-cao/EMR_BA256/${thongTinCoBan?.id}?nbDotDieuTriId=${thongTinCoBan?.id}&conThu=${dataForm?.conThu}&baoCaoId=${_baoCaoItem?.id}`
    );
  };

  const onCapNhatThoiGian = (e) => {
    e.stopPropagation();
    showConfirm(
      {
        title: t("common.canhBao"),
        content: t(
          "quanLyNoiTru.thongTinCon.capNhatThoiGianLapBenhAnVaThoiGianNhapVienTheoThoiGianSinh"
        ),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        try {
          showLoading();
          const thoiGian = moment(dataForm.ngaySinh).format(
            "YYYY-MM-DD HH:mm:ss"
          );

          await nbDotDieuTriProvider.chinhSuaThoiGianVaoKhoaNhapVien({
            nbDotDieuTriId: dataForm?.nbDotDieuTriId,
            thoiGianVaoKhoaNhapVien: thoiGian,
          });

          await sleep(300);
          message.success(t("common.capNhatThanhCong"));
          getById(id);
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onClickTaoBACon = async (e) => {
    e.stopPropagation();
    try {
      await form.validateFields();
      refModalTaoBenhAnCon.current &&
        refModalTaoBenhAnCon.current.show({ ...dataForm, idChanDoanBenh });
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div style={{ width: "100%" }}>
      <FormThongTinConCustomized
        ref={customizedFormRef}
        fromSetting={false}
        thongTinCon={dataForm}
        form={form}
        activeSections={["thongTinCon"]}
        loaiHoSoSinh={loaiHoSoSinh}
        formThongTinMe={formThongTinMe}
        isReadonly={false}
        onValuesChange={onFormValuesChange}
        thongTinConIndex={thongTinConIndex}
        extraHeaderRight={
          <>
            {dataForm?.id && (
              <div style={{ display: "flex", alignItems: "center" }}>
                {!isDisabled ? (
                  <Button
                    onClick={onClickTaoBACon}
                    type="primary"
                    rightIcon={
                      <SVG.IcSuccess color={"var(--color-blue-primary)"} />
                    }
                  >
                    {t("quanLyNoiTru.thongTinCon.taoBenhAnCon")}
                  </Button>
                ) : !dataForm?.soGiayChungSinh ? (
                  <Button
                    onClick={onTaoGiayChungSinh}
                    type="primary"
                    disabled={false}
                    className="btn-tao-giay-chung-sinh"
                  >
                    {t("quanLyNoiTru.thongTinCon.taoGiayChungSinh")}
                  </Button>
                ) : null}
                {dataForm?.maBenhAn && (
                  <Tooltip
                    title={t(
                      "quanLyNoiTru.thongTinCon.capNhatThoiGianLapBenhAnVaThoiGianNhapVienTheoThoiGianSinh"
                    )}
                    placement="topLeft"
                  >
                    <SVG.IcTime
                      width={28}
                      height={28}
                      className="cursor-pointer"
                      color={"var(--color-blue-primary)"}
                      onClick={onCapNhatThoiGian}
                    />
                  </Tooltip>
                )}
              </div>
            )}
          </>
        }
      />

      <ModalTaoBenhAnCon ref={refModalTaoBenhAnCon} />
    </div>
  );
}

export default forwardRef(FormThongTinCon);
