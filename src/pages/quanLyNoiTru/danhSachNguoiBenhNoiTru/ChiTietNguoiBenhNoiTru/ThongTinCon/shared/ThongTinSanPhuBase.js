import React, {
  forwardRef,
  memo,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
} from "react";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import moment from "moment";
import styled from "styled-components";
import { merge, omit } from "lodash";
import { Form, Spin } from "antd";
import FormThongTinSanPhuLayout from "pages/application/TuyChinhGiaoDienPhamMem/NoiTru/SapXepTruongThongTinNoiTru/FormThongTinSanPhu/FormThongTinSanPhu";
import FormThongTinCon from "../FormThongTinCon";
import { useStore, useThietLap, useLoading, useCache } from "hooks";
import { Card } from "components";
import { isArray, isNumber, fillEmptyData } from "utils/index";
import { CACHE_KEY, DS_TINH_CHAT_KHOA, THIET_LAP_CHUNG } from "constants/index";
import { LOAI_HO_SO_SINH } from "../utils";

const ThongTinSanPhuContainer = styled.div`
  flex: 1;
  overflow: hidden;

  fieldset {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }

  .ant-spin-container {
    height: 100%;
  }

  .mn-card {
    height: 100%;
    overflow: auto;
  }

  ul > li {
    list-style-type: disc;
    margin-left: 20px;
  }

  .ant-form-item {
    margin-bottom: 0;
  }

  .ant-collapse {
    margin-top: 0px;
    .ant-collapse-item {
      .ant-collapse-header {
        padding: 5px !important;
        padding-left: 10px !important;
        font-weight: bold;
        background-color: #e6effe;
        .ant-collapse-expand-icon {
          /* color: #0762f7; */
        }
      }
      .ant-collapse-content-box {
        padding: 10px;
      }
    }
  }

  .select-box-chan-doan {
    .ant-select-arrow {
      right: 5px;
      margin-top: -12px;
    }
  }
  .custom-label .ant-form-item-label label {
    color: rgba(0, 0, 0, 0.5);
  }
  .ant-checkbox-wrapper {
    line-height: 32px;
  }
  .ant-input[disabled] {
    background-color: unset;
    color: rgba(0, 0, 0, 0.7) !important;
  }
  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector {
    color: rgba(0, 0, 0, 0.7);
  }
  .ant-select-status-error.ant-select:not(.ant-select-disabled):not(
      .ant-select-customize-input
    ):not(.ant-pagination-size-changer)
    .ant-select-selector {
    background-color: unset !important;
    border: 1px solid #ff4d4f !important;
  }
  .ant-input-status-error:not(.ant-input-disabled):not(
      .ant-input-borderless
    ).ant-input {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 6 6"><circle cx="3" cy="3" r="0.4" fill="black" /></svg>')
      20px;
    background-position-y: 7px;
    background-size: 5px 25px;
  }
  .select-box-chan-doan .ant-select-clear {
    margin-top: -12px !important;
    right: 5px;
  }
`;

const tinhTienThai = (data) => {
  const {
    soLanSinhConDuThang,
    soLanSinhConThieuThang,
    soLanSayThai,
    soConConSong,
  } = data;
  return [
    soLanSinhConDuThang,
    soLanSinhConThieuThang,
    soLanSayThai,
    soConConSong,
  ]
    .map((item) => (isNumber(item) ? String(item) : ""))
    .join("");
};

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const ThongTinSanPhuBase = forwardRef(
  (
    {
      nbDotDieuTriId,
      thongTinSanPhu,
      isReadonly = false,
      isLoading = false,
      activeSections = ["thongTinSanPhu", "tinhTrangSucKhoeMe"],
      onBack,
      onSaveSuccess,
      fromSetting = false,
    },
    ref
  ) => {
    const history = useHistory();
    const { showLoading, hideLoading } = useLoading();

    const nbNguoiBaoLanh = useStore(
      "nbDotDieuTri.thongTinBenhNhan.nbNguoiBaoLanh",
      {}
    );
    const authId = useStore("auth.auth.id");
    const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
    const [khoaLamViec, _, loadFinishKhoaLamViec] = useCache(
      authId + DS_TINH_CHAT_KHOA.NOI_TRU,
      CACHE_KEY.DATA_KHOA_LAM_VIEC,
      "",
      false,
      false
    );

    const [form] = Form.useForm();
    const _soConSinhLanNay = Form.useWatch("soConSinhLanNay", form);
    const loaiHoSoSinh = Form.useWatch("loaiHoSoSinh", form);
    const hinhThucSinhId = Form.useWatch("hinhThucSinhId", form);

    useImperativeHandle(ref, () => ({
      form,
      onCreateOrEdit,
    }));

    const refFormThongTinCon = useRef({});

    const [dataMOI_QUAN_HE_CHONG, loadFinish] = useThietLap(
      THIET_LAP_CHUNG.MOI_QUAN_HE_CHONG
    );

    const {
      nbThongTinSanPhu: { patchNbThongTinSanPhu, getById },
      nbDotDieuTri: { getById: getThongTinBenhNhanTongHop },
    } = useDispatch();

    const soConSinhLanNay = Math.max(parseInt(_soConSinhLanNay) || 0, 0);

    const isDisabledSoConSinh = useMemo(() => {
      if (isArray(thongTinSanPhu?.dsThongTinCon, true)) {
        return thongTinSanPhu.dsThongTinCon.some((item) => {
          return (
            item.maBenhAn !== null && item.maHoSo !== null && item.maNb !== null
          );
        });
      }
      return false;
    }, [thongTinSanPhu?.dsThongTinCon]);

    // Form data setup
    const setFormData = (
      data,
      dataMOI_QUAN_HE_CHONG,
      nbNguoiBaoLanh,
      khoaLamViec
    ) => {
      let maQuanHeCha = dataMOI_QUAN_HE_CHONG?.toUpperCase();
      let tenCha = null;

      if (data?.tenCha) {
        tenCha = data.tenCha;
      } else if (nbNguoiBaoLanh?.moiQuanHe?.ma === maQuanHeCha) {
        tenCha = nbNguoiBaoLanh?.hoTen;
      } else if (nbNguoiBaoLanh?.moiQuanHe2?.ma === maQuanHeCha) {
        tenCha = nbNguoiBaoLanh?.hoTen2;
      }

      const hinhThucSinhId = data?.dsThongTinCon?.[0]?.hinhThucSinhId;
      let formData = {
        // Thông tin sản phụ
        tenCha: tenCha ? tenCha.toUpperCase() : null,
        hinhThucSinhId: hinhThucSinhId || null,
        lanSinh: data?.lanSinh || null,
        ngoiThaiId: data?.ngoiThaiId || null,
        soConSinhLanNay: data?.id
          ? data?.dsThongTinCon?.length ?? null
          : data?.soConSinhLanNay ?? null,
        nguoiDo1Id: data?.nguoiDo1Id || null,
        nguoiDo2Id: data?.nguoiDo2Id || null,
        soMauMat: data?.soMauMat || null,
        bienPhapCamMauId: data?.bienPhapCamMauId || null,
        bacSiPhauThuatId: data?.bacSiPhauThuatId || null,
        soLanSinhConDuThang: data?.soLanSinhConDuThang || 0,
        soLanSinhConThieuThang: data?.soLanSinhConThieuThang || 0,
        soLanSayThai: data?.soLanSayThai || 0,
        soConConSong: data?.soConConSong || 0,
        tinhTrangCon: data?.tinhTrangCon || null,
        ketQua: data?.ketQua || null,
        corticoid:
          typeof data?.corticoid === "boolean" ? String(data?.corticoid) : null,
        mgSO4: typeof data?.mgSO4 === "boolean" ? String(data?.mgSO4) : null,
        loaiHoSoSinh: data?.loaiHoSoSinh || 10, //mặc định loại thường
        diUng: data?.diUng || null,
        thoiGianVoOi: data?.thoiGianVoOi ? moment(data?.thoiGianVoOi) : null,
        mauSacOi: data?.mauSacOi ?? [],
        mauSacOiKhac: data?.mauSacOiKhac ?? null,
        luongOi: data?.luongOi ?? null,
        khoaSinhId: data?.id ? data?.khoaSinhId ?? null : khoaLamViec?.id,

        // Tình trạng sức khoẻ mẹ
        tpha: data?.tpha || false,
        viemGanB: data?.viemGanB || false,
        lienCauKhuanBgbs: data?.lienCauKhuanBgbs || false,
        hiv: data?.hiv || false,
        hbeAg: data?.hbeAg || false,
        hbsAg: data?.hbsAg || false,
        tieuDuongThaiKy: data?.tieuDuongThaiKy || false,
        tienSanGiat: data?.tienSanGiat || false,
        rauTienDao: data?.rauTienDao || false,
        rauBongNon: data?.rauBongNon || false,
        tangHuyetAp: data?.tinhTrangSucKhoe?.includes(10) || false,
        benhLyTuyenGiap: data?.tinhTrangSucKhoe?.includes(20) || false,
        sotTruocSinh: data?.tinhTrangSucKhoe?.includes(30) || false,
        viemAmDao: data?.tinhTrangSucKhoe?.includes(40) || false,
        nhiemTrungTietNieu: data?.tinhTrangSucKhoe?.includes(50) || false,
      };

      formData = merge(formData, {
        tienThai: tinhTienThai(formData),
      });
      form.setFieldsValue({ ...formData });
    };

    useEffect(() => {
      if (loadFinish && loadFinishKhoaLamViec) {
        setFormData(
          thongTinSanPhu,
          dataMOI_QUAN_HE_CHONG,
          nbNguoiBaoLanh,
          khoaLamViec
        );
      }
    }, [
      thongTinSanPhu,
      loadFinish,
      dataMOI_QUAN_HE_CHONG,
      nbNguoiBaoLanh,
      khoaLamViec,
      loadFinishKhoaLamViec,
    ]);

    useEffect(() => {
      if (nbDotDieuTriId) {
        getThongTinBenhNhanTongHop(nbDotDieuTriId);
      }
    }, [nbDotDieuTriId]);

    // Form change handlers
    const handleValuesChange = (values, allValues) => {
      if (
        [
          "soLanSinhConDuThang",
          "soLanSinhConThieuThang",
          "soLanSayThai",
          "soConConSong",
        ].some((i) => Object.keys(values).includes(i))
      ) {
        let tienThai = tinhTienThai({
          ...allValues,
          ...values,
        });
        form.setFieldsValue({ tienThai });
      }
      if (values.hasOwnProperty("tienThai")) {
        let tienThai = values.tienThai;
        const soLanSinhConDuThang = tienThai.substring(0, 1) || "0";
        const soLanSinhConThieuThang = tienThai.substring(1, 2) || "0";
        const soLanSayThai = tienThai.substring(2, 3) || "0";
        const soConConSong = tienThai.substring(3, 4) || "0";
        form.setFieldsValue({
          soLanSinhConDuThang: +soLanSinhConDuThang,
          soLanSinhConThieuThang: +soLanSinhConThieuThang,
          soLanSayThai: +soLanSayThai,
          soConConSong: +soConConSong,
        });
      }
      if (values.hasOwnProperty("tenCha")) {
        form.setFieldsValue({
          tenCha: values.tenCha?.toUpperCase(),
        });
      }
      if (values.loaiHoSoSinh == LOAI_HO_SO_SINH.THAI_LUU) {
        for (let i = 0; i < soConSinhLanNay; i++) {
          refFormThongTinCon.current[i].current.setFieldsValue({
            tinhTrang: "false",
          });
        }
      }
    };

    const onCreateOrEdit = async () => {
      try {
        showLoading();
        let values = await form.validateFields();
        let dsThongTinCon = [];

        const {
          tangHuyetAp,
          benhLyTuyenGiap,
          sotTruocSinh,
          viemAmDao,
          nhiemTrungTietNieu,
        } = values;

        const thongTinSanPhuEdit = omit(values, [
          "tangHuyetAp",
          "benhLyTuyenGiap",
          "sotTruocSinh",
          "viemAmDao",
          "nhiemTrungTietNieu",
        ]);

        for (let i = 0; i < soConSinhLanNay; i++) {
          let val = await refFormThongTinCon.current[i].current.submit();
          dsThongTinCon.push(val);
        }

        const tinhTrangSucKhoe = [];
        if (tangHuyetAp) tinhTrangSucKhoe.push(10);
        if (benhLyTuyenGiap) tinhTrangSucKhoe.push(20);
        if (sotTruocSinh) tinhTrangSucKhoe.push(30);
        if (viemAmDao) tinhTrangSucKhoe.push(40);
        if (nhiemTrungTietNieu) tinhTrangSucKhoe.push(50);

        const payload = {
          id: thongTinSanPhu?.id,
          ...thongTinSanPhuEdit,
          tinhTrangSucKhoe,
          nbDotDieuTriId,
          dsThongTinCon,
          thoiGianVoOi: values.thoiGianVoOi
            ? moment(values.thoiGianVoOi).format("YYYY-MM-DD HH:mm:ss")
            : null,
        };

        // fill empty data = null để update về null khi xoá dữ liệu
        const cleanedPayload = fillEmptyData(payload);
        const res = await patchNbThongTinSanPhu(cleanedPayload);

        if (cleanedPayload?.id) {
          await sleep(300);
          getById(cleanedPayload.id);
        } else if (res?.id && onSaveSuccess) {
          onSaveSuccess(res);
        } else if (res?.id) {
          history.push(
            `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/thong-tin-con/chi-tiet/${res.id}`
          );
        }
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    };

    return (
      <ThongTinSanPhuContainer>
        <fieldset disabled={isReadonly}>
          <Spin spinning={isLoading || !thongTinCoBan?.id}>
            <Card className="content">
              <FormThongTinSanPhuLayout
                fromSetting={fromSetting}
                thongTinSanPhu={thongTinSanPhu}
                onValuesChange={handleValuesChange}
                activeSections={activeSections}
                parentState={{
                  isDisabledSoConSinh,
                }}
                form={form}
              />
              {soConSinhLanNay > 0 && (
                <>
                  {[...Array(parseInt(soConSinhLanNay))].map((_, idx) => {
                    if (!refFormThongTinCon.current?.[idx]) {
                      refFormThongTinCon.current[idx] = React.createRef();
                    }
                    let dataForm = (thongTinSanPhu?.dsThongTinCon || []).sort(
                      (a, b) => a.conThu - b.conThu
                    );
                    return (
                      <FormThongTinCon
                        dataForm={dataForm?.[idx]}
                        key={idx}
                        ref={refFormThongTinCon.current[idx]}
                        thongTinConIndex={idx}
                        loaiHoSoSinh={loaiHoSoSinh}
                        hinhThucSinhId={hinhThucSinhId}
                        formThongTinMe={form}
                      />
                    );
                  })}
                </>
              )}
            </Card>
          </Spin>
        </fieldset>
      </ThongTinSanPhuContainer>
    );
  }
);

export default memo(ThongTinSanPhuBase);
