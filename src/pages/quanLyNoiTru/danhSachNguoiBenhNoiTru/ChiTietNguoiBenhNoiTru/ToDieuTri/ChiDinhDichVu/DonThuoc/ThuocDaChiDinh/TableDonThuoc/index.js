import React, { useState, useRef, useEffect, useMemo } from "react";
import { Tooltip, Checkbox, TableWrapper, HeaderSearch } from "components";
import { useDispatch } from "react-redux";
import SuaThongTinThuoc from "pages/khamBenh/DonThuoc/SuaThongTinThuoc";
import { useTranslation } from "react-i18next";
import { useConfirm, useEnum, useLoading, useStore } from "hooks";
import moment from "moment";
import {
  DOI_TUONG,
  ENUM,
  LOAI_CHI_DINH,
  LOAI_DICH_VU,
  LOAI_DON_THUOC,
  ROLES,
  TRANG_THAI_HOAN,
  TRANG_THAI_THUOC,
} from "constants/index";
import { CaretRightOutlined, CaretDownOutlined } from "@ant-design/icons";
import { groupBy, sumBy, union } from "lodash";
import { SVG } from "assets";
import { isArray, roundNumberPoint } from "utils/index";
import IcXemHdsd from "pages/khamBenh/components/Thuoc/IcXemHdsd";
import { Main } from "./styled";
import useThuocDaKe from "pages/khamBenh/components/TableDonThuoc/useThuocDaKe";
import useManHinh from "pages/khamBenh/components/TableDonThuoc/useManHinh";
import { checkRole } from "lib-utils/role-utils";
import ModalNhapSoLuong from "pages/khamBenh/DonThuoc/ModalNhapSoLuong";
import { buildPayloadFromDsThuocGop } from "utils/chi-dinh-thuoc-utils";

const { Setting } = TableWrapper;

const TableDonThuoc = ({
  onThemThuocDungKem,
  onChuyenThuoc,
  listDvThuoc,
  isReadonly,
  isTuTruc,
  khoBhyt,
  expandedKeys,
  isShowChuyenThuoc,
  isManHinhPhauThuat,
  thuocNhaThuoc,
  isShowSoLuongDinhMuc = false,
  listThuocDungKem,
  ...props
}) => {
  const { showConfirm } = useConfirm();
  const { listDvThuoc: listDichVuThuoc } = useThuocDaKe({
    visible: true,
  });
  const {
    chiDinhDichVuKho: {
      onDeleteDichVu,
      getListDichVuThuoc,
      onDeleteDichVuNhaThuoc,
      getListThuocNhaThuoc,
    },
    traHangHoa: { postDsDvThuocTraKho },
  } = useDispatch();
  const refSettings = useRef(null);
  const refModalNhapSoLuong = useRef(null);
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [listtrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);

  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const refSuaThongTinThuoc = useRef(null);

  useEffect(() => {
    if (expandedKeys) {
      setState({ expandedKeys: union(expandedKeys, state.expandedKeys) });
    }
  }, [expandedKeys]);

  const dataSource = useMemo(() => {
    let _listDvThuoc = listDvThuoc.sort((a, b) => {
      return a.dungChoCon === b.dungChoCon ? 0 : a.dungChoCon ? 1 : -1;
    });
    const listThuocCha = _listDvThuoc.filter((x) => !x.dungKemId);

    const _listThuocDungKem = isArray(listThuocDungKem, true)
      ? listThuocDungKem
      : listDvThuoc.filter((x) => x.dungKemId);

    let data = [];
    let thuocChaIdCoDungKem = [];
    (listThuocCha || []).map((item, index) => {
      let thuocDungKem = Object.entries(
        groupBy(
          (_listThuocDungKem || []).filter(
            (x) =>
              x.dungKemId === item.id ||
              (item.dsThuocGopId || []).includes(x.dungKemId)
          ),
          "chiDinhId"
        )
      ).map(([chiDinhId, items]) => {
        return {
          ...items[0],
          soLuong: sumBy(items, "soLuong"),
          soLuongHuy: sumBy(items, "soLuongHuy"),
          soLuongYeuCau: sumBy(items, "soLuongYeuCau"),
          soLuongTra: sumBy(items, "soLuongTra"),
          soLuongSoCap: sumBy(items, "soLuongSoCap"),
        };
      });
      data.push({
        ...item,
        children: thuocDungKem,
        index: index + 1,
      });
      if (thuocDungKem.length > 0) {
        thuocChaIdCoDungKem.push(item.id);
      }
    });

    setState({ expandedKeys: thuocChaIdCoDungKem });
    return data;
  }, [listDvThuoc, listThuocDungKem]);

  const listDsPhaChung = useMemo(() => {
    return [
      ...listDvThuoc.map((item) => ({
        ...item,
        ten: `${item.tenDichVu} - ${item.soLuong} ${
          item.tenDvtSuDung || ""
        } - ${item.cachDung || ""}`,
      })),
      ...listDichVuThuoc.map((item) => ({
        ...item,
        ten: `${item.tenDichVu} - ${item.soLuong} ${
          item.tenDvtSuDung || ""
        } - ${item.cachDung || ""}`,
      })),
    ]?.filter((x) => !x.dungKemId);
  }, [listDvThuoc, listDichVuThuoc]);

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content:
          t("common.banCoChacMuonXoa") +
          (record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "") +
          "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        try {
          showLoading();

          if (thuocNhaThuoc) {
            await onDeleteDichVuNhaThuoc(record.id);
          } else {
            await onDeleteDichVu(record.id);
          }

          refreshData();
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };
  const refreshData = () => {
    if (thuocNhaThuoc) {
      getListThuocNhaThuoc({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20],
      });
    } else {
      getListDichVuThuoc({
        nbDotDieuTriId: configData.nbDotDieuTriId,
        chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
        dsTrangThaiHoan: [0, 10, 20, 42],
      });
    }
  };
  const onEdit = (record) => () => {
    refSuaThongTinThuoc.current &&
      refSuaThongTinThuoc.current.show(
        {
          data: record,
          chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          nbDotDieuTriId: configData.nbDotDieuTriId,
          chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
          isReadonly: isReadonly,
          isTuTruc,
          khoBhyt,
          thuocNhaThuoc: thuocNhaThuoc,
        },
        () => {
          refreshData();
        }
      );
  };

  const onCreate = (record) => {
    onThemThuocDungKem &&
      onThemThuocDungKem({
        loaiDonThuoc: record.nhaThuoc
          ? LOAI_DON_THUOC.NHA_THUOC
          : record.khoId
          ? LOAI_DON_THUOC.THUOC_KHO
          : "",
        khoId: record.khoId,
        dungKemId: record.id,
        loaiChiDinh: record.loaiChiDinh,
        isDisabledThemDungKem: true,
      });
  };

  const onNgungYLenh = (record) => async (e) => {
    if (record.trangThai < TRANG_THAI_THUOC.DA_PHAT.id) {
      showConfirm(
        {
          title: t("common.xacNhan"),
          content: t("khamBenh.xacNhanNgungYLenh"),
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          let payload = [
            {
              nbDichVuId: record.id,
              soLuong: record.soLuongYeuCau,
            },
          ];
          if (isArray(record.dsThuocGop, 2)) {
            payload = record.dsThuocGop.map((item) => ({
              nbDichVuId: item.id,
              soLuong: item.soLuongYeuCau,
            }));
          }
          await postDsDvThuocTraKho({
            loaiHangHoa: LOAI_DICH_VU.THUOC,
            message: t("khamBenh.ngungYLenhThanhCong"),
            payload: payload,
          });
          refreshData();
        }
      );
    } else {
      refModalNhapSoLuong.current &&
        refModalNhapSoLuong.current.show(
          {
            message: t("khamBenh.soLuongNgungThuCap"),
            title: t("khamBenh.ngungYLenh"),
          },
          async (value) => {
            let payload = [
              {
                nbDichVuId: record.id,
                soLuong: value,
              },
            ];
            if (isArray(record.dsThuocGop, 2)) {
              payload = buildPayloadFromDsThuocGop(record, value);
            }

            if (!isArray(payload, 1)) {
              return;
            }

            await postDsDvThuocTraKho({
              loaiHangHoa: LOAI_DICH_VU.THUOC,
              message: t("khamBenh.ngungYLenhThanhCong"),
              payload: payload,
            });
            refreshData();
          }
        );
    }
  };

  const renderStt = (value, row, index) => {
    const obj = {
      children: value,
      props: {},
    };
    if (row.isParent) {
      obj.props.rowSpan = row.rowSpan;
      obj.children = value;
    }
    return obj;
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} sort_key="index" />,
      width: 64,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: renderStt,
    },
    {
      title: <HeaderSearch title={t("danhMuc.maThuoc")} />,
      width: 80,
      dataIndex: "maDichVu",
      key: "maDichVu",
      show: true,
      i18Name: "danhMuc.maThuoc",
      render: (item, record) => {
        return (
          <span
            className="flex align-items-center gap-4"
            style={{ flexWrap: "wrap" }}
          >
            <IcXemHdsd record={record} />
            <span style={{ overflow: "hidden" }}>{item}</span>
          </span>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.tenThuocHamLuong")}
          sort_key="ten"
        />
      ),
      width: 220,
      key: "tenDichVu",
      colSpan: 1,
      i18Name: "khamBenh.donThuoc.tenThuocHamLuong",
      className: "tenDichVu",
      show: true,
      render: (item, record) => {
        const ten =
          record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "";
        const tenLieuDung = `${
          record?.tenLieuDung || record?.lieuDung?.ten || ""
        }`;
        const tenDuongDung = `${
          record?.tenDuongDung ? " - " + record?.tenDuongDung : ""
        }`;
        const tenCachDung = `${
          record?.cachDung ? " - " + record?.cachDung : ""
        }`;
        const content1 = `${tenLieuDung}${tenDuongDung}${tenCachDung}${
          tenLieuDung || tenDuongDung || tenCachDung ? `. ` : ""
        }`;
        return (
          <span>
            <span>{`${ten} ${
              record.tenHoatChat ? " (" + record.tenHoatChat + ")" : " "
            } ${record.hamLuong ? " - " + record.hamLuong : ""}`}</span>
            <br />
            <span style={{ fontSize: "12px" }}>
              {`${content1} `}
              {record.ghiChu ? `Lưu ý: ${record.ghiChu}` : ""}
            </span>
          </span>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.slChiDinh")}
          sort_key={thuocNhaThuoc ? "soLuong" : "soLuongYeuCau"}
        />
      ),
      width: 80,
      dataIndex: thuocNhaThuoc ? "soLuong" : "soLuongYeuCau",
      key: thuocNhaThuoc ? "soLuong" : "soLuongYeuCau",
      colSpan: 1,
      i18Name: "khamBenh.donThuoc.slChiDinh",
      show: true,
      render: (item, list) => {
        const tenDvt = list?.tenDonViTinh || "";
        return roundNumberPoint(item, 3) + ` ${tenDvt}`;
      },
    },
    ...(isShowSoLuongDinhMuc
      ? [
          {
            title: (
              <HeaderSearch
                title={t("khamBenh.donThuoc.slDinhMuc")}
                sort_key="soLuongDinhMuc"
              />
            ),
            width: 80,
            dataIndex: "soLuongDinhMuc",
            key: "soLuongDinhMuc",
            colSpan: 1,
            align: "center",
            i18Name: "khamBenh.donThuoc.slDinhMuc",
            show: true,
          },
        ]
      : []),
    ...(!thuocNhaThuoc
      ? [
          {
            title: (
              <HeaderSearch
                title={t("khamBenh.donThuoc.slTra")}
                sort_key="soLuongTra"
              />
            ),
            width: 80,
            dataIndex: "soLuongTra",
            key: "soLuongTra",
            colSpan: 1,
            i18Name: "khamBenh.donThuoc.slTra",
            show: true,
            render: (item, list) => {
              const tenDvt = list?.tenDonViTinh || "";
              return item + ` ${tenDvt}`;
            },
          },
        ]
      : []),
    ...(!thuocNhaThuoc
      ? [
          {
            title: <HeaderSearch title={t("khamBenh.donThuoc.slThucDung")} />,
            width: 80,
            dataIndex: "soLuong",
            key: "soLuong",
            // colSpan: 1,
            i18Name: "khamBenh.donThuoc.slThucDung",
            show: true,
            render: (item, list) => {
              const tenDvt = list?.tenDonViTinh || "";
              return item + ` ${tenDvt}`;
            },
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianThucHien")} />
      ),
      width: 180,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "quanLyNoiTru.dvNoiTru.thoiGianThucHien",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    ...(!thuocNhaThuoc
      ? [
          {
            title: <HeaderSearch title={t("pttt.kho")} sort_key="tenKho" />,
            width: 150,
            dataIndex: "tenKho",
            key: "tenKho",
            // colSpan: 1,
            i18Name: "khamBenh.donThuoc.kho",
            show: true,
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.bacSiChiDinh")}
          sort_key="tenBacSiChiDinh"
        />
      ),
      width: 180,
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      // colSpan: 1,
      i18Name: "khamBenh.donThuoc.bacSiChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch title={t("common.lieuDung")} sort_key="tenLieuDung" />
      ),
      width: 150,
      dataIndex: "tenLieuDung",
      key: "tenLieuDung",
      // colSpan: 1,
      i18Name: "common.lieuDung",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.cachDung")} sort_key="cachDung" />,
      width: 150,
      dataIndex: "cachDung",
      key: "cachDung",
      // colSpan: 1,
      i18Name: "common.cachDung",
      show: true,
    },
    {
      title: (
        <HeaderSearch title={t("khamBenh.donThuoc.soPhieuLinh")} sort_key="" />
      ),
      width: 100,
      dataIndex: "soPhieuLinh",
      key: "soPhieuLinh",
      i18Name: "khamBenh.donThuoc.soPhieuLinh",
      show: true,
    },
    {
      title: <HeaderSearch title="TT20" />,
      width: 100,
      dataIndex: "tenMucDich",
      key: "tenMucDich",
      i18Name: "common.tt20",
      show: true,
    },
    {
      title: <HeaderSearch title={t("pttt.daDuyetPhat")} sort_key="phat" />,
      width: 100,
      dataIndex: "phat",
      key: "phat",
      // colSpan: 1,
      align: "center",
      i18Name: "khamBenh.donThuoc.daDuyetPhat",
      show: true,
      render: (item, record) => {
        const checked = record?.trangThai >= TRANG_THAI_THUOC.DA_PHAT.id;
        return (
          <div>
            <Checkbox checked={checked} />{" "}
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      // hidden: !isTuTruc && !khoBhyt,
      dataIndex: "tuTra",
      key: "tuTra",
      width: 50,
      show: !isManHinhPhauThuat,
      i18Name: "common.tuTra",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.khongTinhTien")} />,
      // hidden: !isTuTruc && !khoBhyt,
      width: 80,
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      show: !isManHinhPhauThuat,
      i18Name: "common.khongTinhTien",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.thanhTien")} />,
      width: 120,
      dataIndex: "thanhTien",
      key: "thanhTien",
      show: !isManHinhPhauThuat,
      i18Name: "common.thanhTien",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh")} />
      ),
      width: 180,
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      i18Name: "quanLyNoiTru.dvNoiTru.thoiGianChiDinh",
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("common.slHuy")} />,
      dataIndex: "soLuongHuy",
      key: "soLuongHuy",
      width: 70,
      show: true,
      i18Name: "common.soLuongHuy",
    },
    {
      title: <HeaderSearch title={t("common.lyDoHuy")} />,
      dataIndex: "lyDoHuy",
      key: "lyDoHuy",
      width: 120,
      show: true,
      i18Name: "common.lyDoHuy",
    },
    {
      title: <HeaderSearch title={t("kho.dotXuat")} />,
      width: 80,
      dataIndex: "loaiChiDinh",
      key: "dotXuat",
      i18Name: "kho.dotXuat",
      align: "center",
      show: true,
      render: (item) => {
        return <Checkbox checked={item == LOAI_CHI_DINH.DOT_XUAT} />;
      },
    },
    {
      title: <HeaderSearch title={t("kho.boSung")} />,
      width: 80,
      dataIndex: "loaiChiDinh",
      key: "boSung",
      i18Name: "kho.boSung",
      align: "center",
      show: true,
      render: (item) => {
        return <Checkbox checked={item == LOAI_CHI_DINH.BO_SUNG} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.ngaySDThuoc")} />,
      dataIndex: "sttNgaySuDung",
      key: "sttNgaySuDung",
      width: 120,
      show: true,
      align: "center",
      i18Name: "common.ngaySDThuoc",
    },
    {
      title: <HeaderSearch title={t("khamBenh.yKienDuocSi")} />,
      width: 120,
      dataIndex: "ghiChuDls",
      key: "ghiChuDls",
      show: true,
      i18Name: "khamBenh.yKienDuocSi",
    },
    {
      title: <HeaderSearch title={t("khamBenh.yKienBacSi")} />,
      width: 120,
      dataIndex: "ghiChuBacSi",
      key: "ghiChuBacSi",
      show: true,
      i18Name: "khamBenh.yKienBacSi",
    },
    {
      title: <HeaderSearch title={t("common.phaChung")} />,
      width: 150,
      dataIndex: "dsPhaChungId",
      key: "dsPhaChungId",
      i18Name: "common.phaChung",
      show: true,
      render: (item) => {
        if (isArray(item, true) && isArray(listDsPhaChung, true)) {
          return item
            .reduce((acc, cur) => {
              let curItem = listDsPhaChung.find((i) => i.id === cur);
              if (curItem) {
                acc.push(curItem.ten);
              }
              return acc;
            }, [])
            .join(", ");
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch title={t("thuNgan.trangThaiHoan")} isTitleCenter={true} />
      ),
      width: 120,
      dataIndex: "trangThaiHoan",
      key: "trangThaiHoan",
      align: "center",
      i18Name: "thuNgan.trangThaiHoan",
      show: true,
      render: (item, list, index) => {
        return listtrangThaiHoan?.find((e) => e.id === item)?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.dungChoCon")} />,
      width: 80,
      dataIndex: "dungChoCon",
      key: "dungChoCon",
      i18Name: "khamBenh.dungChoCon",
      align: "center",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 150,
      key: "action",
      align: "center",
      fixed: "right",
      colSpan: 1,
      ignore: true,
      render: (item, record, index) => {
        return (
          <div>
            <div className="item">
              <div className="action-btn">
                {record?.children &&
                  !isReadonly &&
                  checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_VA_CHO_PHEP_KE_KEM_THUOC,
                  ]) && (
                    <Tooltip title={t("pttt.thuocDungKem")} placement="bottom">
                      <SVG.IcAdd
                        onClick={() => onCreate(record)}
                        className="icon"
                      />
                    </Tooltip>
                  )}
                <Tooltip title={t("pttt.suaThongTinThuoc")} placement="bottom">
                  <SVG.IcEdit onClick={onEdit(record)} className="icon" />
                </Tooltip>
                {record?.children && isShowChuyenThuoc && !isReadonly && (
                  <Tooltip title={t("pttt.chuyenDichVu")} placement="bottom">
                    <SVG.IcChuyenDichVu
                      onClick={onChuyenThuoc(record)}
                      className="icon"
                    />
                  </Tooltip>
                )}
                {!isReadonly && (
                  <Tooltip title={t("pttt.xoaThuoc")} placement="bottom">
                    <SVG.IcDelete
                      onClick={() => onDelete(record)}
                      className="icon"
                    />
                  </Tooltip>
                )}
                {record.trangThaiHoan === TRANG_THAI_HOAN.THUONG &&
                  record.soLuong > 0 &&
                  checkRole([
                    ROLES["KHAM_BENH"].NGUNG_Y_LENH_KHONG_THUC_HIEN,
                  ]) && (
                    <>
                      <Tooltip
                        title={t("khamBenh.ngungYLenh")}
                        placement="bottom"
                      >
                        <SVG.IcXoaHoSo
                          className="ic-action"
                          onClick={onNgungYLenh(record)}
                          color="var(--color-red-primary)"
                        />
                      </Tooltip>
                    </>
                  )}
              </div>
            </div>
          </div>
        );
      },
    },
  ];

  const setRowClassName = (record) => {
    if (
      record?.tenMucDich &&
      configData.thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      if (record.khongTinhTien) {
        return "row-tt35 green-color";
      }
      if (record.tuTra) {
        return "row-tt35 orange-color";
      }
      return "row-tt35";
    }

    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  return (
    <Main>
      <TableWrapper
        columns={columns}
        dataSource={dataSource}
        tableName="table_ChiDinhDichVuToDieuTri_Thuoc"
        ref={refSettings}
        rowClassName={setRowClassName}
        expandIconColumnIndex={1}
        expandedRowKeys={state?.expandedKeys}
        rowKey={(record) => record.id}
        columnResizable={true}
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) =>
            record?.children?.length ? (
              expanded ? (
                <CaretDownOutlined
                  onClick={(e) => {
                    onExpand(record, e);
                    setState({
                      expandedKeys: state?.expandedKeys.filter(
                        (x) => x !== record.id
                      ),
                    });
                    e.stopPropagation();
                  }}
                />
              ) : (
                <CaretRightOutlined
                  onClick={(e) => {
                    onExpand(record, e);
                    setState({
                      expandedKeys: [...state?.expandedKeys, record.id],
                    });
                    e.stopPropagation();
                  }}
                />
              )
            ) : null,
        }}
      />
      <SuaThongTinThuoc
        ref={refSuaThongTinThuoc}
        isShowSoLuongDinhMuc={isShowSoLuongDinhMuc}
        isReadonlyDvNoiTru={isReadonly}
      />
      <ModalNhapSoLuong ref={refModalNhapSoLuong} />
    </Main>
  );
};

export default React.memo(TableDonThuoc);
