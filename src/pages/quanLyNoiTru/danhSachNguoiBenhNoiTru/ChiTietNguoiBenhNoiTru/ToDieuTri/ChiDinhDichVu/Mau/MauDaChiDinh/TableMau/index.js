import React, { useState, useRef, useEffect, useMemo } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Auth<PERSON>rapper } from "components";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useEnum,
  useLazyKVMap,
  useLoading,
  useQueryAll,
  useStore,
  useThietLap,
} from "hooks";
import {
  DOI_TUONG,
  ENUM,
  GIOI_TINH_BY_VALUE,
  THIET_LAP_CHUNG,
  TRANG_THAI_MAU,
  LOAI_BIEU_MAU,
  LOAI_DICH_VU,
  TRANG_THAI_DICH_VU,
  DOI_TUONG_SU_DUNG,
  TRANG_THAI_HOAN,
  ROLES,
} from "constants/index";
import ChinhSuaMau from "pages/chiDinhDichVu/DichVuMau/ChinhSuaMau";
import { CaretRightOutlined, CaretDownOutlined } from "@ant-design/icons";
import ModalDVKemTheo from "../../ModalDVKemTheo";
import moment from "moment";
import { SVG } from "assets";
import ModalYeuCauTraDV from "../../ModalYeuCauTraDV";
import { Checkbox, message } from "antd";
import printProvider from "data-access/print-provider";
import { isArray } from "utils";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import ChinhSuaDVKyThuat from "pages/chiDinhDichVu/DichVuKyThuat/ChinhSuaDVKyThuat";
import ModalNhapKetQuaDichVu from "pages/khamBenh/ChiDinhDichVu/ModalNhapKetQuaDichVu";
import { query } from "redux-store/stores";
import { checkRole } from "lib-utils/role-utils";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";

const { Setting } = TableWrapper;

const TableMau = ({
  listDvMau,
  isReadonly,
  listDvKemTheo,
  listDvVatTuKemTheo,
  onGetListDichVuKemTheo,
  ...props
}) => {
  const { showConfirm } = useConfirm();
  const {
    chiDinhMau: {
      onDeleteDichVu,
      getListChePhamMau,
      getListDichVuKemTheo,
      inPhieuYeuCauTraMau,
    },
    nbXetNghiem: { onDeleteDichVu: onDeleteDichVuXn },
    baoCao: { onSearch },
    chiDinhKhamBenh: { huyKetQua, coKetQua, huyTiepNhan },
    traHangHoa: { postDsDvThuocTraKho },
    truyenPhatMau: { xacNhanTruyenMau, huyTruyenMau },
  } = useDispatch();
  const refSettings = useRef(null);
  const refSuaThongTin = useRef(null);
  const refSuaThongTinDVKT = useRef(null);
  const refModalDVKemTheo = useRef(null);
  const refModalHoanDichVu = useRef(null);
  const refModalNhapLyDo = useRef(null);
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const auth = useStore("auth.auth", {});
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const [getKhoa] = useLazyKVMap(listAllKhoa);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const listBaoCao = useStore("baoCao.listBaoCao", []);
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refModalNhapKetQua = useRef(null);

  const [listTrangThaiMau] = useEnum(ENUM.TRANG_THAI_MAU);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listMucDo] = useEnum(ENUM.MUC_DO_CHE_PHAM_MAU);

  const [dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU
  );
  const [dataMucDoChePhamMau, isFinish] = useThietLap(
    THIET_LAP_CHUNG.MUC_DO_CHE_PHAM_MAU
  );
  const [listXetNghiemCmv] = useEnum(ENUM.XET_NGHIEM_CMV);

  const listMucDoChePhamMau = useMemo(() => {
    if (isFinish && dataMucDoChePhamMau) {
      return listMucDo.filter((x) =>
        dataMucDoChePhamMau
          .split(",")
          .map((s) => Number(s.trim()))
          .includes(x.id)
      );
    }
    return listMucDo;
  }, [isFinish, dataMucDoChePhamMau, listMucDo]);

  const [dataTHOI_GIAN_DUOC_TRA_MAU] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_DUOC_TRA_MAU
  );

  const [state, _setState] = useState({
    dataThuoc: [],
    expandIds: [],
    dataDvKemTheo: {},
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const dataSource = useMemo(() => {
    const _listDvMau = (listDvMau || []).map((item, index) => {
      const children = (item?.dsMauKemTheo || []).map((item) => {
        return {
          ...item,
          key: `dv-kem-che-pham-mau-${item.id}`,
          isMau: true,
        };
      });
      return {
        ...item,
        key: `dv-mau-${item.id}-${index}`,
        index: index + 1,
        isMau: true,
        children: children.length ? children : undefined,
      };
    });

    const _listDvKemTheo = (listDvKemTheo || []).map((item, index) => {
      return {
        ...item,
        loaiDichVu: LOAI_DICH_VU.XET_NGHIEM,
        key: `dv-kem-theo-${item.id}-${index}`,
        index: index + 1,
      };
    });
    const _listDvVatTuKemTheo = (listDvVatTuKemTheo || []).map(
      (item, index) => {
        return {
          ...item,
          key: `dv-vat-tu-kem-theo-${item.id}-${index}`,
          index: index + 1,
        };
      }
    );
    if (_listDvKemTheo.length > 0) {
      _listDvMau.push(
        ...[
          {
            type: "group",
            nameDichVu: t("pttt.dichVuXetNghiemDiKem"),
          },
          ..._listDvKemTheo,
        ]
      );
    }
    if (_listDvVatTuKemTheo.length > 0) {
      _listDvMau.push(
        ...[
          {
            type: "group",
            nameDichVu: t("pttt.dichVuVatTuDiKem"),
          },
          ..._listDvVatTuKemTheo,
        ]
      );
    }
    return _listDvMau;
  }, [listDvMau, listDvKemTheo, listDvVatTuKemTheo]);

  useEffect(() => {
    onSearch({ page: 0, size: 10, dataSearch: { ma: "EMR_BA268" } });
  }, []);

  const dataNb = useMemo(() => {
    let gender = chiTietNguoiBenhNoiTru?.gioiTinh
      ? GIOI_TINH_BY_VALUE[chiTietNguoiBenhNoiTru?.gioiTinh]
      : "";

    let age =
      chiTietNguoiBenhNoiTru?.thangTuoi > 36 || chiTietNguoiBenhNoiTru?.tuoi
        ? `${chiTietNguoiBenhNoiTru?.tuoi} ${t("common.tuoi")}`
        : `${chiTietNguoiBenhNoiTru?.thangTuoi} ${t("common.thang")}`;
    return { gender, age };
  }, []);

  const onHoanDv = (record) => {
    const data = Array(record);
    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = dataNb?.gender;
        itemLoop.tuoi = dataNb?.age;
      });
      const thoiGianDuocTra = dataTHOI_GIAN_DUOC_TRA_MAU || 0;
      if (record.thoiGianDuyet && thoiGianDuocTra) {
        const thoiGianDuyet = moment(record.thoiGianDuyet)
          .add(thoiGianDuocTra, "minutes")
          .valueOf();
        const thoiGianHienTai = moment().valueOf();
        if (thoiGianDuyet < thoiGianHienTai) {
          message.error(
            t("quanLyNoiTru.yeuCauTraMauErr", { num: thoiGianDuocTra })
          );
        } else {
          refModalHoanDichVu.current &&
            refModalHoanDichVu.current.show({ data }, () => {
              refreshData();
            });
        }
      } else {
        refModalHoanDichVu.current &&
          refModalHoanDichVu.current.show({ data }, () => {
            refreshData();
          });
      }
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onDelete = (record) => {
    if (
      dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU?.eval() &&
      record?.maTuiMau &&
      record?.nguoiPhat1Id
    ) {
      return message.error(
        t("quanLyNoiTru.mau.xoaKhongThanhCong_MauDaĐuocienThongMaTuiMau")
      );
    }
    if (
      (record.dsDoiTuongSuDung || []).includes(
        DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
      ) &&
      record.trangThai !== 25
    ) {
      return message.error(
        "Không xóa được dịch vụ có Trạng thái: Đã tiếp nhận mẫu"
      );
    }
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("thuNgan.banCoChacMuonXoaChiDinh", {
          tenDichVu:
            record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "",
        })}`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        try {
          record.isMau
            ? await onDeleteDichVu(record.id)
            : await onDeleteDichVuXn(record.id);
          refreshData();
        } catch (error) {}
      }
    );
  };

  const onNgungYLenh = (record) => async (e) => {
    if (record.trangThai < TRANG_THAI_MAU.DA_PHAT) {
      showConfirm(
        {
          title: t("common.xacNhan"),
          content: t("khamBenh.xacNhanNgungYLenh"),
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "warning",
        },
        async () => {
          await postDsDvThuocTraKho({
            loaiHangHoa: LOAI_DICH_VU.CHE_PHAM_MAU,
            message: t("khamBenh.ngungYLenhThanhCong"),
            payload: [
              {
                nbDichVuId: record.id,
                soLuong: record.soLuong,
              },
            ],
          });
          refreshData();
        }
      );
    } else {
      refModalNhapLyDo.current &&
        refModalNhapLyDo.current.show(
          {
            title: t("khamBenh.lyDoNgungYLenh"),
            message: t("khamBenh.nhapLyDoNgungYLenh"),
          },
          (lyDo) => {
            traMau([
              {
                id: record.id,
                lyDoTra: lyDo,
                thoiGianTra: moment().format(),
                nguoiTraId: auth?.nhanVienId,
              },
            ]).then(() => {
              refreshData();
            });
          }
        );
    }
  };

  const refreshData = () => {
    getListChePhamMau({
      actionTableMau: true,
      nbDotDieuTriId: configData.nbDotDieuTriId,
      chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
      chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
      dsTrangThaiHoan: [0, 10, 20, 40, 42],
    }).then(async (res) => {
      const _listDvMau = res?.data || [];
      onGetListDichVuKemTheo(_listDvMau);
    });
  };

  const renderStt = (value, row, index) => {
    const obj = {
      children: value,
      props: {},
    };
    if (row.type != "group") {
      obj.props.rowSpan = row.rowSpan;
      obj.children = value;
    } else {
      obj.props.rowSpan = 0;
    }
    return obj;
  };

  const onEdit = (record) => (e) => {
    e.stopPropagation();
    if (record.isMau) {
      refSuaThongTin.current && refSuaThongTin.current.show(record);
    } else {
      refSuaThongTinDVKT.current &&
        refSuaThongTinDVKT.current.show(record, () => {
          onGetListDichVuKemTheo(listDvMau);
        });
    }
  };

  const onPrintPhieuYCTraMau = (data) => async (e) => {
    e.stopPropagation();
    e.preventDefault();
    try {
      if (isArray(listBaoCao, true)) {
        let isEditor = listBaoCao[0].loai === LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA;
        if (isEditor) {
          window.open(`/editor/bao-cao/EMR_BA268/${data.id}`);
        } else {
          showLoading();
          const res = await inPhieuYeuCauTraMau({
            nbDotDieuTriId: configData.nbDotDieuTriId,
            phieuNhapXuatId: data.phieuNhapXuatId,
            dsId: [data.id],
          });
          if (isArray(res, true)) {
            const listUrl = (res || []).map((item) => {
              return item.file.pdf;
            });
            printProvider.printMergePdf(listUrl);
          }
        }
      } else {
        message.error(t("khoMau.inPhieuTraMauLoi"));
      }
    } finally {
      hideLoading();
    }
  };

  const onChangeTuTraKhongTinhTien = (key, item) => (e) => {
    let nbDichVu = {};
    if (key === "tuTra") {
      nbDichVu = {
        tuTra: e.target.checked,
        khongTinhTien: !e.target.checked,
      };
    } else {
      nbDichVu = {
        tuTra: !e.target.checked,
        khongTinhTien: e.target.checked,
      };
    }
    nbDvXetNghiemProvider
      .themThongTin([
        {
          id: item.id,
          nbDichVu,
        },
      ])
      .then((s) => {
        onGetListDichVuKemTheo(listDvMau);
        message.success(t("pttt.themThongTinThanhCong"));
      })
      .catch((e) => {
        message.error(t(e?.message || "quanLyNoiTru.themThongTinThatBai"));
      });
  };

  const onCoKetQua = (record) => async () => {
    refModalNhapKetQua.current &&
      refModalNhapKetQua.current.show(
        {
          id: record.id,
          loaiDichVu: record.loaiDichVu,
          ketQua: record.ketQua,
        },
        async () => {
          const s = await coKetQua({
            payload: [record.id],
            loaiDichVu: record.loaiDichVu,
          });
          if (s.code === 0) {
            onGetListDichVuKemTheo(listDvMau);
          }
        }
      );
  };

  const onHuyKetQua = (record) => async () => {
    try {
      showLoading();

      const s = await huyKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        onGetListDichVuKemTheo(listDvMau);
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onHuyTiepNhan = (record) => () => {
    showConfirm(
      {
        title: t("common.huyTiepNhan"),
        content: `${t("khamBenh.chiDinh.xacNhanHuyTiepNhan")} ${
          record.tenDichVu
        }?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        try {
          showLoading();

          const s = await huyTiepNhan({
            listId: [record.id],
            loaiDichVu: record.loaiDichVu,
          });

          if (s.code === 0) {
            onGetListDichVuKemTheo(listDvMau);
          }
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onXacNhanTruyenMau = async (record) => {
    try {
      showLoading();
      await xacNhanTruyenMau([record.id]);
      message.success(t("khoMau.xacNhanTruyenMauThanhCong"));
      refreshData();
    } catch (error) {
      message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    } finally {
      hideLoading();
    }
  };

  const onHuyTruyenMau = async (record) => {
    try {
      showLoading();
      await huyTruyenMau([record.id]);
      message.success(t("khoMau.huyXacNhanTruyenMauThanhCong"));
      refreshData();
    } catch (error) {
      message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    } finally {
      hideLoading();
    }
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} sort_key="index" />,
      width: "64px",
      dataIndex: "index",
      key: "index",
      align: "center",
      render: renderStt,
      ignore: true,
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} sort_key="ten" />,
      width: "300px",
      dataIndex: "nameDichVu",
      key: "nameDichVu",
      colSpan: 1,
      i18Name: "common.tenDichVu",
      className: "tenDichVu",
      show: true,
      render: (item, list, index) => {
        if (list.type !== "group") {
          const ten = list?.tenDichVu || "";
          const tenDuongDung = `${
            list?.tenDuongDung ? " - " + list?.tenDuongDung : ""
          }`;
          const content1 = `${tenDuongDung}${tenDuongDung ? `. ` : ""}`;

          return (
            <div>
              {list.children &&
                (state.expandIds.includes(list.id) ? (
                  <CaretRightOutlined />
                ) : (
                  <CaretDownOutlined />
                ))}{" "}
              <span>{`${ten}`}</span>
              <br />
              <span style={{ fontSize: "12px" }}>
                {`${content1} `}
                {list.ghiChu ? `Lưu ý: ${list.ghiChu}` : ""}
              </span>
            </div>
          );
        }

        return {
          children: <span className="group-title">{item}</span>,
          props: {
            colSpan: 3,
          },
        };
      },
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: 80,
      dataIndex: "soLuong",
      key: "soLuong",
      show: true,
      i18Name: "common.soLuong",
    },
    {
      title: <HeaderSearch title={t("khoMau.nhomMauNb")} />,
      width: 120,
      dataIndex: "nhomMauNb",
      key: "nhomMauNb",
      show: true,
      i18Name: "khoMau.nhomMauNb",
      render: (item) =>
        (listNhomMau || []).find((x) => x.id == item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("khoMau.nhomMauPhat")} />,
      width: 120,
      dataIndex: "nhomMau",
      key: "nhomMauPhat",
      show: true,
      i18Name: "khoMau.nhomMauPhat",
      render: (item) =>
        (listNhomMau || []).find((x) => x.id == item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.mau.maTuiMau")} />,
      width: 120,
      dataIndex: "maTuiMau",
      key: "maTuiMau",
      show: true,
      i18Name: "quanLyNoiTru.mau.maTuiMau",
    },
    {
      title: <HeaderSearch title={t("khoMau.mucDo")} />,
      width: 120,
      dataIndex: "mucDo",
      key: "mucDo",
      show: true,
      i18Name: "khoMau.mucDo",
      render: (item) =>
        listMucDoChePhamMau.find((x) => x.id == item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.mau.trangThai")} />,
      width: 120,
      dataIndex: "trangThai",
      key: "trangThai",
      show: true,
      i18Name: "quanLyNoiTru.mau.trangThai",
      render: (item) => listTrangThaiMau.find((x) => x.id == item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("common.trangThaiHoan")} />,
      width: 120,
      dataIndex: "trangThaiHoan",
      key: "trangThaiHoan",
      show: true,
      i18Name: "common.trangThaiHoan",
      render: (item) => listTrangThaiHoan.find((x) => x.id == item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("khoMau.khoaSuDung")} />,
      width: 120,
      dataIndex: "khoaThucHienId",
      key: "khoaThucHienId",
      show: true,
      i18Name: "khoMau.khoaSuDung",
      render: (item) => getKhoa(item)?.ten || "",
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianThucHien")} />
      ),
      width: 180,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: t("quanLyNoiTru.dvNoiTru.thoiGianThucHien"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh")} />
      ),
      width: 180,
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      i18Name: t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      width: 120,
      dataIndex: "tuTra",
      key: "tuTra",
      show: true,
      i18Name: "common.tuTra",
      align: "center",
      render: (value, item) =>
        item.type === "group" ? (
          ""
        ) : (
          <Checkbox
            checked={value}
            onChange={
              isReadonly || item.isMau
                ? () => {}
                : onChangeTuTraKhongTinhTien("tuTra", item)
            }
          />
        ),
    },
    {
      title: <HeaderSearch title={t("khamBenh.ketQua.ketQuaKetLuan")} />,
      width: 180,
      dataIndex: "ketQua",
      key: "ketQua",
      align: "left",
      i18Name: t("khamBenh.ketQua.ketQuaKetLuan"),
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.khongTinhTien")} />,
      width: 120,
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      show: true,
      i18Name: "common.khongTinhTien",
      align: "center",
      render: (value, item) =>
        item.type === "group" ? (
          ""
        ) : (
          <Checkbox
            checked={value}
            onChange={
              isReadonly || item.isMau
                ? () => {}
                : onChangeTuTraKhongTinhTien("khongTinhTien", item)
            }
          />
        ),
    },
    {
      title: <HeaderSearch title={t("common.phongThucHien")} />,
      width: 120,
      dataIndex: "tenPhongThucHien",
      key: "tenPhongThucHien",
      show: true,
      i18Name: "common.phongThucHien",
    },
    {
      title: <HeaderSearch title={t("kho.tenKho")} />,
      width: 120,
      dataIndex: "tenKho",
      key: "tenKho",
      show: true,
      i18Name: "kho.tenKho",
    },
    {
      title: (
        <HeaderSearch title={t("khoMau.dieuCheBoSung")} isTitleCenter={true} />
      ),
      dataIndex: "dsXetNghiemCmv",
      key: "dsXetNghiemCmv",
      width: 130,
      align: "left",
      show: true,
      i18Name: "khoMau.dieuCheBoSung",
      render: (item, record, index) => {
        const data = listXetNghiemCmv?.filter((x) => item?.includes(x.id));
        return data.map((x1) => x1.ten)?.join(",");
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 100,
      dataIndex: "action",
      key: "action",
      align: "center",
      fixed: "right",
      colSpan: 1,
      ignore: true,
      render: (item, record, index) => {
        if (record.type === "group") {
          const obj = {
            props: {},
          };
          obj.props.colSpan = 0;
          return obj;
        }
        const isThucHienTaiKhoa = (record.dsDoiTuongSuDung || []).includes(
          DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
        );

        return (
          <div>
            <div className="item">
              <div className="action-btn">
                <Tooltip
                  title={
                    record.isMau
                      ? t("pttt.suaChiDinhMau")
                      : t("common.chinhSua")
                  }
                  placement="bottom"
                >
                  <SVG.IcEdit onClick={onEdit(record)} className="icon" />
                </Tooltip>
                {record.isMau &&
                  record.trangThai === TRANG_THAI_MAU.DA_PHAT &&
                  !isReadonly && (
                    <Tooltip placement="bottom" title={t("common.traMau")}>
                      <SVG.IcHoanDv
                        onClick={() => onHoanDv(record)}
                        className="ic-action"
                      />
                    </Tooltip>
                  )}
                {record.isMau &&
                  record.trangThai === TRANG_THAI_MAU.DA_LINH &&
                  !isReadonly && (
                    <AuthWrapper
                      accessRoles={[ROLES["KHO_MAU"].XAC_NHAN_TRUYEN_PHAT_MAU]}
                    >
                      <Tooltip
                        placement="bottom"
                        title={t("khoMau.xacNhanTruyenMau")}
                      >
                        <SVG.IcTick
                          color="var(--color-green-primary)"
                          onClick={() => onXacNhanTruyenMau(record)}
                          className="ic-action"
                        />
                      </Tooltip>
                    </AuthWrapper>
                  )}
                {record.isMau &&
                  record.trangThai === TRANG_THAI_MAU.DA_TRUYEN &&
                  !isReadonly && (
                    <AuthWrapper
                      accessRoles={[ROLES["KHO_MAU"].XAC_NHAN_TRUYEN_PHAT_MAU]}
                    >
                      <Tooltip
                        placement="bottom"
                        title={t("khoMau.huyXacNhanTruyenMau")}
                      >
                        <SVG.IcHuyHoanDv
                          onClick={() => onHuyTruyenMau(record)}
                          className="ic-action"
                        />
                      </Tooltip>
                    </AuthWrapper>
                  )}
                {record.isMau &&
                  record.trangThai === TRANG_THAI_MAU.YEU_CAU_TRA &&
                  !isReadonly && (
                    <Tooltip
                      placement="bottom"
                      title={t("phieuIn.inPhieuYeuCauTraMau")}
                    >
                      <SVG.IcPrint
                        className="icon"
                        onClick={onPrintPhieuYCTraMau(record)}
                      />
                    </Tooltip>
                  )}
                {!isReadonly && (
                  <Tooltip title={t("pttt.xoaChiDinhMau")} placement="bottom">
                    <SVG.IcDelete
                      onClick={(e) => {
                        e.stopPropagation();
                        onDelete(record);
                      }}
                      className="icon"
                    />
                  </Tooltip>
                )}
                {!isReadonly &&
                  isThucHienTaiKhoa &&
                  record.trangThai < TRANG_THAI_DICH_VU.DA_CO_KET_QUA &&
                  TRANG_THAI_DICH_VU.TIEP_NHAN_MAU >= record.trangThai &&
                  record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM && (
                    <Tooltip title={t("common.coKetQua")} placement="bottom">
                      <SVG.IcTick
                        color={"var(--color-green-primary)"}
                        className="ic-action"
                        onClick={onCoKetQua(record)}
                      />
                    </Tooltip>
                  )}
                {!isReadonly &&
                  isThucHienTaiKhoa &&
                  record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
                  record.trangThai >= TRANG_THAI_DICH_VU.DA_CO_KET_QUA && (
                    <Tooltip title={t("common.huyKetQua")} placement="bottom">
                      <SVG.IcCloseCircle
                        color={"var(--color-red-primary)"}
                        className="ic-action"
                        onClick={onHuyKetQua(record)}
                      />
                    </Tooltip>
                  )}
                {!isReadonly &&
                  isThucHienTaiKhoa &&
                  ![25, 155]?.includes(record.trangThai) && (
                    <Tooltip title={t("common.huyTiepNhan")} placement="bottom">
                      <SVG.IcCancel
                        className="ic-action-1"
                        onClick={onHuyTiepNhan(record)}
                      />
                    </Tooltip>
                  )}
                {record.trangThaiHoan === TRANG_THAI_HOAN.THUONG &&
                  checkRole([
                    ROLES["KHAM_BENH"].NGUNG_Y_LENH_KHONG_THUC_HIEN,
                  ]) &&
                  record.soLuong > 0 && (
                    <>
                      <Tooltip
                        title={t("khamBenh.ngungYLenh")}
                        placement="bottom"
                      >
                        <SVG.IcXoaHoSo
                          className="icon"
                          onClick={onNgungYLenh(record)}
                          color="var(--color-red-primary)"
                        />
                      </Tooltip>
                    </>
                  )}
              </div>
            </div>
          </div>
        );
      },
    },
  ];

  const setRowClassName = (record) => {
    if (
      record?.tenMucDich &&
      configData.thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
    )
      return "row-tt35";
    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        let expandIds = [];
        const findIndex = state.expandIds.find((x) => x === record.id);
        if (findIndex > -1) {
          expandIds = state.expandIds.filter((x) => x !== record.id);
          setState({
            expandIds,
          });
        } else {
          expandIds = [...state.expandIds, record.id];
          getListDichVuKemTheo({
            nbDotDieuTriId: record.nbDotDieuTriId,
            phieuNhapXuatId: record.phieuNhapXuatId,
          }).then((res) => {
            setState({
              dataDvKemTheo: { ...state.dataDvKemTheo, [record.id]: res },
              expandIds,
            });
          });
        }
      },
    };
  };
  return (
    <Main>
      <TableWrapper
        columns={columns}
        dataSource={dataSource}
        tableName="table_ChiDinhDichVuToDieuTri_Mau"
        ref={refSettings}
        rowClassName={setRowClassName}
        expandable={{
          expandRowByClick: true,
          expandIcon: ({ expanded, onExpand, record }) => null,
          defaultExpandAllRows: true,
        }}
        rowKey={(row) => row.key}
        onRow={onRow}
      />

      <ModalDVKemTheo ref={refModalDVKemTheo} />
      <ModalYeuCauTraDV ref={refModalHoanDichVu} />
      <ChinhSuaMau
        ref={refSuaThongTin}
        isReadonlyDvNoiTru={isReadonly}
        afterSubmit={refreshData}
      />
      <ChinhSuaDVKyThuat
        ref={refSuaThongTinDVKT}
        isDvXetNghiemMauDiKem={true}
      />
      <ModalNhapKetQuaDichVu ref={refModalNhapKetQua} />
      <ModalNhapLyDo ref={refModalNhapLyDo} />
    </Main>
  );
};

export default React.memo(TableMau);
