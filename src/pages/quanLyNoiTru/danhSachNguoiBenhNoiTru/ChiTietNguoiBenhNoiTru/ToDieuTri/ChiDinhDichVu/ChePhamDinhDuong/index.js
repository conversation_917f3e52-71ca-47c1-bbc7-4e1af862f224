import React, { useEffect, useMemo, useRef, useState } from "react";
import { Select, HotKeyTrigger } from "components";
import { Main, CollapseWrapper } from "./styled";
import { Input, Collapse } from "antd";
import Header from "./components/Header";
import Table from "./components/Table";
import { useDispatch } from "react-redux";
import { LOAI_DICH_VU, ROLES, THIET_LAP_CHUNG } from "constants/index";
import IcArrow from "assets/images/khamBenh/icArrow.svg";
import { groupBy, uniqBy } from "lodash";
import ChiDinhDichVuChePhamDinhDuong from "pages/chiDinhDichVu/DichVuChePhamDinhDuong";
import { useTranslation } from "react-i18next";
import { checkRole } from "lib-utils/role-utils";
import { useStore, useThietLap } from "hooks";
import { SVG } from "assets";

const { Panel } = Collapse;

const ChePhamDinhDuong = (props) => {
  const { t } = useTranslation();
  const modalKeCPDDRef = useRef(null);

  const {
    boChiDinh: { getBoChiDinh },
    chiDinhChePhamDinhDuong: { getListDichVuChePhamDD },
  } = useDispatch();
  const currentToDieuTri = useStore("toDieuTri.currentToDieuTri");
  const listThietLapChonKho = useStore(
    "thietLapChonKho.listThietLapChonKhoChePhamDD"
  );
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const listDvCPDD = useStore("chiDinhChePhamDinhDuong.listDvCPDD", []);
  const { isReadonly, layerId, checkChongChiDinh } = props;
  const [state, _setState] = useState({});

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const [dataCHI_DINH_LOAI_CHE_DO_AN] = useThietLap(
    THIET_LAP_CHUNG.CHI_DINH_LOAI_CHE_DO_AN,
    "FALSE"
  );

  useEffect(() => {
    if (listThietLapChonKho.length === 1) {
      setState({ khoId: listThietLapChonKho[0]?.id });
    } else {
      setState({ khoId: null });
    }
  }, [listThietLapChonKho]);

  const onChiDinhChePhamDinhDuong = (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    if (!state.khoId) {
      return;
    }
    modalKeCPDDRef.current &&
      modalKeCPDDRef.current.show(
        {
          khoId: state.khoId,
          dataKho: uniqBy(listThietLapChonKho || [], "id"),
        },
        () => {}
      );
  };

  useEffect(() => {
    getBoChiDinh({
      dsLoaiDichVu: LOAI_DICH_VU.CHE_PHAM_DINH_DUONG,
      bacSiChiDinhId: nhanVienId,
    });
  }, []);

  useEffect(() => {
    if (currentToDieuTri?.nbDotDieuTriId) {
      getListDichVuChePhamDD({
        nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
        chiDinhTuLoaiDichVu: 210,
        dsChiDinhTuDichVuId: currentToDieuTri?.id,
        dsTrangThaiHoan: [0, 10, 20],
      });
    }
  }, [currentToDieuTri]);

  const listPanel = useMemo(() => {
    const grouped = groupBy(listDvCPDD, "soPhieuLinh");
    return Object.keys(grouped || []).map((key) => {
      let groupByIdArr = grouped[key];

      return {
        header: (
          <Header
            title={`${t("quanLyNoiTru.dvNoiTru.chePhamDinhDuong")}${
              groupByIdArr[0].soPhieuLinh ? ` - ${key}` : ""
            }`}
            listDvCPDD={groupByIdArr}
            nbDotDieuTriId={currentToDieuTri?.nbDotDieuTriId}
            chiDinhTuDichVuId={currentToDieuTri?.id}
            isReadonly={isReadonly}
            dataCHI_DINH_LOAI_CHE_DO_AN={dataCHI_DINH_LOAI_CHE_DO_AN}
          />
        ),
        content: (
          <Table
            listDvCPDD={groupByIdArr}
            nbDotDieuTriId={currentToDieuTri?.nbDotDieuTriId}
            isReadonly={isReadonly}
            dataCHI_DINH_LOAI_CHE_DO_AN={dataCHI_DINH_LOAI_CHE_DO_AN}
          />
        ),
        key,
      };
    });
  }, [listDvCPDD, currentToDieuTri, isReadonly]);

  const onSelectKho = (value) => {
    setState({
      khoId: value,
    });
  };

  return (
    <Main>
      {checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_CHE_PHAM_DINH_DUONG,
      ]) &&
        !isReadonly && (
          <div className="selection">
            <span
              style={{ fontSize: "16px", color: "#172B4D", fontWeight: "700" }}
            >
              {t("quanLyNoiTru.toDieuTri.themChiDinh")}
            </span>
            <div>&nbsp;&nbsp;&nbsp;</div>
            <div>
              <Select
                placeholder={t("khamBenh.donThuoc.vuiLongChonKho")}
                data={listThietLapChonKho}
                onChange={onSelectKho}
                value={state.khoId}
              />
              {listThietLapChonKho?.length && !state.khoId ? (
                <div
                  style={{
                    height: 18,
                    color: "red",
                    visibility: "inherit",
                  }}
                >
                  {t("khamBenh.donThuoc.vuiLongChonKho")}
                </div>
              ) : null}
            </div>
            <div>&nbsp;&nbsp;&nbsp;</div>
            <div>
              <HotKeyTrigger
                layerIds={[layerId]}
                hotKey="F2"
                triggerEvent={onChiDinhChePhamDinhDuong}
              >
                <div className="input-box">
                  <SVG.IcSearch />
                  <Input
                    placeholder={`${t("common.timKiem")} [F2]`}
                    onKeyPress={(e) => {
                      e.preventDefault();
                      e.target.value = "";
                      return null;
                    }}
                    onClick={onChiDinhChePhamDinhDuong}
                  />
                </div>
              </HotKeyTrigger>
            </div>
          </div>
        )}

      <div className="collapse-content">
        <CollapseWrapper
          bordered={false}
          expandIcon={({ isActive }) => (
            <IcArrow
              style={
                isActive
                  ? { transform: "rotate(90deg)" }
                  : { transform: "unset" }
              }
            />
          )}
          className="site-collapse-custom-collapse"
        >
          {listPanel.map((panel) => (
            <Panel key={panel.key} header={panel.header}>
              {panel.content}
            </Panel>
          ))}
        </CollapseWrapper>
      </div>
      <ChiDinhDichVuChePhamDinhDuong
        ref={modalKeCPDDRef}
        dataNb={currentToDieuTri}
        chiDinhTuLoaiDichVu={210}
        dataCHI_DINH_LOAI_CHE_DO_AN={dataCHI_DINH_LOAI_CHE_DO_AN}
      />
    </Main>
  );
};

export default ChePhamDinhDuong;
