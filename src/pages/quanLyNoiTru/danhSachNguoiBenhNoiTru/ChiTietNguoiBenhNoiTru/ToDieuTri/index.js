import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { Row, Col, Radio, Menu, Space, message, Collapse, Spin } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import moment from "moment";
import empty from "assets/images/kho/empty.png";
import { Button, Tooltip, Popover, Dropdown, IframeResizer } from "components";
import { useParams, useHistory } from "react-router-dom";
import DsDichVu from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/DsDichVu";
import ThuocHoSoBenhAn from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/ThuocHoSoBenhAn";
import NoiDungToDieuTri from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/containers/NoiDungToDieuTri";
import VacxinHsba from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/VacxinHsba";
import VatTuHoSoBenhAn from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/VatTuHoSoBenhAn";
import HoaChat from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/HoaChat";
import { useTranslation } from "react-i18next";
import { flatten, uniqBy } from "lodash";
import { Tabs } from "components";
import DanhSachSuatAn from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/DanhSachSuatAn";
import Mau from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/Mau";
import DanhSachChePhamDD from "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/DsChePhamDD";
import {
  useCache,
  useConfirm,
  useLoading,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import ModalInToDieuTriNhieuNgay from "./modals/ModalInToDieuTriNhieuNgay";
import ModalSaoChepDichVuNhieuNgay from "./modals/ModalSaoChepDichVuNhieuNgay";
import { SVG } from "assets";
import printProvider, { printJS } from "data-access/print-provider";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import { checkBatBuocPhanPhongGiuong } from "utils";
import {
  CACHE_KEY,
  LOAI_DICH_VU,
  LOAI_IN,
  LOAI_TO_DIEU_TRI,
  MA_BIEU_MAU_EDITOR,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_HOAN,
  TRANG_THAI_NB,
} from "constants/index";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import { combineUrlParams, openInNewTab } from "utils";
import ModalChonKhoaPhieuTruyenDich from "./modals/ModalChonKhoaPhieuTruyenDich";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { Element } from "react-scroll";
import ActionBar from "components/ModalSignPrint/XemTruoc/ActionBar";
import ModalChiDinhDichVu from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu";
import { LOAI_DICH_VU_CHI_DINH } from "pages/khamBenh/configs";
import ChiDinhDichVuSuatAn from "pages/chiDinhDichVu/DichVuSuatAn";
import ChiDinhDichVuHoatChat from "pages/chiDinhDichVu/DichVuHoaChat";
import ChiDinhDichVuVatTu from "pages/chiDinhDichVu/DichVuVatTu";
import ChiDinhDichVuChePhamDinhDuong from "pages/chiDinhDichVu/DichVuChePhamDinhDuong";
import ModalChiDinhThuoc from "pages/khamBenh/DonThuoc/ModalChiDinhThuoc";
import ChiDinhDichVuMau from "pages/chiDinhDichVu/DichVuMau";
import { showError } from "utils/message-utils";
import ModalInChiDinhTheoDV from "pages/khamBenh/components/StepWrapper/ModalInChiDinhTheoDV";
import ModalChonTieuChi from "../ModalChonTieuChi";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";

const { Panel } = Collapse;

const DS_TRANG_THAI_HOAN = [
  TRANG_THAI_HOAN.THUONG,
  TRANG_THAI_HOAN.CHO_DUYET_HOAN,
  TRANG_THAI_HOAN.CHO_DUYET_DOI,
];

const DS_TRANG_THAI_HOAN_SUAT_AN = [
  TRANG_THAI_HOAN.THUONG,
  TRANG_THAI_HOAN.CHO_DUYET_HOAN,
  TRANG_THAI_HOAN.CHO_DUYET_DOI,
  TRANG_THAI_HOAN.KHONG_THUC_HIEN,
];

const ToDieuTri = (props) => {
  const { showConfirm } = useConfirm();
  const {
    isReadonly,
    khoaLamViec,
    id: _idDotDieuTri,
    isBatBuocPhanPhongGiuong,
    loaiToDieuTri,
    loaiPhcnId,
    phcnId,
    checkChongChiDinh,
    loaiPhcn,
  } = props;
  const history = useHistory();
  const params = useParams();
  const refModalInChiDinhTheoDV = useRef(null);
  const refModalChonPhieuTruyenDich = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();
  const [tab] = useQueryString("tab", 0);

  const [hienThiToDieuTriTheoKhoa, setDataHienThiToDieuTri, loadFinish] =
    useCache("", CACHE_KEY.DATA_HIEN_THI_TO_DIEU_TRI_THEO_KHOA, "1", false);
  const configData = useStore("chiDinhKhamBenh.configData", null);
  const { denNgayTheBhyt, tuNgayTheBhyt } = configData?.thongTinNguoiBenh || {};

  const refModalChiDinhDichVu = useRef(null);
  const refModalChiDinhVatTu = useRef(null);
  const modalKeCPDDRef = useRef(null);
  const refModalChiDinhThuoc = useRef(null);
  const modalKeHoaChatRef = useRef(null);
  const modalKeSuatAnRef = useRef(null);
  const refChiDinhDichVuMau = useRef(null);
  const refModalChonTieuChi = useRef(null);
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const chiTietNoiTruActiveKey = useStore("toDieuTri.activeKey", null);
  const dsToDieuTri = useStore("toDieuTri.listToDieuTri", []);
  const toDieuTriId = useStore("toDieuTri.toDieuTriId", null);

  const [CANH_BAO_CHUA_TAM_UNG_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_CHUA_TAM_UNG_NOI_TRU
  );
  const [dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP,
      "FALSE"
    );

  const listLoaiChiDinhDV = useMemo(() => {
    const list = LOAI_DICH_VU_CHI_DINH.filter(
      (x) => x.id !== LOAI_DICH_VU.NGOAI_DIEU_TRI
    ).map((item) => {
      item.ten = t(item.i18n);
      return item;
    });
    return list;
  }, [t]);

  const {
    toDieuTri: {
      getToDieuTri,
      clearData: clearDataToDieuTri,
      getToDieuTriById,
    },
    khamBenh: { getTatCaGiayChiDinh },
    phieuIn: {
      getListPhieu,
      showFileEditor,
      getFilePhieuIn,
      getDataDanhSachPhieu,
    },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    chiDinhKhamBenh: { updateConfigData },
    dsThuoc: { onSearch: onSearchThuoc },
    dsVatTu: { onSearch: onSearchVatTu },
    dsChePhamDD: { onSearch: onSearchChePhamDd },
    dsDichVuKyThuat: { onSearch: onSearchDvKt },
    dsSuatAn: { onSearch: onSearchSuatAn },
    dsHoaChat: { onSearch: onSearchHoaChat },
    dsMau: { onSearch: onSearchMau },
  } = useDispatch();
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");

  const [state, _setState] = useState({
    activeTab: "1",
    activeKey: [],
    isLoading: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModalInToDieuTri = useRef();
  const refModalSaoChepToDieuTri = useRef();

  useEffect(() => {
    return () => {
      clearDataToDieuTri();
    };
  }, []);

  useEffect(() => {
    if (toDieuTriId) setState({ toDieuTriId: toDieuTriId });
  }, [toDieuTriId]);

  const nbDotDieuTriId = useMemo(() => {
    return _idDotDieuTri || params?.id;
  }, [params?.id, _idDotDieuTri]);

  const listToDieuTri = useMemo(() => {
    return dsToDieuTri.filter(
      (x) => x.loai == (loaiToDieuTri || LOAI_TO_DIEU_TRI.TRONG_VIEN)
    );
  }, [dsToDieuTri, loaiToDieuTri]);

  const dataToDieuTri = useMemo(() => {
    return listToDieuTri.map((item) => ({
      id: item.id,
      ten: `${moment(item.thoiGianYLenh).format("DD/MM/YYYY HH:mm:ss")} - ${
        item.tenBacSiDieuTri || ""
      }`,
    }));
  }, [listToDieuTri]);

  const onSelectItem = (item) => {
    setState({
      toDieuTriId: item?.id,
      toDieuTri: item,
      keyCollapse: [],
    });
  };

  const onMoRong = (id) => {
    const params = {
      loaiPhcnId,
      loaiPhcn,
      phcnId,
    };

    const filteredParams = Object.entries(params).reduce((acc, [key, val]) => {
      if (val) acc[key] = val;
      return acc;
    }, {});

    const queryParams = new URLSearchParams(filteredParams).toString();

    if (id)
      history.push({
        pathname: `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri/${id}`,
        search: queryParams ? `?${queryParams}` : "",
        state: { tab },
      });
  };

  const onCreate = async () => {
    if (
      (thongTinCoBan?.tienTamUng || 0) - (thongTinCoBan?.tienHoanUng || 0) <=
        0 &&
      CANH_BAO_CHUA_TAM_UNG_NOI_TRU?.eval() &&
      !(thongTinCoBan?.theTam && thongTinBenhNhan?.maDoiTuongKcb?.ma === "1.7")
    ) {
      showConfirm({
        title: t("common.xacNhan"),
        content: t(
          "quanLyNoiTru.nguoiBenhChuaTamUngNoiTruVuiLongTamUngTruocKhiThemMoi",
          { title: t("editor.toDieuTri.toDieuTri") }
        ),
        cancelText: t("common.huy"),
      });
      return;
    }
    if (
      chiTietNguoiBenhNoiTru.trangThai !== TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA
    ) {
      if (
        isBatBuocPhanPhongGiuong &&
        checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong)
      ) {
        return;
      } else {
        const params = {
          loai: loaiToDieuTri,
          loaiPhcnId,
          loaiPhcn,
        };

        const filteredParams = Object.entries(params).reduce(
          (acc, [key, val]) => {
            if (val) acc[key] = val;
            return acc;
          },
          {}
        );

        const queryParams = new URLSearchParams(filteredParams).toString();
        const queryString = queryParams
          ? `?${queryParams}&phcnId=${phcnId}`
          : "";

        if (loaiToDieuTri) {
          history.push(
            `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${nbDotDieuTriId}/to-dieu-tri/them-moi${queryString}`
          );
        } else {
          history.push(
            `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${nbDotDieuTriId}/to-dieu-tri/them-moi`
          );
        }
      }
    } else {
      showConfirm({
        title: t("common.xacNhan"),
        content: t("quanLyNoiTru.toDieuTri.nguoiBenhCanTiepNhanVaoKhoa"),
        cancelText: t("common.huy"),
      });
    }
  };

  const handleShowModal = () => {
    refModalInToDieuTri.current &&
      refModalInToDieuTri.current.show({
        nbDotDieuTriId,
        khoaId: khoaLamViec.id,
        maBaoCao: "P088",
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
        mhParams: {
          maManHinh: "006",
          maViTri: "00601",
          kySo: true,
          maPhieuKy: "P088",
          nbDotDieuTriId,
        },
      });
  };

  const onSaoChepDichVu = (item) => {
    refModalSaoChepToDieuTri.current &&
      refModalSaoChepToDieuTri.current.show({
        ...item,
        dsKhoaChiDinhId: khoaLamViec?.id,
        loai: loaiToDieuTri || LOAI_TO_DIEU_TRI.TRONG_VIEN,
      });
  };

  const getToDieuTriTheoKhoa = (isTheoKhoa) => {
    if (isTheoKhoa == 1) {
      getToDieuTri({
        nbDotDieuTriId,
        dsKhoaChiDinhId: khoaLamViec?.id,
        loai: loaiToDieuTri || LOAI_TO_DIEU_TRI.TRONG_VIEN,
      });
    } else {
      getToDieuTri({
        nbDotDieuTriId,
        loai: loaiToDieuTri || LOAI_TO_DIEU_TRI.TRONG_VIEN,
      });
    }
  };

  useEffect(() => {
    if (!nbDotDieuTriId || !khoaLamViec?.id || !loadFinish) return;
    getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
    setState({ hienThiToDieuTriTheoKhoa: hienThiToDieuTriTheoKhoa });
  }, [khoaLamViec?.id, nbDotDieuTriId, hienThiToDieuTriTheoKhoa, loadFinish]);

  useEffect(() => {
    setState({ toDieuTriId: null, url: null });
  }, [nbDotDieuTriId]);

  const onChangeRadio = (e) => {
    setState({ hienThiToDieuTriTheoKhoa: e.target.value });
    getToDieuTriTheoKhoa(e.target.value);
    setDataHienThiToDieuTri(e.target.value);
  };

  const popovercontent = (
    <div style={{ display: "grid" }}>
      {t("quanLyNoiTru.toDieuTri.caiDatHienThiToDieuTri")}:
      <Radio.Group
        defaultValue={state?.hienThiToDieuTriTheoKhoa}
        style={{ display: "grid" }}
        onChange={onChangeRadio}
      >
        <Radio value={"1"}>{t("quanLyNoiTru.toDieuTri.cuaKhoaLamViec")}</Radio>
        <Radio value={"2"}>{t("quanLyNoiTru.toDieuTri.cuaTatCaKhoa")}</Radio>
      </Radio.Group>
    </div>
  );

  const [valuePhieuChiDinh, setValuePhieuChiDinh] = useCache(
    "",
    CACHE_KEY.DATA_TO_DIEU_TRI_OPTION_IN_PHIEU_CHI_DINH,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 1
  );

  const inGiayTo = (id) => () => {
    if (!id) {
      return;
    }
    const onPrint = async () => {
      const currentToDieuTri = await getToDieuTriById(id);
      if (currentToDieuTri)
        getListPhieu({
          nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
          maManHinh: "007",
          maViTri: "00701",
          chiDinhTuDichVuId: id,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
        }).then((listPhieu) => {
          setState({
            listPhieu: listPhieu,
            currentId: id,
            currentToDieuTri,
          });
        });
    };
    onPrint();
  };

  const onPrint = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: state?.currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: state?.currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...item.filePdf];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    if ((res || []).every((x1) => (x1 || []).every((x2) => x2.loaiIn == 20))) {
      openInNewTab(s);
    } else {
      printJS({
        printable: s,
        type: "pdf",
      });
    }
  };

  const contentPhieuChiDinh = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuChiDinh}
          onChange={(e) => {
            setValuePhieuChiDinh(e.target.value);
            setState({
              popoverVisible: false,
            });
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("khamBenh.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("khamBenh.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("khamBenh.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const onPrintTheoDichVu = async (chuaThanhToan) => {
    showLoading();
    try {
      let res = null;
      if (!chuaThanhToan) {
        res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
          nbDotDieuTriId: state?.currentToDieuTri?.nbDotDieuTriId,
          chiDinhTuDichVuId: state?.currentToDieuTri?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
        });
        refModalInChiDinhTheoDV.current.show({
          data: res.data,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          nbDotDieuTriId: state?.currentToDieuTri?.nbDotDieuTriId,
          chiDinhTuDichVuId: state?.currentToDieuTri?.id,
        });
      } else {
        res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId: state?.currentToDieuTri?.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          chiDinhTuDichVuId: state?.currentToDieuTri?.id,
          thanhToan: false,
        });
        await printProvider.printPdf(res?.data);
      }
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuChuaIn = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: state?.currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: state?.currentToDieuTri?.id,
      inPhieuChiDinh: false,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...item.filePdf];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };

  const onPrintPhieu = (item) => async () => {
    if (item.key == 0) {
    } else {
      if (item.type == "editor") {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(item)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            nbDotDieuTriId: state.currentToDieuTri.nbDotDieuTriId,
            maManHinh: "007",
            maViTri: "00701",
            chiDinhTuDichVuId: state.currentToDieuTri.id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            kySo: true,
            maPhieuKy: item.ma,
          };
        }

        let lichSuKyId = item?.dsSoPhieu?.length
          ? item?.dsSoPhieu[0].lichSuKyId
          : "";
        if (item?.dsSoPhieu?.length > 1) {
          lichSuKyId = item.dsSoPhieu.find(
            (x) => x.soPhieu == (state?.currentId || nbDotDieuTriId)
          )?.lichSuKyId;
          mhParams = { ...mhParams, lichSuKyId };
        }
        if (item.ma == "P086") {
          refModalChonPhieuTruyenDich.current &&
            refModalChonPhieuTruyenDich.current.show(
              {
                khoaChiDinhId: khoaLamViec?.id,
              },
              (filterData) => {
                showFileEditor({
                  phieu: item,
                  id: filterData?.id,
                  nbDotDieuTriId: state.currentToDieuTri.nbDotDieuTriId,
                  ma: item.ma,
                  maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                    ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                    : "",
                  chiDinhTuDichVuId: state.currentToDieuTri.id,
                  chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                  khoaChiDinhId: filterData?.khoaChiDinhId,
                  lichSuKyId,
                });
              }
            );
        } else if (item.ma == "P135") {
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              {
                data: (item.dsSoPhieu || []).map((x) => ({
                  id: x.soPhieu,
                  ten: `Số phiếu ${x.ten1}`,
                })),
              },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: state.currentToDieuTri.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  lichSuKyId,
                  mhParams,
                });
              }
            );
        } else {
          showFileEditor({
            phieu: item,
            id: state.currentId,
            nbDotDieuTriId: state.currentToDieuTri.nbDotDieuTriId,
            ma: item.ma,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            khoaChiDinhId: khoaLamViec?.id,
            chiDinhTuDichVuId: state.currentToDieuTri.id,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            lichSuKyId,
            mhParams,
          });
        }
      } else {
        try {
          showLoading();
          const { finalFile, dsPhieu } = await getFilePhieuIn({
            listPhieus: [item],
            nbDotDieuTriId:
              item.ma == "P075"
                ? state.currentToDieuTri.nbDotDieuTriId
                : state.currentToDieuTri.id,
            showError: true,
          });

          if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
            openInNewTab(finalFile);
          } else {
            printJS({
              printable: finalFile,
              type: "pdf",
            });
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          hideLoading();
        }
      }
    }
  };

  const onPrintPhieuChiDinh = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    showLoading();
    switch (valuePhieuChiDinh) {
      case 1: {
        // tất cả chỉ định
        await onPrint();
        break;
      }
      case 2: {
        // chỉ định chưa in
        await onPrintPhieuChuaIn();
        break;
      }
      case 3: {
        // in chỉ định theo dịch vụ
        await onPrintTheoDichVu();
        break;
      }
      case 4: {
        // in chỉ định theo dịch vụ
        await onPrintTheoDichVu(true);
        break;
      }
      default:
        // tất cả chỉ định
        await onPrint();
        break;
    }
    hideLoading();
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || []).map((item, index) => {
          if (item.ma == "P057") {
            return {
              key: index + "",
              label: (
                <div style={{ display: "flex" }}>
                  <div onClick={onPrintPhieuChiDinh} style={{ flex: 1 }}>
                    {item.ten || item.tenBaoCao}
                  </div>

                  <Popover
                    getPopupContainer={(trigger) => trigger.parentNode}
                    overlayClassName={"step-wrapper-in-options right"}
                    placement="rightTop"
                    content={contentPhieuChiDinh()}
                    trigger="click"
                    visible={state.popoverVisible}
                  >
                    <SVG.IcOption
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setState({ popoverVisible: !state.popoverVisible });
                      }}
                    />
                  </Popover>
                </div>
              ),
            };
          }
          return {
            key: index,
            label: (
              <div style={{ cursor: "pointer" }} onClick={onPrintPhieu(item)}>
                {item.ten || item.tenBaoCao}
              </div>
            ),
          };
        })}
      />
    );
  }, [state?.listPhieu, state?.currentId]);

  const listDataSource = useMemo(() => {
    if (listToDieuTri?.length) {
      const listData = listToDieuTri.reduce((a, b) => {
        const { thoiGianYLenh, tenKhoaChiDinh, khoaChiDinhId } = b;
        const thoiGian =
          thoiGianYLenh && moment(thoiGianYLenh).format("DD/MM/YYYY");
        const key = `${thoiGian} - ${tenKhoaChiDinh} - ${khoaChiDinhId}`;
        a[key] = a[key] || {
          thoiGian,
          tenKhoaChiDinh,
          khoaChiDinhId,
          danhSachToDieuTri: [],
        };
        a[key]["danhSachToDieuTri"].push(b);
        return a;
      }, {});

      return listData;
    }
    return "";
  }, [listToDieuTri]);

  useEffect(() => {
    if (listDataSource && Object.values(listDataSource).length)
      setState({
        activeKey: Object.values(listDataSource).map(
          (item) => `${item.thoiGian} - ${item.tenKhoaChiDinh}`
        ),
      });
  }, [JSON.stringify(listDataSource)]);

  const onViewToDieuTri = (data) => {
    const mhParams = {
      maManHinh: "006",
      maViTri: "00601",
      kySo: true,
      maPhieuKy: "P088",
    };

    const params = {
      ...mhParams,
      nbDotDieuTriId: nbDotDieuTriId,
      khoaChiDinhId: data.khoaChiDinhId,
      tuThoiGianYLenh: moment(data.thoiGian, "DD/MM/YYYY")
        .startOf("day")
        .format(),
      denThoiGianYLenh: moment(data.thoiGian, "DD/MM/YYYY")
        .endOf("day")
        .format(),
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
      loai: loaiToDieuTri || LOAI_TO_DIEU_TRI.TRONG_VIEN,
    };

    let url = combineUrlParams(
      `/headless-editor/bao-cao/${MA_BIEU_MAU_EDITOR.P088.maBaoCao}?`
    );

    Object.keys(params).forEach((item, index) => {
      if (params[item]) {
        url += `${index === 0 ? "" : "&"}${item}=${params[item]}`;
      }
    });
    setState({ url, isLoading: true });
  };

  const onPrintToDieuTri = () => {
    window.frames["printf"].focus();
    window.frames["printf"].print();
  };

  const renderTooltipToDieuTri = (item1) => {
    return (
      <>
        <div>
          {item1?.thoiGianYLenh &&
            moment(item1?.thoiGianYLenh).format("DD/MM/YYYY")}
        </div>
        <div>{item1.tenKhoaChiDinh}</div>
        <div>
          {(item1?.dsCdChinh || [])
            .map((x) => {
              return x.ma + " - " + x.ten;
            })
            .join(", ")}
        </div>
        <div>{item1.tenBacSiDieuTri}</div>
      </>
    );
  };

  const onExpand = (key) => (e) => {
    let _activeKeys = state.activeKey;
    if (state.activeKey.includes(key)) {
      _activeKeys = state.activeKey.filter((x) => x != key);
    } else {
      _activeKeys.push(key);
    }
    setState({ activeKey: _activeKeys });
    e.stopPropagation();
  };

  const onLoad = () => {
    setState({ isLoading: false });
  };

  const onChangeTab = (key) => () => {
    setState({ activeTab: key });
  };

  const onChiDinhDichVu = () => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    refModalChiDinhDichVu.current &&
      refModalChiDinhDichVu.current.show(
        {
          dsDoiTuongSuDung: [30],
          nbThongTinId: configData.nbThongTinId,
          nbDotDieuTriId: configData.nbDotDieuTriId,
          khoaChiDinhId: configData.khoaChiDinhId,
          chiDinhTuDichVuId: configData.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: configData.chiDinhTuLoaiDichVu,
          disableChiDinh: false,
          isHiddenTyLett: true,
          isPhauThuat: configData.isPhauThuat,
          listLoaiChiDinhDV: listLoaiChiDinhDV,
          doiTuong: configData.thongTinNguoiBenh?.doiTuong,
          doiTuongKcb: configData.thongTinNguoiBenh?.doiTuongKcb,
          loaiDoiTuongId: configData.thongTinNguoiBenh?.loaiDoiTuongId,
          thoiGianVaoVien: configData?.thongTinNguoiBenh.thoiGianVaoVien,
          ngaySinh: configData?.thongTinNguoiBenh.ngaySinh,
          gioiTinh: configData?.thongTinNguoiBenh?.gioiTinh,
        },
        () => {
          if (denNgayTheBhyt && tuNgayTheBhyt) {
            const tuNgay = moment(tuNgayTheBhyt);
            const denNgay = moment(denNgayTheBhyt);
            if (!moment().isBetween(tuNgay, denNgay)) {
              message.error(
                `Giá trị thẻ hiện tại của NB đã hết hạn. Thời gian sử dụng từ ${tuNgay.format(
                  "DD/MM/YYYY"
                )} đến ${denNgay.format("DD/MM/YYYY")}`
              );
            }
          }
          onSearchDvKt({
            dataSearch: {
              nbDotDieuTriId,
              chiDinhTuDichVuId: state.toDieuTriId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              dsTrangThaiHoan: DS_TRANG_THAI_HOAN_SUAT_AN,
            },
            page: 0,
            size: 10,
            dataSortColumn: { thoiGianThucHien: 1 },
          });
          getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
        }
      );
  };

  const getListKhoTheoTaiKhoan = async (loaiDichVu) => {
    const toDieuTri = listToDieuTri?.find((x) => x.id === state.toDieuTriId);
    const listThietLapChonKho = await getListThietLapChonKhoTheoTaiKhoan({
      khoaNbId: chiTietNguoiBenhNoiTru?.khoaNbId,
      khoaChiDinhId: toDieuTri?.khoaChiDinhId,
      doiTuong: chiTietNguoiBenhNoiTru?.doiTuong,
      loaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
      capCuu: chiTietNguoiBenhNoiTru?.capCuu,
      phongId: chiTietNguoiBenhNoiTru?.phongId,
      noiTru: true,
      canLamSang: false,
      loaiDichVu: loaiDichVu,
    });
    return listThietLapChonKho?.payload?.listThietLapChonKho || [];
  };

  const onChiDinhSuatAn = async (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.SUAT_AN
    );
    modalKeSuatAnRef.current &&
      modalKeSuatAnRef.current.show(
        {
          loaiDonThuoc: state.loaiDonVatTu,
          khoId:
            listThietLapChonKho?.length === 1
              ? listThietLapChonKho[0]?.id
              : null,
          dataKho: uniqBy(listThietLapChonKho || [], "id"),
        },
        () => {
          onSearchSuatAn({
            dataSearch: {
              nbDotDieuTriId,
              chiDinhTuDichVuId: state.toDieuTriId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              dsTrangThaiHoan: DS_TRANG_THAI_HOAN_SUAT_AN,
            },
            page: 0,
            size: 10,
            dataSortColumn: { thoiGianThucHien: 1 },
          });
          getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
        }
      );
  };

  const onChiDinhHoaChat = async (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.HOA_CHAT
    );

    modalKeHoaChatRef.current &&
      modalKeHoaChatRef.current.show(
        {
          dataKho: uniqBy(listThietLapChonKho || [], "id"),
          khoId:
            listThietLapChonKho?.length === 1
              ? listThietLapChonKho[0]?.id
              : null,
        },
        () => {
          if (denNgayTheBhyt && tuNgayTheBhyt) {
            const tuNgay = moment(tuNgayTheBhyt);
            const denNgay = moment(denNgayTheBhyt);
            if (!moment().isBetween(tuNgay, denNgay)) {
              message.error(
                `${t("quanLyNoiTru.giaTriTheHienTaiCuaNbDaHetHan")}. ${t(
                  "quanLyNoiTru.thoiGianSuDungTu{{tuNgay}}den{{denNgay}}",
                  {
                    tuNgay: tuNgay.format("DD/MM/YYYY"),
                    denNgay: denNgay.format("DD/MM/YYYY"),
                  }
                )}`
              );
            }
          }
          onSearchHoaChat({
            dataSearch: {
              nbDotDieuTriId,
              chiDinhTuDichVuId: state.toDieuTriId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              dsTrangThaiHoan: DS_TRANG_THAI_HOAN,
            },
            page: 0,
            size: 10,
            dataSortColumn: { thoiGianThucHien: 1 },
          });
          getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
        }
      );
  };

  const onChiDinhVatTu = async (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.VAT_TU
    );

    const options = {
      dataKho: uniqBy(listThietLapChonKho || [], "id"),
      khoId:
        listThietLapChonKho?.length === 1 ? listThietLapChonKho[0]?.id : null,
    };
    const callbackFunc = () => {
      if (denNgayTheBhyt && tuNgayTheBhyt) {
        const tuNgay = moment(tuNgayTheBhyt);
        const denNgay = moment(denNgayTheBhyt);
        if (!moment().isBetween(tuNgay, denNgay)) {
          message.error(
            `Giá trị thẻ hiện tại của NB đã hết hạn. Thời gian sử dụng từ ${tuNgay.format(
              "DD/MM/YYYY"
            )} đến ${denNgay.format("DD/MM/YYYY")}`
          );
        }
      }
      onSearchVatTu({
        dataSearch: {
          nbDotDieuTriId,
          chiDinhTuDichVuId: state.toDieuTriId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dsTrangThaiHoan: DS_TRANG_THAI_HOAN,
        },
        page: 0,
        size: 10,
        dataSortColumn: { thoiGianThucHien: 1 },
      });
      getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
    };
    refModalChiDinhVatTu.current &&
      refModalChiDinhVatTu.current.show(options, callbackFunc);
  };

  const onChiDinhChePhamDinhDuong = async () => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.CHE_PHAM_DINH_DUONG
    );

    modalKeCPDDRef.current &&
      modalKeCPDDRef.current.show(
        {
          dataKho: uniqBy(listThietLapChonKho || [], "id"),
          khoId:
            listThietLapChonKho?.length === 1
              ? listThietLapChonKho[0]?.id
              : null,
        },
        () => {
          onSearchChePhamDd({
            dataSearch: {
              nbDotDieuTriId,
              dsChiDinhTuDichVuId: state.toDieuTriId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              dsTrangThaiHoan: DS_TRANG_THAI_HOAN,
            },
            page: 0,
            size: 10,
            dataSortColumn: { thoiGianThucHien: 1 },
          });
          getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
        }
      );
  };

  const onChiDinhThuoc = async () => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.THUOC
    );
    const callbackFunc = () => {
      if (denNgayTheBhyt && tuNgayTheBhyt) {
        const tuNgay = moment(tuNgayTheBhyt);
        const denNgay = moment(denNgayTheBhyt);
        if (!moment().isBetween(tuNgay, denNgay)) {
          message.error(
            `Giá trị thẻ hiện tại của NB đã hết hạn. Thời gian sử dụng từ ${tuNgay.format(
              "DD/MM/YYYY"
            )} đến ${denNgay.format("DD/MM/YYYY")}`
          );
        }
      }
      onSearchThuoc({
        dataSearch: {
          nbDotDieuTriId,
          chiDinhTuDichVuId: state.toDieuTriId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dsTrangThaiHoan: DS_TRANG_THAI_HOAN,
        },
        page: 0,
        size: 10,
        dataSortColumn: { thoiGianThucHien: 1 },
      });
      getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
    };
    const options = {
      dataKho: uniqBy(listThietLapChonKho || [], "id"),
      khoId:
        listThietLapChonKho?.length === 1 ? listThietLapChonKho[0]?.id : null,
      toDieuTriId: state.toDieuTriId,
      dataToDieuTri,
    };
    refModalChiDinhThuoc.current &&
      refModalChiDinhThuoc.current.show(options, callbackFunc);
  };

  const onChiDinhMau = async (e) => {
    const chongChiDinhObject = checkChongChiDinh?.();
    if (chongChiDinhObject?.chongChiDinh) {
      chongChiDinhObject?.showConfirm();
      return;
    }
    const listThietLapChonKho = await getListKhoTheoTaiKhoan(
      LOAI_DICH_VU.CHE_PHAM_MAU
    );
    refChiDinhDichVuMau.current &&
      refChiDinhDichVuMau.current.show(
        {
          khoId:
            listThietLapChonKho?.length === 1
              ? listThietLapChonKho[0]?.id
              : null,
          khoaChiDinhId: configData.khoaChiDinhId,
        },
        () => {
          if (denNgayTheBhyt && tuNgayTheBhyt) {
            const tuNgay = moment(tuNgayTheBhyt);
            const denNgay = moment(denNgayTheBhyt);
            if (!moment().isBetween(tuNgay, denNgay)) {
              message.error(
                `Giá trị thẻ hiện tại của NB đã hết hạn. Thời gian sử dụng từ ${tuNgay.format(
                  "DD/MM/YYYY"
                )} đến ${denNgay.format("DD/MM/YYYY")}`
              );
            }
          }
          onSearchMau({
            dataSearch: {
              nbDotDieuTriId,
              chiDinhTuDichVuId: state.toDieuTriId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              dsTrangThaiHoan: DS_TRANG_THAI_HOAN,
            },
            page: 0,
            size: 10,
            dataSortColumn: { thoiGianThucHien: 1 },
          });
          getToDieuTriTheoKhoa(hienThiToDieuTriTheoKhoa);
        }
      );
  };

  useEffect(() => {
    if (state.toDieuTriId && chiTietNoiTruActiveKey == "2") {
      const toDieuTri = listToDieuTri?.find((x) => x.id === state.toDieuTriId);
      let maBenhId =
        toDieuTri?.dsCdChinhId && toDieuTri?.dsCdChinhId.length > 0
          ? toDieuTri?.dsCdChinhId[0]
          : null;

      updateConfigData({
        configData: {
          chiDinhTuDichVuId: toDieuTri.id,
          dsChiDinhTuDichVuId: toDieuTri.id,
          thoiGianThucHien: toDieuTri.thoiGianYLenh,
          nbDotDieuTriId: toDieuTri.nbDotDieuTriId,
          nbThongTinId: chiTietNguoiBenhNoiTru.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          khoaChiDinhId: toDieuTri.khoaChiDinhId,
          thongTinNguoiBenh: chiTietNguoiBenhNoiTru,
          isPhauThuat: toDieuTri.phauThuat,
          trangThaiKham: "",
          phongThucHienId: chiTietNguoiBenhNoiTru.phongId,
          doiTuongKcb: chiTietNguoiBenhNoiTru.doiTuongKcb,
          maBenhId,
          isNoiTru: true,
          canLamSang: false,
          thoiGianYLenh: toDieuTri.thoiGianYLenh,
        },
      });
    }
  }, [state.toDieuTriId, chiTietNoiTruActiveKey]);

  return (
    <Main>
      {!listDataSource &&
        (!isReadonly ||
          checkRole([
            ROLES["QUAN_LY_NOI_TRU"].TAO_TO_DIEU_TRI_NB_DA_THANH_TOAN_RA_VIEN,
          ])) && (
          <div className="content-tab">
            <div className="content-tab_1">
              <img src={empty} alt="..." />
              <div style={{ padding: "10px 0" }}>
                {t("quanLyNoiTru.toDieuTri.chuaTaoToDieuTri")}
              </div>
              {checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_MOI_TO_DIEU_TRI]) && (
                <Button type="primary" onClick={() => onCreate()}>
                  {t("quanLyNoiTru.toDieuTri.taoToDieuTriMoi")}
                </Button>
              )}
            </div>
          </div>
        )}
      {listDataSource && (
        <>
          <Col className="col-left" sm={9} md={8} lg={7} xl={6} xxl={5}>
            <h1 className="header">
              <span>
                <span style={{ fontSize: "15px" }}>
                  {" "}
                  {t("quanLyNoiTru.toDieuTri.title")} ({listToDieuTri?.length})
                </span>{" "}
                <Tooltip
                  title={t("quanLyNoiTru.toDieuTri.inToTieuTriNhieuNgay")}
                >
                  <SVG.IcPrint onClick={handleShowModal} />
                </Tooltip>
                <Popover
                  trigger={"click"}
                  content={popovercontent}
                  placement="bottom"
                >
                  <Tooltip
                    title={t("quanLyNoiTru.toDieuTri.caiDatHienThiToDieuTri")}
                  >
                    <SVG.IcSetting />
                  </Tooltip>
                </Popover>
              </span>
              {(!isReadonly ||
                checkRole([
                  ROLES["QUAN_LY_NOI_TRU"]
                    .TAO_TO_DIEU_TRI_NB_DA_THANH_TOAN_RA_VIEN,
                ])) &&
                checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_MOI_TO_DIEU_TRI]) && (
                  <Button
                    type="success"
                    rightIcon={
                      <SVG.IcAdd
                        title={t("quanLyNoiTru.toDieuTri.themMoiToDieuTri")}
                      />
                    }
                    onClick={() => onCreate()}
                    height={30}
                    style={{ marginBottom: 10 }}
                    iconHeight={15}
                  >
                    {t("common.themMoi")}
                  </Button>
                )}
            </h1>
            <div className="content-item">
              <Collapse activeKey={state.activeKey}>
                {Object.values(listDataSource).map((item) => {
                  const key = `${item.thoiGian} - ${item.tenKhoaChiDinh}`;
                  return (
                    <Panel
                      showArrow={false}
                      header={
                        <div
                          className="item-header"
                          onClick={() => {
                            if (key !== state.keyCollapse) {
                              setState({
                                keyCollapse: key,
                                toDieuTriId: null,
                              });
                              onViewToDieuTri(item);
                            }
                          }}
                        >
                          <div className="left">
                            {state.activeKey.includes(key) && (
                              <SVG.IcArrowDown onClick={onExpand(key)} />
                            )}
                            {!state.activeKey.includes(key) && (
                              <SVG.IcArrowDown
                                rotate={270}
                                onClick={onExpand(key)}
                              />
                            )}
                          </div>
                          <div className="right">
                            <span>{`${item.thoiGian} (${item.danhSachToDieuTri?.length})`}</span>{" "}
                            <span style={{ display: "inline-block" }}>
                              {item.tenKhoaChiDinh}
                            </span>{" "}
                          </div>
                        </div>
                      }
                      key={key}
                      className={`info-content ${
                        key == state.keyCollapse ? "active" : ""
                      }`}
                    >
                      {(item.danhSachToDieuTri || []).map((item1) => {
                        return (
                          <Row
                            key={item1.id}
                            onClick={() => onSelectItem(item1)}
                            className={`item ${
                              state?.toDieuTriId === item1?.id ? "actived" : ""
                            }  `}
                          >
                            <div className="item-content">
                              <div className="left">
                                <Tooltip title={renderTooltipToDieuTri(item1)}>
                                  {item1?.thoiGianYLenh &&
                                    moment(item1?.thoiGianYLenh).format(
                                      "HH:mm:ss"
                                    )}
                                </Tooltip>
                              </div>
                              <div className="right">
                                {(item1.tonTaiThuoc ||
                                  item1.tonTaiThuocDaChiDinh) && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhThuoc"
                                    )}
                                  >
                                    <SVG.IcDonThuoc
                                      className="ic-print"
                                      onClick={onChangeTab("1")}
                                    />
                                  </Tooltip>
                                )}
                                {item1.tonTaiVatTu && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhVatTu"
                                    )}
                                  >
                                    <SVG.IcVatTu
                                      className="ic-print"
                                      onClick={onChangeTab("4")}
                                    />
                                  </Tooltip>
                                )}
                                {item1.tonTaiHoaChat && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhHoaChat"
                                    )}
                                  >
                                    <SVG.IcHoaChat
                                      className="ic-print"
                                      onClick={onChangeTab("7")}
                                    />
                                  </Tooltip>
                                )}
                                {item1.tonTaiSuatAn && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhSuatAn"
                                    )}
                                  >
                                    <SVG.IcSuatAn
                                      className="ic-print"
                                      onClick={onChangeTab("5")}
                                    />
                                  </Tooltip>
                                )}
                                {item1.tonTaiChePhamDd && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhChePhamDd"
                                    )}
                                  >
                                    <SVG.IcChePhamDD
                                      className="ic-print"
                                      onClick={onChangeTab("9")}
                                    />
                                  </Tooltip>
                                )}
                                {item1.tonTaiDvKyThuat && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhDichVuKyThuat"
                                    )}
                                  >
                                    <SVG.IcDVKT
                                      className="ic-print"
                                      onClick={onChangeTab("3")}
                                    />
                                  </Tooltip>
                                )}
                                {item1.tonTaiChePhamMau && (
                                  <Tooltip
                                    title={t(
                                      "quanLyNoiTru.toDieuTriCoChiDinhMau"
                                    )}
                                  >
                                    <SVG.IcChePhamMau
                                      className="ic-print"
                                      onClick={onChangeTab("6")}
                                    />
                                  </Tooltip>
                                )}
                                <Dropdown
                                  overlay={menu}
                                  trigger={["click"]}
                                  placement="top"
                                >
                                  <Tooltip title={t("khamBenh.inGiayTo")}>
                                    <SVG.IcPrint
                                      className="ic-print"
                                      onClick={inGiayTo(item1?.id)}
                                    />
                                  </Tooltip>
                                </Dropdown>
                                {!isReadonly &&
                                  khoaLamViec?.id === item1?.khoaChiDinhId && (
                                    <Tooltip title={t("khamBenh.saoChep")}>
                                      <SVG.IcSaoChep
                                        onClick={() => onSaoChepDichVu(item1)}
                                      />
                                    </Tooltip>
                                  )}
                                <Tooltip title={t("common.moRong")}>
                                  <SVG.IcMoRong
                                    onClick={() => onMoRong(item1?.id)}
                                  />
                                </Tooltip>
                              </div>
                            </div>
                          </Row>
                        );
                      })}
                    </Panel>
                  );
                })}
              </Collapse>
            </div>
          </Col>
          {state?.toDieuTriId ? (
            <Col className="col-right" sm={15} md={16} lg={17} xl={18} xxl={19}>
              <Tabs
                activeKey={state?.activeTab}
                onChange={(tab) => {
                  setState({ activeTab: tab });
                }}
              >
                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("hsba.danhSachThuoc")}
                      {state.activeTab == "1" &&
                        checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_THUOC,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd onClick={onChiDinhThuoc}></SVG.IcAdd>
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"1"}
                >
                  <ThuocHoSoBenhAn
                    thongTinBenhNhan={chiTietNguoiBenhNoiTru}
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN}
                    configData={configData}
                    isShowAction={true}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane tab={t("common.noiDung")} key={"2"}>
                  <NoiDungToDieuTri toDieuTriId={state?.toDieuTriId} />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("hsba.danhSachDichVu")}
                      {state.activeTab == "3" &&
                        checkRoleOr([
                          ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_DVKT,
                          ROLES["QUAN_LY_NOI_TRU"]
                            .DIEU_DUONG_THEM_CHI_DINH_DVKT,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd onClick={onChiDinhDichVu} />
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"3"}
                >
                  <DsDichVu
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN_SUAT_AN}
                    isShowAction={true}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("hsba.danhSachVatTu")}
                      {state.activeTab == "4" &&
                        checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_VAT_TU,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd onClick={onChiDinhVatTu}></SVG.IcAdd>
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"4"}
                >
                  <VatTuHoSoBenhAn
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN}
                    isShowAction={true}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("hsba.danhSachSuatAn")}
                      {state.activeTab == "5" &&
                        checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_SUAT_AN,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd onClick={onChiDinhSuatAn}></SVG.IcAdd>
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"5"}
                >
                  <DanhSachSuatAn
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN_SUAT_AN}
                  />
                </Tabs.TabPane>

                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("quanLyNoiTru.dvNoiTru.mau")}
                      {state.activeTab == "6" &&
                        checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_MAU,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd onClick={onChiDinhMau}></SVG.IcAdd>
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"6"}
                >
                  <Mau
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("danhMuc.hoaChat")}
                      {state.activeTab == "7" &&
                        checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_HOA_CHAT,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd onClick={onChiDinhHoaChat}></SVG.IcAdd>
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"7"}
                >
                  <HoaChat
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane tab={t("danhMuc.vacXin")} key={"8"}>
                  <VacxinHsba
                    nbDotDieuTriId={nbDotDieuTriId}
                    chiDinhTuDichVuId={state.toDieuTriId}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={
                    <div className="item-tab">
                      {t("quanLyNoiTru.dvNoiTru.chePhamDinhDuong")}
                      {state.activeTab == "9" &&
                        checkRole([
                          ROLES["QUAN_LY_NOI_TRU"]
                            .THEM_CHI_DINH_CHE_PHAM_DINH_DUONG,
                        ]) &&
                        checkChotThoiGianDotDieuTri(
                          { thoiGian: state.toDieuTri?.thoiGianYLenh },
                          listAllPhieuThuChotDot
                        ) && (
                          <Tooltip title={t("common.themMoi")}>
                            <SVG.IcAdd
                              onClick={onChiDinhChePhamDinhDuong}
                            ></SVG.IcAdd>
                          </Tooltip>
                        )}
                    </div>
                  }
                  key={"9"}
                >
                  <DanhSachChePhamDD
                    nbDotDieuTriId={nbDotDieuTriId}
                    dsChiDinhTuDichVuId={[state.toDieuTriId]}
                    chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
                    dsTrangThaiHoan={DS_TRANG_THAI_HOAN}
                  />
                </Tabs.TabPane>
              </Tabs>
            </Col>
          ) : (
            <Col className="col-right" sm={15} md={16} lg={17} xl={18} xxl={19}>
              <Spin spinning={state.isLoading}>
                <Element className="element-page">
                  <ActionBar
                    url={state.url}
                    styleWrap={{ marginTop: "10px" }}
                    onPrint={onPrintToDieuTri}
                  ></ActionBar>

                  <IframeResizer
                    id="printf"
                    name="printf"
                    src={state.url}
                    style={{
                      width: "1px",
                      minWidth: "100%",
                    }}
                    scrolling="yes"
                    onLoad={onLoad}
                  />
                </Element>
              </Spin>
            </Col>
          )}
        </>
      )}
      <ModalInToDieuTriNhieuNgay
        ref={refModalInToDieuTri}
      ></ModalInToDieuTriNhieuNgay>
      <ModalSaoChepDichVuNhieuNgay
        ref={refModalSaoChepToDieuTri}
      ></ModalSaoChepDichVuNhieuNgay>
      <ModalInChiDinhTheoDV ref={refModalInChiDinhTheoDV} />
      <ModalChonKhoaPhieuTruyenDich ref={refModalChonPhieuTruyenDich} />
      <ModalChiDinhDichVu ref={refModalChiDinhDichVu} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />

      <ChiDinhDichVuVatTu ref={refModalChiDinhVatTu} />
      <ChiDinhDichVuChePhamDinhDuong
        ref={modalKeCPDDRef}
        chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
      />
      <ModalChiDinhThuoc ref={refModalChiDinhThuoc} isToDieuTriNoiTru={true} />
      <ChiDinhDichVuHoatChat
        ref={modalKeHoaChatRef}
        dataNb={listToDieuTri?.find((x) => x.id === state.toDieuTriId)}
        chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
      />
      <ChiDinhDichVuSuatAn
        ref={modalKeSuatAnRef}
        dataNb={listToDieuTri?.find((x) => x.id === state.toDieuTriId)}
        chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
      />
      <ChiDinhDichVuMau
        ref={refChiDinhDichVuMau}
        chiDinhTuLoaiDichVu={LOAI_DICH_VU.TO_DIEU_TRI}
        chiDinhTuDichVuId={configData?.chiDinhTuDichVuId}
      />
    </Main>
  );
};
export default memo(ToDieuTri);
