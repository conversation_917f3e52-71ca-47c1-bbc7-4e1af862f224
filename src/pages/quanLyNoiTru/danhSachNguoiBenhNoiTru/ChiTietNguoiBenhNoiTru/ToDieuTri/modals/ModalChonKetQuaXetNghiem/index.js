import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import { Form, message } from "antd";
import { Main } from "./styled";
import {
  Button,
  DatePicker,
  InputTimeout,
  ModalTemplate,
  Select,
} from "components";
import { useLoading, useStore, useThietLap } from "hooks";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import FormWraper from "components/FormWraper";
import { LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";
import moment from "moment";
import { uniq } from "lodash";
import ModalScanPrint from "pages/hoSoBenhAn/components/ModalScanPrint";
import stringUtils from "mainam-react-native-string-utils";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";

const ModalChonKetQuaXetNghiem = (props, ref) => {
  const { t } = useTranslation();
  const { hideLoading, showLoading } = useLoading();
  const [form] = Form.useForm();
  const refModal = useRef(null);
  const refModalSignPrint = useRef(null);
  const { isHsba = false } = props;
  const tuThoiGianThucHien = Form.useWatch("tuThoiGianThucHien", form);
  const denThoiGianThucHien = Form.useWatch("denThoiGianThucHien", form);

  const [dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_XET_NGHIEM
  );

  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );

  const listDichVuXetNghiem = useStore(
    "dsDichVuKyThuat.listDichVuXetNghiem",
    []
  );

  const {
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
    chiDinhKhamBenh: { inPhieuKetQua },
    dsDichVuKyThuat: { getListNbDvXN },
  } = useDispatch();

  const [state, _setState] = useState({
    show: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM) {
      getAllTongHopDichVuCap2({
        page: "",
        size: "",
        "nhomDichVuCap1.ma": dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM,
      });
    }
  }, [dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM]);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useImperativeHandle(ref, () => ({
    show: ({ nbDotDieuTriId, thoiGianThucHien = null, baoCaoId }) => {
      if (thoiGianThucHien) {
        form.setFieldsValue({
          tuThoiGianThucHien: moment(thoiGianThucHien)
            .set("hour", 0)
            .set("minute", 0)
            .set("second", 0),
          denThoiGianThucHien: moment(thoiGianThucHien)
            .set("hour", 23)
            .set("minute", 59)
            .set("second", 59),
        });
      }
      getListNbDvXN({ active: true, nbDotDieuTriId });
      setState({ show: true, baoCaoId });
    },
  }));

  const onClose = () => {
    form.resetFields();
    setState({ show: false, baoCaoId: null });
  };

  const onInKetQua = () => {
    form.validateFields().then((values) => {
      const {
        nhomDichVuCap2Id,
        tuThoiGianThucHien,
        denThoiGianThucHien,
        soPhieu,
      } = values;
      let listDvPrint = listDichVuXetNghiem;
      listDvPrint = listDvPrint.filter(
        (x) => x.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
      );

      if (nhomDichVuCap2Id) {
        listDvPrint = listDvPrint.filter(
          (x) => x.nhomDichVuCap2Id == nhomDichVuCap2Id
        );
      }

      if (tuThoiGianThucHien) {
        listDvPrint = listDvPrint.filter((x) =>
          moment(x.thoiGianThucHien).isAfter(
            moment(tuThoiGianThucHien)
              .set("hour", 0)
              .set("minute", 0)
              .set("second", 0)
          )
        );
      }
      if (denThoiGianThucHien) {
        listDvPrint = listDvPrint.filter((x) =>
          moment(x.thoiGianThucHien).isBefore(
            moment(denThoiGianThucHien)
              .set("hour", 23)
              .set("minute", 59)
              .set("second", 59)
          )
        );
      }

      if (soPhieu) {
        listDvPrint = listDvPrint.filter((x) => x.soPhieu == soPhieu);
      }

      if (listDvPrint && listDvPrint.length > 0) {
        const dsSoPhieuId = (listDvPrint || []).map((x) => x.soPhieuId);
        const { nbDotDieuTriId, loaiDichVu } = listDvPrint[0] || {};
        showLoading();
        inPhieuKetQua({
          nbDotDieuTriId,
          dsSoPhieuId: uniq(dsSoPhieuId),
          loaiDichVu,
          returnData: true,
        })
          .then(async (s) => {
            if (!s.length) return;
            const _listPhieu = s.map((item) => {
              return {
                ...item,
                key: stringUtils.guid(),
                data: {
                  filePdf: item.dsDuongDan
                    ? item.dsDuongDan.length
                      ? item.dsDuongDan[0]
                      : null
                    : item.file.pdf,
                },
                ten: `Kết quả pdf`,
                nbDotDieuTriId: item.nbDotDieuTriId || nbDotDieuTriId,
                trangThai: item?.lichSuKy?.trangThai,
                baoCaoId: item.baoCaoId,
                soPhieu: item.soPhieu,
                lichSuKy: item.lichSuKy,
              };
            });

            const listPhieu = await Promise.all(
              _listPhieu.map(async (item) => {
                if (item.lichSuKy) {
                  const itemDaKy =
                    await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                      id: item.lichSuKy?.id,
                      chuKySo: item.lichSuKy?.chuKySo,
                    });

                  return {
                    ...item,
                    data: { filePdf: itemDaKy.data?.file?.pdf },
                  };
                }
                return item;
              })
            );

            refModalSignPrint.current &&
              refModalSignPrint.current.show(
                {
                  dsPhieu: listPhieu,
                  isSignPdf: true,
                  nbDotDieuTriId,
                  baoCaoId: state.baoCaoId || listPhieu[0].baoCaoId,
                },
                async () => {
                  const s = await inPhieuKetQua({
                    nbDotDieuTriId,
                    dsSoPhieuId: uniq(dsSoPhieuId),
                    loaiDichVu,
                    returnData: true,
                  });
                  const _listPhieu = s.map((item) => {
                    return {
                      ...item,
                      key: stringUtils.guid(),
                      data: {
                        filePdf: item.dsDuongDan
                          ? item.dsDuongDan.length
                            ? item.dsDuongDan[0]
                            : null
                          : item.file.pdf,
                      },
                      ten: `Kết quả pdf`,
                      nbDotDieuTriId: item.nbDotDieuTriId || nbDotDieuTriId,
                      trangThai: item?.lichSuKy?.trangThai,
                      baoCaoId: item.baoCaoId,
                      soPhieu: item.soPhieu,
                      lichSuKy: item.lichSuKy,
                      baoCaoId: state.baoCaoId,
                    };
                  });

                  const listPhieu = await Promise.all(
                    _listPhieu.map(async (item) => {
                      if (item.lichSuKy) {
                        const itemDaKy =
                          await danhSachPhieuChoKyProvider.getBaoCaoDaKy({
                            id: item.lichSuKy?.id,
                            chuKySo: item.lichSuKy?.chuKySo,
                          });

                        return {
                          ...item,
                          data: { filePdf: itemDaKy.data?.file?.pdf },
                        };
                      }
                      return item;
                    })
                  );
                  return listPhieu;
                }
              );
          })
          .catch(() => {})
          .finally(() => {
            hideLoading();
          });
      } else {
        message.error(t("quanLyNoiTru.khongCoPhieuThoaMan"));
      }
    });
  };

  return (
    <ModalTemplate
      width={300}
      closable={true}
      ref={refModal}
      title={t("quanLyNoiTru.toDieuTri.inKetQua")}
      onCancel={onClose}
      actionLeft={
        <Button.QuayLai
          onClick={() => {
            onClose();
          }}
        />
      }
      actionRight={
        <Button type="primary" onClick={onInKetQua}>
          {t("common.xacNhan")}
        </Button>
      }
    >
      <Main>
        <div className="content">
          <FormWraper
            form={form}
            style={{ width: "100%" }}
            labelAlign={"left"}
            layout="vertical"
          >
            <Form.Item
              label={t("pttt.tuNgayThucHien")}
              name="tuThoiGianThucHien"
            >
              <DatePicker
                format="DD/MM/YYYY"
                placeholder={t("quanLyNoiTru.chonTuNgay")}
                {...(denThoiGianThucHien && {
                  disabledDate: (date) => date > denThoiGianThucHien,
                })}
              />
            </Form.Item>

            <Form.Item
              label={t("pttt.denNgayThucHien")}
              name="denThoiGianThucHien"
            >
              <DatePicker
                format="DD/MM/YYYY"
                placeholder={t("quanLyNoiTru.chonDenNgay")}
                {...(tuThoiGianThucHien && {
                  disabledDate: (date) => date < tuThoiGianThucHien,
                })}
              />
            </Form.Item>

            <Form.Item label={t("common.loaiDichVu")} name="nhomDichVuCap2Id">
              <Select
                data={listAllNhomDichVuCap2}
                placeholder={t("danhMuc.chonLoaiDichVu")}
              />
            </Form.Item>

            <Form.Item label={t("common.soPhieu")} name="soPhieu">
              <InputTimeout placeholder={t("thuNgan.nhapSoPhieu")} />
            </Form.Item>
          </FormWraper>
        </div>
      </Main>
      <ModalScanPrint ref={refModalSignPrint} />
    </ModalTemplate>
  );
};

export default forwardRef(ModalChonKetQuaXetNghiem);
