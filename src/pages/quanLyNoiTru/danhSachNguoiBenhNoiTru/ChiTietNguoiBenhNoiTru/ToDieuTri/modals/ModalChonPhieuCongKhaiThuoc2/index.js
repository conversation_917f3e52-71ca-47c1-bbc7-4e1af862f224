import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import {
  DatePicker,
  Button,
  ModalTemplate,
  Select,
  Checkbox,
} from "components";
import { DS_TINH_CHAT_KHOA, HOTKEY, ENUM } from "constants/index";
import { Main } from "./styled";
import { Form, message } from "antd";
import { SVG } from "assets";
import { isArray, isEmpty, isObject } from "lodash";
import { useEnum, useIsMounted } from "hooks";
import moment from "moment";
import dmKhoaProvider from "data-access/categories/dm-khoa-provider";
import TableChonPhieu from "../TableChonPhieu";
import ModalConfirmChonPhieu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalConfirmChonPhieu";

const ModalChonPhieuCongKhaiThuoc2 = (props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    show: false,
    data: {},
    selectedPhieu: null,
    dsSoPhieu: [],
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };
  const refModal = useRef(null);
  const refModalConfirmChonPhieu = useRef(null);
  const refCallback = useRef(null);
  const isMounted = useIsMounted();
  const [form] = Form.useForm();
  const tuThoiGian = Form.useWatch("tuThoiGian", form);
  const denThoiGian = Form.useWatch("denThoiGian", form);

  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);

  useImperativeHandle(ref, () => ({
    show: (data = {}, callBack) => {
      const _dsSoPhieu = (data.dsSoPhieu || [])
        .filter((item) => !isEmpty(item.soPhieu))
        .map((item) => ({
          ...item,
          id: item.soPhieu,
        }));

      setState({ show: true, data, dsSoPhieu: _dsSoPhieu });
      form.setFieldsValue({
        khoaChiDinhId: data?.khoaChiDinhId || null,
        tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
        denThoiGian: moment()
          .set("hour", 23)
          .set("minute", 59)
          .set("second", 59),
        dsLoaiChiDinh: listLoaiChiDinh.map((item) => item.id),
      });
      dmKhoaProvider
        .searchTheoTaiKhoan({
          active: true,
          page: "",
          size: "",
          dsTinhChatKhoa: [
            DS_TINH_CHAT_KHOA.NOI_TRU,
            DS_TINH_CHAT_KHOA.CDHA_TDCN,
            DS_TINH_CHAT_KHOA.PHAU_THUAT,
            DS_TINH_CHAT_KHOA.CAP_CUU,
            DS_TINH_CHAT_KHOA.LAM_SANG,
          ],
        })
        .then((s) => {
          if (isMounted()) setState({ listKhoa: s?.data });
        });
      refCallback.current = callBack;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current.show();
    } else {
      refModal.current.hide();
    }
  }, [state.show]);

  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({ show: false });
    }
  };

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  const onSelectPhieuDaIn = (payload = {}) => {
    const _dsLoaiChiDinhId = listLoaiChiDinh
      .filter((item) => state.selectedPhieu.ten2?.indexOf(item.ten) > -1)
      ?.map((item) => item.id);

    const data = {
      id: payload.id,
      khoaChiDinhId: state.selectedPhieu?.khoaChiDinhId,
      tuThoiGian: state.selectedPhieu?.tuThoiGian
        ? moment(state.selectedPhieu?.tuThoiGian).format("YYYY-MM-DD HH:mm:ss")
        : null,
      denThoiGian: state.selectedPhieu?.denThoiGian
        ? moment(state.selectedPhieu?.denThoiGian).format("YYYY-MM-DD HH:mm:ss")
        : null,
      ...(["P183", "P248"].includes(state.data?.ma)
        ? {
            dsLoaiChiDinh: _dsLoaiChiDinhId,
          }
        : {}),
    };

    refCallback.current && refCallback.current(data);
    setState({
      show: false,
      selectedPhieu: null,
      dsSoPhieu: [],
      selectedRowKeys: null,
    });
  };

  const onHandleSubmit = () => {
    if (state.selectedPhieu) {
      onSelectPhieuDaIn(state.selectedPhieu);
    } else {
      form
        .validateFields()
        .then((values) => {
          const data = {
            ...values,
            tuThoiGian: values?.tuThoiGian
              ? moment(values?.tuThoiGian).format("YYYY-MM-DD 00:00:00")
              : null,
            denThoiGian: values?.denThoiGian
              ? moment(values?.denThoiGian).format("YYYY-MM-DD 23:59:59")
              : null,
            ...(["P183", "P248"].includes(state.data?.ma)
              ? {
                  dsLoaiChiDinh: values?.dsLoaiChiDinh || [],
                }
              : {}),
          };

          //kiểm tra trùng từ ngày trong ds số phiếu
          const _tuNgay = moment(values?.tuThoiGian).format("DD/MM/YYYY");
          const _checkSoPhieu = (state.dsSoPhieu || []).filter(
            (item) => moment(item.tuThoiGian).format("DD/MM/YYYY") == _tuNgay
          );

          if (_checkSoPhieu.length > 0) {
            refModalConfirmChonPhieu.current &&
              refModalConfirmChonPhieu.current.show(
                {
                  dsSoPhieu: _checkSoPhieu,
                  tuNgay: _tuNgay,
                },
                () => {
                  refCallback.current && refCallback.current(data);
                  setState({ show: false });
                },
                onSelectPhieuDaIn
              );
          } else {
            refCallback.current && refCallback.current(data);
            setState({ show: false });
          }
        })
        .catch((e) => {
          message.error(e);
        });
    }
  };

  const validateTuThoiGian = (rule, value) => {
    const startDate = moment(value);
    const endDate = moment(denThoiGian);
    if (tuThoiGian && value && startDate > endDate) {
      return Promise.reject("Validation failed");
    }
    return Promise.resolve();
  };
  const validateDenThoiGian = (days) => (rule, value) => {
    const startDate = moment(tuThoiGian);
    const endDate = moment(value);
    if (days) {
      const diffInDays = endDate.diff(startDate, "days");
      if (tuThoiGian && value && (diffInDays > 2 || diffInDays < 0)) {
        return Promise.reject("Validation failed");
      }
    } else {
      if (tuThoiGian && value && startDate > endDate) {
        return Promise.reject("Validation failed");
      }
    }
    return Promise.resolve();
  };

  return (
    <ModalTemplate
      ref={refModal}
      hotKeys={hotKeys}
      onCancel={onOk(false)}
      destroyOnClose
      title={t("quanLyNoiTru.chonTieuChi")}
      width={["P183", "P248"].includes(state.data?.ma) ? 800 : 650}
      actionLeft={
        <Button.QuayLai onClick={onOk(false)}>
          {t("common.quayLai")} [ESC]
        </Button.QuayLai>
      }
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onOk(true)}
          rightIcon={<SVG.IcSave></SVG.IcSave>}
        >
          <span> {t("common.dongY")}</span>{" "}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          <Form.Item
            label={t("baoCao.khoaChiDinh")}
            name="khoaChiDinhId"
            style={{ width: "100%" }}
            rules={[
              {
                required: !state.selectedPhieu,
                message: t("baoCao.vuiLongChonKhoa"),
              },
            ]}
          >
            <Select
              className="select"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              data={state.listKhoa}
              allowClear={false}
            ></Select>
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.toDieuTri.tuNgay")}
            name="tuThoiGian"
            style={{ width: "100%" }}
            dependencies={["denThoiGian"]}
            rules={[
              {
                required: !state.selectedPhieu,
                message: t("common.vuiLongChonNgay"),
              },
              {
                message: t(
                  "quanLyNoiTru.toDieuTri.thoiGianTuNgayPhaiNhoHonThoiGianDenNgay"
                ),
                validator: validateTuThoiGian,
              },
            ]}
          >
            <DatePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              format={"DD/MM/YYYY"}
              // disabledDate={disabledTuNgay}
            />
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.toDieuTri.denNgay")}
            name="denThoiGian"
            style={{ width: "100%" }}
            dependencies={["tuThoiGian"]}
            rules={[
              {
                required: !state.selectedPhieu,
                message: t("common.vuiLongChonNgay"),
              },
              ...(state?.data?.ma === "P183"
                ? [
                    {
                      message: t(
                        "quanLyNoiTru.toDieuTri.thoiGianDenNgayPhaiLonHonThoiGianTuNgayVaKhongQua2Ngay"
                      ),
                      validator: validateDenThoiGian(2),
                    },
                  ]
                : [
                    {
                      message: t(
                        "quanLyNoiTru.toDieuTri.thoiGianDenNgayPhaiLonHonThoiGianTuNgay"
                      ),
                      validator: validateDenThoiGian(),
                    },
                  ]),
            ]}
          >
            <DatePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              format={"DD/MM/YYYY"}
              // disabledDate={disabledDenNgay}
            />
          </Form.Item>

          {["P183", "P248"].includes(state.data?.ma) && (
            <Form.Item name={"dsLoaiChiDinh"}>
              <Checkbox.Group>
                {listLoaiChiDinh.map((item) => {
                  return (
                    <Checkbox value={item.id} key={item.id}>
                      {item.ten}
                    </Checkbox>
                  );
                })}
              </Checkbox.Group>
            </Form.Item>
          )}
        </Form>

        <TableChonPhieu
          dsSoPhieu={state?.dsSoPhieu}
          setParentState={setState}
          selectedRowKeys={state?.selectedRowKeys}
          {...(["P183", "P248"].includes(state.data?.ma)
            ? { showTen2: true, ten2Title: t("common.loaiChiDinh") }
            : {})}
        />

        <ModalConfirmChonPhieu ref={refModalConfirmChonPhieu} />
      </Main>
    </ModalTemplate>
  );
};
export default forwardRef(ModalChonPhieuCongKhaiThuoc2);
