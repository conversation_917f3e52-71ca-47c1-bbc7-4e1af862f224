import React from "react";
import { HeaderStyled } from "../styled";
import { SVG } from "assets";
import { t } from "i18next";

const HeaderPanel = ({
  keyPanel,
  title,
  soKhoan,
  onRefreshList,
  onDeleteDichVu,
  onEditDichVu,
  onSaveDichVu,
  onXemPhieuKy,
  isEditing = false,
}) => {
  return (
    <HeaderStyled $soKhoan={soKhoan}>
      <div className="title pl-5">{title}</div>
      <div className="so-khoan pl-5">{`(${soKhoan} ${t(
        "quanLyNoiTru.khoan"
      )})`}</div>
      {/* <PERSON><PERSON><PERSON> mục có dữ liệu mới hiển thị icon delete và edit */}
      {soKhoan > 0 && ![4, 5, 8, 9, 11, 12].includes(keyPanel) && (
        <SVG.IcDelete
          className="ml-5"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDeleteDichVu();
          }}
        />
      )}
      {soKhoan > 0 &&
        [6, 7, 11].includes(keyPanel) &&
        (!isEditing ? (
          <SVG.IcEdit
            className="ml-5"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onEditDichVu();
            }}
          />
        ) : (
          <SVG.IcSave
            className="ml-5"
            color={"var(--color-blue-primary)"}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onSaveDichVu();
            }}
          />
        ))}
      {keyPanel === 9 && (
        <a
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onXemPhieuKy();
          }}
          className="phieu-ky"
        >
          {t("quanLyNoiTru.xemCacPhieu")}
        </a>
      )}
      <SVG.IcReload
        className="ml-5"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onRefreshList();
        }}
      />
    </HeaderStyled>
  );
};

export default HeaderPanel;
