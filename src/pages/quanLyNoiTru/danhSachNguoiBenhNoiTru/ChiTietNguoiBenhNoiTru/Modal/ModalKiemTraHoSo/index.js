import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useMemo,
} from "react";
import { Collapse, Form, message, Tooltip } from "antd";
import { Main, CollapseWrapper } from "./styled";
import { useSelector, useDispatch } from "react-redux";
import { AlertMessage, ModalTemplate } from "components";
import IcArrow from "assets/images/khamBenh/icArrow.svg";
import { useConfirm, useEnum, useStore } from "hooks";
import HeaderPanel from "./components/HeaderPanel";
import TableDVChuaHoanThanh from "./components/TableDVKTChuaHoanThanh";
import { orderBy } from "lodash";
import { ENUM, LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";
import TableVatTuChuaTra from "./components/TableVatTuChuaTra";
import TableSuatAnChuaTra from "./components/TableSuatAnChuaTra";
import TableVatTuChuaLinh from "./components/TableVatTuChuaLinh";
import TableThoiGianCoKetQua from "./components/TableThoiGianCoKetQua";
import TableDVThieuThoiGian from "./components/TableDVThieuThoiGian";
import TableThoiGianYLenh from "./components/TableThoiGianYLenh";
import TableDvTrungThoiGian from "./components/TableDvTrungThoiGian";
import moment from "moment";
import { useTranslation } from "react-i18next";
import TableDVKTThieuT35 from "./components/TableDVKTThieuT35";
import TableThuocBHThieuT30 from "./components/TableThuocBHThieuT30";
import TableThuocDauSaoChuaHoiChan from "./components/TableThuocDauSaoChuaHoiChan";
import TableToDieuTriSaiThoiGianYLenh from "./components/TableToDieuTriSaiThoiGianYLenh";
import TableLoiTheBaoHiem from "./components/TableLoiTheBaoHiem";
import TablePhieuChuaKy from "./components/TablePhieuChuaKy";
import SelectDoiTuongKCB from "./components/SelectDoiTuongKCB";
import { SVG } from "assets";
import { showError } from "utils/message-utils";
import { toSafePromise } from "lib-utils";

const { Panel } = Collapse;

const ModalKiemTraHoSo = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const refModal = useRef(null);
  const refTableDVChuaHoanThanh = useRef(null);
  const refTableVatTuChuaLinh = useRef(null);
  const refTableThoiGianCoKetQua = useRef(null);
  const refTableVatTuChuaTra = useRef(null);
  const refTableSuatAnChuaTra = useRef(null);
  const refTableDVKTThieuT35 = useRef(null);
  const refTableThuocBHThieuT30 = useRef(null);
  const refTableToDieuTriSaiThoiGianYLenh = useRef(null);
  const refTableThoiGianTrungNhau = useRef(null);
  const refTablePhieuChuaKy = useRef(null);
  const refSelectDoiTuongKCB = useRef(null);
  const refTableThuocDauSaoChuaHoiChan = useRef(null);
  const { tenNb, tuoi2, thoiGianRaVien, gioiTinh } = useStore(
    "nbDotDieuTri.thongTinCoBan"
  );
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const raVien = thoiGianRaVien
    ? moment(thoiGianRaVien).format("DD/MM/YYYY HH:mm:ss")
    : "";
  const { openModalPhongGiuong } = props;
  const [state, _setState] = useState({ show: false, editKeys: [], thongTinHoSo: {} });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };


  const {
    nbDotDieuTri: {
      getThongTinCoBan, kiemTraRaVien, onUpdate },
    danhSachNguoiBenhNoiTru: {
      deleteDichVu,
      themThongTin,
      deleteToDieuTri,
      traDichVu,
      updateThoiGianYLenhToDieuTri,
    },
  } = useDispatch();



  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (state.show && state.nbDotDieuTriId) {
      getThongTinCoBan(state.nbDotDieuTriId)
    }
  }, [state.show, state.nbDotDieuTriId])


  const onRefreshList = async () => {
    const [e, thongTinHoSo] = await toSafePromise(
      kiemTraRaVien({
        id: state.nbDotDieuTriId,
        dsTrangThaiHoan: [0, 10, 20],
        ...state.addCheck,
      })
    );
    if (thongTinHoSo)
      setState({ thongTinHoSo })
    else
      showError(e?.message);
  };

  const onXemPhieuKy = (key) => {
    switch (key) {
      case 9:
        refTablePhieuChuaKy.current &&
          refTablePhieuChuaKy.current.xemCacPhieu();
        break;

      default:
        break;
    }
  };

  const listPanel = useMemo(() => {
    const _thongTinHoSo = {
      dsDvChuaHoanThanh: state.thongTinHoSo?.dsDvChuaHoanThanh || [],
      dsDvChuaLinh: state.thongTinHoSo?.dsDvChuaLinh || [],
      dsDvChuaTra: state.thongTinHoSo?.dsDvChuaTra || [],
      dsDvSuatAnChuaTra: state.thongTinHoSo?.dsDvSuatAnChuaTra || [],
      phanGiuong: state.thongTinHoSo?.phanGiuong,
      thoiGian: {
        dsDvSaiThoiGianTrongKhoa:
          state.thongTinHoSo?.dsDvSaiThoiGianThucHien?.dsDvSaiThoiGianTrongKhoa,
        dsDvSaiThoiGianVaoVienRaVien:
          state.thongTinHoSo?.dsDvSaiThoiGianThucHien?.dsDvSaiThoiGianVaoVienRaVien,
        dsDvSaiThoiGianCoKetQua: state.thongTinHoSo?.dsDvSaiThoiGianCoKetQua,
        dsDvThieuThoiGianCoKq: state.thongTinHoSo?.dsDvThieuThoiGianCoKq,
        dsToDieuTriSaiThoiGianYLenh: state.thongTinHoSo?.dsToDieuTriSaiThoiGianYLenh,
        dsDvTrungThoiGian: state.thongTinHoSo?.dsDvTrungThoiGian,
        dsDvLechThoiGian: state.thongTinHoSo?.dsDvLechThoiGian,
      },
      dsDvThieuT35: (state.thongTinHoSo?.dsDvThieuMucDich || []).filter(
        (x) => x.loaiDichVu != LOAI_DICH_VU.THUOC
      ),
      dsThuocBHThieuT30: (state.thongTinHoSo?.dsDvThieuMucDich || []).filter(
        (x) => x.loaiDichVu == LOAI_DICH_VU.THUOC
      ),
      dsTheBhChuaDenNgayApDung: state.thongTinHoSo?.dsTheBhChuaDenNgayApDung || [],
      dsPhieuChuaKy: state.thongTinHoSo?.dsPhieuChuaKy || [],
      maDoiTuongKcb: state.thongTinHoSo?.maDoiTuongKcb,
      dsThuocChuaHoiChan: state.thongTinHoSo?.dsThuocChuaHoiChan || [],
    };

    const _list = Object.keys(_thongTinHoSo).map((key) => {
      switch (key) {
        case "dsDvChuaHoanThanh":
          return {
            key: 1,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t("quanLyNoiTru.dichVuKyThuatChuaHoanThanh"),
            content: (
              <TableDVChuaHoanThanh
                ref={refTableDVChuaHoanThanh}
                data={_thongTinHoSo[key]}
              />
            ),
          };

        case "dsDvChuaLinh":
          return {
            key: 2,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: (
              <>
                {t(
                  "quanLyNoiTru.thuocVatTuMauHoaChatCpddChuaTaoPhieuLinhChuaDuyetLinh"
                )}
                {_thongTinHoSo[key]?.length !== 0 && <span style={{ color: "red" }}> {t(
                  "quanLyNoiTru.chuaHoanThanhSuDung")}</span>}
              </>
            ),
            content: (
              <TableVatTuChuaLinh
                ref={refTableVatTuChuaLinh}
                data={_thongTinHoSo[key]}
              />
            ),
          };

        case "dsDvChuaTra":
          return {
            key: 3,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t(
              "quanLyNoiTru.thuocVatTuMauHoaChatCpddChuaTaoPhieuTraChuaDuyetTra"
            ),
            content: (
              <TableVatTuChuaTra
                ref={refTableVatTuChuaTra}
                data={_thongTinHoSo[key]}
              />
            ),
          };

        case "phanGiuong":
          return {
            key: 4,
            title: t("quanLyNoiTru.loiLienQuanDenGiuong"),
            soKhoan: !_thongTinHoSo[key] ? 1 : 0,
            content: !_thongTinHoSo[key] ? (
              <div className="phan-giuong">
                <div className="text-err">
                  {`1. ${t("quanLyNoiTru.nguoiBenhChuaPhanGiuong")} ${state.khoaLamViec?.ten
                    ? `(${t("quanLyNoiTru.neuKhoaChoRaVienKhac", {
                      khoa: state.khoaLamViec?.ten,
                    })})`
                    : ""
                    }`}
                </div>
                <a
                  href="#!"
                  onClick={() => {
                    openModalPhongGiuong && openModalPhongGiuong();
                    onClose();
                  }}
                >
                  <span>{t("quanLyNoiTru.phanGiuongChoNguoiBenh")}</span>
                  <SVG.IcMoRong />
                </a>
              </div>
            ) : (
              ""
            ),
          };

        case "thoiGian":
          const _listThoiGian = Object.keys(_thongTinHoSo[key]).filter(
            (key2) =>
              _thongTinHoSo[key][key2] && _thongTinHoSo[key][key2].length > 0
          );

          return {
            key: 5,
            soKhoan: _listThoiGian?.length || 0,
            title: t("quanLyNoiTru.loiLienQuanDenThoiGian"),
            content: (
              <div>
                {_listThoiGian.map((key2) => {
                  const _thoiGianRaVien = state.addCheck?.thoiGianRaVien
                    ? state.addCheck?.thoiGianRaVien instanceof moment
                      ? moment(state.addCheck?.thoiGianRaVien).format(
                        "DD/MM/YYYY HH:mm:ss"
                      )
                      : state.addCheck?.thoiGianRaVien
                    : raVien;
                  switch (key2) {
                    case "dsDvSaiThoiGianTrongKhoa":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`1.1- ${t(
                              "quanLyNoiTru.loiThoiGianThucHienKhongNamTrongKhoa"
                            )}`}
                            &emsp;
                            <Tooltip title={t("common.xoa")}>
                              <SVG.IcDelete
                                className="ml-5"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  onDeleteDichVu(51);
                                }}
                              />
                            </Tooltip>
                            &emsp;
                            {!state.editKeys.includes(51) ? (
                              <Tooltip title={t("common.sua")}>
                                <SVG.IcEdit
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onEditDichVu(51);
                                  }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title={t("common.luu")}>
                                <SVG.IcSave
                                  color={"var(--color-blue-primary)"}
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onSaveDichVu(51);
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>
                          <TableThoiGianCoKetQua
                            data={_thongTinHoSo[key][key2]}
                            ref={refTableThoiGianCoKetQua}
                            keyThoiGian={key2}
                          />
                        </div>
                      );
                    case "dsDvSaiThoiGianVaoVienRaVien":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`1.2- ${t(
                              "quanLyNoiTru.loiThoiGianThucHienKhongNamTrongVaoVienRaVien"
                            )}`}
                            &emsp;
                            <Tooltip title={t("common.xoa")}>
                              <SVG.IcDelete
                                className="ml-5"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  onDeleteDichVu(52);
                                }}
                              />
                            </Tooltip>
                            &emsp;
                            {!state.editKeys.includes(52) ? (
                              <Tooltip title={t("common.sua")}>
                                <SVG.IcEdit
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onEditDichVu(52);
                                  }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title={t("common.luu")}>
                                <SVG.IcSave
                                  color={"var(--color-blue-primary)"}
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onSaveDichVu(52);
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>
                          <TableThoiGianCoKetQua
                            data={_thongTinHoSo[key][key2]}
                            ref={refTableThoiGianCoKetQua}
                            keyThoiGian={key2}
                          />
                        </div>
                      );

                    case "dsDvSaiThoiGianCoKetQua":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`2- ${t(
                              "quanLyNoiTru.loiThoiGianCoKetQuaKhongDuocSauThoiGianRaVien"
                            )}: ${_thoiGianRaVien}`}
                          </div>
                          <TableDVThieuThoiGian
                            data={_thongTinHoSo[key][key2]}
                          />
                        </div>
                      );

                    case "dsDvThieuThoiGianCoKq":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`3- ${t(
                              "quanLyNoiTru.loiDichVuKhongDuocThieuThoiGianCoKetQua"
                            )}`}
                          </div>
                          <TableThoiGianYLenh data={_thongTinHoSo[key][key2]} />
                        </div>
                      );

                    case "dsToDieuTriSaiThoiGianYLenh":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`4- ${t(
                              "quanLyNoiTru.loiToDieuTriCoThoiGianYLenhKhongDuocSauThoiGianRaVien"
                            )}: ${_thoiGianRaVien}`}
                            &emsp;
                            <Tooltip title={t("common.xoa")}>
                              <SVG.IcDelete
                                className="ml-5"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  onDeleteToDieuTri(54);
                                }}
                              />
                            </Tooltip>
                            &emsp;
                            {!state.editKeys.includes(54) ? (
                              <Tooltip title={t("common.sua")}>
                                <SVG.IcEdit
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onEditDichVu(54);
                                  }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title={t("common.luu")}>
                                <SVG.IcSave
                                  color={"var(--color-blue-primary)"}
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onSaveThoiGianYLenh(54);
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>
                          <TableToDieuTriSaiThoiGianYLenh
                            data={_thongTinHoSo[key][key2]}
                            ref={refTableToDieuTriSaiThoiGianYLenh}
                          />
                        </div>
                      );
                    case "dsDvTrungThoiGian":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`5- ${t(
                              "quanLyNoiTru.loiDichVuThoiGianKhongDuocCoThoiGianTrungNhau"
                            )}`}
                            &emsp;
                            {!state.editKeys.includes(55) ? (
                              <Tooltip title={t("common.sua")}>
                                <SVG.IcEdit
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onEditDichVu(55);
                                  }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title={t("common.luu")}>
                                <SVG.IcSave
                                  color={"var(--color-blue-primary)"}
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onSaveDichVu(55);
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>
                          <TableDvTrungThoiGian
                            data={_thongTinHoSo[key][key2]}
                            ref={refTableThoiGianTrungNhau}
                          />
                        </div>
                      );
                    case "dsDvLechThoiGian":
                      return (
                        <div className="thoi-gian-item">
                          <div className="text-err">
                            {`6- ${t(
                              "quanLyNoiTru.loiDichVuKhongDuocCoNgayThucHienKhacYlenh"
                            )}`}
                            &emsp;
                            {!state.editKeys.includes(56) ? (
                              <Tooltip title={t("common.sua")}>
                                <SVG.IcEdit
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onEditDichVu(56);
                                  }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title={t("common.luu")}>
                                <SVG.IcSave
                                  color={"var(--color-blue-primary)"}
                                  className="ml-5"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onSaveDichVu(56);
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>
                          <TableDvTrungThoiGian
                            data={_thongTinHoSo[key][key2]}
                            ref={refTableThoiGianTrungNhau}
                          />
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>
            ),
          };

        case "dsDvThieuT35":
          return {
            key: 6,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t(
              "quanLyNoiTru.dichVuKyThuatChuaDienMucDichSuDungTheoThongTu35"
            ),
            content: (
              <TableDVKTThieuT35
                ref={refTableDVKTThieuT35}
                data={_thongTinHoSo[key]}
              />
            ),
          };

        case "dsThuocBHThieuT30":
          return {
            key: 7,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t(
              "quanLyNoiTru.thuocBaoHiemChuaDienMucDichSuDungTheoThongTu20"
            ),
            content: (
              <TableThuocBHThieuT30
                ref={refTableThuocBHThieuT30}
                data={_thongTinHoSo[key]}
              />
            ),
          };

        case "dsTheBhChuaDenNgayApDung":
          return {
            key: 8,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t("quanLyNoiTru.loiLienQuanToiTheBaoHiem"),
            content: (
              <div className="thoi-gian-item">
                {!!_thongTinHoSo[key]?.length && (
                  <div className="text-err">
                    {`1. ${t(
                      "quanLyNoiTru.loiThoiGianApDungTheBaoHiemLonHonNgayRaVien"
                    )} ${thoiGianRaVien
                      ? moment(thoiGianRaVien).format(
                        "DD/MM/YYYY HH:mm:ss"
                      )
                      : ""
                      }`}
                    &emsp;
                  </div>
                )}
                <TableLoiTheBaoHiem
                  ref={refTableThuocBHThieuT30}
                  data={_thongTinHoSo[key]}
                  onRefreshList={onRefreshList}
                />
              </div>
            ),
          };

        case "dsPhieuChuaKy":
          return {
            key: 9,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t("quanLyNoiTru.phieuBieuMauThieuKySoKyDienTu"),
            content: (
              <TablePhieuChuaKy
                ref={refTablePhieuChuaKy}
                data={_thongTinHoSo[key]}
                nbDotDieuTriId={state.nbDotDieuTriId}
              />
            ),
          };
        case "dsDvSuatAnChuaTra":
          return {
            key: 10,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t("quanLyNoiTru.suatAnChuaTaoPhieuTraChuaDuyetTra"),
            content: (
              <TableSuatAnChuaTra
                ref={refTableSuatAnChuaTra}
                data={_thongTinHoSo[key]}
              />
            ),
          };
        case "maDoiTuongKcb":
          return {
            key: 11,
            soKhoan: _thongTinHoSo[key] ? 1 : 0,
            title: t("quanLyNoiTru.khaiBaoThieuThongTinDoiTuongKcb"),
            content: (
              <SelectDoiTuongKCB
                ref={refSelectDoiTuongKCB}
                data={_thongTinHoSo[key]}
                nbDotDieuTriId={state.nbDotDieuTriId}
              />
            ),
          };
        case "dsThuocChuaHoiChan":
          return {
            key: 12,
            soKhoan: _thongTinHoSo[key]?.length || 0,
            title: t("quanLyNoiTru.thuocDauSaoChuaHoiChan"),
            content: (
              <TableThuocDauSaoChuaHoiChan
                ref={refTableThuocDauSaoChuaHoiChan}
                data={_thongTinHoSo[key]}
              />
            ),
          };

        default:
          return {};
      }
    });

    return orderBy(_list, ["key"], "asc");
  }, [state.thongTinHoSo, state.editKeys]);

  useImperativeHandle(ref, () => ({
    show: async ({ khoaLamViec, nbDotDieuTriId, thongTinHoSo, ...rest }) => {
      if (!nbDotDieuTriId) {
        showError(t("common.thieuThongTinNb"));
        return;
      }
      const addCheck = rest || {};
      setState({
        show: true,
        nbDotDieuTriId,
        khoaLamViec,
        thongTinHoSo,
        addCheck
      });
      if (!thongTinHoSo) {
        const [e, s] = await toSafePromise(
          kiemTraRaVien({
            id: nbDotDieuTriId,
            dsTrangThaiHoan: [0, 10, 20],
            ...addCheck,
          }));
        if (s) {
          setState({
            thongTinHoSo: s
          })
        } else {
          showError(e?.message);
        }
      }
    },
  }));

  const onDeleteToDieuTri = (key) => {
    let listToDieuTri = [];

    switch (key) {
      case 54:
        listToDieuTri = refTableToDieuTriSaiThoiGianYLenh.current
          ? refTableToDieuTriSaiThoiGianYLenh.current.getDeleteDvList()
          : null;
        break;
      default:
        break;
    }

    if (!listToDieuTri || listToDieuTri.length == 0) {
      message.error(t("quanLyNoiTru.vuiLongChonToDieuTriDeXoa"));
      return;
    }
    showConfirm(
      {
        title: t("common.canhBao"),
        content: t("quanLyNoiTru.banCoChacChanMuonXoaToDieuTriDaChon", {
          num: listToDieuTri.length,
        }),
        cancelText: t("common.huy"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
        okText: t("common.dongY"),
      },
      () => {
        listToDieuTri.forEach((element) => {
          deleteToDieuTri(element.id).then(() => {
            onRefreshList();
          });
        });
      }
    );
  };

  const onDeleteDichVu = (key) => {
    let listDeleteDv = [];

    switch (key) {
      case 1:
        listDeleteDv = refTableDVChuaHoanThanh.current
          ? refTableDVChuaHoanThanh.current.getDeleteDvList()
          : null;
        break;
      case 2:
        listDeleteDv = refTableVatTuChuaLinh.current
          ? refTableVatTuChuaLinh.current.getDeleteDvList()
          : null;
        break;
      case 3:
        listDeleteDv = refTableVatTuChuaTra.current
          ? refTableVatTuChuaTra.current.getDeleteDvList()
          : null;
        break;
      case 10:
        listDeleteDv = refTableSuatAnChuaTra.current
          ? refTableSuatAnChuaTra.current.getDeleteDvList()
          : null;
        break;
      case 51:
      case 52:
        listDeleteDv = refTableThoiGianCoKetQua.current
          ? refTableThoiGianCoKetQua.current.getDeleteDvList()
          : null;
        break;
      case 6:
        listDeleteDv = refTableDVKTThieuT35.current
          ? refTableDVKTThieuT35.current.getDeleteDvList()
          : null;
        break;
      case 7:
        listDeleteDv = refTableThuocBHThieuT30.current
          ? refTableThuocBHThieuT30.current.getDeleteDvList()
          : null;
        break;
      default:
        break;
    }

    if (!listDeleteDv || listDeleteDv.length == 0) {
      message.error(
        `${t("quanLyNoiTru.vuiLongChonDichVuDe", {
          title: key == 3 ? t("quanLyNoiTru.tra") : t("common.xoa"),
        })}!`
      );
      return;
    }
    showConfirm(
      {
        title: t("common.canhBao"),
        content: t("quanLyNoiTru.banCoChacChanMuonTitleDichVuDaChon", {
          title: `${key == 3 ? t("quanLyNoiTru.tra") : t("common.xoa")} ${listDeleteDv.length
            }`,
        }),
        cancelText: t("common.huy"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
        okText: t("common.dongY"),
      },
      () => {
        const apiCall = key == 3 ? traDichVu : deleteDichVu;
        apiCall(listDeleteDv)
          .then(() => {
            message.success(
              t("quanLyNoiTru.titleThanhCongDichVu", {
                title: key == 3 ? t("quanLyNoiTru.tra") : t("common.xoa"),
              })
            );
            onRefreshList();
          })
          .catch((errArr) => {
            if (errArr) {
              const errTxt = errArr
                .filter((x) => x.code != 0)
                .map((x) => `${x?.nbDichVu?.dichVu?.ten}: ${x.message}`)
                .join(", <br />");

              showConfirm(
                {
                  title: t("quanLyNoiTru.khongTheXoaDuocNhungDichVuDuoiDay"),
                  content: errTxt,
                  cancelText: t("common.dong"),
                  classNameOkText: "button-error",
                  showImg: true,
                  typeModal: "error",
                },
                () => { }
              );

              if (errArr.some((x) => x.code == 0)) {
                onRefreshList();
              }
            }
          });
      }
    );
  };

  const onEditDichVu = (key) => {
    let editKeys = [...state.editKeys, key];
    setState({ editKeys });

    switch (key) {
      case 51:
      case 52:
        refTableThoiGianCoKetQua.current &&
          refTableThoiGianCoKetQua.current.setEditDV(true);
        break;

      case 54:
        refTableToDieuTriSaiThoiGianYLenh.current &&
          refTableToDieuTriSaiThoiGianYLenh.current.setEditDV(true);
        break;
      case 55:
      case 56:
        refTableThoiGianTrungNhau.current &&
          refTableThoiGianTrungNhau.current.setEditDV(true);
        break;
      case 6:
        refTableDVKTThieuT35.current &&
          refTableDVKTThieuT35.current.setEditDV(true);
        break;
      case 7:
        refTableThuocBHThieuT30.current &&
          refTableThuocBHThieuT30.current.setEditDV(true);
        break;
      case 11:
        refSelectDoiTuongKCB.current &&
          refSelectDoiTuongKCB.current.setEditDV(true);
        break;
      default:
        break;
    }
  };

  const onSaveThoiGianYLenh = (key) => {
    let editKeys = (state.editKeys || []).filter((x) => x != key);
    setState({ editKeys });

    let listEditDv = [];

    switch (key) {
      case 54:
        listEditDv = refTableToDieuTriSaiThoiGianYLenh.current
          ? refTableToDieuTriSaiThoiGianYLenh.current.getEditDvList()
          : null;
        refTableToDieuTriSaiThoiGianYLenh.current &&
          refTableToDieuTriSaiThoiGianYLenh.current.setEditDV(false);
        break;

      default:
        break;
    }

    updateThoiGianYLenhToDieuTri(listEditDv)
      .then(() => {
        message.success(t("quanLyNoiTru.suaThongTinToDieuTriThanhCong"));
        onRefreshList();
      })
      .catch((err) => {
        (err || [])
          .filter((x) => x.code != 0)
          .forEach((element) => {
            message.error(
              element?.message || t("kiosk.coLoiXayRaVuiLongThuLai")
            );
          });
      });
  };

  const onUpdateDoiTuongKcb = (maDoiTuongKcbId) => {
    let params = {
      maDoiTuongKcbId,
      id: state.nbDotDieuTriId,
    };
    onUpdate(params)
      .then(() => {
        message.success(t("pttt.themThongTinThanhCong"));
        onRefreshList();
      })
      .catch((err) => {
        message.error(err?.message || t("kiosk.coLoiXayRaVuiLongThuLai"));
      });
  };

  const onSaveDichVu = (key) => {
    let editKeys = (state.editKeys || []).filter((x) => x != key);
    setState({ editKeys });

    let listEditDv = [];

    switch (key) {
      case 51:
      case 52:
        listEditDv = refTableThoiGianCoKetQua.current
          ? refTableThoiGianCoKetQua.current.getEditDvList()
          : null;
        refTableThoiGianCoKetQua.current &&
          refTableThoiGianCoKetQua.current.setEditDV(false);
        break;
      case 55:
      case 56:
        listEditDv = refTableThoiGianTrungNhau.current
          ? refTableThoiGianTrungNhau.current.getEditDvList()
          : null;
        refTableThoiGianTrungNhau.current &&
          refTableThoiGianTrungNhau.current.setEditDV(false);
        break;
      case 6:
        listEditDv = refTableDVKTThieuT35.current
          ? refTableDVKTThieuT35.current.getEditDvList()
          : null;
        refTableDVKTThieuT35.current &&
          refTableDVKTThieuT35.current.setEditDV(false);
        break;

      case 7:
        listEditDv = refTableThuocBHThieuT30.current
          ? refTableThuocBHThieuT30.current.getEditDvList()
          : null;
        refTableThuocBHThieuT30.current &&
          refTableThuocBHThieuT30.current.setEditDV(false);
        break;
      case 11:
        let maDoiTuongKcbId = refSelectDoiTuongKCB.current
          ? refSelectDoiTuongKCB.current.getEditDvList()
          : null;
        refSelectDoiTuongKCB.current &&
          refSelectDoiTuongKCB.current.setEditDV(false);
        onUpdateDoiTuongKcb(maDoiTuongKcbId);
        return;

      default:
        break;
    }
    themThongTin(listEditDv)
      .then(() => {
        message.success(t("pttt.themThongTinThanhCong"));
        onRefreshList();
      })
      .catch((err) => {
        (err || [])
          .filter((x) => x.code != 0)
          .forEach((element) => {
            message.error(
              element?.message || t("kiosk.coLoiXayRaVuiLongThuLai")
            );
          });
      });
  };

  const onClose = () => {
    form.resetFields();
    setState({ show: false });
  };

  const rightTitle = useMemo(() => {
    const tenGioiTinh = (listGioiTinh || []).find(
      (item) => item.id === gioiTinh
    )?.ten;
    return [tenNb, tenGioiTinh, tuoi2].filter(item => item).join(" - ");
  }, [tenNb, gioiTinh, listGioiTinh, tuoi2])

  return (
    <ModalTemplate
      width={"90%"}
      closable={true}
      ref={refModal}
      title={t("quanLyNoiTru.kiemTraHoSo")}
      rightTitle={rightTitle}
      onCancel={onClose}
    >
      <Main>
        <AlertMessage
          keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_POPUP_KIEM_TRA_HO_SO}
        />
        <div className="collapse-content">
          <CollapseWrapper
            bordered={false}
            defaultActiveKey={[]}
            expandIcon={({ isActive }) => (
              <IcArrow
                style={
                  isActive
                    ? { transform: "rotate(90deg)" }
                    : { transform: "unset" }
                }
              />
            )}
            className="site-collapse-custom-collapse"
          >
            {(listPanel || []).map((panel) => {
              return (
                <Panel
                  key={panel.key}
                  header={
                    <HeaderPanel
                      {...panel}
                      keyPanel={panel.key}
                      onRefreshList={onRefreshList}
                      onDeleteDichVu={() => onDeleteDichVu(panel.key)}
                      onEditDichVu={() => onEditDichVu(panel.key)}
                      onSaveDichVu={() => onSaveDichVu(panel.key)}
                      onXemPhieuKy={() => onXemPhieuKy(panel.key)}
                      isEditing={state.editKeys.includes(panel.key)}
                    />
                  }
                >
                  {panel.content}
                </Panel>
              );
            })}
          </CollapseWrapper>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalKiemTraHoSo);
