import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import { Button, ModalTemplate } from "components";
import { HOTKEY, LOAI_IN } from "constants/index";
import { SVG } from "assets";
import { combineUrlParams } from "utils";
import { useLoading } from "hooks";
import printProvider from "data-access/print-provider";

const ModalViewPhieu = (props, ref) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({
    show: false,
    loai: null,
    hideSubmitButton: false,
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };
  const refModal = useRef(null);
  const refResolve = useRef(null);
  const refReject = useRef(null);
  const refOk = useRef(null);
  const refCancel = useRef(null);
  const setContentRef = useRef();
  const refTimeoutResolve = useRef(null);

  useImperativeHandle(ref, () => ({
    show: (
      { allowHideLoading = true, hideSubmitButton = false, ...data } = {},
      onOk,
      onCancel
    ) => {
      return new Promise((resolve, reject) => {
        refResolve.current = resolve;
        refReject.current = reject;
        refOk.current = onOk;
        refCancel.current = onCancel;
        clearTimeoutResolve();
        const params = {
          ...data,
          scale: window.screen.width > 1366 ? 1.7 : 1.2,
          khongDongTab: true,
          time: new Date().getTime(), //thêm để không bị cache trang web
        };
        const url = combineUrlParams(`/print-file/bang-ke/${data.id}`, {
          ...params,
        });
        console.log(data);
        if (
          [LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(data.loaiIn)
        ) {
          setTimeoutResolve(); //sau 30s thì tự resolve để xử lý trong trường hợp treo pdf thì resolve luôn
          setState({
            show: false,
            src: `${url}&fromIframe=true&notPrint=false`,
            loaiIn: data.loaiIn,
            allowHideLoading,
          });
          showLoading();
        } else {
          setState({
            show: true,
            src: `${url}&notPrint=true`,
            allowHideLoading,
            loaiIn: null,
            hideSubmitButton,
          });
        }
      });
    },
  }));

  const onOk = (isOk) => () => {
    if (isOk) {
      setContentRef.current.contentWindow.document
        .getElementById("btn-print")
        .click();

      setState({ show: false });
      if (refOk.current) refOk.current();
    } else {
      setState({ show: false, src: "" });
      if (refCancel.current) refCancel.current();
    }
  };
  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  const clearTimeoutResolve = () => {
    refTimeoutResolve.current && clearTimeout(refTimeoutResolve.current);
    refTimeoutResolve.current = null;
  };

  const setTimeoutResolve = () => {
    clearTimeoutResolve();
    refTimeoutResolve.current = setTimeout(() => {
      refResolve.current && refResolve.current();
    }, 30000);
  };

  useEffect(() => {
    if (state.show) {
      // nếu là show popup thì hideLoading đi.
      hideLoading();
      refModal.current?.show();
    } else {
      refResolve.current && refResolve.current();
      refModal.current?.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if ([LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(state.loaiIn)) {
      const handler = async (ev) => {
        const { data = {} } = ev || {};
        switch (data.type) {
          case "PDF-FILE":
            const dataPrint = data.value;
            if (dataPrint) {
              // nếu có trả về file pdf thì in file, ngược lại thì không làm gì
              await printProvider.printPdf([dataPrint], {
                isEditor: true,
                mergePdfFile: dataPrint?.file?.pdf,
              });
            }
            clearTimeoutResolve();
            refResolve.current && refResolve.current();
            if (state.allowHideLoading) {
              hideLoading();
            }
            break;
          case "FINISH":
            if (state.allowHideLoading) {
              hideLoading();
            }
        }
      };

      window.addEventListener("message", handler);

      // Don't forget to remove addEventListener
      return () => window.removeEventListener("message", handler);
    }
  }, [state.loaiIn, state.allowHideLoading]);

  if ([LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(state.loaiIn)) {
    return (
      <div style={{ display: "none" }}>
        <iframe ref={setContentRef} src={state.src}></iframe>
      </div>
    );
  }

  return (
    <ModalTemplate
      ref={refModal}
      hotKeys={hotKeys}
      onCancel={onOk(false)}
      title={t("quanLyNoiTru.bangKeChiPhi")}
      width={window.screen.width > 1366 ? 1600 : 1200}
      actionLeft={
        <Button.QuayLai onClick={onOk(false)}>
          {t("common.quayLai")} [ESC]{" "}
        </Button.QuayLai>
      }
      {...(!state.hideSubmitButton && {
        actionRight: (
          <Button
            type="primary"
            minWidth={100}
            onClick={onOk(true)}
            rightIcon={<SVG.IcSave />}
          >
            <span> {t("common.in")}</span>{" "}
          </Button>
        ),
      })}
    >
      <iframe
        ref={setContentRef}
        width={window.screen.width > 1366 ? 1600 : 1200}
        style={{
          border: "none",
        }}
        height={window.screen.width > 1366 ? 700 : 500}
        src={state.src}
      ></iframe>
    </ModalTemplate>
  );
};
export default forwardRef(ModalViewPhieu);
