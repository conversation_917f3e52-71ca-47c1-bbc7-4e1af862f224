import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Page, ThongTinBenhNhan, <PERSON>bs, Button, DateTimePicker } from 'components'
import { useConfirm, useGuid, useLoading, useQueryString, useStore, useThietLap } from 'hooks'
import { useTranslation } from 'react-i18next';
import { Main } from './styled';
import ThongTinChung from "./components/ThongTinChung";
import Toan<PERSON>han from "./components/ToanThan";
import HoHap from "./components/HoHap";
import TuanHoan from "./components/TuanHoan";
import TietNieu from "./components/TietNieu";
import TieuHoa from "./components/TieuHoa";
import GiacNgu from "./components/GiacNgu";
import ThanKinh from "./components/ThanKinh";
import CoXuong from "./components/CoXuong";
import VetThuong from "./components/VetThuong";
import DanLuu from "./components/DanLuu";
import OngThong from "./components/OngThong";
import TheoDoiKhac from "./components/TheoDoiKhac";
import MoKhiQuan from "./components/MoKhiQuan";
import CanThiepDieuDuong from "./components/CanThiepDieuDuong";
import { getCanThiep } from "./components/CanThiepDieuDuong/CanThiep";
import ChanDoanDieuDuong from "./components/ChanDoanDieuDuong";
import BanGiao from "./components/BanGiao";
import GhiChu from "./components/GhiChu";
import { Timeline, Row, Col, Card, message } from "antd";
import { cloneDeep, flatten, set, isEmpty, get } from 'lodash';
import { SVG } from "assets";
import { useDispatch } from 'react-redux';
import { useParams, useLocation } from "react-router-dom";
import editorProvider from 'data-access/editor-provider';
import moment from 'moment';
import useSign from 'components/editor/cores/ImageSign/useSign';
import { showError } from 'utils/message-utils';
import { useHistory } from 'react-router-dom';
import { toSafePromise } from 'lib-utils';
import ModalSelectSinhHieu from 'components/editor/cores/Components/ModalSelectSinhHieu';
import SinhHieu from '../PhieuChamSocCap1CR/components/SinhHieu';
import { THIET_LAP_CHUNG } from 'constants/index';
import { combineUrlParams } from 'utils';
export const NUM_OF_COLS = 5;




export default function index() {
    const { showLoading, hideLoading } = useLoading();
    const { showAsyncConfirm, showConfirm } = useConfirm();
    const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan")
    const history = useHistory();
    const refModalSinhHieu = useRef(null);
    const refFormData = useRef(null);
    const { search, pathname } = useLocation();
    const { id, maBaoCao } = useParams();
    const editorId = useGuid();
    const queries = useMemo(() => {
        const queries = {};
        search
            .substring(1)
            .split("&")
            .forEach((item) => {
                const arr = item.split("=");
                if (arr[0] != "printNewTab")
                    queries[arr[0]] = arr[1];
            });
        return queries;
    }, [search]);
    const { t } = useTranslation();
    const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId", "");
    const [khoaChiDinhId] = useQueryString("khoaChiDinhId");
    const [dataSO_BANG_TOI_DA_TRONG_PHIEU_CHAM_SOC_CAP_23] = useThietLap(THIET_LAP_CHUNG.SO_BANG_TOI_DA_TRONG_PHIEU_CHAM_SOC_CAP_23, "5")

    const { files: {
        getBaoCaoByMaBaoCao,
        deletePhieuEditor
    }
    } = useDispatch();
    // const signStatus = useEditor(editorId, "signStatus", {});

    const [state, _setState] = useState({ data: null, dsChiTiet: [], chiTiet: {} });
    const setState = (newState) => {
        _setState((prevState) => ({
            ...prevState,
            ...newState,
        }));
    };
    const handleSelect = (item) => async () => {
        if (state.changed && !dataSignDDTH) {
            await onSave(item);
        } else {
            setState({
                chiTiet: cloneDeep(item),
            });
        }
    };

    const onChange = (arrayKey, value, saveToState = true) => {
        const chiTiet = state.chiTiet;
        set(chiTiet, arrayKey.join("."), value);
        if ("loaiTriGiac" == arrayKey[0]) {
            chiTiet.triGiac = null;
            chiTiet.glassgow = null;
        }
        if (saveToState)
            setState({ chiTiet: { ...chiTiet }, changed: true });
    }

    const onChangeMultipleKey = (arrKeyValue) => {
        const chiTiet = state.chiTiet;
        arrKeyValue.forEach(keyValue => {
            const { arrayKey, value } = keyValue;
            onChange(arrayKey, value, false);
        })
        setState({ chiTiet: { ...chiTiet }, changed: true });
    }

    const onChangeState = (key) => (value) => {
        setState({ [key]: value, changed: true })
    }

    const viTriKyDieuDuongThucHien = useMemo(() => {
        return (state.chiTiet.indexTheoDoi * 10 + 1) * NUM_OF_COLS + state.chiTiet?.indexChiTiet + 1;
    }, [state.chiTiet])

    const viTriKyDieuNhanBanGiao = useMemo(() => {
        return (state.chiTiet.indexTheoDoi * 10 + 0) * NUM_OF_COLS + state.chiTiet?.indexChiTiet + 1;
    }, [state.chiTiet])

    const {
        onHuyKyPhieu,
        showBtnSign,
        isShowHuyKy,
        getUrlBase64,
        onKyPhieu,
        contentAlign,
        positionCa,
        checkShowbtnHuyKy,
        dataSign: dataSignDDTH,
        checkPermissonSign,
        onTrinhKy,
        refModalPatientSign,
        refModalTrinhKy,
        renderTenChanKy,
        onTuChoiKyPhieu,
        isShowBtnTuChoiKy,
        renderImageSign,
        renderLabelThoiGianKy,
        checkTrangThaiKy,
    } = useSign({
        editorId,
        itemProps: {
            fontSize: 12,
            capKy: 1,
            loaiKy: 1,
            width: 60,
            height: 40,
            showCa: false,
            isMultipleSign: true,
            showPatientSign: false,
            viTri: viTriKyDieuDuongThucHien,
            customText: "Ký điều dưỡng thực hiện",
            allowReset: true,
            alwaysShow: true,
            dataSign: {
                id: state.data?.id,
                soPhieu: state.data?.soPhieu,
                lichSuKyId: state.data?.lichSuKy?.id,
                duLieu: refFormData.current
            },
        },
        form: { lichSuKy: state.data?.lichSuKy, },
        mode: "",
        component: {
            props: {
                // disableIfSigned: true 
            }
        }
    });

    const {
        onHuyKyPhieu: onHuyKyPhieuBanGiao,
        showBtnSign: showBtnSignBanGiao,
        isShowHuyKy: isShowHuyKyBanGiao,
        getUrlBase64: getUrlBase64BanGiao,
        onKyPhieu: onKyPhieuBanGiao,
        contentAlign: contentAlignBanGiao,
        positionCa: positionCaBanGiao,
        checkShowbtnHuyKy: checkShowbtnHuyKyBanGiao,
        dataSign: dataSignBanGiao,
        checkPermissonSign: checkPermissonSignBanGiao,
        onTrinhKy: onTrinhKyBanGiao,
        refModalPatientSign: refModalPatientSignBanGiao,
        refModalTrinhKy: refModalTrinhKyBanGiao,
        renderTenChanKy: renderTenChanKyBanGiao,
        onTuChoiKyPhieu: onTuChoiKyPhieuBanGiao,
        isShowBtnTuChoiKy: isShowBtnTuChoiKyBanGiao,
        renderImageSign: renderImageSignBanGiao,
        renderLabelThoiGianKy: renderLabelThoiGianKyBanGiao,
        checkTrangThaiKy: checkTrangThaiKyBanGiao,
    } = useSign({
        editorId,
        itemProps: {
            fontSize: 12,
            capKy: 1,
            loaiKy: 1,
            width: 60,
            height: 40,
            showCa: false,
            isMultipleSign: true,
            showPatientSign: false,
            viTri: viTriKyDieuNhanBanGiao,
            customText: "Ký điều dưỡng nhận bàn giao ",
            allowReset: true,
            alwaysShow: true,
            dataSign: {
                id: state.data?.id,
                soPhieu: state.data?.soPhieu,
                lichSuKyId: state.data?.lichSuKy?.id,
                duLieu: refFormData.current
            },
        },
        form: { lichSuKy: state.data?.lichSuKy, },
        mode: "",
        component: {
            props: {
                // disableIfSigned: true 
            }
        }
    });

    useEffect(() => {
        if (nbDotDieuTriId) {
            getData({ id, maBaoCao, queries: queries })
        }
    }, [id, nbDotDieuTriId, editorId, search]);

    // useEffect(() => {
    //     if (!state.ngayThucHien && thoiGianThucHien && !id) {
    //         setState({ ngayThucHien: moment(thoiGianThucHien) });
    //     }
    // }, [thoiGianThucHien, id, state.ngayThucHien])

    useEffect(() => {
        if (state.chiTiet)
            checkTrangThaiKy();
    }, [viTriKyDieuDuongThucHien, state.chiTiet])

    useEffect(() => {
        if (state.chiTiet)
            checkTrangThaiKyBanGiao();
    }, [viTriKyDieuNhanBanGiao, state.chiTiet])


    const getData = async ({ id, maBaoCao, queries }) => {
        showLoading();
        const baoCao = await getBaoCaoByMaBaoCao(editorId, {
            maBaoCao,
            id,
            queries,
        });
        const data = await editorProvider.getFormDataEMR({
            api: baoCao?.api,
            id,
            queries,
        });
        if (data?.code === 0) {
            xuLyData({ data, baoCao });
        }
        hideLoading();
    }

    const xuLyData = async ({ data, baoCao, currentChiTiet }) => {
        const fileData = data?.data || {};
        // let ngayThucHien = null;
        if (!id && fileData.id) {
            history.replace(pathname + "/" + fileData?.id + search);
        }
        if (!fileData?.dsTheoDoi?.length)
            fileData.dsTheoDoi = [{}];
        let dsChiTiet = flatten((fileData?.dsTheoDoi || []).map((item, index1) => {
            // if (!ngayThucHien)
            //     ngayThucHien = item.ngayThucHien;

            if (!item.dsChiTiet)
                item.dsChiTiet = [];
            (new Array(5).fill({})).forEach((item2, index) => {
                item.dsChiTiet[index] = item.dsChiTiet[index] || {}
            })

            return item.dsChiTiet?.map((chiTiet, index2) => {
                chiTiet = chiTiet || {};
                chiTiet.banGiao2 = item.banGiao2;
                chiTiet.ghiChu2 = item.ghiChu;
                chiTiet.khungChanDoanDd0 = item.dsChiTiet?.[0]?.khungChanDoanDd || []
                chiTiet.ctVanDe = item.dsChiTiet?.[0]?.ctVanDe || "";
                chiTiet.ctMucTieu = item.dsChiTiet?.[0]?.ctMucTieu || "";
                chiTiet.ngayTheoDoi = item?.ngayThucHien;
                chiTiet.indexTheoDoi = index1;
                chiTiet.indexChiTiet = index2
                //nếu có dữ liệu trong theoDoi thì dùng, còn không thì convert từ chi tiet ra.
                chiTiet.thucHienThuoc3 = !item.thucHienThuoc3?.length ? getCanThiep("thucHienThuoc", item.dsChiTiet) : item.thucHienThuoc3;
                chiTiet.thucHienCls3 = !item.thucHienCls3?.length ? getCanThiep("thucHienCls", item.dsChiTiet) : item.thucHienCls3;
                chiTiet.chamSocDieuDuong3 = !item.chamSocDieuDuong3?.length ? getCanThiep("chamSocDieuDuong", item.dsChiTiet) : item.chamSocDieuDuong3;
                return chiTiet;
            });
        }));

        dsChiTiet = dsChiTiet.map((chiTiet, index) => {
            chiTiet.sttChiTiet = index;
            return chiTiet;
        });

        let chiTiet = dsChiTiet?.[0] || {};
        if (currentChiTiet)
            chiTiet = dsChiTiet?.find(item => item.sttChiTiet === currentChiTiet.sttChiTiet
                && item.indexTheoDoi == item.indexTheoDoi) || dsChiTiet?.[0] || {};

        refFormData.current = fileData;
        setState({
            changed: false,
            baoCao,
            data: fileData,
            dsChiTiet,
            chiTiet: cloneDeep(chiTiet),
            ghiRo: fileData.ghiRo,
            tienSuDiUng: fileData.tienSuDiUng,
            ghiChu: fileData.dsTheoDoi?.[0]?.ghiChu,
            // ngayThucHien: ngayThucHien ? moment(ngayThucHien) : null
        });
    }

    const thoiGian = useMemo(() => {
        return state.chiTiet.thoiGianThucHien ? moment(state.chiTiet.thoiGianThucHien) : new moment();
    }, [state.chiTiet])

    const readOnly = useMemo(() => {
        return !!dataSignDDTH;
    }, [dataSignDDTH])

    const readOnlyBanGiao = useMemo(() => {
        return !!dataSignBanGiao;
    }, [dataSignBanGiao])


    const onChangeThoiGian = (value) => {
        if (value && thoiGian.diff(value, 'minutes') != 0) {
            // const newMoment = moment(state.ngayThucHien)
            //     .set({
            //         hour: value.hour(),
            //         minute: value.minute(),
            //         second: 0,
            //         millisecond: 0,
            //     });
            state.chiTiet.thoiGianThucHien = value;
            setState({ chiTiet: { ...state.chiTiet }, changed: true });
        }
    }

    const onDeleteForm = async () => {
        const confirm = await showAsyncConfirm(
            {
                title: t("common.xoaDuLieu"),
                content: t("editor.banCoMuonXoaBanGhiNay"),
                cancelText: t("common.quayLai"),
                okText: t("common.dongY"),
                classNameOkText: "button-error",
                showImg: true,
                showBtnOk: true,
            });
        if (confirm.action == "ok") {
            try {
                showLoading();
                await deletePhieuEditor({ api: state.baoCao?.api, id: state.data?.id });
                const url = pathname.substring(0, pathname.lastIndexOf('/'));
                history.push(url + search)
            } catch (error) {
            } finally {
                hideLoading();
            }
        }
    }
    const onSave = async (nextItem) => {
        if (!state.changed && state.chiTiet.thoiGianThucHien) {
            message.error("Mốc thời gian bạn chọn không có thông tin nào thay đổi");
            return;
        }
        // if (dataSignDDTH) {
        //     showError("Dữ liệu thời gian này đã được ký, không thể chỉnh sửa");
        //     return;
        // }
        if (!nextItem)
            nextItem = state.chiTiet;
        // if (!state.ngayThucHien) {
        //     message.error("Vui lòng nhập ngày thực hiện");
        //     return;
        // }
        showLoading();

        try {
            const [e, data] = await toSafePromise(editorProvider
                .getFormDataEMR({
                    api: state.baoCao?.api,
                    id,
                    queries,
                }));
            if (e) {
                showError(e?.message);
                return;
            }
            let payload = cloneDeep(data.data);
            if (!payload.dsTheoDoi?.[state.chiTiet?.indexTheoDoi]?.dsChiTiet) // nếu chưa có dữ liệu thì khởi tạo sẵn 5 dòng.
                set(payload, `dsTheoDoi[${state.chiTiet?.indexTheoDoi}].dsChiTiet`, new Array(5).fill({}).map((chiTiet, idx) => {
                    return {};
                }));
            //nếu lưu cột mà đang chưa nhập thời gian -> thì mặc định gửi lên thời gian hiện tại
            if (!state.chiTiet.thoiGianThucHien)
                state.chiTiet.thoiGianThucHien = new Date();
            //nếu chỉ số sống chưa có thông tin khoa chỉ định -> thì truyền khoa chỉ định id vào
            if (!state.chiTiet.chiSoSong?.khoaChiDinhId)
                set(state.chiTiet, "chiSoSong.khoaChiDinhId", khoaChiDinhId);
            if (!state.chiTiet?.khoaDieuDuongThucHienId) {
                state.chiTiet.khoaDieuDuongThucHienId = khoaChiDinhId;
            }
            payload = set(payload, `dsTheoDoi[${state.chiTiet?.indexTheoDoi}].dsChiTiet[${state.chiTiet?.indexChiTiet}]`, state.chiTiet);
            payload.dsTheoDoi.map((item, indexTheoDoi) => {
                if (state.chiTiet.indexTheoDoi == indexTheoDoi) {
                    (state.chiTiet.khungChanDoanDd0 || []).forEach((khung, index) => {
                        //nếu chi tiết đầu tiên chưa có khung chẩn đoán nào, thì khởi tạo rỗng
                        if (!get(payload, `dsTheoDoi[${indexTheoDoi}].dsChiTiet[0].khungChanDoanDd[${index}]`))
                            set(payload, `dsTheoDoi[${indexTheoDoi}].dsChiTiet[0].khungChanDoanDd[${index}]`, {});
                        payload.dsTheoDoi[indexTheoDoi].dsChiTiet[0].khungChanDoanDd[index].ctMucTieu = khung.ctMucTieu;
                        payload.dsTheoDoi[indexTheoDoi].dsChiTiet[0].khungChanDoanDd[index].ctVanDe = khung.ctVanDe;
                    })
                    item.banGiao2 = state.chiTiet.banGiao2;
                    item.ghiChu = state.chiTiet.ghiChu2;
                    item.thucHienThuoc3 = state.chiTiet.thucHienThuoc3;
                    item.thucHienCls3 = state.chiTiet.thucHienCls3;
                    item.chamSocDieuDuong3 = state.chiTiet.chamSocDieuDuong3;
                }
                item.ngayThucHien = state.ngayThucHien;
                return item;
            });
            payload.ghiRo = state.ghiRo;
            payload.tienSuDiUng = state.tienSuDiUng;
            const [e2, res] = await toSafePromise(editorProvider.onSaveForm({
                file: state.baoCao,
                data: payload,
            }));
            if (e2) {
                showError(e2.message);
                return;
            }
            xuLyData({ data: res, baoCao: state.baoCao, currentChiTiet: nextItem || state.chiTiet });
            message.success("Cập nhật dữ liệu thành công")
        } finally {
            hideLoading();
        }
    };


    const listTabs = [
        {
            key: "thongTinChung",
            label: "Thông tin chung",
            component: <ThongTinChung readOnly={readOnly} onChange={onChange} onChangeState={onChangeState} state={state} data={state.chiTiet} />
        },
        {
            key: "sinhHieu",
            label: "Sinh hiệu",
            component: <SinhHieu showNhipTho={true} readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "toanThan",
            label: "Toàn thân",
            component: <ToanThan readOnly={readOnly} onChange={onChange} onChangeMultipleKey={onChangeMultipleKey} data={state.chiTiet} />
        },
        {
            key: "hoHap",
            label: "Hô hấp",
            component: <HoHap readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "tuanHoan",
            label: "Tuần hoàn",
            component: <TuanHoan readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "tieuHoa",
            label: "Tiêu hóa",
            component: <TieuHoa readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "tietNieu",
            label: "Tiết niệu",
            component: <TietNieu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />

        },
        {
            key: "giacNgu",
            label: "Giấc ngủ, VSCN",
            component: <GiacNgu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "thanKinh",
            label: "Thần kinh",
            component: <ThanKinh readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "coXuong",
            label: "Xương khớp",
            component: <CoXuong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "vetThuong",
            label: "Vết thương",
            component: <VetThuong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "danLuu",
            label: "Dẫn lưu",
            component: <DanLuu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "ongThong",
            label: "Ống thông",
            component: <OngThong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "theoDoiKhac",
            label: "Theo dõi khác",
            component: <TheoDoiKhac readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "moKhiQuan",
            label: "Mở khí quản",
            component: <MoKhiQuan readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "ghiChu",
            label: "Ghi chú",
            component: <GhiChu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "chanDoanDieuDuong",
            label: "Chẩn đoán điều dưỡng",
            component: <ChanDoanDieuDuong readOnly={readOnly} onChange={onChange} onChangeState={onChangeState} state={state} data={state.chiTiet} />
        },
        {
            key: "canThiepDieuDuong",
            label: "Can thiệp điều dưỡng",
            component: <CanThiepDieuDuong readOnly={false} onChange={onChange} onChangeMultipleKey={onChangeMultipleKey} data={state.chiTiet} />
        },
        {
            key: "banGiao",
            label: "Bàn giao",
            component: <BanGiao readOnly={readOnlyBanGiao} onChange={onChange} data={state.chiTiet} />
        },
    ];
    const trangThaiKy = useMemo(() => {
        return <div>
            <div className='flex flex-a-center gap-16'>
                <span>Điều dưỡng thực hiện: </span>
                {
                    dataSignDDTH ? <div className='signed'>
                        <SVG.IcProtect color={"green"} /> Đã ký bởi <b>{dataSignDDTH.tenNguoiKy}</b> lúc <i>{dataSignDDTH?.thoiGianKy?.toDateObject().format("dd/MM/yyyy HH:mm")}</i>
                    </div> : "Chưa ký"
                }
            </div>
            <div className='flex flex-a-center gap-16'>
                <span>Điều dưỡng nhận bàn giao: </span>
                {
                    dataSignBanGiao ? <div className='signed'>
                        <SVG.IcProtect color={"green"} /> Đã ký bởi <b>{dataSignBanGiao.tenNguoiKy}</b> lúc <i>{dataSignBanGiao?.thoiGianKy?.toDateObject().format("dd/MM/yyyy HH:mm")}</i>
                    </div> : "Chưa ký"
                }
            </div>
        </div>
    }, [dataSignDDTH, dataSignBanGiao])

    const onSaoChep = index => async () => {
        const confirm = await showAsyncConfirm(
            {
                title: t("common.thongBao"),
                content: "Bạn có muốn sao chép dữ liệu của mốc thời gian này thay thế cho bản ghi hiện tại",
                cancelText: t("common.quayLai"),
                okText: t("common.dongY"),
                showImg: true,
                showBtnOk: true,
            });
        if (confirm.action == "ok") {
            const newChiTiet = cloneDeep(state.dsChiTiet?.[index] || {});
            delete newChiTiet.chiSoSongId;
            if (newChiTiet.chiSoSong)
                delete newChiTiet.chiSoSong.id;
            newChiTiet.sttChiTiet = state.chiTiet.sttChiTiet;
            newChiTiet.indexChiTiet = state.chiTiet.indexChiTiet;
            newChiTiet.indexTheoDoi = state.chiTiet.indexTheoDoi;
            // newChiTiet.thucHienCls3 = [];
            // newChiTiet.thucHienCls = "";
            // newChiTiet.chamSocDieuDuong3 = [];
            // newChiTiet.chamSocDieuDuong = "";
            // newChiTiet.thucHienThuoc3 = [];
            // newChiTiet.thucHienThuoc = "";
            newChiTiet.banGiao = "";
            newChiTiet.ghiChu = "";
            newChiTiet.thoiGianBanGiao = "";
            setState({ chiTiet: newChiTiet, changed: true })
        }
    }

    const onCopySinhHieu = () => {
        refModalSinhHieu.current?.show({ nbDotDieuTriId, khoaChiDinhId }, sinhHieu => {
            if (sinhHieu) {
                const ngayThucHien = sinhHieu.thoiGianThucHien
                    ? moment(sinhHieu.thoiGianThucHien).format("YYYY-MM-DD")
                    : "";
                const thoiGian = sinhHieu.thoiGianThucHien
                    ? moment(sinhHieu.thoiGianThucHien).format("HH:mm:ss")
                    : "";

                const trungSinhHieu = state.dsChiTiet?.find(el => {
                    return el.chiSoSong?.thoiGianThucHien?.toDateObject().format(
                        "ddMMyyyyHHmm"
                    ) === sinhHieu.thoiGianThucHien?.toDateObject().format("ddMMyyyyHHmm")
                });

                if (trungSinhHieu) {
                    message.error(
                        t("editor.nbDaTonTaiSinhHieu", {
                            time: trungSinhHieu.chiSoSong.thoiGianThucHien?.toDateObject().format(
                                "dd/MM/YYYY HH:mm:ss"
                            ),
                        })
                    );
                } else {
                    const chiTiet = cloneDeep(state.chiTiet);
                    chiTiet.chiSoSong = sinhHieu;
                    chiTiet.ngayThucHien = ngayThucHien;
                    chiTiet.thoiGian = thoiGian;
                    chiTiet.thoiGianThucHien = moment(
                        sinhHieu.thoiGianThucHien
                    ).format("YYYY-MM-DD HH:mm:00");
                    setState({ chiTiet, changed: true });
                }
            }
        })
    }
    const onHandleKy = async () => {
        if (state.changed)
            await onSave();
        if (isShowBtnTuChoiKy) {
            onTuChoiKyPhieu();
            return;
        }
        if (!dataSignDDTH) {
            if (checkPermissonSign)
                onKyPhieu({ duLieuKy: refFormData.current });
            else
                onTrinhKy();
        }
        else
            onHuyKyPhieu();
    }
    const onHandleKyBanGiao = async () => {
        if (state.changed)
            await onSave();
        if (isShowBtnTuChoiKyBanGiao) {
            onTuChoiKyPhieuBanGiao();
            return;
        }
        if (!dataSignBanGiao) {
            if (checkPermissonSignBanGiao)
                onKyPhieuBanGiao({ duLieuKy: refFormData.current });
            else
                onTrinhKyBanGiao();
        }
        else
            onHuyKyPhieuBanGiao();
    }

    const onDeleteCol = () => {
        showConfirm(
            {
                title: t("common.xoaDuLieu"),
                content: "Bạn có muốn xóa dữ liệu mốc thời gian này",
                cancelText: t("common.quayLai"),
                okText: t("common.dongY"),
                classNameOkText: "button-error",
                showImg: true,
                showBtnOk: true,
            }, () => {
                setState({
                    changed: true,
                    chiTiet: {
                        ngayThucHien: null,
                        ngayTheoDoi: state.chiTiet?.ngayThucHien,
                        indexTheoDoi: state.chiTiet?.indexTheoDoi,
                        indexChiTiet: state.chiTiet?.indexChiTiet,
                        sttChiTiet: state.chiTiet?.sttChiTiet
                    }
                })
            });
    }
    const onAddCol = async () => {
        if ((state.data?.dsTheoDoi || [])?.length < (Number(dataSO_BANG_TOI_DA_TRONG_PHIEU_CHAM_SOC_CAP_23) || 1)) {
            const chiTiet = {
                sttChiTiet: state.dsChiTiet.length,
                indexChiTiet: state.dsChiTiet.length % 5,
                indexTheoDoi: Math.floor((state.dsChiTiet.length + 1) / 5),
                ngayTheoDoi: state.data?.thoiGianThucHien
            }
            const dsChiTiet = [...state.dsChiTiet, chiTiet];
            setState({ dsChiTiet });
            handleSelect(chiTiet)
        } else {
            const thoiGianThucHien = new Date().format("dd/MM/yyyy HH:mm");
            const confirm = await showAsyncConfirm({
                title: "Tạo phiếu chăm sóc cấp 2-3 mới",
                content: `Bạn có muốn tạo mới phiếu chăm sóc cấp 2-3 mới cho người bệnh <b>${thongTinCoBan?.tenNb}</b> vào thời gian <b>${thoiGianThucHien}</b>`,
                cancelText: t("common.huy"),
                okText: t("common.dongY"),
                showBtnOk: true,
                typeModal: "warning",
            });
            if (confirm.action === "ok") {
                const url = combineUrlParams(location.pathname.replace("/" + id, "").replace("?", ""), { ...queries, thoiGianThucHien: new Date().format("yyyy-MM-dd HH:mm") });
                history.replace(url);
            }
        }
    }
    const isAllowAddTable = useMemo(() => {
        //yêu cầu tất cả các cột phải được điền thì mới cho tạo bảng mới
        return state.dsChiTiet?.every(item => item.thoiGianThucHien);
    }, [state.dsChiTiet])
    return (
        <Page
            breadcrumb={[
                { title: "Quản lý nội trú", link: "/quan-ly-noi-tru" },
            ]}
            title={"Phiếu chăm sóc cấp 2-3"}
            actionRight={<>
                {state.chiTiet &&
                    <>
                        <Button rightIcon={<SVG.IcSign />} minWidth={100}
                            onClick={onHandleKy}>
                            {isShowBtnTuChoiKy ? "Từ chối ký thực hiện" : !dataSignDDTH ? renderTenChanKy : "Hủy ký thực hiện"}
                        </Button>
                        <Button rightIcon={<SVG.IcSign />} minWidth={100}
                            onClick={onHandleKyBanGiao}>
                            {isShowBtnTuChoiKyBanGiao ? "Từ chối ký bàn giao" : !dataSignBanGiao ? renderTenChanKyBanGiao : "Hủy ký bàn giao"}
                        </Button>
                        {!readOnly &&
                            <Button rightIcon={<SVG.IcVitalSign />} onClick={onCopySinhHieu}>Chọn sinh hiệu</Button>
                        }
                    </>
                }
                <Button rightIcon={<SVG.IcEye />} onClick={() => {
                    window.open(window.location.href.replace("quan-ly-noi-tru/dieu-duong/phieu-cham-soc-cap-2", "editor/bao-cao"));
                }}>
                    Xem tổng hợp
                </Button>
                {(!!id && isEmpty(state.data?.lichSuKy)) &&
                    <Button rightIcon={<SVG.IcDelete />} onClick={onDeleteForm} type="error">
                        Xóa phiếu
                    </Button>
                }
                {state.chiTiet &&
                    <Button rightIcon={<SVG.IcSave />} type="primary" onClick={e => onSave()} minWidth={100}>
                        {t("common.luu")}
                    </Button>
                }
            </>}
        >
            <Main disabled={readOnly}>
                <ThongTinBenhNhan nbDotDieuTriId={nbDotDieuTriId} isShowDongBoGia={false} />
                {!!state.dsChiTiet?.length &&
                    <Row gutter={16} style={{ flex: 1, overflow: 'hidden' }}>
                        <Col className='col-timeline'>
                            <div className='list-timeline'>
                                <Timeline mode="left">
                                    {state.dsChiTiet.map((chiTiet, index) => (
                                        <Timeline.Item
                                            key={index}
                                            color={index === state.chiTiet.sttChiTiet ? "blue" : "gray"}
                                        >
                                            <div className='flex flex-j-spw flex-a-center mr-5'>
                                                <div
                                                    onClick={handleSelect(chiTiet)}
                                                    style={{
                                                        cursor: "pointer",
                                                        fontWeight: index === state.chiTiet.sttChiTiet ? "bold" : "normal",
                                                    }}
                                                >
                                                    {chiTiet.thoiGianThucHien ? chiTiet.thoiGianThucHien.toDateObject().format("dd/MM/yyyy HH:mm") : chiTiet?.ngayTheoDoi ? chiTiet?.ngayTheoDoi.toDateObject()?.format("dd/MM/yyyy") : "Chưa có thông tin"}
                                                </div>
                                                {
                                                    index !== state.chiTiet.sttChiTiet && !dataSignDDTH && chiTiet.thoiGianThucHien &&
                                                    <div className='action'>
                                                        <SVG.IcSaoChep className="pointer" onClick={onSaoChep(index)} title="Thực hiện sao chép dữ liệu" />
                                                    </div>
                                                }
                                            </div>
                                        </Timeline.Item>
                                    ))}
                                </Timeline>
                            </div>
                            {
                                isAllowAddTable && <>
                                    <hr />
                                    <Button rightIcon={<SVG.IcAdd />} onClick={onAddCol} type="primary">
                                        Thêm mới thời gian
                                    </Button>
                                </>
                            }
                        </Col>

                        <Col flex="auto" style={{ flex: 1, overflow: 'hidden', height: "100%" }} className='form-container'>
                            <Card title={
                                <div className='flex flex-j-spw flex-a-center'>
                                    <div className='flex gap-5 flex-a-center'>{`Thơi gian nhận định - `}
                                        <DateTimePicker
                                            value={thoiGian}
                                            onChange={onChangeThoiGian}
                                            placeholder='__/__/__ __:__'
                                            format={"DD/MM/YYYY HH:mm"}
                                            className="input-filter"
                                            disabled={readOnly}
                                        />
                                        {
                                            !dataSignDDTH && !dataSignBanGiao && <Button type="warning" rightIcon={<SVG.IcDelete />} onClick={onDeleteCol}>Xóa dữ liệu</Button>
                                        }
                                    </div>
                                    {trangThaiKy}
                                </div>
                            } style={{ height: "100%" }}>
                                <Tabs >
                                    {listTabs.map((obj) => {
                                        return (
                                            <Tabs.TabPane
                                                key={obj.key}
                                                tab={
                                                    <div>
                                                        {obj?.label}
                                                    </div>
                                                }
                                            >
                                                {obj.component}
                                            </Tabs.TabPane>
                                        );
                                    })}
                                </Tabs>
                            </Card>
                        </Col>
                    </Row>
                }
            </Main>
            <ModalSelectSinhHieu ref={refModalSinhHieu} />
        </Page >
    )
}
