import { Col, Row } from 'antd'
import React from 'react'
import { DateTimePicker, InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import MultipleChoiceByGroup from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoiceByGroup'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'
import moment from 'moment'
import env from 'module_env/ENV'


const DANH_SACH_TINH_TRANG_BUNG = [
    { id: 1, ten: "Mềm" },
    { id: 2, ten: "Chướng" },
    { id: 3, ten: "<PERSON><PERSON><PERSON> báng" },
    { id: 4, ten: "Ấn đau" },
]

const MAU_SAC_DICH_DA_DAY = [
    { id: 1, ten: "Vàng" },
    { id: 2, ten: "Nâu" },
    { id: 3, ten: "Xanh" },
    { id: 4, ten: "Đỏ" },
    { id: 5, ten: "Đen" },
]

const TINH_CHAT_DICH_DA_DAY = [
    { id: 1, ten: "Trong" },
    { id: 2, ten: "Lợn cợn" },
]

const DATA_AN_QUA_ONG_THONG = [
    { id: 1, ten: "Ống thông dạ dày" },
    { id: 2, ten: "Hổng tràng ra da" },
    { id: 3, ten: "Mở dạ dày ra da" },
]

const DATA_CO_KHONG = [
    { id: 1, ten: "Có" },
    { id: 2, ten: "Không" },
];

const DATA_KHONG_CO = [
    { id: 2, ten: "Không" },
    { id: 1, ten: "Có" },
];

const MAU_DAI_TIEN = [
    { id: 1, ten: "Xanh" },
    { id: 2, ten: "Vàng" },
    { id: 3, ten: "Đỏ tươi" },
    { id: 4, ten: "Đỏ bầm" },
    { id: 5, ten: "Đen" },
    { id: 6, ten: "Trắng" },
];

const TINH_CHAT_DAI_TIEN = [
    { id: 1, ten: "Sệt" },
    { id: 2, ten: "Lỏng" },
    { id: 3, ten: "Khuôn" },
    { id: 4, ten: "Cứng" },
];

const DANH_SACH_BAI_TIET = [
    { id: 1, ten: "Tự nhiên" },
    { id: 2, ten: "HMNT" }
]
const DATA_TINH_TRANG_TIEU_HOA = [
    { id: 1, ten: "Chưa ghi nhận bất thường" },
    { id: 2, ten: "Ợ chua" },
    { id: 3, ten: "Đầy bụng/khó tiêu" },
    { id: 4, ten: "Nôn ói" },
    { id: 5, ten: "Táo bón" },
    { id: 6, ten: "Tiêu chảy" },
];

const TieuHoa = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"Tình trạng bụng"} direction={2} titleMinWidth={150}>
                        <SingleChoice
                            checkboxWidth={100}
                            type="radio"
                            disabled={readOnly}
                            className="flex1"
                            outputTypeArray={true}
                            value={get(data, "tinhTrangBung")}
                            data={DANH_SACH_TINH_TRANG_BUNG} onChange={e => {
                                onChange(["tinhTrangBung"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    {!["choray"].includes(env.HOSPITAL) &&
                        <FieldItem title={"Tình trạng bụng khác"} direction={2} titleMinWidth={150}>
                            <InputTimeout
                                disabled={readOnly}
                                className="flex1"
                                value={get(data, "ctTinhTrangBung")}
                                onChange={e => {
                                    onChange(["ctTinhTrangBung"], e);
                                }}></InputTimeout>
                        </FieldItem>
                    }
                    <FieldItem title={"Nhu động ruột"} direction={2} titleMinWidth={150}>
                        <SingleChoice
                            disabled={readOnly}
                            type="radio"
                            outputTypeArray={true}
                            value={get(data, "nhuDongRuot")}
                            data={DATA_CO_KHONG} onChange={e => {
                                onChange(["nhuDongRuot"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    {!["choray"].includes(env.HOSPITAL) &&
                        <FieldItem title={"Nhu động ruột khác"} direction={2} titleMinWidth={150}>
                            <InputTimeout
                                disabled={readOnly}
                                className="flex1"
                                value={get(data, "ctNhuDongRuot")}
                                onChange={e => {
                                    onChange(["ctNhuDongRuot"], e);
                                }}></InputTimeout>
                        </FieldItem>
                    }
                    <FieldItem title={"Tình trạng tiêu hóa"} direction={2} align='flex-start' titleMinWidth={150}>
                        <MultipleChoiceByGroup
                            disabled={readOnly}
                            value={get(data, "tinhTrangTieuHoa")}
                            type="groupCheckbox"
                            dataGroups={[{ name: "Chưa ghi nhận bất thường", values: [1] }, { name: "Bất thường", color: "red", values: [2, 3, 4, 5] }]}
                            data={DATA_TINH_TRANG_TIEU_HOA} onChange={e => {
                                onChange(["tinhTrangTieuHoa"], e);
                            }}></MultipleChoiceByGroup>
                        {
                            isArray(data.tinhTrangTieuHoa) && !!data.tinhTrangTieuHoa?.length && !data.tinhTrangTieuHoa.includes(1) ? (
                                <>
                                    <FieldItem title="Số lượng dịch nôn ói/ tiêu chảy" direction={2} width={300} titleMinWidth={175}>
                                        <div className='flex flex-a-center gap-5'>
                                            <InputTimeout
                                                disabled={readOnly}
                                                type="number"
                                                className="flex1"
                                                value={get(data, "slDich")}
                                                onChange={e => {
                                                    onChange(["slDich"], e);
                                                }}></InputTimeout>
                                            <span>ml</span>
                                        </div>
                                    </FieldItem>
                                    <FieldItem title="Màu sắc" direction={2} titleMinWidth={175}>
                                        <InputTimeout
                                            disabled={readOnly}
                                            className="flex1"
                                            value={get(data, "mauSacDich")}
                                            onChange={e => {
                                                onChange(["mauSacDich"], e);
                                            }}></InputTimeout>
                                    </FieldItem>
                                    <FieldItem title="Tính chất" direction={2} titleMinWidth={175}>
                                        <SingleChoice
                                            type="radio"
                                            disabled={readOnly}
                                            className="flex1"
                                            value={Number(get(data, "tinhChatOi"))}
                                            data={TINH_CHAT_DICH_DA_DAY} onChange={e => {
                                                onChange(["tinhChatOi"], e);
                                            }}></SingleChoice>
                                    </FieldItem>
                                </>
                            ) : null
                        }
                    </FieldItem>
                    <FieldItem title={"Ăn qua miệng"} direction={2} width={400} titleMinWidth={175}>
                        <div className='flex flex-a-center gap-5'>
                            <span>Ăn được</span>
                            <InputTimeout
                                disabled={readOnly}
                                type="number"
                                className="flex1"
                                value={get(data, "anQuaDuongMieng")}
                                onChange={e => {
                                    onChange(["anQuaDuongMieng"], e);
                                }}></InputTimeout>
                            <span>% suất ăn</span>
                        </div>
                    </FieldItem>
                    <FieldItem title={"Ăn qua ống thông"} direction={2} titleMinWidth={180} align='flex-start'>
                        <SingleChoice
                            disabled={readOnly}
                            type="radio"
                            value={Number(get(data, "anQuaOngThong"))}
                            data={DATA_AN_QUA_ONG_THONG} onChange={e => {
                                onChange(["anQuaOngThong"], e);
                            }}></SingleChoice>
                        {data.anQuaOngThong &&

                            <FieldItem title={"Số lượng thức ăn (ml) x số lần/ 24 giờ"} direction={2} width={400}>
                                <div className='flex flex-a-center gap-5'>
                                    <InputTimeout
                                        disabled={readOnly}
                                        className="flex1"
                                        type="number"
                                        value={get(data, "slThucAn")}
                                        onChange={e => {
                                            onChange(["slThucAn"], e);
                                        }}></InputTimeout>
                                    (ml)
                                </div>
                            </FieldItem>
                        }
                    </FieldItem>
                    <FieldItem title={"Chế độ ăn, uống"} direction={2} width={400} titleMinWidth={180}>
                        <InputTimeout
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "cheDoAn")}
                            onChange={e => {
                                onChange(["cheDoAn"], e);
                            }}></InputTimeout>
                    </FieldItem>

                    <FieldItem title={"Dinh dưỡng qua tĩnh mạch"} direction={2} titleMinWidth={180}>
                        <SingleChoice
                            type="radio"
                            disabled={readOnly}
                            outputTypeArray={true}
                            value={get(data, "dinhDuongTinhMach")}
                            data={DATA_KHONG_CO} onChange={e => {
                                onChange(["dinhDuongTinhMach"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"} direction={2} titleMinWidth={180}>
                        <InputTimeout
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tieuHoaKhac")}
                            onChange={e => {
                                onChange(["tieuHoaKhac"], e);
                            }}></InputTimeout>
                    </FieldItem>

                </Col>
            </Row>
        </Main>
    )
}
export default TieuHoa;