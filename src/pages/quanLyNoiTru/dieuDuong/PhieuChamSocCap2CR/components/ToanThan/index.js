import { Col, Collapse, Row } from 'antd'
import React from 'react'
import { InputTimeout } from 'components'
import { get, isArray } from 'lodash'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import MultipleChoiceByGroup from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoiceByGroup'
import { Main } from './styled'
import { useEnum, useLazyKVMap } from 'hooks'
import { ENUM } from 'constants/index'

const DANH_SACH_TINH_THAN = [
    { id: 1, ten: "Bình thường" },
    { id: 2, ten: "Lo lắng" },
    { id: 3, ten: "Ủ rũ" },
    { id: 4, ten: "<PERSON><PERSON>ch động" },
]
const D<PERSON><PERSON>_SACH_MAU_SAC = [
    { id: "1", ten: "Hồng" },
    { id: "2", ten: "Nhạt" },
    { id: "3", ten: "Vàng" },
]
const DANH_SACH_MAU_SAC_DA = [
    { id: 1, ten: "Bình thường" },
    { id: 2, ten: "Vàng" },
    { id: 3, ten: "Xanh xao" },
    { id: 4, ten: "Tím tái" },
]

const DANH_SACH_TINH_CHAT_DA = [
    { id: 1, ten: "Ấm" },
    { id: 2, ten: "Nóng" },
    { id: 3, ten: "Lạnh" },
]

const TINH_TRANG_DA = [
    { id: 1, ten: "Bình thường" },
    { id: 2, ten: "Viêm tại chỗ" },
    { id: 3, ten: "Rơm lở" },
    { id: 4, ten: "Nổi mẩn" },
    { id: 5, ten: "Khối tụ máu" },
    { id: 6, ten: "Chấm/ mảng xuất huyêt" },
]

const DATA_KHONG_CO = [
    { id: 1, ten: "Không" },
    { id: 2, ten: "Có" },
];

const DATA_MUC_DO_TRAN_KHI = [
    { id: 1, ten: "Rât ít" },
    { id: 2, ten: "Ít" },
    { id: 3, ten: "Nhiều" },
]

const DATA_LOAI_TRI_GIAC = [
    {
        id: 1, ten: "Ý thức (ACVPU)",
    },
    {
        id: 2, ten: "Glasgow",
    }
];

const DANH_GIA_ACVPU = [
    { id: 1, ten: "(A) Tỉnh táo" },
    { id: 2, ten: "(C) Lơ mơ" },
    { id: 3, ten: "(V) Đáp ứng với âm thanh" },
    { id: 4, ten: "(P) Đáp ứng với kích thích đau" },
    { id: 5, ten: "(U) Không đáp ứng" },
]

const ToanThan = ({ onChange, data, readOnly, ...props }) => {
    const [listGlasgowMatEnum] = useEnum(ENUM.GLASSGOW_MAT);
    const [listGlasgowVanDongEnum] = useEnum(ENUM.GLASSGOW_VAN_DONG);
    const [listGlasgowLoiNoiEnum] = useEnum(ENUM.GLASSGOW_LOI_NOI);
    const [getGlasgowMat] = useLazyKVMap(listGlasgowMatEnum)
    const [getGlasgowVanDong] = useLazyKVMap(listGlasgowVanDongEnum)
    const [getGlasgowLoiNoi] = useLazyKVMap(listGlasgowLoiNoiEnum)

    const getValue = (key, value) => {
        const x = key === "E" ? getGlasgowMat(value) : key === "V" ? getGlasgowLoiNoi(value) : getGlasgowVanDong(value);
        if (x) {
            const m = x.ten.match(/^\((\d+)\)/);    // số trong ngoặc đầu
            return m ? Number(m[1]) : null;
        }
        return null;
    }

    if (data) {
        if (!data.loaiTriGiac) {
            data.loaiTriGiac = get(data, "triGiac") || get(data, "chiSoSong.acvpu") ? [1] :
                get(data, "glassgow") ||
                    get(data, "chiSoSong.glasgowMat") ||
                    get(data, "chiSoSong.glasgowLoiNoi") ||
                    get(data, "chiSoSong.glasgowVanDong") ? [2] : null
        }
        if (!data.chiSoSong) {
            data.chiSoSong = {};
        }
        data.chiSoSong.glasgow = [{ key: "E", value: data.chiSoSong.glasgowMat }, { key: "V", value: data.chiSoSong.glasgowLoiNoi }, { key: "M", value: data.chiSoSong.glasgowVanDong }]
            .filter(item => item.value).map(item => `${item.key}${getValue(item.key, item.value)}`).join("") || data.chiSoSong.glasgow || data.glassgow;
    }
    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"Tri giác"} direction={2}>
                        <SingleChoice
                            type="radio"
                            radioWidth={150}
                            disabled={readOnly}
                            data={DATA_LOAI_TRI_GIAC}
                            outputTypeArray={true}
                            value={get(data, "loaiTriGiac")}
                            defaultValue={get(data, "triGiac") ? 1 : get(data, "glassgow") ? 2 : null}
                            onChange={(e) => {
                                if (e)
                                    onChange(["loaiTriGiac"], e);
                            }} />
                    </FieldItem>
                    {!!data.loaiTriGiac?.length &&
                        <>
                            <FieldItem direction={2} title={"Đánh giá " + DATA_LOAI_TRI_GIAC.find(item => item.id == data.loaiTriGiac?.[0])?.ten}>
                                {
                                    data.loaiTriGiac.includes(1) ?
                                        <SingleChoice
                                            type="radio"
                                            radioWidth={150}
                                            disabled={readOnly}
                                            data={DANH_GIA_ACVPU}
                                            // outputTypeArray={true}
                                            value={Number(get(data, "triGiac"))}
                                            onChange={(e) => {
                                                onChange(["triGiac"], e);
                                            }} /> :
                                        <InputTimeout
                                            style={{ maxWidth: 100 }}
                                            disabled={true}
                                            className="flex1"
                                            value={get(data, "chiSoSong.glasgow")}
                                            onChange={(e) => {
                                                onChange(["chiSoSong.glasgow"], e);
                                            }}
                                        />
                                }
                            </FieldItem>
                            {
                                !data.loaiTriGiac.includes(1) && <>
                                    <FieldItem direction={2} title={"Mắt (E):"}>
                                        <SingleChoice
                                            type="radio"
                                            radioWidth={200}
                                            disabled={readOnly}
                                            data={listGlasgowMatEnum}
                                            // outputTypeArray={true}
                                            value={Number(get(data, "chiSoSong.glasgowMat"))}
                                            onChange={(e) => {
                                                onChange(["chiSoSong", "glasgowMat"], e);
                                            }} />
                                    </FieldItem>
                                    <FieldItem direction={2} title={"Lời nói (V):"}>
                                        <SingleChoice
                                            type="radio"
                                            radioWidth={200}
                                            disabled={readOnly}
                                            data={listGlasgowLoiNoiEnum}
                                            // outputTypeArray={true}
                                            value={Number(get(data, "chiSoSong.glasgowLoiNoi"))}
                                            onChange={(e) => {
                                                onChange(["chiSoSong", "glasgowLoiNoi"], e);
                                            }} />
                                    </FieldItem>
                                    <FieldItem direction={2} title={"Hành động (M):"}>
                                        <SingleChoice
                                            type="radio"
                                            radioWidth={200}
                                            disabled={readOnly}
                                            data={listGlasgowVanDongEnum}
                                            // outputTypeArray={true}
                                            value={Number(get(data, "chiSoSong.glasgowVanDong"))}
                                            onChange={(e) => {
                                                onChange(["chiSoSong", "glasgowVanDong"], e);
                                            }} />
                                    </FieldItem>
                                </>
                            }
                        </>
                    }

                    <FieldItem title={"Tinh thần"} align="flex-start" direction={2}>
                        <MultipleChoiceByGroup
                            disabled={readOnly}
                            className="flex1"
                            radioWidth={150}
                            value={get(data, "tinhThan2")}
                            mode={"multiple"}
                            type="groupCheckbox"
                            dataGroups={[{ name: "Bình thường", values: [1] }, { name: "Bất thường", color: "red", values: [2, 3, 4] }]}
                            data={DANH_SACH_TINH_THAN} onChange={e => {
                                onChange(["tinhThan2"], e);
                            }}></MultipleChoiceByGroup>
                    </FieldItem>
                    <FieldItem title={"Màu sắc niêm"} direction={2}>
                        <SingleChoice
                            radioWidth={120}
                            disabled={readOnly}
                            type="radio"
                            className="flex1"
                            value={get(data, "daNiemMac")}
                            data={DANH_SACH_MAU_SAC} onChange={e => {
                                onChange(["daNiemMac"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    <FieldItem title={"Màu sắc da"} direction={2}>
                        <SingleChoice
                            type="radio"
                            radioWidth={120}
                            outputTypeArray={true}
                            disabled={readOnly}
                            value={get(data, "mauSacDa")}
                            data={DANH_SACH_MAU_SAC_DA} onChange={e => {
                                onChange(["mauSacDa"], e);
                            }}></SingleChoice>
                    </FieldItem>
                    <FieldItem title={"Tính chất da"} direction={2}>
                        <SingleChoice
                            radioWidth={120}
                            outputTypeArray={true}
                            type="radio"
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tinhChatDa")}
                            data={DANH_SACH_TINH_CHAT_DA} onChange={e => {
                                onChange(["tinhChatDa"], e);
                            }}></SingleChoice>
                    </FieldItem>

                    <FieldItem title={"Tình trạng da"} direction={2} align="flex-start">
                        <MultipleChoiceByGroup
                            disabled={readOnly}
                            className="flex1"
                            type="groupCheckbox"
                            radioWidth={150}
                            value={get(data, "tinhTrangDa")}
                            dataGroups={[{ name: "Bình thường", values: [1] }, { name: "Bất thường", color: "red", values: [2, 3, 4, 5, 6] }]}
                            data={TINH_TRANG_DA} onChange={e => {
                                onChange(["tinhTrangDa"], e);
                            }}></MultipleChoiceByGroup>
                        {
                            (isArray(data?.tinhTrangDa) && !!data.tinhTrangDa?.length && !data.tinhTrangDa.includes(1)) &&
                            <div className='group'>
                                <label className="label">Vị trí, kích thước (cm x cm)</label>
                                <InputTimeout
                                    disabled={readOnly}
                                    className="flex1 mw-100"
                                    value={get(data, "viTriKichThuoc")}
                                    onChange={(e) => {
                                        onChange(["viTriKichThuoc"], e);
                                    }}
                                />
                            </div>
                        }
                    </FieldItem>
                    <FieldItem title={"Tràn khí dưới da/Mô cơ"} direction={2} align='flex-start'>
                        <SingleChoice
                            outputTypeArray={true}
                            type="radio"
                            disabled={readOnly}
                            className="flex1"
                            value={get(data, "tranKhiDuoiDa")}
                            data={DATA_KHONG_CO} onChange={e => {
                                onChange(["tranKhiDuoiDa"], e);
                            }}></SingleChoice>
                        {data.tranKhiDuoiDa?.length && data.tranKhiDuoiDa.includes(2) &&
                            <FieldItem title={"Mức độ tràn khí"} direction={2}
                                titleMinWidth={103}
                            >
                                <SingleChoice
                                    outputTypeArray={true}
                                    type="radio"
                                    disabled={readOnly}
                                    className="flex1"
                                    value={get(data, "mucDoTranKhi")}
                                    data={DATA_MUC_DO_TRAN_KHI} onChange={e => {
                                        onChange(["mucDoTranKhi"], e);
                                    }}></SingleChoice>
                            </FieldItem>
                        }
                    </FieldItem>
                    <FieldItem title={"Khác"} direction={2}>
                        <InputTimeout
                            disabled={readOnly}

                            className="flex1"
                            value={get(data, "toanThanKhac")}
                            onChange={e => {
                                onChange(["toanThanKhac"], e);
                            }}></InputTimeout>
                    </FieldItem>
                </Col>
            </Row >
        </Main >
    )
}
export default ToanThan;