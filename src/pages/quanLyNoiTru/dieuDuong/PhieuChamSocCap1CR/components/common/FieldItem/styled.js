import styled from "styled-components";

export const Main = styled.div`
    ${props => props.width ? `width: ${props.width}px !important;` : ``}
    ${props => props.maxWidth ? `max-width: ${props.maxWidth}px !important;` : ``}
    margin-top: ${props => props.top}px;
    margin-bottom: 10px;
    ${props => props.direction == 2 ? `
      display: flex; 
      flex-direction: row;
      align-items: ${props.align};
      & .content > div{
        margin-bottom: 0px;
        & > .list-checkbox{
          margin-top: 0px !important;
        }
      }
      
      & .field-item-header{
        margin-top: ${props.titleMarginTop || 0}px;
        margin-bottom: 0px !important;
        & .title{
          margin-bottom: 0px !important;
        }
      }
      `
    : ""}
    ${props => props.border ? `border: 1px solid gray; padding: 10px; ` : ``}
    & .field-item-header
    { 
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 5px;
      > .title
      {
          text-align: ${props => props.titleTextAlign};
          display: inline-block;
          margin-bottom: 5px;
          font-weight: bold;
          min-width: ${props => props.titleMinWidth || 0}px;
          ${props => props.border ? `border-bottom: 1px solid gray; ` : ``}
      }
    }
    & .content{
      margin-left: 5px;
      flex: 1;
      overflow: hidden;
      flex-wrap: wrap;  
      & > div{
        width: 100%;
      }
      & .label{
        font-weight: bold;
      }
      & .group{
        margin-top: 10px;
        align-items: center;
        display: flex;
        gap: 5px;
        & > .label{
          min-width: 130px;
        }
      }
    }
    & .ant-select{
      width: 100%;
    }
    & .mw-100
    {
      max-width: 100px;
    }
`;
