import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Page, ThongTinBenh<PERSON>han, <PERSON><PERSON>, Button, DateTimePicker } from 'components'
import { useConfirm, useGuid, useLoading, useQueryString } from 'hooks'
import { useTranslation } from 'react-i18next';
import { Main } from './styled';
import Sinh<PERSON><PERSON> from "./components/SinhHieu";
import ToanThan from "./components/ToanThan";
import HoHap from "./components/HoHap";
import TuanHoan from "./components/TuanHoan";
import TietNieu from "./components/TietNieu";
import TieuHoa from "./components/TieuHoa";
import GiacNgu from "./components/GiacNgu";
import CoXuong from "./components/CoXuong";
import VetThuong from "./components/VetThuong";
import DanLuu from "./components/DanLuu";
import OngThong from "./components/OngThong";
import <PERSON>DoiKhac from "./components/TheoDoiKhac";
import CanBangDich from "./components/CanBangDich";
import CanThiepDieuDuong from "./components/CanThiepDieuDuong";
import ChanDoanDieuDuong from "./components/ChanDoanDieuDuong";
import GhiChuBanGiao from "./components/GhiChuBanGiao";
import { Timeline, Row, Col, Card, message } from "antd";
import { cloneDeep, flatten, set, isEmpty } from 'lodash';
import { SVG } from "assets";
import { useDispatch } from 'react-redux';
import { useParams, useLocation } from "react-router-dom";
import editorProvider from 'data-access/editor-provider';
import moment from 'moment';
import useSign from 'components/editor/cores/ImageSign/useSign';
import { showError } from 'utils/message-utils';
import { useHistory } from 'react-router-dom';
import { toSafePromise } from 'lib-utils';
import ModalSelectSinhHieu from 'components/editor/cores/Components/ModalSelectSinhHieu';

export default function index() {
    const { showLoading, hideLoading } = useLoading();
    const { showAsyncConfirm } = useConfirm();
    const history = useHistory();
    const refModalSinhHieu = useRef(null);
    const { search, pathname } = useLocation();
    const { id, maBaoCao } = useParams();
    const editorId = useGuid();
    const queries = useMemo(() => {
        const queries = {};
        search
            .substring(1)
            .split("&")
            .forEach((item) => {
                const arr = item.split("=");
                if (arr[0] != "printNewTab")
                    queries[arr[0]] = arr[1];
            });
        return queries;
    }, [search]);
    const { t } = useTranslation();
    const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId", "");
    const [khoaChiDinhId] = useQueryString("khoaChiDinhId");
    const [thoiGianThucHien] = useQueryString("thoiGianThucHien");

    const { files: {
        getBaoCaoByMaBaoCao,
        deletePhieuEditor
    }
    } = useDispatch();
    // const signStatus = useEditor(editorId, "signStatus", {});

    const [state, _setState] = useState({ data: null, dsChiTiet: [], chiTiet: {}, chanDoanDieuDuong: [] });
    const setState = (newState) => {
        _setState((prevState) => ({
            ...prevState,
            ...newState,
        }));
    };
    const handleSelect = (item) => async () => {
        if (state.changed && !dataSign) {
            await onSave(item);
        } else {
            setState({
                chiTiet: cloneDeep(item),
            });
        }
    };

    const getTong = ({ dsChiTiet, chiTiet, index, key, keyTong }) => {
        let item = chiTiet.sttChiTiet == index ? chiTiet : dsChiTiet[index];
        return (
            (index == 0
                ? 0
                : Number(dsChiTiet[index - 1][keyTong] || 0)) +
            Number(item[key] || 0)
        );
    };

    const onChange = (arrayKey, value, saveToState = true) => {
        const chiTiet = state.chiTiet;
        set(chiTiet, arrayKey.join("."), value);
        if (["dichTruyen", "anUong", "dichVaoKhac",
            "nuocTieu", "dichLocMau", "dichDaDay", "danLuu", "dichRaKhac", "phan"
        ].includes(arrayKey[0])) {
            tinhToanCanBangDich({ dsChiTiet: state.dsChiTiet, chiTiet, index: chiTiet.sttChiTiet })
        } else
            if ("loaiTriGiac" == arrayKey[0]) {
                chiTiet.acvpu = null;
                chiTiet.glasgow = null;
                chiTiet.rass = null;
            }
        if (saveToState)
            setState({ chiTiet: { ...chiTiet }, changed: true });
    }

    const onChangeMultipleKey = (arrKeyValue) => {
        const chiTiet = state.chiTiet;
        arrKeyValue.forEach(keyValue => {
            const { arrayKey, value } = keyValue;
            onChange(arrayKey, value, false);
        })
        setState({ chiTiet: { ...chiTiet }, changed: true });
    }

    const onChangeText = (key) => (value) => {
        setState({ [key]: value, changed: true })
    }

    const onChangeCdDieuDuong = (index, key, value) => {
        if (index == -1) {
            setState({ chanDoanDieuDuong: value, changed: true })
        }
        else {
            const chanDoanDieuDuong = [...state.chanDoanDieuDuong];
            if (!chanDoanDieuDuong[index])
                chanDoanDieuDuong[index] = {};
            if (key) {
                chanDoanDieuDuong[index][key] = value;
            }
            setState({
                chanDoanDieuDuong, changed: true
            })
        }
    }

    const viTriKy = useMemo(() => {
        return state.chiTiet?.indexChiTiet + 1 + state.chiTiet.indexTheoDoi * 500;
    }, [state.chiTiet])

    const {
        onHuyKyPhieu,
        showBtnSign,
        isShowHuyKy,
        getUrlBase64,
        onKyPhieu,
        contentAlign,
        positionCa,
        checkShowbtnHuyKy,
        dataSign,
        checkPermissonSign,
        onTrinhKy,
        refModalPatientSign,
        refModalTrinhKy,
        renderTenChanKy,
        onTuChoiKyPhieu,
        isShowBtnTuChoiKy,
        renderImageSign,
        renderLabelThoiGianKy,
        checkTrangThaiKy,
    } = useSign({
        editorId,
        itemProps: {
            fontSize: 12,
            capKy: 1,
            loaiKy: 1,
            width: 60,
            height: 40,
            showCa: false,
            isMultipleSign: true,
            showPatientSign: false,
            viTri: viTriKy,
            customText: "Ký",
            allowReset: true,
            alwaysShow: true,
            dataSign: {
                id: state.data?.id,
                soPhieu: state.data?.soPhieu,
                lichSuKyId: state.data?.lichSuKy?.id,
            },
        }, form: { lichSuKy: state.data?.lichSuKy, }, mode: "", component: {
            props: {
                // disableIfSigned: true 
            }
        }
    });

    useEffect(() => {
        if (nbDotDieuTriId) {
            getData({ id, maBaoCao, queries })
        }
    }, [id, nbDotDieuTriId, editorId]);

    useEffect(() => {
        if (!state.ngayThucHien && thoiGianThucHien && !id) {
            setState({ ngayThucHien: moment(thoiGianThucHien) });
        }
    }, [thoiGianThucHien, id, state.ngayThucHien])

    useEffect(() => {
        if (state.chiTiet)
            checkTrangThaiKy();
    }, [viTriKy, state.chiTiet])


    const getData = async ({ id, maBaoCao, queries }) => {
        const baoCao = await getBaoCaoByMaBaoCao(editorId, {
            maBaoCao,
            id,
            queries,
        });
        const data = await editorProvider.getFormDataEMR({
            api: baoCao?.api,
            id,
            queries,
        });
        if (data?.code === 0) {
            xuLyData({ data, baoCao });
        }
    }

    const tinhToanCanBangDich = ({
        dsChiTiet,
        chiTiet,
        index
    }) => {
        chiTiet.tongDichTruyen = getTong({ dsChiTiet, chiTiet, index, key: "dichTruyen", keyTong: "tongDichTruyen" });
        chiTiet.tongAnUong = getTong({ dsChiTiet, chiTiet, index, key: "anUong", keyTong: "tongAnUong" });
        chiTiet.tongDichVaoKhac = getTong({ dsChiTiet, chiTiet, index, key: "dichVaoKhac", keyTong: "tongDichVaoKhac" });
        chiTiet.dichVao =
            chiTiet.dichTruyen || chiTiet.anUong || chiTiet.dichVaoKhac
                ? Number(chiTiet.dichTruyen || 0) +
                Number(chiTiet.anUong || 0) +
                Number(chiTiet.dichVaoKhac || 0)
                : "";
        chiTiet.tongDichVao =
            chiTiet.tongDichTruyen || chiTiet.tongAnUong || chiTiet.tongDichVaoKhac
                ? Number(chiTiet.tongDichTruyen || 0) +
                Number(chiTiet.tongAnUong || 0) +
                Number(chiTiet.tongDichVaoKhac || 0)
                : "";

        chiTiet.tongNuocTieu = getTong({ dsChiTiet, chiTiet, index, key: "nuocTieu", keyTong: "tongNuocTieu" });
        chiTiet.tongDichLocMau = getTong({ dsChiTiet, chiTiet, index, key: "dichLocMau", keyTong: "tongDichLocMau" });
        chiTiet.tongDichDaDay = getTong({ dsChiTiet, chiTiet, index, key: "dichDaDay", keyTong: "tongDichDaDay" });
        chiTiet.tongDanLuu = getTong({ dsChiTiet, chiTiet, index, key: "danLuu", keyTong: "tongDanLuu" });
        chiTiet.tongDichRaKhac = getTong({ dsChiTiet, chiTiet, index, key: "dichRaKhac", keyTong: "tongDichRaKhac" });
        chiTiet.tongPhan = getTong({ dsChiTiet, chiTiet, index, key: "phan", keyTong: "tongPhan" });
        chiTiet.dichRa =
            chiTiet.nuocTieu ||
                chiTiet.dichLocMau ||
                chiTiet.dichDaDay ||
                chiTiet.danLuu ||
                chiTiet.dichRaKhac ||
                chiTiet.phan
                ? Number(chiTiet.nuocTieu || 0) +
                Number(chiTiet.dichLocMau || 0) +
                Number(chiTiet.dichDaDay || 0) +
                Number(chiTiet.danLuu || 0) +
                Number(chiTiet.dichRaKhac || 0) +
                Number(chiTiet.phan || 0)
                : "";
        chiTiet.tongDichRa =
            chiTiet.tongNuocTieu ||
                chiTiet.tongDichLocMau ||
                chiTiet.tongDichDaDay ||
                chiTiet.tongDanLuu ||
                chiTiet.tongDichRaKhac ||
                chiTiet.tongPhan
                ? Number(chiTiet.tongNuocTieu || 0) +
                Number(chiTiet.tongDichLocMau || 0) +
                Number(chiTiet.tongDichDaDay || 0) +
                Number(chiTiet.tongDanLuu || 0) +
                Number(chiTiet.tongDichRaKhac || 0) +
                Number(chiTiet.tongPhan || 0)
                : "";
        chiTiet.dichVaoRa =
            chiTiet.dichVao || chiTiet.dichRa
                ? Number(chiTiet.dichVao || 0) - Number(chiTiet.dichRa || 0)
                : "";
        chiTiet.tongDichVaoRa =
            chiTiet.tongDichVao || chiTiet.tongDichRa
                ? Number(chiTiet.tongDichVao || 0) - Number(chiTiet.tongDichRa || 0)
                : "";
    }

    const xuLyData = ({ data, baoCao, currentChiTiet }) => {
        const fileData = data?.data || {};
        let ngayThucHien = null;
        if (!id && fileData.id) {
            history.replace(pathname + "/" + fileData?.id + search);
        }
        let dsChiTiet = flatten((fileData?.dsTheoDoi || []).map((item, index1) => {
            if (!ngayThucHien)
                ngayThucHien = item.ngayThucHien;
            if (!item.dsChiTiet)
                item.dsChiTiet = new Array(24).fill({}).map((chiTiet, idx) => {
                    return {};
                });
            return item.dsChiTiet?.map((chiTiet, index2) => {
                chiTiet = chiTiet || {};
                chiTiet.ngayTheoDoi = item?.ngayThucHien;
                chiTiet.indexTheoDoi = index1;
                chiTiet.indexChiTiet = index2
                return chiTiet;
            });
        }));

        dsChiTiet = dsChiTiet.map((chiTiet, index) => {
            chiTiet.sttChiTiet = index;
            tinhToanCanBangDich({ dsChiTiet, chiTiet, index });
            return chiTiet;
        });

        let chiTiet = dsChiTiet?.[0] || {};
        if (currentChiTiet)
            chiTiet = dsChiTiet?.find(item => item.sttChiTiet === currentChiTiet.sttChiTiet
                && item.indexTheoDoi == item.indexTheoDoi) || dsChiTiet?.[0] || {};

        const chanDoanDieuDuong = [];
        for (let i = 0; i < 10; i++) {
            if (fileData.dsTheoDoi?.[0]?.chanDoanDieuDuong?.["chanDoan" + (i + 1)])
                chanDoanDieuDuong.push(fileData.dsTheoDoi?.[0]?.chanDoanDieuDuong?.["chanDoan" + (i + 1)])
        }

        setState({
            changed: false,
            baoCao,
            data: fileData,
            dsChiTiet,
            chiTiet: cloneDeep(chiTiet),
            chanDoanDieuDuong,
            ghiChu: fileData.dsTheoDoi?.[0]?.ghiChu,
            ghiChuDacBiet: fileData.dsTheoDoi?.[0]?.ghiChuDacBiet,
            ngayThucHien: ngayThucHien ? moment(ngayThucHien) : null
        });
    }

    const thoiGian = useMemo(() => {
        return state.chiTiet.thoiGianThucHien ? moment(state.chiTiet.thoiGianThucHien) : new moment();
    }, [state.chiTiet])

    const readOnly = useMemo(() => {
        return !!dataSign;
    }, [dataSign])

    const onChangeThoiGian = (value) => {
        if (value && thoiGian.diff(value, 'minutes') != 0) {
            // const newMoment = moment(state.ngayThucHien)
            //     .set({
            //         hour: value.hour(),
            //         minute: value.minute(),
            //         second: 0,
            //         millisecond: 0,
            //     });
            state.chiTiet.thoiGianThucHien = value;
            setState({ chiTiet: { ...state.chiTiet }, changed: true });
        }
    }

    const onDeleteForm = async () => {
        const confirm = await showAsyncConfirm(
            {
                title: t("common.xoaDuLieu"),
                content: t("editor.banCoMuonXoaBanGhiNay"),
                cancelText: t("common.quayLai"),
                okText: t("common.dongY"),
                classNameOkText: "button-error",
                showImg: true,
                showBtnOk: true,
            });
        if (confirm.action == "ok") {
            try {
                showLoading();
                await deletePhieuEditor({ api: state.baoCao?.api, id: state.data?.id });
                const url = pathname.substring(0, pathname.lastIndexOf('/'));
                history.push(url + search)
            } catch (error) {
            } finally {
                hideLoading();
            }
        }
    }
    const onSave = async (nextItem) => {
        if (dataSign) {
            showError("Dữ liệu thời gian này đã được ký, không thể chỉnh sửa");
            return;
        }
        if (!nextItem)
            nextItem = state.chiTiet;
        if (!state.ngayThucHien) {
            message.error("Vui lòng nhập ngày thực hiện");
            return;
        }
        showLoading();

        try {
            const [e, data] = await toSafePromise(editorProvider
                .getFormDataEMR({
                    api: state.baoCao?.api,
                    id,
                    queries,
                }));
            if (e) {
                showError(e?.message);
                return;
            }
            let payload = cloneDeep(data.data);
            if (!payload.dsTheoDoi?.[state.chiTiet?.indexTheoDoi]?.dsChiTiet) // nếu chưa có dữ liệu thì khởi tạo sẵn 24 dòng.
                set(payload, `dsTheoDoi[${state.chiTiet?.indexTheoDoi}].dsChiTiet`, new Array(24).fill({}).map((chiTiet, idx) => {
                    return {};
                }));
            //nếu lưu cột mà đang chưa nhập thời gian -> thì mặc định gửi lên thời gian hiện tại
            if (!state.chiTiet.thoiGianThucHien)
                state.chiTiet.thoiGianThucHien = new Date();
            //nếu chỉ số sống chưa có thông tin khoa chỉ định -> thì truyền khoa chỉ định id vào
            if (!state.chiTiet.chiSoSong?.khoaChiDinhId)
                set(state.chiTiet, "chiSoSong.khoaChiDinhId", khoaChiDinhId);
            payload = set(payload, `dsTheoDoi[${state.chiTiet?.indexTheoDoi}].dsChiTiet[${state.chiTiet?.indexChiTiet}]`, state.chiTiet);
            payload.dsTheoDoi.map(item => {
                item.ngayThucHien = state.ngayThucHien;
                item.chanDoanDieuDuong = state.chanDoanDieuDuong.reduce((a, b, index) => {
                    a["chanDoan" + (index + 1)] = b;
                    return a;
                }, {})
                item.ghiChu = state.ghiChu;
                item.ghiChuDacBiet = state.ghiChuDacBiet;
                return item;
            });
            if (!payload.canNang)
                payload.canNang = 90;
            const [e2, res] = await toSafePromise(editorProvider.onSaveForm({
                file: state.baoCao,
                data: payload,
            }));
            if (e2) {
                showError(e2.message);
                return;
            }
            xuLyData({ data: res, baoCao: state.baoCao, currentChiTiet: nextItem || state.chiTiet });
            message.success("Cập nhật dữ liệu thành công")
        } finally {
            hideLoading();
        }
    };


    const listTabs = [
        {
            key: "sinhHieu",
            label: "Sinh hiệu",
            component: <SinhHieu phieuCap1={true} readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "toanThan",
            label: "Toàn thân",
            component: <ToanThan readOnly={readOnly} onChange={onChange} onChangeMultipleKey={onChangeMultipleKey} data={state.chiTiet} />
        },
        {
            key: "hoHap",
            label: "Hô hấp",
            component: <HoHap readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "tuanHoan",
            label: "Tuần hoàn",
            component: <TuanHoan readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "tietNieu",
            label: "Tiết niệu",
            component: <TietNieu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />

        },
        {
            key: "tieuHoa",
            label: "Tiêu hóa",
            component: <TieuHoa readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "giacNgu",
            label: "Giấc ngủ, VSCN",
            component: <GiacNgu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "coXuong",
            label: "Cơ xương",
            component: <CoXuong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "vetThuong",
            label: "Vết thương",
            component: <VetThuong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "danLuu",
            label: "Dẫn lưu",
            component: <DanLuu readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "ongThong",
            label: "Ống thông",
            component: <OngThong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "theoDoiKhac",
            label: "Theo dõi khác",
            component: <TheoDoiKhac readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "canBangDich",
            label: "Cân bằng dịch",
            component: <CanBangDich readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "canThiepDieuDuong",
            label: "Can thiệp điều dưỡng",
            component: <CanThiepDieuDuong readOnly={readOnly} onChange={onChange} data={state.chiTiet} />
        },
        {
            key: "chanDoanDieuDuong",
            label: "Chẩn đoán điều dưỡng",
            component: <ChanDoanDieuDuong readOnly={readOnly} onChange={onChangeCdDieuDuong} data={state.chanDoanDieuDuong} />
        },
        {
            key: "ghiChuBanGiao",
            label: "Ghi chú bàn giao",
            component: <GhiChuBanGiao readOnly={readOnly} onChange={onChange} onChangeText={onChangeText} data={state.chiTiet} state={state} />
        },
    ];
    const trangThaiKy = useMemo(() => {
        return <div className='flex flex-a-center gap-16'>
            <span>Trạng thái ký: </span>
            {
                dataSign ? <div className='signed'>
                    <SVG.IcProtect color={"green"} /> Đã ký bởi <b>{dataSign.tenNguoiKy}</b> lúc <i>{dataSign?.thoiGianKy?.toDateObject().format("dd/MM/yyyy HH:mm")}</i>
                </div> : "Chưa ký"
            }
        </div>
    }, [dataSign])

    const onSaoChep = index => async () => {
        const confirm = await showAsyncConfirm(
            {
                title: t("common.thongBao"),
                content: "Bạn có muốn sao chép dữ liệu của mốc thời gian này thay thế cho bản ghi hiện tại",
                cancelText: t("common.quayLai"),
                okText: t("common.dongY"),
                showImg: true,
                showBtnOk: true,
            });
        debugger;
        if (confirm.action === "ok") {
            const newChiTiet = cloneDeep(state.dsChiTiet?.[index] || {});
            delete newChiTiet.chiSoSongId;
            if (newChiTiet.chiSoSong)
                delete newChiTiet.chiSoSong.id;
            newChiTiet.sttChiTiet = state.chiTiet.sttChiTiet;
            newChiTiet.indexChiTiet = state.chiTiet.indexChiTiet;
            newChiTiet.indexTheoDoi = state.chiTiet.indexTheoDoi;
            newChiTiet.ghiChu = "";
            newChiTiet.ghiChuDacBiet = "";
            setState({ chiTiet: newChiTiet, changed: true })
        }
    }

    const onCopySinhHieu = () => {
        refModalSinhHieu.current?.show({ nbDotDieuTriId, khoaChiDinhId }, sinhHieu => {
            if (sinhHieu) {
                const ngayThucHien = sinhHieu.thoiGianThucHien
                    ? moment(sinhHieu.thoiGianThucHien).format("YYYY-MM-DD")
                    : "";
                const thoiGian = sinhHieu.thoiGianThucHien
                    ? moment(sinhHieu.thoiGianThucHien).format("HH:mm:ss")
                    : "";

                const trungSinhHieu = state.dsChiTiet?.find(el => {
                    return el.chiSoSong?.thoiGianThucHien?.toDateObject().format(
                        "ddMMyyyyHHmm"
                    ) === sinhHieu.thoiGianThucHien?.toDateObject().format("ddMMyyyyHHmm")
                });

                if (trungSinhHieu) {
                    message.error(
                        t("editor.nbDaTonTaiSinhHieu", {
                            time: trungSinhHieu.chiSoSong.thoiGianThucHien?.toDateObject().format(
                                "dd/MM/YYYY HH:mm:ss"
                            ),
                        })
                    );
                } else {
                    const chiTiet = cloneDeep(state.chiTiet);
                    chiTiet.chiSoSong = sinhHieu;
                    chiTiet.ngayThucHien = ngayThucHien;
                    chiTiet.thoiGian = thoiGian;
                    chiTiet.thoiGianThucHien = moment(
                        sinhHieu.thoiGianThucHien
                    ).format("YYYY-MM-DD HH:mm:00");
                    setState({ chiTiet, changed: true });
                }
            }
        })
    }
    const onHandleKy = async () => {
        if (state.changed)
            await onSave();
        if (isShowBtnTuChoiKy) {
            onTuChoiKyPhieu();
            return;
        }
        if (!dataSign) {
            if (checkPermissonSign)
                onKyPhieu();
            else
                onTrinhKy();
        }
        else
            onHuyKyPhieu();
    }

    const onDeleteCol = async () => {
        const confirm = await showAsyncConfirm(
            {
                title: t("common.xoaDuLieu"),
                content: "Bạn có muốn xóa dữ liệu mốc thời gian này",
                cancelText: t("common.quayLai"),
                okText: t("common.dongY"),
                classNameOkText: "button-error",
                showImg: true,
                showBtnOk: true,
            });
        if (confirm.action == "ok") {
            setState({
                changed: true,
                chiTiet: {
                    ngayThucHien: null,
                    ngayTheoDoi: state.chiTiet?.ngayThucHien,
                    indexTheoDoi: state.chiTiet?.indexTheoDoi,
                    indexChiTiet: state.chiTiet?.indexChiTiet,
                    sttChiTiet: state.chiTiet?.sttChiTiet
                }
            })
        }
    }
    const onAddCol = () => {
        const chiTiet = {
            sttChiTiet: state.dsChiTiet.length,
            indexChiTiet: state.dsChiTiet.length % 24,
            indexTheoDoi: Math.floor((state.dsChiTiet.length + 1) / 24),
            ngayTheoDoi: state.data?.thoiGianThucHien
        }
        const dsChiTiet = [...state.dsChiTiet, chiTiet];
        setState({ dsChiTiet });
        handleSelect(chiTiet)
    }
    return (
        <Page
            breadcrumb={[
                { title: "Quản lý nội trú", link: "/quan-ly-noi-tru" },
            ]}
            title={"Phiếu chăm sóc cấp 1"}
            actionRight={<>
                {state.chiTiet &&
                    <>
                        <Button rightIcon={<SVG.IcSign />} minWidth={100}
                            onClick={onHandleKy}>
                            {isShowBtnTuChoiKy ? t("phieuIn.tuChoiKy") : !dataSign ? renderTenChanKy : t("kySo.huyKy")}
                        </Button>
                        {!readOnly &&
                            <Button rightIcon={<SVG.IcVitalSign />} onClick={onCopySinhHieu}>Chọn sinh hiệu</Button>
                        }
                    </>
                }
                <Button rightIcon={<SVG.IcEye />} onClick={() => {
                    window.open(window.location.href.replace("quan-ly-noi-tru/dieu-duong/phieu-cham-soc-cap-1", "editor/bao-cao"));
                }}>
                    Xem tổng hợp
                </Button>
                {(!!id && isEmpty(state.data?.lichSuKy)) &&
                    <Button rightIcon={<SVG.IcDelete />} onClick={onDeleteForm} type="error">
                        Xóa phiếu
                    </Button>
                }
                {state.chiTiet && !readOnly &&
                    <Button rightIcon={<SVG.IcSave />} type="primary" onClick={e => onSave()} minWidth={100}>
                        {t("common.luu")}
                    </Button>
                }
            </>}
        >
            <Main disabled={readOnly}>
                <ThongTinBenhNhan nbDotDieuTriId={nbDotDieuTriId} isShowDongBoGia={false} />
                {!!state.dsChiTiet?.length &&
                    <Row gutter={16} style={{ flex: 1, overflow: 'hidden' }}>
                        <Col className='col-timeline'>
                            <div className="flex gap-5 flex-a-center"><span>Ngày tạo phiếu: </span><DateTimePicker
                                showTime={false}
                                className="flex1"
                                placeholder="DD/MM/YYYY"
                                format="DD/MM/YYYY"
                                style={{ width: 130 }}
                                value={state.ngayThucHien}
                                onChange={e => setState({ ngayThucHien: e, changed: true })}
                            /></div>
                            <hr />
                            <div className='list-timeline'>
                                <Timeline mode="left">
                                    {state.dsChiTiet.map((chiTiet, index) => (
                                        <Timeline.Item
                                            key={index}
                                            color={index === state.chiTiet.sttChiTiet ? "blue" : "gray"}
                                        >
                                            <div className='flex flex-j-spw flex-a-center mr-5'>
                                                <div
                                                    onClick={handleSelect(chiTiet)}
                                                    style={{
                                                        cursor: "pointer",
                                                        fontWeight: index === state.chiTiet.sttChiTiet ? "bold" : "normal",
                                                    }}
                                                >
                                                    {chiTiet.thoiGianThucHien ? chiTiet.thoiGianThucHien.toDateObject().format("dd/MM/yyyy HH:mm") : chiTiet?.ngayTheoDoi ? chiTiet?.ngayTheoDoi.toDateObject()?.format("dd/MM/yyyy") : "Chưa có thông tin"}
                                                </div>
                                                {
                                                    index !== state.chiTiet.sttChiTiet && !dataSign && chiTiet.thoiGianThucHien &&
                                                    <div className='action'>
                                                        <SVG.IcSaoChep className="pointer" onClick={onSaoChep(index)} title="Thực hiện sao chép dữ liệu" />
                                                    </div>
                                                }
                                            </div>
                                        </Timeline.Item>
                                    ))}
                                </Timeline>
                            </div>
                            <hr />
                            <Button rightIcon={<SVG.IcAdd />} onClick={onAddCol} type="primary">
                                Thêm mới thời gian
                            </Button>
                        </Col>

                        <Col flex="auto" style={{ flex: 1, overflow: 'hidden', height: "100%" }} className='form-container'>
                            <Card title={
                                <div className='flex flex-j-spw flex-a-center'>
                                    <div className='flex gap-5 flex-a-center'>{`Thời gian nhận định - `}
                                        <DateTimePicker
                                            value={thoiGian}
                                            onChange={onChangeThoiGian}
                                            placeholder='__/__/__ __:__'
                                            format={"DD/MM/YYYYY HH:mm"}
                                            className="input-filter"
                                            disabled={readOnly}
                                        />
                                        {
                                            !dataSign && <Button type="warning" rightIcon={<SVG.IcDelete />} onClick={onDeleteCol}>Xóa dữ liệu</Button>
                                        }
                                    </div>
                                    {trangThaiKy}
                                </div>
                            } style={{ height: "100%" }}>
                                <Tabs >
                                    {listTabs.map((obj) => {
                                        return (
                                            <Tabs.TabPane
                                                key={obj.key}
                                                tab={
                                                    <div>
                                                        {obj?.label}
                                                    </div>
                                                }
                                            >
                                                {obj.component}
                                            </Tabs.TabPane>
                                        );
                                    })}
                                </Tabs>
                            </Card>
                        </Col>
                    </Row>
                }
            </Main>
            <ModalSelectSinhHieu ref={refModalSinhHieu} />
        </Page >
    )
}
