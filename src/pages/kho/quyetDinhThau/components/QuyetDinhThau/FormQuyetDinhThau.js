import React, { useEffect, useRef } from "react";
import { Input, Form } from "antd";
import {
  Checkbox,
  CreatedWrapper,
  Select,
  InputTimeout,
  DateTimePicker,
} from "components";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { openInNewTab } from "utils";
import { useConfirm, useEnum, useListAll } from "hooks";
import { ENUM, HOTKEY } from "constants/index";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { getState } from "redux-store/stores";

const FormQuyetDinhThau = ({ layerId, ...props }) => {
  const { showConfirm } = useConfirm();
  const [form] = Form.useForm();
  const capThau = Form.useWatch("capThau", form);
  const { t } = useTranslation();
  const { onRegisterHotkey } = useDispatch().phimTat;
  const refClickBtnSave = useRef();
  const refAutoFocus = useRef();

  const { dataEditDefault } = useSelector((state) => state.quyetDinhThau);
  const { listAllNguonNhapKho } = useSelector((state) => state.nguonNhapKho);

  const { listKhoaTheoTaiKhoan } = useSelector((state) => state.khoa);
  const {
    onSearch,
    onComplete,
    onUndoComplete,
    onVerify,
    onUndoVerify,
    createOrEdit,
  } = useDispatch().quyetDinhThau;
  const [listloaiDichVuThuocVatTuHoaChat] = useEnum(
    ENUM.LOAI_DICH_VU_THUOC_VAT_TU_HOA_CHAT
  );
  const [listloaiThau] = useEnum(ENUM.LOAI_THAU);
  const [listtrangThaiThau] = useEnum(ENUM.TRANG_THAI_THAU);
  const [listAllBenhVien] = useListAll("benhVien");
  const [listCapThau] = useEnum(ENUM.CAP_THAU);
  const [listAllTinh] = useListAll("tinh", { active: true }, true);

  useEffect(() => {
    if (dataEditDefault) {
      const newInfo = {
        ...dataEditDefault,
        ngayCongBo:
          dataEditDefault.ngayCongBo && moment(dataEditDefault.ngayCongBo),
        ngayHieuLuc:
          dataEditDefault.ngayHieuLuc && moment(dataEditDefault.ngayHieuLuc),
        ngayHopDong:
          dataEditDefault.ngayHopDong && moment(dataEditDefault.ngayHopDong),
      };
      form.setFieldsValue(newInfo);
    } else {
      form.resetFields();
    }
  }, [dataEditDefault]);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
  }, []);
  const dateFormat = "DD/MM/YYYY";

  const onRefetch = () => {
    onSearch({
      page: getState()?.quyetDinhThau?.page ?? 0,
    });
  };

  const onSave = () => {
    form.submit();
  };
  refClickBtnSave.current = onSave;

  const onCompleteDocument = () => {
    showConfirm(
      {
        title: "Xác nhận",
        content: t("kho.quyetDinhThau.banCoChacChanHoanThanhQuyetDinhThau"),
        cancelText: "Huỷ",
        okText: "Đồng ý",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        onComplete(dataEditDefault?.id)
          .then(() => {
            onRefetch();
          })
          .catch((err) => console.error(err));
      }
    );
  };

  const onUndoCompleteDocument = () => {
    showConfirm(
      {
        title: "Xác nhận",
        content: t("kho.quyetDinhThau.banCoChacChanHuyHoanThanhQuyetDinhThau"),
        cancelText: "Huỷ",
        okText: "Đồng ý",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        onUndoComplete(dataEditDefault?.id)
          .then(() => {
            onRefetch();
          })
          .catch((err) => console.error(err));
      }
    );
  };

  const onVerifyDocument = () => {
    showConfirm(
      {
        title: "Xác nhận",
        content: t("kho.quyetDinhThau.banCoChacChanDuyetQuyetDinhThau"),
        cancelText: "Huỷ",
        okText: "Đồng ý",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        onVerify(dataEditDefault?.id)
          .then(() => {
            onRefetch();
          })
          .catch((err) => console.error(err));
      }
    );
  };

  const onUndoVerifyDocument = () => {
    showConfirm(
      {
        title: "Xác nhận",
        content: t("kho.quyetDinhThau.banCoChacChanHuyDuyetQuyetDinhThau"),
        cancelText: "Huỷ",
        okText: "Đồng ý",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        onUndoVerify(dataEditDefault?.id)
          .then(() => {
            onRefetch();
          })
          .catch((err) => console.error(err));
      }
    );
  };

  const onCancel = (data) => {
    const newInfo = {
      ...data,
      ngayCongBo: data.ngayCongBo && moment(data.ngayCongBo),
      ngayHieuLuc: data.ngayHieuLuc && moment(data.ngayHieuLuc),
    };
    form.setFieldsValue(newInfo);
  };

  const setButton = (value) => {
    if ("cancel" === value) {
      if (!dataEditDefault || dataEditDefault.trangThai !== 20)
        return () => onCancel(dataEditDefault);
      if (20 === dataEditDefault?.trangThai) return onUndoCompleteDocument;
    }
    if ("ok" === value) {
      if (!dataEditDefault) return onSave;
      if (10 === dataEditDefault?.trangThai) return onCompleteDocument;
      if (20 === dataEditDefault?.trangThai) return onVerifyDocument;
      if (30 === dataEditDefault?.trangThai) return onUndoVerifyDocument;
    }
  };

  const setButtonText = (value) => {
    if ("cancel" === value) {
      if (!dataEditDefault) return t("common.huy");
      if (20 === dataEditDefault?.trangThai)
        return t("kho.quyetDinhThau.huyHoanThanh");
    }
    if ("ok" === value) {
      if (!dataEditDefault) return `${t("common.luu")} [F4]`;
      if (10 === dataEditDefault?.trangThai)
        return `${t("kho.quyetDinhThau.hoanThanh")} [F4]`;
      if (20 === dataEditDefault?.trangThai)
        return `${t("kho.quyetDinhThau.duyet")} [F4]`;
      if (30 === dataEditDefault?.trangThai)
        return `${t("kho.quyetDinhThau.huyDuyet")} [F4]`;
    }
  };

  const setHiddenButton = (value) => {
    if ("cancel" === value)
      return 20 !== dataEditDefault?.trangThai && dataEditDefault;
  };

  const handleSumitForm = (values) => {
    if (values) {
      const id = dataEditDefault?.id;
      const body = {
        ...values,
        ngayCongBo: values.ngayCongBo.format("YYYY-MM-DD"),
        ngayHieuLuc: values.ngayHieuLuc.format("YYYY-MM-DD"),
        ngayHopDong: values.ngayHopDong?.format("YYYY-MM-DD"),
        id: id,
      };

      createOrEdit(body)
        .then((s) => {
          if (id) {
            onRefetch();
          } else {
            onSearch();
          }
        })
        .catch((err) => console.error(err));
    }
  };

  const handleValuesChange = (changeValues) => {
    if (changeValues.capThau) {
      form.setFieldsValue({
        maSo: null,
        benhVienId: null,
        tinhThanhPhoId: null,
      });
    }
  };

  const handleErrors = (errors) => {
    console.log("errors: ", errors);
  };
  return (
    <>
      <CreatedWrapper
        title={t("kho.quyetDinhThau.thongTinChiTiet")}
        onCancel={setButton("cancel")}
        cancelText={setButtonText("cancel")}
        hiddenCancel={setHiddenButton("cancel")}
        onOk={setButton("ok")}
        okText={setButtonText("ok")}
        hiddenOk={setHiddenButton("ok")}
        additionBtn={
          dataEditDefault?.trangThai == 10
            ? {
                text: `${t("common.luu")}`,
                onClick: onSave,
                rightIcon: <SVG.IcSave />,
              }
            : null
        }
      >
        <Form
          form={form}
          onFinish={handleSumitForm}
          onError={handleErrors}
          onValuesChange={handleValuesChange}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
        >
          <Form.Item
            label={t("kho.quyetDinhThau.nam")}
            name="nam"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongChonNam"),
              },
            ]}
          >
            <Input
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              className="input-option"
              placeholder={t("kho.quyetDinhThau.vuiLongNhapNam")}
              ref={refAutoFocus}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.title")}
            name="quyetDinhThau"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongNhapQuyetDinhThau"),
              },

              {
                whitespace: true,
                message: t("kho.quyetDinhThau.vuiLongNhapQuyetDinhThau"),
              },
            ]}
          >
            <Input
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              className="input-option"
              placeholder={t("kho.quyetDinhThau.vuiLongNhapQuyetDinhThau")}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.tenGoiThau")}
            name="goiThau"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongNhapGoiThau"),
              },

              {
                whitespace: true,
                message: t("kho.quyetDinhThau.vuiLongNhapGoiThau"),
              },
            ]}
          >
            <Input
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              className="input-option"
              placeholder={t("kho.quyetDinhThau.vuiLongNhapGoiThau")}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.loaiDichVu")}
            name="dsLoaiDichVu"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongChonLoaiDichVu"),
              },
            ]}
          >
            <Select
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              data={listloaiDichVuThuocVatTuHoaChat}
              mode="multiple"
              placeholder={t("kho.quyetDinhThau.vuiLongChonLoaiDichVu")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nguon-nhap-kho")}
              >
                {t("kho.nguonNhapKho")}
              </div>
            }
            name="nguonNhapKhoId"
            rules={[
              {
                required: true,
                message: t("kho.vuiLongChonNguonNhapKho"),
              },
            ]}
          >
            <Select
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              data={listAllNguonNhapKho}
              placeholder={t("kho.vuiLongChonNguonNhapKho")}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.loaiThau")}
            name="loaiThau"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongChonLoaiThau"),
              },
            ]}
          >
            <Select
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              data={listloaiThau}
              placeholder={t("kho.quyetDinhThau.vuiLongChonLoaiThau")}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.ngayCongBo")}
            name="ngayCongBo"
            className="item-date"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongChonNgayCongBo"),
              },
            ]}
          >
            <DateTimePicker
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              placeholder={t("kho.quyetDinhThau.vuiLongChonNgayCongBo")}
              format={dateFormat}
              showTime={false}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.hieuLucThau")}
            name="ngayHieuLuc"
            className="item-date"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongChonHieuLucThau"),
              },
            ]}
          >
            <DateTimePicker
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              placeholder={t("kho.quyetDinhThau.vuiLongChonHieuLucThau")}
              format={dateFormat}
              showTime={false}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.khoa")}
            name="khoaId"
            rules={[
              {
                required: true,
                message: t("kho.vuiLongChonKhoa"),
              },
            ]}
          >
            <Select
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              data={listKhoaTheoTaiKhoan}
              placeholder={t("kho.vuiLongChonKhoa")}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.trangThai")}
            name="trangThai"
            defaultValue={10}
          >
            <Select data={listtrangThaiThau} disabled />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.ngayHopDong")}
            name="ngayHopDong"
            className="item-date"
          >
            <DateTimePicker
              disabled={10 !== dataEditDefault?.trangThai && dataEditDefault}
              placeholder={t("kho.quyetDinhThau.chonNgayHopDong")}
              format={dateFormat}
              showTime={false}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.capThau")}
            name="capThau"
            // rules={[
            //   {
            //     required: true,
            //     message: t("kho.quyetDinhThau.vuiLongChonCapThau"),
            //   },
            // ]}
          >
            <Select
              placeholder={t("kho.quyetDinhThau.vuiLongChonCapThau")}
              data={listCapThau}
            />
          </Form.Item>
          {capThau == 10 && (
            <Form.Item
              label={t("kho.quyetDinhThau.benhVien")}
              name="benhVienId"
              rules={[
                {
                  validator: (rules, value, callback) => {
                    if (!value && form.getFieldValue("capThau") == 10) {
                      callback(t("kho.quyetDinhThau.vuiLongChonBenhVien"));
                    } else {
                      callback();
                    }
                  },
                },
              ]}
            >
              <Select
                placeholder={t("kho.quyetDinhThau.vuiLongChonBenhVien")}
                data={listAllBenhVien}
              />
            </Form.Item>
          )}
          {capThau == 30 && (
            <Form.Item
              label={t("kho.quyetDinhThau.maSo")}
              name="maSo"
              rules={[
                {
                  validator: (rules, value, callback) => {
                    if (!value && form.getFieldValue("capThau") == 30) {
                      callback(t("kho.quyetDinhThau.vuiLongChonMaSo"));
                    } else {
                      callback();
                    }
                  },
                },
              ]}
            >
              <InputTimeout
                placeholder={t("kho.quyetDinhThau.vuiLongChonMaSo")}
              />
            </Form.Item>
          )}
          {capThau == 40 && (
            <Form.Item
              label={t("kho.quyetDinhThau.tinhTP")}
              name="tinhThanhPhoId"
              rules={[
                {
                  validator: (rules, value, callback) => {
                    if (!value && form.getFieldValue("capThau") == 40) {
                      callback(t("kho.quyetDinhThau.vuiLongChonTinhTP"));
                    } else {
                      callback();
                    }
                  },
                },
              ]}
            >
              <Select
                placeholder={t("kho.quyetDinhThau.vuiLongChonTinhTP")}
                data={listAllTinh}
              />
            </Form.Item>
          )}
          <Form.Item
            label={t("kho.quyetDinhThau.soThau")}
            name="soThau"
            rules={[
              {
                required: true,
                message: t("kho.quyetDinhThau.vuiLongNhapSoThau"),
              },
            ]}
          >
            <InputTimeout
              className="input-option"
              placeholder={t("kho.quyetDinhThau.vuiLongNhapSoThau")}
            />
          </Form.Item>
          <Form.Item
            label={t("kho.quyetDinhThau.soQDDuyetThau")}
            name="soQuyetDinhThau"
          >
            <InputTimeout
              className="input-option"
              placeholder={t("kho.quyetDinhThau.nhapSoQDDuyetThau")}
            />
          </Form.Item>
          {dataEditDefault && (
            <Form.Item
              name="active"
              valuePropName="checked"
              // hidden={10 !== dataEditDefault?.trangThai && dataEditDefault}
            >
              <Checkbox>{t("kho.coHieuLuc")}</Checkbox>
            </Form.Item>
          )}
        </Form>
      </CreatedWrapper>
    </>
  );
};
export default FormQuyetDinhThau;
