import React, { useEffect, useState, useRef, useMemo } from "react";
import { useDispatch } from "react-redux";
import ThietLapChonKho from "components/DanhMuc/ThietLapChonKho";
import KhoTrucThuoc from "components/DanhMuc/KhoTrucThuoc";
import NhanVienQuanLy from "components/DanhMuc/NhanVienQuanLy";
import MultiLevelTab from "components/MultiLevelTab";
import Kho from "components/DanhMuc/Kho";
import ThongTinKho from "components/DanhMuc/ThongTinKho";
import { Main } from "./styled";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  ROLES,
  HOTKEY,
} from "constants/index";
import { Col } from "antd";
import { useGuid, useStore } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { Button } from "components";
import ThietLapChanKy from "./components/ThietLapChanKy";

const QuanTriKho = (props) => {
  const { t } = useTranslation();
  const [collapseStatus, setCollapseStatus] = useState(false);
  const [editStatus, setEditStatus] = useState(false);
  const {
    kho: { updateData },
  } = useDispatch();
  const currentItem = useStore("kho.currentItem");

  const [state, _setState] = useState({
    showFullTable: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const layerId = useGuid();
  const refClickBtnAdd = useRef();
  const { onAddLayer, onRemoveLayer, onRegisterHotkey } = useDispatch().phimTat;
  // register layerId
  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  const listPanel = [
    {
      title: t("kho.thongTinKho"),
      key: 1,
      render: () => {
        return (
          <ThongTinKho
            currentItem={currentItem}
            layerId={layerId}
            roleSave={[ROLES["KHO"].THEM_QUAN_TRI_KHO]}
            roleEdit={[ROLES["KHO"].SUA_QUAN_TRI_KHO]}
          />
        );
      },
    },
    {
      key: 2,
      title: t("kho.khoTrucThuoc"),
      render: () => {
        return (
          <KhoTrucThuoc
            khoQuanLyId={currentItem?.id}
            parentData={currentItem}
            roleSave={[ROLES["KHO"].THEM_QUAN_TRI_KHO]}
            roleEdit={[ROLES["KHO"].SUA_QUAN_TRI_KHO]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["KHO"].SUA_QUAN_TRI_KHO])
                : !checkRole([ROLES["KHO"].THEM_QUAN_TRI_KHO])
            }
          />
        );
      },
    },
    {
      key: 3,
      title: t("kho.thietLapKhoChiDinh"),
      render: () => {
        return (
          <ThietLapChonKho
            khoId={currentItem?.id}
            roleSave={[ROLES["KHO"].THEM_QUAN_TRI_KHO]}
            roleEdit={[ROLES["KHO"].SUA_QUAN_TRI_KHO]}
          />
        );
      },
    },
    {
      key: 4,
      title: t("kho.nhanVienQuanLy"),
      render: () => {
        return (
          <NhanVienQuanLy
            khoId={currentItem?.id}
            roleSave={[ROLES["KHO"].THEM_QUAN_TRI_KHO]}
            roleEdit={[ROLES["KHO"].SUA_QUAN_TRI_KHO]}
          />
        );
      },
    },
    {
      key: 5,
      title: t("kho.thietLapChanKy"),
      render: () => {
        return <ThietLapChanKy khoId={currentItem?.id} />;
      },
    },
  ];

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    updateData({
      currentItem: {},
    });
  };
  refClickBtnAdd.current = handleClickedBtnAdded;

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };
  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };

  const buttonHeader = useMemo(() => {
    const arr = [
      {
        className: `btn-change-full-table ${
          state.showFullTable ? "small" : "large"
        }`,
        title: state.showFullTable ? <SVG.IcShowThuNho /> : <SVG.IcShowFull />,
        onClick: handleChangeshowTable,
      },
      {
        className: "btn-collapse",
        title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
        onClick: handleCollapsePane,
      },
    ];

    if (checkRole([ROLES["KHO"].THEM_QUAN_TRI_KHO]))
      arr.unshift({
        content: (
          <Button
            type="success"
            onClick={handleClickedBtnAdded}
            rightIcon={<SVG.IcAdd />}
          >
            {t("common.themMoi")}
          </Button>
        ),
      });

    return arr;
  }, [collapseStatus, state.showFullTable]);

  return (
    <Main
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        {
          title: t("kho.thietLapQuanTriKho"),
          link: "/kho/quan-tri-kho",
        },
      ]}
    >
      <Col
        {...(!state.showFullTable
          ? collapseStatus
            ? TABLE_LAYOUT_COLLAPSE
            : TABLE_LAYOUT
          : null)}
        span={state.showFullTable ? 24 : null}
        className={`pr-3 ${state.changeShowFullTbale ? "" : "transition-ease"}`}
      >
        <Kho
          title={t("kho.quanTriKho")}
          scroll={{ x: 1000 }}
          classNameRow={"custom-header"}
          styleMain={{ marginTop: 0 }}
          styleContainerButtonHeader={{
            display: "flex",
            width: "100%",
            justifyContent: "flex-end",
            alignItems: "center",
            paddingRight: 35,
          }}
          buttonHeader={buttonHeader}
          layerId={layerId}
          setEditStatus={setEditStatus}
        />
      </Col>
      {!state.showFullTable && (
        <Col
          {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
          className={`tab-area mt-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
          style={
            state.isSelected
              ? { border: "2px solid #c1d8fd", borderRadius: 20 }
              : {}
          }
        >
          <MultiLevelTab
            defaultActiveKey={1}
            listPanel={listPanel}
            isBoxTabs={true}
          ></MultiLevelTab>
        </Col>
      )}
    </Main>
  );
};

export default QuanTriKho;
