import React, { useRef, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { Main } from "./styled";
import { Pagination, TableWrapper, HeaderSearch, Checkbox } from "components";
import { useEnum, useQueryString, useStore, useThietLap } from "hooks";
import { ENUM, LOAI_NHAP_XUAT, THIET_LAP_CHUNG } from "constants/index";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { isEqual } from "lodash";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { isArray } from "utils/index";

const listTrangThaiShowSoPhieuDoiUng = [
  LOAI_NHAP_XUAT.DU_TRU, //20
  LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO, //30
  LOAI_NHAP_XUAT.LINH_BU_TU_TRUC, //80
  LOAI_NHAP_XUAT.LINH_NOI_TRU, //85
];

const DanhSachPhieuXuat = (props, ref) => {
  const { t } = useTranslation();
  const history = useHistory();
  const refSettings = useRef(null);

  const [dataYC_DUYET_DLS_PHIEU_LINH] = useThietLap(
    THIET_LAP_CHUNG.YC_DUYET_DLS_PHIEU_LINH
  );
  const [dataSO_PHIEU_NHAP_XUAT_KHO] = useThietLap(
    THIET_LAP_CHUNG.SO_PHIEU_NHAP_XUAT_KHO
  );

  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT, []);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);

  const { phieuNhapXuatId } = useSelector((state) => state.nhapKhoChiTiet);
  const {
    sort = {},
    listPhieuXuat,
    totalElements,
    page,
    size,
    dataSearch,
  } = useStore("phieuXuat", null, {
    fields: "sort, listPhieuXuat, totalElements, page, size, dataSearch",
  });

  const {
    phieuXuat: { getListPhieuXuat, onSizeChange },
    nhapKhoChiTiet: { updateData: updateDataNhapKho },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    settingTable: () => {
      onSettings();
    },
  }));

  const isTachSoPhieu = useMemo(() => {
    return [1, 2, 3, 4].includes(parseInt(dataSO_PHIEU_NHAP_XUAT_KHO));
  }, [dataSO_PHIEU_NHAP_XUAT_KHO]);

  const onClickSort = (key, value) => {
    getListPhieuXuat({ sort: { key, value } });
  };
  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);

    getListPhieuXuat({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        const { id } = record;
        let pathname = "";

        if (record.loaiNhapXuat === 80) {
          pathname = `/kho/xuat-kho/chi-tiet-linh-bu/${id}`;
        } else if (record.loaiNhapXuat === 85) {
          pathname = `/kho/chi-tiet-phieu-linh/${id}`;
        } else {
          pathname = `/kho/xuat-kho/chi-tiet/${id}`;
        }

        history.push({
          pathname,
          state: getAllQueryString(undefined, { filterEmpty: false }),
        });
        updateDataNhapKho({
          phieuNhapXuatId: id,
          currentItem: { ...record },
        });
      },
    };
  };

  const setRowClassName = (record) => {
    let idDiff;
    idDiff = phieuNhapXuatId;
    return record.id === idDiff ? "row-selected-detail" : "";
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const tenSoPhieuDoiUng = (() => {
    if (
      [LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO].some((i) =>
        dataSearch?.dsLoaiNhapXuat?.includes(i)
      )
    ) {
      return "kho.soPhieuNhap";
    }
    if (
      [
        LOAI_NHAP_XUAT.DU_TRU,
        LOAI_NHAP_XUAT.LINH_BU_TU_TRUC,
        LOAI_NHAP_XUAT.LINH_NOI_TRU,
      ].some((i) => dataSearch?.dsLoaiNhapXuat?.includes(i))
    ) {
      return "baoCao.soPhieuXuat";
    }
    return "goiDichVu.soPhieuDoiUng";
  })();

  const isShowSoPhieuDoiUng = (() => {
    if (isArray(dataSearch?.dsLoaiNhapXuat, true)) {
      return (
        isTachSoPhieu &&
        listTrangThaiShowSoPhieuDoiUng.some((i) =>
          dataSearch?.dsLoaiNhapXuat?.includes(i)
        )
      );
    } else {
      return isTachSoPhieu;
    }
  })();

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      fixed: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.soPhieu")}
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={sort.key === "soPhieu" ? sort.value : 0}
        />
      ),
      width: 80,
      dataIndex: "soPhieu",
      key: "soPhieu",
      align: "center",
      fixed: "left",
      i18Name: "kho.soPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t(tenSoPhieuDoiUng)}
          sort_key="soPhieuDoiUng"
          onClickSort={onClickSort}
          dataSort={sort.key === "soPhieuDoiUng" ? sort.value : 0}
        />
      ),
      width: 100,
      dataIndex: "soPhieuDoiUng",
      key: "soPhieuDoiUng",
      align: "center",
      i18Name: tenSoPhieuDoiUng,
      show: true,
      hidden: !isShowSoPhieuDoiUng,
      render: (item, data) => {
        if (listTrangThaiShowSoPhieuDoiUng.includes(data.loaiNhapXuat)) {
          return item;
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.thanhTien")}
          sort_key="thanhTien"
          onClickSort={onClickSort}
          dataSort={sort.key === "thanhTien" ? sort.value : 0}
        />
      ),
      width: 80,
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      i18Name: "kho.thanhTien",
      show: true,
      render: (item) => item && item.formatPrice(),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.phieuLinhLan")}
          sort_key="lan"
          onClickSort={onClickSort}
          dataSort={sort.key === "lan" ? sort.value : 0}
        />
      ),
      width: 60,
      dataIndex: "lan",
      key: "lan",
      i18Name: "kho.phieuLinhLan",
      show: true,
      hidden: !isEqual(dataSearch?.dsLoaiNhapXuat || [], [80, 85]), // phiếu lĩnh
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoNhap")}
          sort_key="kho"
          onClickSort={onClickSort}
          dataSort={sort.key === "kho" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "tenKho",
      key: "tenKho",
      //Xem phiếu xuất + phiếu lĩnh => hiện tên kho đối ứng
      render: (item, data) =>
        [30, 40, 85, 90].includes(data?.loaiNhapXuat)
          ? data.tenKhoDoiUng
          : item,
      i18Name: "kho.khoNhap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.xuat.trangThai")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={sort.key === "trangThai" ? sort.value : 0}
        />
      ),
      width: 80,
      dataIndex: "trangThai",
      key: "trangThai",
      align: "center",
      render: (item) => {
        return listTrangThaiPhieuNhapXuat.find((x) => x.id == item)?.ten;
      },
      i18Name: "kho.xuat.trangThai",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.trangThaiDls")}
          sort_key="trangThaiDls"
          onClickSort={onClickSort}
          dataSort={sort.key === "trangThaiDls" ? sort.value : 0}
        />
      ),
      width: 80,
      dataIndex: "trangThaiDls",
      key: "trangThaiDls",
      align: "center",
      render: (item) => {
        return listTrangThaiPhieuNhapXuat.find((x) => x.id == item)?.ten;
      },
      i18Name: "kho.trangThaiDls",
      show: true,
      hidden: !(
        dataYC_DUYET_DLS_PHIEU_LINH &&
        dataYC_DUYET_DLS_PHIEU_LINH.toLowerCase() === "true"
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.loaiXuat")}
          sort_key="loaiNhapXuat"
          onClickSort={onClickSort}
          dataSort={sort.key === "loaiNhapXuat" ? sort.value : 0}
        />
      ),
      width: 100,
      key: "loaiNhapXuat",
      align: "center",
      dataIndex: "loaiNhapXuat",
      render: (item) => listLoaiNhapXuat?.find((i) => i.id === item)?.ten,
      i18Name: "kho.loaiXuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dieuChinhCoSoTren")}
          sort_key="dieuChinhCoSo"
          dataSort={sort.key === "dieuChinhCoSo" ? sort.value : 0}
          onClickSort={onClickSort}
        />
      ),
      width: 100,
      dataIndex: "dieuChinhCoSo",
      key: "dieuChinhCoSo",
      i18Name: "kho.dieuChinhCoSoTren",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO].some((i) =>
        dataSearch?.dsLoaiNhapXuat?.includes(i)
      ),
      render: (field) => (
        <div className="flex-center">
          <Checkbox defaultChecked={!!field} />
        </div>
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dieuChinhCoSoDuoi")}
          sort_key="dieuChinhCoSoDuoi"
          dataSort={sort.key === "dieuChinhCoSoDuoi" ? sort.value : 0}
          onClickSort={onClickSort}
        />
      ),
      width: 100,
      dataIndex: "dieuChinhCoSoDuoi",
      key: "dieuChinhCoSoDuoi",
      i18Name: "kho.dieuChinhCoSoDuoi",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO].some((i) =>
        dataSearch?.dsLoaiNhapXuat?.includes(i)
      ),
      render: (field) => (
        <div className="flex-center">
          <Checkbox defaultChecked={!!field} />
        </div>
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.hinhThucXuat")}
          sort_key="tenHinhThucNhapXuat"
          onClickSort={onClickSort}
          dataSort={sort.key === "tenHinhThucNhapXuat" ? sort.value : 0}
        />
      ),
      width: 100,
      key: "tenHinhThucNhapXuat",
      dataIndex: "tenHinhThucNhapXuat",
      i18Name: "kho.hinhThucXuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.thangDuTru")}
          sort_key="thangDuTru"
          onClickSort={onClickSort}
          dataSort={sort.key === "thangDuTru" ? sort.value : 0}
        />
      ),
      width: 80,
      dataIndex: "thangDuTru",
      align: "center",
      key: "thangDuTru",
      render: (item) => (item ? "Tháng " + item : ""),
      i18Name: "kho.thangDuTru",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoXuat")}
          sort_key="khoDoiUng.ten"
          onClickSort={onClickSort}
          dataSort={sort.key === "khoDoiUng.ten" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "tenKhoDoiUng",
      key: "tenKhoDoiUng",
      //Xem phiếu xuất + phiếu lĩnh => hiện tên kho
      render: (item, data) =>
        [30, 40, 85, 90].includes(data.loaiNhapXuat) ? data.tenKho : item,
      i18Name: "kho.khoXuat",
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.khoaChiDinh")} />,
      width: 120,
      dataIndex: "tenKhoaChiDinh",
      key: "tenKhoaChiDinh",
      i18Name: "kho.khoaChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.nguoiTao")}
          sort_key="tenNguoiTaoPhieu"
          onClickSort={onClickSort}
          dataSort={sort.key === "tenNguoiTaoPhieu" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "tenNguoiTaoPhieu",
      key: "tenNguoiTaoPhieu",
      i18Name: "kho.nguoiTao",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.thoiGianTaoPhieu")}
          sort_key="thoiGianTaoPhieu"
          onClickSort={onClickSort}
          dataSort={sort.key === "thoiGianTaoPhieu" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "thoiGianTaoPhieu",
      key: "thoiGianTaoPhieu",
      render: (item, data) => {
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : null;
      },
      i18Name: "kho.thoiGianTaoPhieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.loaiPhieuLinh")}
          onClickSort={onClickSort}
          dataSort={sort.key === "loaiToDieuTri" ? sort.value : 0}
        />
      ),
      show: true,
      width: 120,
      dataIndex: "loaiToDieuTri",
      i18Name: "quanLyNoiTru.phieuLinh.loaiPhieuLinh",
      hidden: ![
        LOAI_NHAP_XUAT.LINH_BU_TU_TRUC,
        LOAI_NHAP_XUAT.LINH_NOI_TRU,
      ].some((i) => dataSearch?.dsLoaiNhapXuat?.includes(i)),
      render: (item, data) => {
        return item === 20
          ? t("quanLyNoiTru.phieuLinh.phieuLinhRaVien")
          : data.loaiNhapXuat === 85
          ? t("quanLyNoiTru.phieuLinh.phieuLinhNoiTru")
          : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.thoiGianDuyetPhieu")}
          sort_key="thoiGianDuyet"
          onClickSort={onClickSort}
          dataSort={sort.key === "thoiGianDuyet" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "thoiGianDuyet",
      key: "thoiGianDuyet",
      render: (item, data) => {
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : null;
      },
      i18Name: "kho.thoiGianDuyetPhieu",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.loaiChiDinh")} />,
      width: 120,
      dataIndex: "loaiChiDinh",
      key: "loaiChiDinh",
      align: "center",
      i18Name: t("common.loaiChiDinh"),
      show: true,
      hidden: !isEqual(dataSearch?.dsLoaiNhapXuat, [80, 85]), // phiếu lĩnh
      render: (item) => {
        return listLoaiChiDinh.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.nguoiDuyet")}
          sort_key="tenNguoiDuyetDls"
          onClickSort={onClickSort}
          dataSort={sort.key === "tenNguoiDuyetDls" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "tenNguoiDuyetDls",
      key: "tenNguoiDuyetDls",
      i18Name: "kho.nguoiDuyet",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("nhaThuoc.nguoiPhat")}
          sort_key="tenNguoiDuyet"
          onClickSort={onClickSort}
          dataSort={sort.key === "tenNguoiDuyet" ? sort.value : 0}
        />
      ),
      width: 120,
      dataIndex: "tenNguoiDuyet",
      key: "tenNguoiDuyet",
      i18Name: "nhaThuoc.nguoiPhat",
      show: true,
    },
  ];
  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={listPhieuXuat}
        onRow={onRow}
        scroll={{ x: 2100 }}
        rowKey={(record) => `${record.id}`}
        rowClassName={setRowClassName}
        tableName="table_KHO_DSPhieuXuat"
        ref={refSettings}
        columnResizable={true}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listPhieuXuat}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          stylePagination={{ justifyContent: "flex-start" }}
        />
      )}
    </Main>
  );
};

export default forwardRef(DanhSachPhieuXuat);
