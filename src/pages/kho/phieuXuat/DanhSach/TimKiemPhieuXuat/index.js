import React, { useEffect, useState, useMemo, forwardRef } from "react";
import { useDispatch } from "react-redux";
import { debounce } from "lodash";
import { BaseSearch } from "components";
import {
  useCache,
  useEnum,
  useQueryAll,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import { useTranslation } from "react-i18next";
import {
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import {
  LOAI_PHIEU_XUAT,
  ENUM,
  ROLES,
  THIET_LAP_CHUNG,
  CACHE_KEY,
} from "constants/index";
import { cleanEmptyData, transformQueryString } from "utils/index";
import { Main } from "./styled";
import { query } from "redux-store/stores";

const listTrangThaiDuyetDls = [
  { i18n: "kho.choDuyetDLS", id: 20 },
  { i18n: "kho.daDuyetDLS", id: 26 },
];

const TimKiemPhieuXuat = (props, ref) => {
  const { t } = useTranslation();
  const [soPhieu] = useQueryString("soPhieu");

  const MAC_DINH_XEM_TAT_CA_LOAI_XUAT = checkRole([
    ROLES.KHO.MAC_DINH_XEM_TAT_CA_LOAI_XUAT,
  ]);

  const [state, _setState] = useState({
    type: MAC_DINH_XEM_TAT_CA_LOAI_XUAT ? undefined : 1,
    soPhieu,
    dsKhoNhapId: null,
    dsTrangThai: null,
    searchKhoNhap: "",
    searchKhoXuat: "",
  });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const authId = useStore("auth.auth.id");
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
  const listHinhThucNhapXuat = useStore("hinhThucNhapXuat.listTongHop", []);
  const listKhoUser = useStore("kho.listKhoUser", []);
  const [dataSO_PHIEU_NHAP_XUAT_KHO] = useThietLap(
    THIET_LAP_CHUNG.SO_PHIEU_NHAP_XUAT_KHO
  );

  const [dsTrangThaiDls, setDsTrangThaiDls, loadFinish] = useCache(
    authId,
    CACHE_KEY.DS_TRANG_THAI_DLS_PHIEU_XUAT
  );

  const {
    phieuXuat: { onChangeInputSearch, onSizeChange },
    hinhThucNhapXuat: { getTongHop: getListHinhThucNhapXuat },
    khoa: { queryAllKhoa },
  } = useDispatch();

  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);

  useEffect(() => {
    getListHinhThucNhapXuat({
      page: "",
      size: "",
      active: true,
      dsHinhThucNhapXuat: [10, 20],
    });
  }, []);

  const dsKhoUserId = useMemo(() => {
    return (listKhoUser || []).map((item) => item.id);
  }, [listKhoUser]);

  const listTrangThaiMemo = useMemo(() => {
    //10: Tạo mới, 15: Tạo mới, đã giữ chỗ, 20: Chờ duyệt, 28: Chờ xác nhận nhập, 30: Đã duyệt
    return (listTrangThaiPhieuNhapXuat || [])
      .filter((x) => [10, 15, 20, 28, 30].includes(x.id))
      .map((item) => ({ label: item.ten, value: item.id }));
  }, [listTrangThaiPhieuNhapXuat]);

  const onChange = (key) => (e) => {
    const newList = Object.assign([], state[key]) || [];
    const index = newList.findIndex((item) => item === e);

    if (index !== -1) {
      newList.splice(index, 1);
    } else if (e) {
      newList.push(e);
    }

    let value = !e ? null : newList;
    if (
      (key === "dsTrangThai" ||
        key === "dsKhoNhapId" ||
        key === "dsKhoXuatId") &&
      !newList.length
    ) {
      value = null;
    }
    setState({ [key]: value });
    if (!dsKhoUserId.length) return;
    onChangeInputSearch({ [key]: value });
    setQueryStringValue(key, value);
  };

  const filterType = (type) => {
    let data = {};
    const setFilterKho = (obj) => {
      if (state.dsKhoXuatId?.length) {
        obj.dsKhoXuatId = state.dsKhoXuatId;
      } else {
        if (dsKhoUserId?.length) obj.dsKhoXuatId = dsKhoUserId;
        else obj.dsKhoXuatId = null;
      }

      if (state.dsKhoNhapId?.length) {
        obj.dsKhoNhapId = state.dsKhoNhapId;
      } else {
        obj.dsKhoNhapId = null;
      }
    };
    if (type !== LOAI_PHIEU_XUAT[2].id) {
      data.dsLoaiChiDinh = null;
    }
    switch (type) {
      case LOAI_PHIEU_XUAT[0].id:
        setFilterKho(data);
        data.dsLoaiNhapXuat = [20];
        break;
      case LOAI_PHIEU_XUAT[1].id:
        setFilterKho(data);
        data.dsLoaiNhapXuat = [30, 40, 90];
        break;
      case LOAI_PHIEU_XUAT[2].id:
        setFilterKho(data);
        data.dsLoaiNhapXuat = [80, 85];
        break;
      case LOAI_PHIEU_XUAT[3].id:
        setFilterKho(data);
        data.dsLoaiNhapXuat = [130];
        break;
      case LOAI_PHIEU_XUAT[4].id:
        data.dsLoaiNhapXuat = [50];
        break;
      case LOAI_PHIEU_XUAT[5].id:
        data.dsLoaiNhapXuat = [45];
        break;
      default:
        // khi xoá sẽ truyền lên tất cả loaiNhapXuat
        data.dsLoaiNhapXuat = [20, 30, 40, 45, 50, 80, 85, 90, 130];
        break;
    }

    return data;
  };

  const listLoaiPhieuXuat = useMemo(() => {
    if (checkRole([ROLES["KHO"].XUAT_KHO_HIEN_THI_PHAN_KHO_TAI_KHOA])) {
      return LOAI_PHIEU_XUAT;
    } else {
      return LOAI_PHIEU_XUAT.filter((i) => i.id !== 5); //Khoa trả về kho
    }
  }, [LOAI_PHIEU_XUAT]);

  useEffect(() => {
    if (!dsKhoUserId.length || !loadFinish) return;
    const { page, size, dataSortColumn, ...queries } = transformQueryString({
      dsTrangThai: {
        format: (value) => value.split(",").map(Number),
        defaultValue: MAC_DINH_XEM_TAT_CA_LOAI_XUAT
          ? [10, 15, 20, 28, 30]
          : [10, 15, 20],
      },
      dsKhoNhapId: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [],
      },
      dsKhoXuatId: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [],
      },
      dsKhoaChiDinhId: {
        format: (value) => Number(value),
      },
      hinhThucNhapXuatId: {
        format: (value) => Number(value),
      },
      type: {
        format: (value) => Number(value),
        defaultValue: MAC_DINH_XEM_TAT_CA_LOAI_XUAT
          ? undefined
          : listLoaiPhieuXuat[0].id,
      },
      khoaChiDinhId: {
        format: (value) => Number(value),
      },
      dsLoaiChiDinh: {
        format: (value) => value.split(",").map(Number),
      },
      thangDuTru: {
        format: (value) => Number(value),
      },
      dsTrangThaiDls: {
        format: (value) => Number(value),
      },
      tuNgayTaoPhieu: {
        defaultValue: moment().format("YYYY-MM-DD 00:00:00"),
        type: "dateOptions",
        format: (value) => moment(value).format("YYYY-MM-DD 00:00:00"),
      },
      denNgayTaoPhieu: {
        defaultValue: moment().format("YYYY-MM-DD 23:59:59"),
        type: "dateOptions",
        format: (value) => moment(value).format("YYYY-MM-DD 23:59:59"),
      },
    });

    if (!queries.dsTrangThaiDls && dsTrangThaiDls) {
      queries.dsTrangThaiDls = dsTrangThaiDls;
    }

    state.dsKhoNhapId = queries.dsKhoNhapId;
    state.dsKhoXuatId = queries.dsKhoXuatId;
    if (!listLoaiPhieuXuat.some((i) => i.id === queries.type)) {
      queries.type = listLoaiPhieuXuat[0].id;
    }
    state.type = queries.type;
    setState({
      ...queries,
      dsTrangThaiDls: queries.dsTrangThaiDls
        ? Array.isArray(queries.dsTrangThaiDls)
          ? queries.dsTrangThaiDls[0]
          : queries.dsTrangThaiDls
        : null,
    });
    let data = filterType(queries.type);

    onSizeChange({
      page: parseInt(page || 0),
      size: parseInt(size || 10),
      dataSearch: cleanEmptyData({
        ...queries,
        ...data,
        dsTrangThaiDls:
          queries?.dsTrangThaiDls && !Array.isArray(queries?.dsTrangThaiDls)
            ? [queries?.dsTrangThaiDls]
            : null,
        dsKhoaChiDinhId: queries?.dsKhoaChiDinhId
          ? [queries?.dsKhoaChiDinhId]
          : null,
        dsKhoXuatId: queries?.dsKhoXuatId?.length
          ? queries?.dsKhoXuatId
          : dsKhoUserId,
      }),
    });
  }, [dsKhoUserId, dsTrangThaiDls, loadFinish, listLoaiPhieuXuat]);

  const onChangType = (e) => {
    let data = filterType(e?.type);
    setState({ type: e?.type });
    if (!dsKhoUserId.length) return;
    onChangeInputSearch(data);
  };

  const onSearchInput = (data) => {
    if (!dsKhoUserId.length) return;
    if (data.dsLoaiChiDinh) {
      data.dsLoaiChiDinh = data.dsLoaiChiDinh.map(Number);
    }
    if (data.hasOwnProperty("dsTrangThaiDls")) {
      if (data.dsTrangThaiDls) {
        data.dsTrangThaiDls = !Array.isArray(data.dsTrangThaiDls)
          ? [data.dsTrangThaiDls]
          : data.dsTrangThaiDls;
      }
      setDsTrangThaiDls(
        Array.isArray(data.dsTrangThaiDls)
          ? data.dsTrangThaiDls[0]
          : data.dsTrangThaiDls,
        false
      );
      setState({
        dsTrangThaiDls: Array.isArray(data.dsTrangThaiDls)
          ? data.dsTrangThaiDls[0]
          : data.dsTrangThaiDls,
      });
    }

    setState(data);
    onChangeInputSearch(data);
  };

  return (
    <Main>
      <BaseSearch
        cacheData={state}
        dataInput={[
          {
            widthInput: "200px",
            placeholder: t("nhaThuoc.chonLoaiNhapXuat"),
            keyValueInput: "type",
            functionChangeInput: onChangType,
            type: "select",
            listSelect: listLoaiPhieuXuat,
            value: state.type,
          },
          {
            widthInput: "130px",
            placeholder: t("kho.khoNhap"),
            title: t("kho.khoNhap"),
            keyValueInput: "dsKhoNhapId",
            type: "selectCheckbox",
            defaultValue: state.dsKhoNhapId,
            virtual: true,
            listSelect: listKhoUser,
            hasSearch: true,
            functionChangeInput: debounce(({ dsKhoNhapId: _dsKhoNhapId }) => {
              const dsKhoNhapId = _dsKhoNhapId.length ? _dsKhoNhapId : null;
              onChangeInputSearch({
                dsKhoNhapId,
              });
              setState({ dsKhoNhapId });
            }, 500),
            height: 400,
          },
          {
            widthInput: "130px",
            placeholder: t("kho.khoXuat"),
            title: t("kho.khoXuat"),
            keyValueInput: "dsKhoXuatId",
            type: "selectCheckbox",
            defaultValue: state.dsKhoXuatId,
            virtual: true,
            hasSearch: true,
            listSelect: listKhoUser,
            functionChangeInput: debounce(({ dsKhoXuatId }) => {
              onChangeInputSearch({
                dsKhoXuatId: dsKhoXuatId.length ? dsKhoXuatId : dsKhoUserId,
              });
              setState({ dsKhoXuatId });
            }, 500),
            height: 400,
          },
          {
            widthInput: "180px",
            placeholder: t("kho.khoaChiDinh"),
            keyValueInput: "dsKhoaChiDinhId",
            functionChangeInput: ({ dsKhoaChiDinhId }) => {
              onSearchInput({
                dsKhoaChiDinhId: dsKhoaChiDinhId ? [dsKhoaChiDinhId] : null,
              });
            },
            type: "select",
            listSelect: listAllKhoa,
            value: state.dsKhoaChiDinhId,
          },
          {
            widthInput: "160px",
            title: t("kho.trangThaiPhieu"),
            keyValueInput: "dsTrangThai",
            functionChangeInput: ({ dsTrangThai }) => {
              onSearchInput({
                dsTrangThai: dsTrangThai.length ? dsTrangThai : null,
              });
              setState({
                dsTrangThai: dsTrangThai.length ? dsTrangThai : null,
              });
              setQueryStringValue("dsTrangThai", dsTrangThai);
            },
            type: "selectCheckbox",
            value: state.dsTrangThai,
            listSelect: listTrangThaiMemo,
          },
          {
            widthInput: "180px",
            placeholder: t("kho.timPhieuLinhLan"),
            keyValueInput: "lan",
            functionChangeInput: onSearchInput,
          },
          {
            widthInput: "220px",
            placeholder: t("kho.timSoPhieu"),
            keyValueInput: "soPhieu",
            functionChangeInput: onSearchInput,
            searchOnInput: true,
          },
          ...([1, 2, 3, 4].includes(parseInt(dataSO_PHIEU_NHAP_XUAT_KHO))
            ? [
                {
                  widthInput: "200px",
                  placeholder: t("goiDichVu.soPhieuDoiUng"),
                  keyValueInput: "soPhieuDoiUng",
                  functionChangeInput: onSearchInput,
                  searchOnInput: true,
                },
              ]
            : []),
          {
            state: state,
            setState: setState,
            keyValueInput: ["tuNgayTaoPhieu", "denNgayTaoPhieu"],
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            type: "dateRange",
            title: t("khoMau.ngayTaoPhieu"),
            showTimeDefaultValue: {
              from: moment().startOf("day"),
              to: moment().endOf("day"),
            },
            functionChangeInput: onSearchInput,
            widthInput: "350px",
            format: "DD/MM/YYYY HH:mm:ss",
            allowClear: true,
          },
        ]}
        filter={{
          open: true,
          width: "110px",
          funcSearchData: (data) => {
            onSearchInput(data);
            setQueryStringValues(data);
          },
          data: [
            {
              placeholder: t("kho.loaiXuat"),
              key: "hinhThucNhapXuatId",
              functionChangeInput: onSearchInput,
              type: "select",
              dataSelect: listHinhThucNhapXuat,
            },
            {
              key: ["tuThoiGianDuyet", "denThoiGianDuyet"],
              placeholder: [t("common.tuNgay"), t("common.denNgay")],
              type: "date-1",
              title: t("khoMau.ngayDuyetPhieu"),
            },
            {
              placeholder: t("kho.thangDuTru"),
              key: "thangDuTru",
              functionChangeInput: onSearchInput,
              type: "select",
              dataSelect: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(
                (item) => ({
                  ten: t("common.thang") + item,
                  id: item,
                })
              ),
            },
            {
              placeholder: t("kho.khoaChiDinh"),
              key: "khoaChiDinhId",
              functionChangeInput: onSearchInput,
              type: "select",
              dataSelect: listAllKhoa,
            },
            {
              placeholder: t("quanLyNoiTru.cpdd.loaiChiDinh"),
              key: "dsLoaiChiDinh",
              type: "select",
              mode: "multiple",
              dataSelect: listLoaiChiDinh.map((item) => ({
                id: item.id + "",
                ten: item.ten,
              })),
              show: state.type === 3,
            },
            {
              placeholder: t("kho.trangThaiDuyetDls"),
              key: "dsTrangThaiDls",
              type: "select",
              dataSelect: listTrangThaiDuyetDls,
            },
          ].filter((i) => i.show !== false),
        }}
      />

      <div className="array-store" style={{ flexFlow: "row wrap" }}>
        {(state.dsKhoNhapId || []).map((item, index) => {
          let currentKho = listKhoUser.find(
            (x) => x.value == item || x.id === item
          );
          return (
            <div className="item" style={{ marginTop: 5 }} key={index}>
              <span>{currentKho?.ten || currentKho?.label}</span>
              <SVG.IcCancel
                onClick={() => {
                  onChange("dsKhoNhapId")(item);
                }}
              />
            </div>
          );
        })}

        {(state.dsTrangThai || []).map((item, index) => {
          return (
            <div className="item" style={{ marginTop: 5 }} key={index}>
              <span>
                {listTrangThaiMemo.find((x) => x.value == item)?.label}
              </span>
              <SVG.IcCancel
                onClick={() => {
                  onChange("dsTrangThai")(item);
                }}
              />
            </div>
          );
        })}

        {(state.dsKhoXuatId || []).map((item, index) => {
          let currentKho = listKhoUser.find(
            (x) => x.value == item || x.id == item
          );
          return (
            <div className="item" style={{ marginTop: 5 }} key={index}>
              <span>{currentKho?.label || currentKho?.ten}</span>
              <SVG.IcCancel
                onClick={() => {
                  onChange("dsKhoXuatId")(item);
                }}
              />
            </div>
          );
        })}
      </div>
    </Main>
  );
};

export default forwardRef(TimKiemPhieuXuat);
