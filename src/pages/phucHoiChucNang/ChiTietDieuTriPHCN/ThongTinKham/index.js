import React, { useEffect, useState, useRef, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { Empty, message } from "antd";
import moment from "moment";

import { useConfirm, useEnum, useListAll, useLoading, useStore } from "hooks";

import {
  TableWrapper,
  Pagination,
  Tooltip,
  Button,
  ModalSignPrint,
} from "components";
import { SVG } from "assets";
import { ENUM, THIET_LAP_CHUNG } from "constants/index";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { Main } from "./styled";
import ModalSuaThongTinKham from "./ModalSuaThongTinKham";

const { Column, Setting } = TableWrapper;

const KHAM_CAC_CO_QUAN_KHAC_TXT = {
  chuaPhatHienBatThuong: "phcn.kham.chuaPhatHienBatThuong",
};

const ThongTinKham = ({ phieuKhamPhcn }) => {
  const { t } = useTranslation();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();

  const refSettings = useRef(null);
  const refModalSignPrint = useRef(null);
  const refModalSuaThongTinKham = useRef(null);

  const [listDichDanLuu] = useEnum(ENUM.DICH_DAN_LUU);
  const [listMucTieuPHCN] = useEnum(ENUM.MUC_TIEU_PHCN);
  const [listGiaoDucSucKhoe] = useEnum(ENUM.GIAO_DUC_SUC_KHOE);
  const _listBacSi = useStore("nhanVien.listBacSi", []);
  const chiTietPHCN = useStore("dieuTriPHCN.chiTietPHCN", {});
  const { totalElements, page, size, listData, loading } = useStore(
    "nbPHCNKham",
    {},
    {
      field:
        "totalElements,page,size,listData,dataSortColumn,dataSearch,loading",
    }
  );

  const {
    nbPHCNKham: {
      onSearch,
      onSizeChange,
      xoaPHCNKham,
      onChangeInputSearch,
      inPhieuKhamChiDinhPhcn,
      createOrEdit,
    },
    phieuIn: { getListPhieu },
    nhanVien: { getListBacSi },
  } = useDispatch();

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getListBacSi({
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      ...param,
    });
  }, []);

  const _listData = useMemo(() => {
    return listData.map((item) => {
      return {
        ...item,
        bacSiChiDinh: _listBacSi.find((el) => el.id === item.bacSiChiDinhId),
      };
    });
  }, [_listBacSi, listData]);

  const [listAllLuongGiaPhcnChiTiet] = useListAll(
    "luongGiaPhcnChiTiet",
    {},
    true
  );

  const [state, _setState] = useState({
    activeKey: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (chiTietPHCN?.nbDotDieuTriId) {
      onSizeChange({
        page: parseInt(page || 0),
        size: parseInt(size || 10),
        dataSearch: {
          nbDotDieuTriId: chiTietPHCN?.nbDotDieuTriId,
          phucHoiCnId: chiTietPHCN?.id,
        },
      });
    }
  }, [chiTietPHCN?.nbDotDieuTriId]);

  useEffect(() => {
    if (phieuKhamPhcn) {
      setState({ phieuKhamPhcn });
    }
  }, [phieuKhamPhcn]);

  const onDelete = (record) => async (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("common.xacNhan"),
        content: t("common.banCoChacMuonXoaBanGhiNay") + "?",
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        try {
          showLoading();

          await xoaPHCNKham(record?.id);

          onChangeInputSearch({});
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onViewDetail = (record) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    history.push({
      pathname: `/phuc-hoi-chuc-nang/phcn-kham/chi-tiet/${chiTietPHCN?.nbDotDieuTriId}/${chiTietPHCN?.id}/${record?.id}`,
    });
  };

  const onPrint = (record) => async (e) => {
    e.preventDefault();
    e.stopPropagation();
    showLoading();

    try {
      if (state.phieuKhamPhcn) {
        if (checkIsPhieuKySo(state.phieuKhamPhcn)) {
          //tìm số phiếu theo id
          let _soPhieu = (state.phieuKhamPhcn?.dsSoPhieu || []).find(
            (i) => i.soPhieu == record?.id
          );
          //tìm số phiếu theo nbDotDieuTriId
          if (!_soPhieu) {
            _soPhieu = (state.phieuKhamPhcn?.dsSoPhieu || []).find(
              (i) => i.soPhieu == record?.nbDotDieuTriId
            );
          }
          if (!_soPhieu) {
            message.error(t("phieuIn.khongTonTaiPhieuIn"));
            return;
          }

          const phieuKhamPhcn = {
            ...state.phieuKhamPhcn,
            dsSoPhieu: [_soPhieu],
          };

          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign(
              {
                phieuKy: phieuKhamPhcn,
                payload: {
                  nbDotDieuTriId: record?.nbDotDieuTriId,
                  chiDinhTuDichVuId: record?.id,
                  maManHinh: "015",
                  maViTri: "01501",
                },
              },
              async () => {
                const res = await getListPhieu({
                  nbDotDieuTriId: record?.nbDotDieuTriId,
                  maManHinh: "015",
                  maViTri: "01501",
                });
                let refreshPhieu = (res || []).find((i) => i.ma === "P394");
                setState({ phieuKhamPhcn: refreshPhieu });
                return {
                  ...refreshPhieu,
                  dsSoPhieu: (refreshPhieu?.dsSoPhieu || []).filter(
                    (i) => i.soPhieu == record?.id
                  ),
                };
              }
            );
        } else {
          await inPhieuKhamChiDinhPhcn(record?.id);
        }
      } else {
        await inPhieuKhamChiDinhPhcn(record?.id);
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onEdit = (item) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    refModalSuaThongTinKham.current &&
      refModalSuaThongTinKham.current.show(item, () => onChangeInputSearch({}));
  };

  const onSaoChep = (record) => async (e) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      showLoading();
      await createOrEdit({
        ...record,
        id: undefined,
      });
      onSearch({});
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      fixed: "left",
    }),
    Column({
      title: t("phaCheThuoc.ngayLapPhieu"),
      width: 120,
      dataIndex: "thoiGianTao",
      key: "thoiGianTao",
      i18Name: "phaCheThuoc.ngayLapPhieu",
      render: (item) => (item ? moment(item).format("DD/MM/YYYY") : ""),
    }),
    Column({
      title: t("phcn.kham.bacSiTaoPhieu"),
      width: 160,
      dataIndex: "bacSiChiDinhId",
      key: "bacSiChiDinhId",
      i18Name: "phcn.kham.bacSiTaoPhieu",
      render: (value, item) => {
        return item.bacSiChiDinh?.ten || "";
      },
    }),
    Column({
      title: t("common.ngayThucHien"),
      width: 120,
      dataIndex: "ngayThucHien",
      key: "ngayThucHien",
      i18Name: "common.ngayThucHien",
      render: (item) => (item ? moment(item).format("DD/MM/YYYY") : ""),
    }),
    Column({
      title: t("phcn.kham.tienSu"),
      width: 160,
      dataIndex: "hutThuoc",
      key: "hutThuoc",
      i18Name: "phcn.kham.tienSu",
      render: (field, item, index) => {
        const { hutThuocId, baoNam, tienSuDungThuocId, tienSuKhac } =
          item || {};

        const hutThuocTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === hutThuocId)?.ten ||
          "";
        const tienSuDungThuocTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === tienSuDungThuocId)
            ?.ten || "";

        return [hutThuocTxt, baoNam, tienSuDungThuocTxt, tienSuKhac]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.trieuChungCoNang"),
      width: 160,
      dataIndex: "hoKhacDomId",
      key: "hoKhacDomId",
      i18Name: "phcn.kham.trieuChungCoNang",
      render: (field, item, index) => {
        const {
          hoKhacDomId,
          mauSacDomId,
          khaNangHoId,
          trieuChungDauId,
          trieuChungDauKhac,
          viTriDau,
          cuongDoDau,
          trieuChungKhac,
        } = item || {};

        const hoKhacDomTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === hoKhacDomId)?.ten ||
          "";
        const mauSacDomTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === mauSacDomId)?.ten ||
          "";
        const khaNangHoTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === khaNangHoId)?.ten ||
          "";
        const trieuChungDauTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === trieuChungDauId)
            ?.ten || "";

        return [
          hoKhacDomTxt,
          mauSacDomTxt,
          khaNangHoTxt,
          trieuChungDauTxt,
          trieuChungDauKhac,
          viTriDau,
          cuongDoDau,
          trieuChungKhac,
        ]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.khamToanThan"),
      width: 160,
      dataIndex: "ythucId",
      key: "ythucId",
      i18Name: "phcn.kham.khamToanThan",
      render: (field, item, index) => {
        const { ythucId, hoTroHoHapId, sondeDanLuuId, viTriSondeId } =
          item || {};

        const ythucTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === ythucId)?.ten || "";
        const hoTroHoHapTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === hoTroHoHapId)?.ten ||
          "";
        const sondeDanLuuTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === sondeDanLuuId)?.ten ||
          "";
        const viTriSondeTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === viTriSondeId)?.ten ||
          "";

        return [ythucTxt, hoTroHoHapTxt, sondeDanLuuTxt, viTriSondeTxt]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.dichDanLuu"),
      width: 160,
      dataIndex: "dsDichDanLuu",
      key: "dsDichDanLuu",
      i18Name: "phcn.kham.dichDanLuu",
      render: (item) =>
        listDichDanLuu
          .filter((x) => (item || []).includes(x.id))
          .map((x) => x.ten)
          .join("; "),
    }),
    Column({
      title: t("phcn.kham.khamHoHap"),
      width: 160,
      dataIndex: "nghePhoiId",
      key: "nghePhoiId",
      i18Name: "phcn.kham.khamHoHap",
      render: (field, item, index) => {
        const { nghePhoiId, viTriNghePhoiId, hoiChung3GiamId } = item || {};

        const nghePhoiTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === nghePhoiId)?.ten ||
          "";
        const viTriNghePhoiTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === viTriNghePhoiId)
            ?.ten || "";
        const hoiChung3GiamTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === hoiChung3GiamId)
            ?.ten || "";

        return [nghePhoiTxt, viTriNghePhoiTxt, hoiChung3GiamTxt]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.khamCacCoQuanKhac"),
      width: 160,
      dataIndex: "chuaPhatHienBatThuong",
      key: "chuaPhatHienBatThuong",
      i18Name: "phcn.kham.khamCacCoQuanKhac",
      render: (field, item, index) => {
        const { ghiChuCoQuanKhac } = item || {};

        const khamCacCoQuanKhacTxt = Object.keys(KHAM_CAC_CO_QUAN_KHAC_TXT)
          .filter((key) => item[key])
          .map((key) => t(KHAM_CAC_CO_QUAN_KHAC_TXT[key]))
          .join("; ");

        return [khamCacCoQuanKhacTxt, ghiChuCoQuanKhac]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.luongGiaChucNangCo"),
      width: 160,
      dataIndex: "coHoHapId",
      key: "coHoHapId",
      i18Name: "phcn.kham.luongGiaChucNangCo",
      render: (field, item, index) => {
        const {
          coHoHapId,
          khaNangVanDongId,
          mip,
          mep,
          coNgoaiViId,
          coNgoaiViBienId,
          coLucBac,
        } = item || {};

        const coHoHapTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === coHoHapId)?.ten || "";
        const khaNangVanDongTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === khaNangVanDongId)
            ?.ten || "";
        const coNgoaiViTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === coNgoaiViId)?.ten ||
          "";
        const coNgoaiViBienTxt =
          listAllLuongGiaPhcnChiTiet.find((x) => x.id === coNgoaiViBienId)
            ?.ten || "";

        return [
          coHoHapTxt,
          khaNangVanDongTxt,
          mip,
          mep,
          coNgoaiViTxt,
          coNgoaiViBienTxt,
          coLucBac,
        ]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.canLamSang"),
      width: 160,
      dataIndex: "chucNangHoHap",
      key: "chucNangHoHap",
      i18Name: "phcn.kham.canLamSang",
      render: (field, item, index) => {
        const { chucNangHoHap, xnViKhuanLao, xnViKhuan, ghiChuCls } =
          item || {};

        return [chucNangHoHap, xnViKhuanLao, xnViKhuan, ghiChuCls]
          .filter((x) => !!x)
          .join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.mucTieuPhucHoiChucNang"),
      width: 160,
      dataIndex: "mucTieuPhcn",
      key: "mucTieuPhcn",
      i18Name: "phcn.kham.mucTieuPhucHoiChucNang",
      render: (field, item, index) => {
        const { dsMucTieuPhcn, ghiChuMucTieu } = item || {};
        const dsMucTieuPhcnTxt = listMucTieuPHCN
          .filter((x) => (dsMucTieuPhcn || []).includes(x.id))
          .map((x) => x.ten)
          .join("; ");

        return [dsMucTieuPhcnTxt, ghiChuMucTieu].filter((x) => !!x).join("; ");
      },
    }),
    Column({
      title: t("phcn.kham.tuVanGiaoDucSucKhoe"),
      width: 160,
      dataIndex: "dsGiaoDucSucKhoe",
      key: "dsGiaoDucSucKhoe",
      i18Name: "phcn.kham.tuVanGiaoDucSucKhoe",
      render: (item) =>
        listGiaoDucSucKhoe
          .filter((x) => (item || []).includes(x.id))
          .map((x) => x.ten)
          .join("; "),
    }),

    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 180,
      dataIndex: "action",
      key: "action",
      fixed: "right",
      ignore: true,
      align: "center",
      render: (_, record) => {
        return (
          <>
            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye className="ic-action" onClick={onViewDetail(record)} />
            </Tooltip>
            <Tooltip title={t("common.sua")}>
              <SVG.IcEdit className="ic-action" onClick={onEdit(record)} />
            </Tooltip>
            <Tooltip title={t("common.xoa")}>
              <SVG.IcDelete className="ic-action" onClick={onDelete(record)} />
            </Tooltip>
            <Tooltip title={t("common.inPhieu")}>
              <SVG.IcPrint className="ic-action" onClick={onPrint(record)} />
            </Tooltip>
            <Tooltip title={t("khamBenh.saoChep")}>
              <SVG.IcSaoChep
                onClick={onSaoChep(record)}
                className="ic-action"
              />
            </Tooltip>
          </>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => ({
    onClick: () => {
      history.push({
        pathname: `/phuc-hoi-chuc-nang/phcn-kham/chi-tiet/${chiTietPHCN?.nbDotDieuTriId}/${chiTietPHCN?.id}/${record?.id}`,
      });
    },
  });

  const setRowClassName = (record) => {
    let idDiff;
    return record.id === idDiff ? "row-actived" : "";
  };

  const onThemMoi = () => {
    history.push(
      `/phuc-hoi-chuc-nang/phcn-kham/${chiTietPHCN?.nbDotDieuTriId}/${chiTietPHCN?.id}/them-moi`
    );
  };

  return (
    <Main>
      {listData.length > 0 ? (
        <>
          <TableWrapper
            columns={columns}
            dataSource={_listData}
            onRow={onRow}
            rowKey={(record) => `${record.id}`}
            rowClassName={setRowClassName}
            scroll={{
              x: 1600,
            }}
            tableName={`table_PHCN_DsPHCNKham`}
            loading={loading}
            ref={refSettings}
          />
          {!!totalElements && (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              total={totalElements}
              listData={listData || []}
              onShowSizeChange={handleSizeChange}
            />
          )}
          <ModalSignPrint ref={refModalSignPrint} />
        </>
      ) : (
        <Empty
          image={<img src={require("assets/images/kho/empty.png")} alt="..." />}
          description={t("phcn.kham.chuaTaoPHCNKham")}
        >
          <Button type="primary" onClick={onThemMoi}>
            {t("phcn.kham.taoPHCNKhamMoi")}
          </Button>
        </Empty>
      )}
      <ModalSuaThongTinKham ref={refModalSuaThongTinKham} />
    </Main>
  );
};

export default ThongTinKham;
