import React, { useEffect, useState, useRef } from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import {
  TableWrapper,
  Pagination,
  Tooltip,
  EllipsisText,
  Button,
  ModalSignPrint,
} from "components";
import { useLoading, useStore, useConfirm } from "hooks";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { SVG } from "assets";
import moment from "moment";
import { Empty } from "antd";

const { Column, Setting } = TableWrapper;

const CAC_CHUC_NANG_KHAC_TXT = {
  chucNangKhacKhongBatThuong:
    "phcn.luongGia.cacChucNangTimMachTieuHoaTietNieuSinhDucDaCacGiacQuanKhongBatThuong",
  khoThoKhiGangSuc: "phcn.luongGia.khoThoKhiGangSuc",
  khoThoKhiDiLai: "phcn.luongGia.khoThoKhiDiLai",
  nuotKho: "phcn.luongGia.nuotKho",
  khoThoKhiLamViecHangNgay:
    "phcn.luongGia.khoThoKhiThucHienCacCongViecHangNgay",
  khacDomKem: "phcn.luongGia.khacDomKem",
  ngheKem: "phcn.luongGia.ngheKem",
  roiLoanCoTron: "phcn.luongGia.roiLoanCoTron",
  biTieu: "phcn.luongGia.biTieu",
  taoBon: "phcn.luongGia.taoBon",
};

const YEU_TO_CA_NHAN_TXT = {
  tamLyOnDinh: "phcn.luongGia.congViecTamLyOnDinhLoiSongTichCucLanhManh",
  tamLyLoLang: "phcn.luongGia.tamLyLoLangVeBenh",
  daKetHon: "phcn.luongGia.daKetHon",
  docThan: "phcn.luongGia.docThan",
  lyThan: "phcn.luongGia.lyThan",
  congViecKhongOnDinh: "phcn.luongGia.congViecKhongOnDinh",
};

const LuongGiaPHCN = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();

  const refSettings = useRef(null);
  const refModalSignPrint = useRef(null);

  const chiTietPHCN = useStore("dieuTriPHCN.chiTietPHCN", {});
  const {
    totalElements,
    page,
    size,
    listData,
    dataSortColumn,
    dataSearch,
    loading,
  } = useStore(
    "nbLuongGiaPHCN",
    {},
    {
      field:
        "totalElements,page,size,listData,dataSortColumn,dataSearch,loading",
    }
  );

  const {
    nbLuongGiaPHCN: {
      onSearch,
      onSizeChange,
      onSortChange,
      xoaLuongGiaPHCN,
      onChangeInputSearch,
      inPhieuLuongGia,
      createOrEdit,
    },
    phieuIn: { getListPhieu },
  } = useDispatch();

  const [state, _setState] = useState({
    activeKey: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (chiTietPHCN?.nbDotDieuTriId) {
      onSizeChange({
        page: parseInt(page || 0),
        size: parseInt(size || 10),
        dataSearch: {
          nbDotDieuTriId: chiTietPHCN?.nbDotDieuTriId,
        },
      });
    }
  }, [chiTietPHCN?.nbDotDieuTriId]);

  const onDelete = (record) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    showConfirm(
      {
        title: t("common.xacNhan"),
        content: t("common.banCoChacMuonXoaBanGhiNay") + "?",
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        try {
          showLoading();
          await xoaLuongGiaPHCN(record?.id);
          onChangeInputSearch({});
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onViewDetail = (record) => (e) => {
    e.preventDefault();
    e.stopPropagation();

    history.push({
      pathname: `/phuc-hoi-chuc-nang/luong-gia-phcn/chi-tiet/${chiTietPHCN?.nbDotDieuTriId}/${chiTietPHCN?.id}/${record?.id}`,
    });
  };

  const onPrint = (record) => async (e) => {
    e.preventDefault();
    e.stopPropagation();

    const listPhieu = await getListPhieu({
      nbDotDieuTriId: chiTietPHCN.nbDotDieuTriId,
      maManHinh: "015",
      maViTri: "01501",
    });
    const phieuP365 = listPhieu.find((i) => i.ma === "P365");
    const soPhieuIn = (phieuP365?.dsSoPhieu || []).find(
      (x) => x.soPhieu == record?.id
    );

    if (phieuP365?.kySo && soPhieuIn) {
      refModalSignPrint.current &&
        refModalSignPrint.current.showToSign({
          phieuKy: { ...phieuP365, dsSoPhieu: [soPhieuIn] },
          payload: {
            nbDotDieuTriId: chiTietPHCN.nbDotDieuTriId,
            maManHinh: "015",
            maViTri: "01501",
          },
        });
    } else {
      inPhieuLuongGia(record?.id);
    }
  };

  const onSaoChep = (record) => async (e) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      showLoading();
      await createOrEdit({
        ...record,
        id: undefined,
      });
      onSearch({});
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      fixed: "left",
    }),
    Column({
      title: t("common.ngayThucHien"),
      width: 120,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: "common.ngayThucHien",
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    }),
    Column({
      title: t("common.khoa"),
      width: 120,
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
      i18Name: "common.khoa",
    }),
    Column({
      title: t("common.phong"),
      width: 60,
      dataIndex: "tenPhong",
      key: "tenPhong",
      i18Name: "common.phong",
    }),
    Column({
      title: t("quanLyNoiTru.giuong"),
      width: 60,
      dataIndex: "soHieu",
      key: "soHieu",
      i18Name: "quanLyNoiTru.giuong",
    }),
    Column({
      title: t("phcn.luongGia.chanDoanRaVien"),
      width: 160,
      dataIndex: "cdRaVien",
      key: "cdRaVien",
      i18Name: "phcn.luongGia.chanDoanRaVien",
      render: (field, item, index) => {
        return (
          <EllipsisText
            content={
              item?.dsCdChinh?.length &&
              item?.dsCdChinh?.map((itemLoop) => itemLoop.ten)?.join("; ")
            }
          />
        );
      },
    }),
    Column({
      title: t("phcn.luongGia.vanDongVaDiChuyen"),
      width: 160,
      dataIndex: "tenVanDongDiChuyen",
      key: "tenVanDongDiChuyen",
      i18Name: "phcn.luongGia.vanDongVaDiChuyen",
      render: (field, item, index) =>
        [field, item.vanDongDiChuyenKhac].filter((x) => !!x).join("; "),
    }),
    Column({
      title: t("phcn.luongGia.chucNangSinhHoatHangNgay"),
      width: 160,
      dataIndex: "tenSinhHoatHangNgay",
      key: "tenSinhHoatHangNgay",
      i18Name: "phcn.luongGia.chucNangSinhHoatHangNgay",
      render: (field, item, index) =>
        [field, item.sinhHoatHangNgayKhac].filter((x) => !!x).join("; "),
    }),
    Column({
      title: t("phcn.luongGia.nhanThucGiaoTiep"),
      width: 160,
      dataIndex: "tenNhanThucGiaoTiep",
      key: "tenNhanThucGiaoTiep",
      i18Name: "phcn.luongGia.nhanThucGiaoTiep",
      render: (field, item, index) =>
        [field, item.nhanThucGiaoTiepKhac].filter((x) => !!x).join("; "),
    }),
    Column({
      title: t("phcn.luongGia.cacChucNangKhac"),
      width: 160,
      dataIndex: "chucNangKhac",
      key: "chucNangKhac",
      i18Name: "phcn.luongGia.cacChucNangKhac",
      render: (field, item, index) => {
        const { chucNangKhac } = item || {};

        const chucNangTxt = Object.keys(CAC_CHUC_NANG_KHAC_TXT)
          .filter((key) => item[key])
          .map((key) => t(CAC_CHUC_NANG_KHAC_TXT[key]))
          .join("; ");

        return [chucNangTxt, chucNangKhac].filter((x) => !!x).join("; ");
      },
    }),
    Column({
      title: t("phcn.luongGia.suThamGiaCacHoatDongTrongGiaDinhVaXaHoi"),
      width: 220,
      dataIndex: "tenHoatDongXaHoi",
      key: "tenHoatDongXaHoi",
      i18Name: "phcn.luongGia.suThamGiaCacHoatDongTrongGiaDinhVaXaHoi",
      render: (field, item, index) =>
        [field, item.hoatDongXaHoiKhac].filter((x) => !!x).join("; "),
    }),
    Column({
      title: t("phcn.luongGia.yeuToMoiTruong"),
      width: 160,
      dataIndex: "tenMoiTruong",
      key: "tenMoiTruong",
      i18Name: "phcn.luongGia.yeuToMoiTruong",
      render: (field, item, index) =>
        [field, item.moiTruongKhac].filter((x) => !!x).join("; "),
    }),
    Column({
      title: t("phcn.luongGia.yeuToCaNhan"),
      width: 160,
      dataIndex: "tamLyOnDinh",
      key: "tamLyOnDinh",
      i18Name: "phcn.luongGia.yeuToCaNhan",
      render: (field, item, index) => {
        const { caNhanKhac } = item || {};

        const caNhanTxt = Object.keys(YEU_TO_CA_NHAN_TXT)
          .filter((key) => item[key])
          .map((key) => t(YEU_TO_CA_NHAN_TXT[key]))
          .join("; ");

        return [caNhanTxt, caNhanKhac].filter((x) => !!x).join("; ");
      },
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 100,
      dataIndex: "action",
      key: "action",
      fixed: "right",
      ignore: true,
      align: "center",
      render: (_, record) => {
        return (
          <>
            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye className="ic-action" onClick={onViewDetail(record)} />
            </Tooltip>
            <Tooltip title={t("common.xoa")}>
              <SVG.IcDelete className="ic-action" onClick={onDelete(record)} />
            </Tooltip>
            <Tooltip title={t("common.inPhieu")}>
              <SVG.IcPrint className="ic-action" onClick={onPrint(record)} />
            </Tooltip>
            <Tooltip title={t("khamBenh.saoChep")}>
              <SVG.IcSaoChep
                onClick={onSaoChep(record)}
                className="ic-action"
              />
            </Tooltip>
          </>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onRow = (record) => ({
    onClick: () => {
      history.push({
        pathname: `/phuc-hoi-chuc-nang/luong-gia-phcn/chi-tiet/${chiTietPHCN?.nbDotDieuTriId}/${chiTietPHCN?.id}/${record?.id}`,
      });
    },
  });

  const setRowClassName = (record) => {
    let idDiff;
    return record.id === idDiff ? "row-actived" : "";
  };

  const onThemMoi = () => {
    history.push(
      `/phuc-hoi-chuc-nang/luong-gia-phcn/${chiTietPHCN?.nbDotDieuTriId}/${chiTietPHCN?.id}/them-moi`
    );
  };

  return (
    <Main>
      {listData.length > 0 ? (
        <>
          <TableWrapper
            columns={columns}
            dataSource={listData}
            onRow={onRow}
            rowKey={(record) => `${record.id}`}
            rowClassName={setRowClassName}
            scroll={{
              x: 3000,
            }}
            tableName={`table_PHCN_DsLuongGiaPHCN`}
            loading={loading}
            ref={refSettings}
          />
          {!!totalElements && (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              total={totalElements}
              listData={listData || []}
              onShowSizeChange={handleSizeChange}
            />
          )}
        </>
      ) : (
        <Empty
          image={<img src={require("assets/images/kho/empty.png")} alt="..." />}
          description={t("phcn.luongGia.chuaTaoLuongGiaPHCN")}
        >
          <Button type="primary" onClick={onThemMoi}>
            {t("phcn.luongGia.taoLuongGiaPHCNMoi")}
          </Button>
        </Empty>
      )}

      <ModalSignPrint ref={refModalSignPrint} />
    </Main>
  );
};

export default LuongGiaPHCN;
