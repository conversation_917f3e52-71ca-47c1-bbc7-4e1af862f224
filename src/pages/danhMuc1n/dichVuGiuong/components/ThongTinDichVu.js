import React, { forwardRef, useState, useEffect, useMemo, useRef } from "react";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { DatePicker, Checkbox, Select, InputTimeout } from "components";
import { Form, Input } from "antd";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { isArray, openInNewTab, parseFloatNumber } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useStore } from "hooks";
import { ENUM } from "constants/index";
import { InputNumberFormat } from "components/common";
import { orderBy } from "lodash";
import env from "module_env/ENV";

function FormServiceInfo(props, ref) {
  const {
    currentItem,
    layerId,
    refCallbackSave = {},
    roleSave,
    roleEdit,
    refTab,
  } = props;

  const listAllDonViTinh = useStore("donViTinh.listAllDonViTinh", []);
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );

  const [listDoiTuongSuDung] = useEnum(ENUM.DOI_TUONG_SU_DUNG);
  const [listPhanLoaiGiuong] = useEnum(ENUM.PHAN_LOAI_GIUONG);
  const [listAllBenhVien] = useListAll("benhVien", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  const listNguonKhacChiTra = useMemo(() => {
    return orderBy(listAllNguonKhacChiTra || [], (a) => a.ma, "asc").map(
      (o) => ({
        ...o,
        ten: `${o.ma} - ${o.ten}`,
      })
    );
  }, [listAllNguonKhacChiTra]);

  const {
    dichVuGiuong: { createOrEdit },
    nhomDichVuCap2: { getAllDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3 },
  } = useDispatch();
  const [state, _setState] = useState({});
  const { t } = useTranslation();
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  let editStatus = useMemo(() => props?.editStatus, [props?.editStatus]);
  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
    form.resetFields();
    loadCurrentItem(currentItem);
    refTab.current &&
      refTab.current.setKhoaChiDinh(!!currentItem?.hanCheKhoaChiDinh);
  }, [currentItem]);

  const listAllBV = useMemo(() => {
    if (isArray(listAllBenhVien, true)) {
      return listAllBenhVien.map((item) => ({
        ...item,
        ten: `${item.ma} - ${item.ten}`,
      }));
    }
    return [];
  }, [listAllBenhVien]);

  const [form] = Form.useForm();

  const loadCurrentItem = (item) => {
    if (item) {
      const {
        dichVu: {
          donViTinhId,
          nguonKhacId,
          giaBaoHiem,
          giaKhongBaoHiem,
          giaPhuThu,
          khongTinhTien,
          ma,
          maTuongDuong,
          nhomChiPhiBh,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          ten,
          tenTuongDuong,
          tyLeBhTt,
          tyLeTtDv,
          mienPhiGiamDocDuyet,
          giaGoc,
        } = {},
        id,
        dsDoiTuongSuDung,
        hanCheKhoaChiDinh,
        hoiSucTichCuc,
        ngayCongBo,
        quyetDinh,
        active,
        phanLoai,
        kiosk,
        csKcbChuyenGiaoId,
        csKcbThucHienId,
      } = item || {};
      const data = {
        id,
        donViTinhId,
        giaBaoHiem,
        giaKhongBaoHiem,
        giaPhuThu,
        khongTinhTien,
        ma,
        maTuongDuong,
        nhomChiPhiBh,
        nhomDichVuCap1Id,
        nhomDichVuCap2Id,
        nhomDichVuCap3Id,
        ten,
        tenTuongDuong,
        tyLeBhTt,
        tyLeTtDv,
        nguonKhacId: nguonKhacId || null,
        dsDoiTuongSuDung: dsDoiTuongSuDung || [],
        hanCheKhoaChiDinh,
        hoiSucTichCuc,
        ngayCongBo: (ngayCongBo && moment(ngayCongBo)) || null,
        quyetDinh,
        active: active !== undefined ? active : true,
        phanLoai,
        kiosk,
        csKcbChuyenGiaoId,
        csKcbThucHienId,
        mienPhiGiamDocDuyet,
        giaGoc,
      };
      form.setFieldsValue(data);
      setState({
        data: data,
      });
    } else {
      form.resetFields();
      setState({
        data: null,
      });
    }
  };

  const onAddNewRow = () => {
    loadCurrentItem({});
  };

  const onCancel = () => {
    if (currentItem?.id) {
      loadCurrentItem({ ...currentItem });
    } else {
      loadCurrentItem({});
      form.resetFields();
    }
  };
  const onSave = (e) => {
    e.preventDefault();
    form.submit();
  };
  refCallbackSave.current = onSave;
  const refAutoFocus = useRef(null);

  const onHandleSubmit = (values) => {
    const {
      donViTinhId,
      giaBaoHiem,
      giaKhongBaoHiem,
      giaPhuThu,
      khongTinhTien,
      ma,
      maTuongDuong,
      nhomChiPhiBh,
      nhomDichVuCap1Id,
      nhomDichVuCap2Id,
      nhomDichVuCap3Id,
      ten,
      tenTuongDuong,
      tyLeBhTt,
      tyLeTtDv,
      nguonKhacId,
      dsDoiTuongSuDung,
      hanCheKhoaChiDinh,
      hoiSucTichCuc,
      ngayCongBo,
      quyetDinh,
      active,
      phanLoai,
      kiosk,
      csKcbChuyenGiaoId,
      csKcbThucHienId,
      mienPhiGiamDocDuyet,
      giaGoc,
    } = values;
    values = {
      dichVu: {
        donViTinhId,
        nguonKhacId: nguonKhacId || null,
        giaBaoHiem:
          typeof giaBaoHiem === "string"
            ? parseFloatNumber(giaBaoHiem)
            : giaBaoHiem,
        giaKhongBaoHiem:
          typeof giaKhongBaoHiem === "string"
            ? parseFloatNumber(giaKhongBaoHiem)
            : giaKhongBaoHiem,
        giaPhuThu:
          typeof giaPhuThu === "string"
            ? parseFloatNumber(giaPhuThu)
            : giaPhuThu,
        khongTinhTien,
        ma,
        maTuongDuong,
        nhomChiPhiBh,
        nhomDichVuCap1Id,
        nhomDichVuCap2Id,
        nhomDichVuCap3Id,
        ten,
        tenTuongDuong,
        tyLeBhTt,
        tyLeTtDv,
        loaiDichVu: 130,
        mienPhiGiamDocDuyet,
        giaGoc,
      },
      active,
      dsDoiTuongSuDung,
      hanCheKhoaChiDinh,
      hoiSucTichCuc,
      ngayCongBo: (ngayCongBo && ngayCongBo.format("YYYY-MM-DD")) || null,
      quyetDinh,
      phanLoai,
      kiosk,
      id: state.data?.id,
      csKcbChuyenGiaoId,
      csKcbThucHienId,
    };
    createOrEdit(values).then((res) => {
      form.resetFields();
    });
  };
  const onChangeFileds = (key, e) => {
    setState({ [key]: e });
  };
  const onChangeHanCheKhoaChiDinh = (e) => {
    refTab.current && refTab.current.setKhoaChiDinh(!!e.target.checked);
  };

  return (
    <EditWrapper
      title={t("danhMuc.thongTinDichVu")}
      onCancel={onCancel}
      onSave={onSave}
      onAddNewRow={onAddNewRow}
      roleSave={roleSave}
      roleEdit={roleEdit}
      editStatus={editStatus}
      forceShowButtonSave={checkRole(roleEdit) && true}
      forceShowButtonCancel={checkRole(roleEdit) && true}
      isHiddenButtonAdd={true}
      layerId={layerId}
    >
      <fieldset disabled={editStatus}>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          <Form.Item
            label={`${t("danhMuc.maTitle", {
              title: t("danhMuc.dichVu").toLowerCase(),
            })}`}
            name="ma"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.VuiLongNhapMaTitle", {
                  title: t("danhMuc.dichVu").toLowerCase(),
                })}`,
              },
              {
                max: 20,
                message: `${t(
                  "danhMuc.vuiLongNhapLabelTitleKhongQuaCountKyTu",
                  {
                    label: t("danhMuc.ma").toLowerCase(),
                    title: t("danhMuc.dichVu").toLowerCase(),
                    count: 20,
                  }
                )}!`,
              },
              {
                whitespace: true,
                message: `${t("danhMuc.VuiLongNhapMaTitle", {
                  title: t("danhMuc.dichVu").toLowerCase(),
                })}!`,
              },
            ]}
          >
            <Input
              autoFocus={true}
              className="input-option"
              placeholder={`${t("danhMuc.VuiLongNhapMaTitle", {
                title: t("danhMuc.dichVu").toLowerCase(),
              })}`}
            />
          </Form.Item>
          <Form.Item
            label={`${t("danhMuc.tenTitle", {
              title: t("danhMuc.dichVu").toLowerCase(),
            })}`}
            name="ten"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.VuiLongNhapTenTitle", {
                  title: t("danhMuc.dichVu").toLowerCase(),
                })}!`,
              },
              {
                max: 1000,
                message: `${t(
                  "danhMuc.vuiLongNhapLabelTitleKhongQuaCountKyTu",
                  {
                    label: t("danhMuc.ten").toLowerCase(),
                    title: t("danhMuc.dichVu").toLowerCase(),
                    count: 1000,
                  }
                )}!`,
              },
              {
                whitespace: true,
                message: `${t("danhMuc.VuiLongNhapTenTitle", {
                  title: t("danhMuc.dichVu").toLowerCase(),
                })}!`,
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={`${t("danhMuc.VuiLongNhapTenTitle", {
                title: t("danhMuc.dichVu").toLowerCase(),
              })}`}
            />
          </Form.Item>
          <Form.Item
            label={`${t("danhMuc.maTitle", {
              title: t("danhMuc.tuongDuong").toLowerCase(),
            })}`}
            name="maTuongDuong"
          >
            <Input
              className="input-option"
              placeholder={`${t("danhMuc.VuiLongNhapMaTitle", {
                title: t("danhMuc.tuongDuong").toLowerCase(),
              })}`}
            />
          </Form.Item>
          <Form.Item
            label={`${t("danhMuc.tenTitle", {
              title: t("danhMuc.tuongDuong").toLowerCase(),
            })}`}
            name="tenTuongDuong"
          >
            <Input
              className="input-option"
              placeholder={`${t("danhMuc.VuiLongNhapTenTitle", {
                title: t("danhMuc.tuongDuong").toLowerCase(),
              })}`}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.donGiaKhongBH")}
            name="giaKhongBaoHiem"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.vuiLongNhapTitle", {
                  title: t("danhMuc.donGiaKhongBH").toLowerCase(),
                })}!`,
              },
            ]}
          >
            <InputNumberFormat
              placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                title: t("danhMuc.donGiaKhongBH").toLowerCase(),
              })}`}
              className="input-option"
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.donGiaBH")} name="giaBaoHiem">
            <InputNumberFormat
              placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                title: t("danhMuc.donGiaBH").toLowerCase(),
              })}`}
              className="input-option"
            />
          </Form.Item>
          <Form.Item
            label={
              ["pshn"].includes(env.HOSPITAL)
                ? t("tenTruong.giaHaoPhi")
                : t("tenTruong.giaGoc")
            }
            name="giaGoc"
            rules={[
              {
                pattern: new RegExp(/^[0-9]/),
                message: t("danhMuc.vuiLongNhapSoDuong"),
              },
            ]}
          >
            <InputTimeout
              type="number"
              formatPrice={true}
              placeholder={
                ["pshn"].includes(env.HOSPITAL)
                  ? t("danhMuc.nhapGiaHaoPhi")
                  : t("danhMuc.nhapGiaGoc")
              }
              className="input-option"
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.phuThu")} name="giaPhuThu">
            <InputNumberFormat
              placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                title: t("danhMuc.phuThu").toLowerCase(),
              })}`}
              className="input-option"
            />
          </Form.Item>
          {/* <Form.Item
            label={"Nhóm chi phí"}
            name="nhomChiPhiBh"
            rules={[
              {
                required: true,
                message: "Vui lòng chọn nhóm chi phí",
              },
            ]}
          >
            <Select
              placeholder="Vui lòng chọn nhóm chi phí"
              data={listNhomChiPhiBh}
            />
          </Form.Item> */}
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/don-vi-tinh")}
              >
                {t("danhMuc.donViTinh")}
              </div>
            }
            name="donViTinhId"
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonDonViTinh"),
              },
            ]}
          >
            <Select
              data={listAllDonViTinh}
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.donViTinh").toLowerCase(),
              })}`}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=1")}
              >
                {t("danhMuc.nhomDVCap1")}
              </div>
            }
            name="nhomDichVuCap1Id"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.vuiLongChonTitle", {
                  title: t("danhMuc.nhomDVCap1").toLowerCase(),
                })}`,
              },
            ]}
          >
            <Select
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.nhomDVCap1").toLowerCase(),
              })}`}
              data={listAllNhomDichVuCap1}
              onChange={(e) => {
                if (e) {
                  getAllDichVuCap2({ nhomDichVuCap1Id: e });
                } else {
                  getAllDichVuCap2();
                }
              }}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=2")}
              >
                {t("danhMuc.nhomDVCap2")}
              </div>
            }
            name="nhomDichVuCap2Id"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.vuiLongChonTitle", {
                  title: t("danhMuc.nhomDVCap2").toLowerCase(),
                })}!`,
              },
            ]}
          >
            <Select
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.nhomDVCap2").toLowerCase(),
              })}`}
              data={listAllNhomDichVuCap2}
              onChange={(e) => {
                if (e) {
                  getAllTongHopDichVuCap3({ nhomDichVuCap2Id: e });
                } else {
                  getAllTongHopDichVuCap3();
                }
              }}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=3")}
              >
                {t("danhMuc.nhomDVCap3")}
              </div>
            }
            name="nhomDichVuCap3Id"
          >
            <Select
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.nhomDVCap3").toLowerCase(),
              })}`}
              data={listAllNhomDichVuCap3}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=3")}
              >
                {t("danhMuc.phanLoaiGiuong")}
              </div>
            }
            name="phanLoai"
            rules={[
              {
                required: true,
                message: `${t("danhMuc.vuiLongChonTitle", {
                  title: t("danhMuc.phanLoaiGiuong").toLowerCase(),
                })}!`,
              },
            ]}
          >
            <Select
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.phanLoaiGiuong").toLowerCase(),
              })}`}
              data={listPhanLoaiGiuong}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.truongHopKeDV")} name="dsDoiTuongSuDung">
            <Select
              data={listDoiTuongSuDung}
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.truongHopKeDV").toLowerCase(),
              })}`}
              mode="multiple"
              showArrow
              style={{ paddingRight: "10pt" }}
            />
          </Form.Item>

          <Form.Item
            label={`${t("danhMuc.maTitle", {
              title: t("danhMuc.soQuyetDinh").toLowerCase(),
            })}`}
            name="quyetDinh"
            // rules={[
            //   {
            //     required: true,
            //     message: "Vui lòng nhập mã số quyết định",
            //   },
            // ]}
          >
            <Input
              className="input-option"
              placeholder={`${t("danhMuc.VuiLongNhapMaTitle", {
                title: t("danhMuc.soQuyetDinh").toLowerCase(),
              })}`}
            />
          </Form.Item>
          <Form.Item
            label={t("danhMuc.ngayQuyetDinh")}
            name="ngayCongBo"
            // rules={[
            //   {
            //     required: true,
            //     message: "Vui lòng chọn ngày quyết định",
            //   },
            // ]}
          >
            <DatePicker
              className="input-option"
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.ngayQuyetDinh").toLowerCase(),
              })}`}
              format={"DD/MM/YYYY"}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.nguonKhacChiTra")} name="nguonKhacId">
            <Select
              data={listNguonKhacChiTra}
              placeholder={`${t("danhMuc.vuiLongChonTitle", {
                title: t("danhMuc.nguonKhacChiTra").toLowerCase(),
              })}`}
              showArrow
              style={{ paddingRight: "10pt" }}
            />
          </Form.Item>
          <Form.Item
            label={t("tenTruong.cskcbChuyenGiaoDvkt")}
            name="csKcbChuyenGiaoId"
          >
            <Select
              data={listAllBV}
              placeholder={t("tenTruong.vuilongChonCskcbChuyenGiao")}
            />
          </Form.Item>
          <Form.Item
            label={t("tenTruong.cskcbThucHienCls")}
            name="csKcbThucHienId"
          >
            <Select
              data={listAllBV}
              placeholder={t("tenTruong.vuilongChonCskcbThucHienCls")}
            />
          </Form.Item>
          <Form.Item name="khongTinhTien" valuePropName="checked">
            <Checkbox>{t("danhMuc.khongTinhTien")}</Checkbox>
          </Form.Item>
          <Form.Item name="hanCheKhoaChiDinh" valuePropName="checked">
            <Checkbox onChange={onChangeHanCheKhoaChiDinh}>
              {t("danhMuc.hanCheKhoaChiDinh")}
            </Checkbox>
          </Form.Item>
          <Form.Item name="hoiSucTichCuc" valuePropName="checked">
            <Checkbox>{t("danhMuc.hoiSucTichCuc")}</Checkbox>
          </Form.Item>
          <Form.Item name="kiosk" valuePropName="checked">
            <Checkbox>{t("danhMuc.hienThiLenKiosk")}</Checkbox>
          </Form.Item>
          <Form.Item
            label=" "
            name="mienPhiGiamDocDuyet"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.mienPhiGiamDocDuyet")}</Checkbox>
          </Form.Item>
          {state.data?.id && (
            <Form.Item name="active" valuePropName="checked">
              <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
            </Form.Item>
          )}
        </Form>
      </fieldset>
    </EditWrapper>
  );
}

export default forwardRef(FormServiceInfo);
