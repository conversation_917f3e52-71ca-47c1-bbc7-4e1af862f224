import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  DatePicker,
} from "components";
import {
  ENUM,
  HAN_CHE_KHOA,
  HIEU_LUC,
  HOTKEY,
  KHONG_TINH_TIEN,
  ROLES,
  YES_NO,
} from "constants/index";
import { Input, InputNumber, Menu } from "antd";
import moment from "moment";
import { useEnum, useLoading, useStore } from "hooks";
import { SVG } from "assets";
import { MAU_XUAT_DU_LIEU_XML } from "client/api";
import ModalImport from "components/DanhMuc/ModalImport";
import ModalExportDanhMucMoi from "components/DanhMuc/ModalExportDanhMucMoi";
import { checkRole } from "lib-utils/role-utils";
import { select } from "redux-store/stores";
import { Main } from "../styled";
import { selectMaTen } from "redux-store/selectors";
import env from "module_env/ENV";

const DanhSachDichVu = ({
  loaiDichVu,
  styleContainerButtonHeader,
  layerId,
  title,
  classNameRow,
  buttonHeader,
  onImport,
  onExport,
  onImportTuyChonGia,
  onExportTuyChonGia,
  onExportNhomChiPhi,
  onImportNhomChiPhi,
  onExportDichVuKemTheo,
  onImportDichVuKemTheo,
  ...props
}) => {
  const { t } = useTranslation();
  const { onRegisterHotkey } = useDispatch().phimTat;
  const refSelectRow = useRef();
  const refModalImportDvGiuong = useRef(null);
  const refModalImportTuyChonGia = useRef(null);
  const refModalExport = useRef(null);
  const refModalImportNhomChiPhi = useRef(null);
  const refModalImportDichVuKemTheo = useRef(null);
  const { showLoading, hideLoading } = useLoading();

  const {
    dichVuGiuong: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData,
    },
    chuyenKhoa: { getListAllChuyenKhoa },
    donViTinh: { getListAllDonViTinh },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3 },
    benhVien: { getListAllBenhVien },
  } = useDispatch();

  const {
    listDichVuGiuong,
    totalElements,
    page,
    size,
    currentItem,
    dataSortColumn,
  } = useStore(
    "dichVuGiuong",
    {},
    {
      fields:
        "listDichVuGiuong,totalElements,page,size,currentItem,dataSortColumn",
    }
  );

  const listAllDonViTinh = useStore("donViTinh.listAllDonViTinh", []);
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );
  const [_, getBenhVienById] = useStore(select.benhVien.listAllBenhVien);

  const [listNhomChiPhiBh] = useEnum(ENUM.NHOM_CHI_PHI_BH);
  const [listNguonKhacChiTra] = useEnum(ENUM.NGUON_KHAC_CHI_TRA);
  const [listDoiTuongSuDung] = useEnum(ENUM.DOI_TUONG_SU_DUNG);
  const [listPhanLoaiGiuong] = useEnum(ENUM.PHAN_LOAI_GIUONG);

  useEffect(() => {
    getListAllBenhVien({ page: "", size: "" });
  }, []);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP, //up
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN, //down
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
      ],
    });
  }, []);
  refSelectRow.current = (index) => {
    const indexNextItem =
      (listDichVuGiuong?.findIndex(
        (item) => item.id === state.currentItem?.id
      ) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < listDichVuGiuong.length) {
      onShowAndHandleUpdate(listDichVuGiuong[indexNextItem]);
      setState({ currentItem: listDichVuGiuong[indexNextItem] });
      document
        .getElementsByClassName(
          "row-id-" + listDichVuGiuong[indexNextItem]?.id
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useEffect(() => {
    updateData({ listDichVuGiuong: [] });
    onSizeChange({
      size: 10,
      dataSearch: {},
    });
    getListAllChuyenKhoa({ page: "", size: "" });
    getListAllDonViTinh({ page: "", size: "" });
    getAllTongHopDichVuCap1({ page: "", size: "" });
    getAllTongHopDichVuCap2({ page: "", size: "" });
    getAllTongHopDichVuCap3({ page: "", size: "" });
  }, []);

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const refTimeOut = useRef(null);
  const onSearchInput = (key) => (e) => {
    if (
      key === "dichVu.nhomDichVuCap1Id" ||
      key === "dichVu.nhomDichVuCap2Id" ||
      key === "dichVu.nhomDichVuCap3Id"
    ) {
      onChangeInputSearch({
        [key]: e,
      });
      return null;
    }
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
      refTimeOut.current = null;
    }
    refTimeOut.current = setTimeout(
      (key, s) => {
        let value = "";
        if (s) {
          if (s?.hasOwnProperty("checked")) value = s?.checked;
          else value = s?.value;
        } else {
          if (e && typeof e === "object" && !Array.isArray(e)) {
            value = moment(e).format("YYYY-MM-DD");
          } else {
            value = e;
          }
        }
        onChangeInputSearch({
          [key]: value,
        });
      },
      500,
      key,
      e?.target
    );
  };

  const columns = [
    {
      title: <HeaderSearch title="STT" />,
      width: "50px",
      dataIndex: "index",
      key: 0,
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maDichVu")}
          sort_key="dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ma"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.maDichVu").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.ma")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: 1,
      render: (item) => {
        return item?.ma;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenDichVu")}
          sort_key="dichVu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ten"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.tenDichVu").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.ten")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dichVu",
      key: 2,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donGiaKhongBH")}
          sort_key="dichVu.giaKhongBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaKhongBaoHiem"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.donGiaKhongBH").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.giaKhongBaoHiem")}
              type="number"
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      key: 3,
      render: (item = []) => {
        return item?.giaKhongBaoHiem?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donGiaBH")}
          sort_key="dichVu.giaBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaBaoHiem"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.timGiaBH")}
              onChange={onSearchInput("dichVu.giaBaoHiem")}
              type="number"
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      key: 4,
      render: (item, list, index) => {
        return item?.giaBaoHiem?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            ["pshn"].includes(env.HOSPITAL)
              ? t("tenTruong.giaHaoPhi")
              : t("tenTruong.giaGoc")
          }
          sort_key="dichVu.giaGoc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaGoc"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={
                ["pshn"].includes(env.HOSPITAL)
                  ? t("tenTruong.giaHaoPhi")
                  : t("tenTruong.giaGoc")
              }
              onChange={onSearchInput("dichVu.giaGoc")}
              type="number"
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      i18Name: ["pshn"].includes(env.HOSPITAL)
        ? "tenTruong.giaHaoPhi"
        : "tenTruong.giaGoc",
      key: "dichVu.giaGoc",
      show: true,
      render: (item, list, index) => {
        return item?.giaGoc?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phuThu")}
          sort_key="dichVu.giaPhuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaPhuThu"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.timGiaPhuThu")}
              onChange={onSearchInput("dichVu.giaPhuThu")}
              type="number"
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      key: 5,
      render: (item, list, index) => {
        return item?.giaPhuThu?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomChiPhi")}
          sort_key="dichVu.nhomChiPhiBh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomChiPhiBh"] || 0}
          searchSelect={
            <Select
              data={listNhomChiPhiBh}
              placeholder={t("danhMuc.chonNhomChiPhi")}
              onChange={onSearchInput("dichVu.nhomChiPhiBh")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dichVu",
      key: 16,
      render: (item, list, index) => {
        if (listNhomChiPhiBh?.length) {
          return (
            listNhomChiPhiBh.find((x) => x.id === item?.nhomChiPhiBh)?.ten || ""
          );
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donViTinh")}
          sort_key="dichVu.donViTinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.donViTinhId"] || 0}
          searchSelect={
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDonViTinh")}
              onChange={onSearchInput("dichVu.donViTinhId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: 17,
      render: (item, list, index) => {
        return item?.donViTinh?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDichVuCap1")}
          sort_key="dichVu.nhomDichVuCap1Id"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap1Id"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonDichVuCap1")}
              data={listAllNhomDichVuCap1}
              onChange={onSearchInput("dichVu.nhomDichVuCap1Id")}
              style={{ width: "100%" }}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      key: 18,
      render: (item) => {
        return item?.nhomDichVuCap1?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDichVuCap2")}
          sort_key="dichVu.nhomDichVuCap2Id"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap2Id"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonDichVuCap2")}
              data={listAllNhomDichVuCap2}
              onChange={onSearchInput("dichVu.nhomDichVuCap2Id")}
              style={{ width: "100%" }}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      key: 19,
      render: (item) => {
        return item?.nhomDichVuCap2?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDichVuCap3")}
          sort_key="dichVu.nhomDichVuCap3Id"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap3Id"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonDichVuCap3")}
              data={listAllNhomDichVuCap3}
              onChange={onSearchInput("dichVu.nhomDichVuCap3Id")}
              style={{ width: "100%" }}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      key: 20,
      render: (item) => {
        return item?.nhomDichVuCap3?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phanLoaiGiuong")}
          sort_key="phanLoai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phanLoai"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonPhanLoaiGiuong")}
              onChange={onSearchInput("phanLoai")}
              data={listPhanLoaiGiuong}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "phanLoai",
      key: 21,
      render: (item) => {
        return listPhanLoaiGiuong.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maTuongDuong")}
          sort_key="dichVu.maTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.maTuongDuong"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaTuongDuong")}
              onChange={onSearchInput("dichVu.maTuongDuong")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: 21,
      render: (item) => {
        return item?.maTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenTuongDuong")}
          sort_key="dichVu.tenTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenTuongDuong"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenTuongDuong")}
              onChange={onSearchInput("dichVu.tenTuongDuong")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: 22,
      render: (item) => {
        return item?.tenTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="hanCheKhoaChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.hanCheKhoaChiDinh || 0}
          searchSelect={
            <Select
              data={HAN_CHE_KHOA}
              placeholder={t("danhMuc.chonHanCheKhoaChiDinh")}
              onChange={onSearchInput("hanCheKhoaChiDinh")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.hanCheKhoaChiDinh")}
        />
      ),
      width: 130,
      dataIndex: "hanCheKhoaChiDinh",
      key: 24,
      align: "center",
      render: (item) => {
        return <Checkbox checked={!!item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.truongHopKeDV")}
          sort_key="dsDoiTuongSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dsDoiTuongSuDung"] || 0}
          searchSelect={
            <Select
              data={listDoiTuongSuDung}
              placeholder={t("danhMuc.chonTruongHopKeDV")}
              onChange={onSearchInput("dsDoiTuongSuDung")}
              mode="multiple"
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dsDoiTuongSuDung",
      key: 25,
      render: (item, list, index) => {
        if (listDoiTuongSuDung?.length) {
          let list =
            item
              ?.map((nguon, index) => {
                let x = listDoiTuongSuDung.find((dv) => dv.id === nguon);
                return x?.ten || "";
              })
              .filter((item) => item) ?? [];

          return list.join(", ");
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="quyetDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinh"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapMaSoQuyetDinh")}
              onChange={onSearchInput("quyetDinh")}
            />
          }
          title={t("danhMuc.maSoQuyetDinh")}
        />
      ),
      width: "150px",
      dataIndex: "quyetDinh",
      key: "quyetDinh",
    },
    {
      title: (
        <HeaderSearch
          sort_key="ngayCongBo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ngayCongBo"] || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("danhMuc.chonNgayQuyetDinh")}
              onChange={onSearchInput("ngayCongBo")}
            />
          }
          title={t("danhMuc.ngayQuyetDinh")}
        />
      ),
      width: "150px",
      dataIndex: "ngayCongBo",
      key: "ngayCongBo",
      render: (item) => {
        return item && new Date(item).format("dd/MM/YYYY");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nguonKhacChiTra")}
          sort_key="dichVu.dsNguonKhacChiTra"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.dsNguonKhacChiTra"] || 0}
          searchSelect={
            <Select
              data={listNguonKhacChiTra}
              placeholder={t("danhMuc.chonNguonKhacChiTra")}
              onChange={onSearchInput("dichVu.dsNguonKhacChiTra")}
              mode="multiple"
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dichVu",
      key: 16,
      render: (item, list, index) => {
        if (listNguonKhacChiTra?.length) {
          let list =
            item?.dsNguonKhacChiTra
              ?.map((nguon, index) => {
                let x = listNguonKhacChiTra.find((dv) => dv.id === nguon);
                return x?.ten || "";
              })
              .filter((item) => item) ?? [];

          return list.join(", ");
        }
        return "";
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="csKcbChuyenGiaoId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["csKcbChuyenGiaoId"] || 0}
          searchSelect={
            <Select
              data={select.benhVien.listAllBenhVien}
              getLabel={selectMaTen}
              placeholder={t("tenTruong.cskcbChuyenGiaoDvkt")}
              onChange={onSearchInput("csKcbChuyenGiaoId")}
              hasAllOption={true}
            />
          }
          title={t("tenTruong.cskcbChuyenGiaoDvkt")}
        />
      ),
      width: 150,
      dataIndex: "csKcbChuyenGiaoId",
      key: 17,
      align: "center",
      render: (item) => {
        return getBenhVienById(item) && selectMaTen(getBenhVienById(item));
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="csKcbThucHienId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["csKcbThucHienId"] || 0}
          searchSelect={
            <Select
              data={select.benhVien.listAllBenhVien}
              getLabel={selectMaTen}
              placeholder={t("tenTruong.cskcbThucHienCls")}
              onChange={onSearchInput("csKcbThucHienId")}
              hasAllOption={true}
            />
          }
          title={t("tenTruong.cskcbThucHienCls")}
        />
      ),
      width: 150,
      dataIndex: "csKcbThucHienId",
      key: 17,
      align: "center",
      render: (item) => {
        return getBenhVienById(item) && selectMaTen(getBenhVienById(item));
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.hoiSucTichCuc")}
          sort_key="hoiSucTichCuc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.hoiSucTichCuc || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonHoiSucTichCuc")}
              onChange={onSearchInput("hoiSucTichCuc")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "hoiSucTichCuc",
      key: 24,
      align: "center",
      render: (item) => {
        return <Checkbox checked={!!item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.khongTinhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.khongTinhTien"] || 0}
          searchSelect={
            <Select
              data={KHONG_TINH_TIEN}
              placeholder={t("danhMuc.chonTinhTien")}
              onChange={onSearchInput("dichVu.khongTinhTien")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.khongTinhTien")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: 17,
      align: "center",
      render: (item) => {
        return item && <Checkbox checked={!!item.khongTinhTien} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={onSearchInput("active")}
              defaultValue=""
              hasAllOption={true}
            />
          }
          title={t("danhMuc.coHieuLuc")}
        />
      ),
      width: 120,
      dataIndex: "active",
      key: 18,
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const onHandleSizeChange = (size) => {
    onSizeChange({ size: size });
  };

  const onShowAndHandleUpdate = (data = {}) => {
    props.setEditStatus(true);
    updateData({
      currentItem: { ...data },
    });
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        onShowAndHandleUpdate(record);
        setState({ currentItem: record });
      },
    };
  };
  const setRowClassName = (record) => {
    let idDiff = currentItem?.id;
    let notValid = !record.active ? "row-gray" : "";

    return record.id == idDiff
      ? "row-actived row-id-" + record.id
      : `row-id-${record.id} ${notValid}`;
  };

  const onImportDuLieuDvGiuong = () => {
    refModalImportDvGiuong.current &&
      refModalImportDvGiuong.current.show({ isModalVisible: true });
  };

  const onExportDuLieuDvGiuong = () => {
    showLoading();
    onExport().finally(() => {
      hideLoading();
    });
  };

  const onImportDuLieuTuyChonGia = () => {
    refModalImportTuyChonGia.current &&
      refModalImportTuyChonGia.current.show({ isModalVisible: true });
  };

  const onImportDuLieuNhomChiPhi = () => {
    refModalImportNhomChiPhi.current &&
      refModalImportNhomChiPhi.current.show({ isModalVisible: true });
  };
  const onImportDuLieuDichVuKemTheo = () => {
    refModalImportDichVuKemTheo.current &&
      refModalImportDichVuKemTheo.current.show({ isModalVisible: true });
  };

  const onExportDuLieuTuyChonGia = () => {
    showLoading();
    onExportTuyChonGia({
      id: loaiDichVu,
    }).finally(() => {
      hideLoading();
    });
  };

  const handleExport = async (api) => {
    try {
      showLoading();
      await api({
        id: loaiDichVu,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onExportDanhMucMoi = () => {
    refModalExport.current &&
      refModalExport.current.show(MAU_XUAT_DU_LIEU_XML.XUAT_DU_LIEU_DM_DVKT);
  };

  const menuMultiTab = () => (
    <Menu
      items={[
        ...(checkRole([ROLES["DANH_MUC"].XUAT_DANH_SACH_DVKT_MOI])
          ? [
              {
                key: 1,
                label: (
                  <a onClick={onExportDanhMucMoi}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>{t("danhMuc.xuatDanhSachDichVuDayCong")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImport
          ? [
              {
                key: 2,
                label: (
                  <a onClick={onImportDuLieuDvGiuong}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.dichVuGiuong"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExport
          ? [
              {
                key: 3,
                label: (
                  <a onClick={onExportDuLieuDvGiuong}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      {t("danhMuc.xuatDuLieuTitle", {
                        title: t("danhMuc.dichVuGiuong"),
                      })}
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportTuyChonGia
          ? [
              {
                key: 4,
                label: (
                  <a onClick={onImportDuLieuTuyChonGia}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.tuyChonGia"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportTuyChonGia
          ? [
              {
                key: 5,
                label: (
                  <a onClick={onExportDuLieuTuyChonGia}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.tuyChonGia"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportNhomChiPhi
          ? [
              {
                key: 6,
                label: (
                  <a onClick={onImportDuLieuNhomChiPhi}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.nhomChiPhi"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportNhomChiPhi
          ? [
              {
                key: 7,
                label: (
                  <a onClick={onExportNhomChiPhi}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.nhomChiPhi"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportDichVuKemTheo
          ? [
              {
                key: 8,
                label: (
                  <a onClick={onImportDuLieuDichVuKemTheo}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.dichVuKemTheo"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportDichVuKemTheo
          ? [
              {
                key: 9,
                label: (
                  <a onClick={() => handleExport(onExportDichVuKemTheo)}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.dichVuKemTheo"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
      ]}
    />
  );

  return (
    <Main>
      <TableWrapper
        title={title}
        scroll={{ x: 1000 }}
        buttonHeader={buttonHeader || []}
        classNameRow={classNameRow}
        columns={columns.filter((item) => {
          return item.display !== false;
        })}
        dataSource={listDichVuGiuong}
        onRow={onRow}
        rowClassName={setRowClassName}
        styleContainerButtonHeader={{ ...styleContainerButtonHeader }}
        menuMultiTab={menuMultiTab}
      ></TableWrapper>
      {totalElements ? (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          listData={listDichVuGiuong}
          onShowSizeChange={onHandleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
      ) : null}
      <ModalImport onImport={onImport} ref={refModalImportDvGiuong} />
      <ModalImport
        onImport={onImportTuyChonGia}
        ref={refModalImportTuyChonGia}
      />
      <ModalExportDanhMucMoi ref={refModalExport} />
      <ModalImport
        onImport={onImportNhomChiPhi}
        ref={refModalImportNhomChiPhi}
      />
      <ModalImport
        onImport={onImportDichVuKemTheo}
        ref={refModalImportDichVuKemTheo}
      />
    </Main>
  );
};

export default DanhSachDichVu;
