import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import FormServiceInfo from "./components/FormServiceInfo";
import LieuDungThuoc from "./components/LieuDungThuoc";
import <PERSON>homChiP<PERSON> from "components/DanhMuc/NhomChiPhi";
import {
  HeaderSearch,
  TableWrapper,
  Select,
  Pagination,
  Checkbox,
  Button,
  SelectLoadMore,
} from "components";
import MultiLevelTab from "components/MultiLevelTab";
import BaseDm3 from "pages/danhMuc/BaseDm3";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  HIEU_LUC,
  KHONG_TINH_TIEN,
  ENUM,
  YES_NO,
  ROLES,
  LOAI_DICH_VU,
  CACHE_KEY,
} from "constants/index";
import { Col, Input, Menu, InputNumber } from "antd";
import { formatNumber, concatString, isNumber } from "utils/index";
import { checkRole } from "lib-utils/role-utils";
import {
  useEnum,
  useGuid,
  useLazyKVMap,
  useListAll,
  useLoading,
  useQueryAll,
  useStore,
} from "hooks";
import MucDichSuDung from "components/DanhMuc//MucDichSuDung";
import { useTranslation } from "react-i18next";
import { t } from "i18next";
import { SVG } from "assets";
import ModalImport from "components/DanhMuc/ModalImport";
import ModalExportDanhMucMoi from "components/DanhMuc/ModalExportDanhMucMoi";
import { MAU_XUAT_DU_LIEU_XML } from "client/api";
import { lowerFirst, orderBy } from "lodash";
import { LoadingWrapper } from "./components/LoadingWrapper";
import { query } from "redux-store/stores";
import DichVuKemTheo from "components/DanhMuc/DichVuKemTheo";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";

const { Setting } = TableWrapper;
const ID_LOAI_DICH_VU = LOAI_DICH_VU.THUOC;
const TEN_LOAI_DICH_VU = t("danhMuc.danhMucThuoc");
const HDSD = [
  {
    id: "true",
    ten: "Có tài liệu",
  },
  {
    id: "false",
    ten: "Không tài liệu",
  },
];
const DanhMucThuoc = (props) => {
  const [editStatus, setEditStatus] = useState(false);
  const [collapseStatus, setCollapseStatus] = useState(false);
  const layerId = useGuid();
  const [state, _setState] = useState({
    showFullTable: false,
  });
  const { t } = useTranslation();

  const {
    listData,
    totalElements,
    page,
    size,
    currentItem,
    dataSortColumn,
    dataSearch,
  } = useSelector((state) => state.danhMucThuoc);

  const listHoatChat = useStore("hoatChat.listHoatChat", []);
  const listAllNhomDichVuKho = useStore(
    "phanNhomDichVuKho.listAllNhomDichVuKho",
    []
  );
  const listAllPhanLoaiThuoc = useStore(
    "phanLoaiThuoc.listAllPhanLoaiThuoc",
    []
  );
  const listAllDonViTinh = useStore("donViTinh.listAllDonViTinh", []);
  const listAllXuatXu = useStore("xuatXu.listAllXuatXu", []);
  const listAllNhaSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);
  const listAllDuongDung = useStore("duongDung.listAllDuongDung", []);

  const {
    danhMucThuoc: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData,
      onImportDMThuoc,
      guiDataSangVitimes,
    },
    hoatChat: { searchHoatChatTongHop },
    nhomDichVuKho: {
      getListMeGrLv1TongHop,
      getListMeGrLv2TongHop,
      getListMeGrLv3TongHop,
    },
    phanNhomDichVuKho: { getListAllNhomDichVuKho },
    phanLoaiThuoc: { getListAllPhanLoaiThuoc },
    donViTinh: { getListAllDonViTinh },
    xuatXu: { getListAllXuatXu },
    doiTac: { getListAllNhaSanXuat, getListAllNhaCungCap },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    nhomDichVuCap2: { getAllTongHopDichVuCap2, searchTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3, searchTongHopDichVuCap3 },
    duongDung: { getListAllDuongDung },
    dichVuKho: { onExportDmThuoc },
    lieuDungThuoc: { onExportLieuDung, onImportLieuDung },
    nhomChiPhi: { onExportNhomChiPhi, onImport: onImportNhomChiPhi },
    mucDichSuDung: { onExportMucDichSuDung, onImportMucDichSuDung },
  } = useDispatch();

  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);

  const [listNhomChiPhiBh] = useEnum(ENUM.NHOM_CHI_PHI_BH);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listGoiThau] = useEnum(ENUM.GOI_THAU);
  const [listNhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listDsMucDichSuDung] = useEnum(ENUM.MUC_DICH_SU_DUNG);
  const [listLoaiLamTron] = useEnum(ENUM.LOAI_LAM_TRON);
  const [listKyHieu] = useEnum(ENUM.KY_HIEU);
  const [dataMIMS_TYPE] = useEnum(ENUM.MIMS_TYPE);

  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);
  const [listAllPhuongPhapCheBien] = useListAll("phuongPhapCheBien", {}, true);
  const refModalImportDMThuoc = useRef(null);
  const refModalImportLieuDung = useRef(null);
  const refModalImportNhomChiPhi = useRef(null);
  const refModalImportMucDichSuDung = useRef(null);
  const refModalExportThuocMoi = useRef(null);
  const refSettings = useRef(null);

  const [getLoaiLamTron] = useLazyKVMap(listLoaiLamTron);
  const [getMucDichSuDung] = useLazyKVMap(listDsMucDichSuDung);
  const [getGoiThau] = useLazyKVMap(listGoiThau);
  const [getNhomThau] = useLazyKVMap(listNhomThau);
  const [getKhoa] = useLazyKVMap(listAllKhoa);
  const [getNguonKhacChiTra] = useLazyKVMap(listAllNguonKhacChiTra);
  const [getNhomChiPhiBh] = useLazyKVMap(listNhomChiPhiBh);

  const { showLoading, hideLoading } = useLoading();
  const listMeGrLv1 = useStore("nhomDichVuKho.listMeGrLv1", []);
  const listMeGrLv2 = useStore("nhomDichVuKho.listMeGrLv2", []);
  const listMeGrLv3 = useStore("nhomDichVuKho.listMeGrLv3", []);
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );
  const listNhomDvCap2 = useStore("nhomDichVuCap2.listGroupService2", []);
  const listNhomDvCap3 = useStore("nhomDichVuCap3.listGroupService3", []);
  const defaultParams = { page: "", size: "", active: true };

  const listNguonKhacChiTra = useMemo(() => {
    return orderBy(listAllNguonKhacChiTra || [], (a) => a.ma, "asc").map(
      (o) => ({
        ...o,
        ten: `${o.ma} - ${o.ten}`,
      })
    );
  }, [listAllNguonKhacChiTra]);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useEffect(() => {
    onSizeChange({ size: 10 });
    searchAllServices();
  }, []);

  const searchAllServices = () => {
    const params = { page: "", size: "", active: true, sort: "ten,asc" };
    searchHoatChatTongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListAllNhomDichVuKho({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListAllPhanLoaiThuoc({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListAllDonViTinh({
      page: "",
      size: "",
      loaiDichVu: ID_LOAI_DICH_VU,
    });
    getListAllXuatXu(params);
    getListAllNhaSanXuat({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: [10],
      loaiDichVu: ID_LOAI_DICH_VU,
    });
    getListAllNhaCungCap({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: [20],
      loaiDichVu: ID_LOAI_DICH_VU,
    });
    // xài cho form
    getAllTongHopDichVuCap1(defaultParams);
    getAllTongHopDichVuCap2(defaultParams);
    getAllTongHopDichVuCap3(defaultParams);
    // xài cho danh sách để không bị ảnh hưởng bởi form
    searchTongHopDichVuCap2(defaultParams);
    searchTongHopDichVuCap3(defaultParams);
    getListAllDuongDung(params);
    getListMeGrLv1TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListMeGrLv2TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListMeGrLv3TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
  };

  const onClickSort = useCallback(
    (key, value) => {
      onSortChange({
        [key]: value,
      });
    },
    [onSortChange]
  );

  const refTimeOut = useRef(null);
  const onSearchInput = useCallback(
    (key) => (e) => {
      if (key === "nhomDvKhoCap1Id") {
        const params = {
          page: "",
          size: "",
          active: true,
          sort: "ten,asc",
          loaiDichVu: ID_LOAI_DICH_VU,
        };
        if (e) {
          getListMeGrLv2TongHop({
            ...params,
            nhomDvKhoCap1Id: e,
          });
        } else {
          getListMeGrLv2TongHop({ ...params });
        }
      }
      if (key === "nhomDvKhoCap2Id") {
        const params = {
          page: "",
          size: "",
          active: true,
          sort: "ten,asc",
          loaiDichVu: ID_LOAI_DICH_VU,
        };
        if (e) {
          getListMeGrLv3TongHop({
            ...params,
            nhomDvKhoCap2Id: e,
          });
        } else {
          getListMeGrLv3TongHop({ ...params });
        }
      }
      if (key === "dichVu.nhomDichVuCap1Id") {
        searchTongHopDichVuCap2({
          ...defaultParams,
          ...(e && { nhomDichVuCap1Id: e }),
        });
      }
      if (key === "dichVu.nhomDichVuCap2Id") {
        searchTongHopDichVuCap3({
          ...defaultParams,
          ...(e && { nhomDichVuCap2Id: e }),
        });
      }
      if (refTimeOut.current) {
        clearTimeout(refTimeOut.current);
        refTimeOut.current = null;
      }

      refTimeOut.current = setTimeout(
        (key, s) => {
          let value = "";
          if (s) {
            if (s?.hasOwnProperty("checked")) value = s?.checked;
            else value = s?.value;
          } else value = e;
          onChangeInputSearch({
            [key]: value,
          });
        },
        500,
        key,
        e?.target
      );
    },
    [onChangeInputSearch]
  );

  const onDayThuocSangVitimecs = (dichVuId) => {
    showLoading();
    let params = {
      loaiDichVu: ID_LOAI_DICH_VU,
      ...(isNumber(dichVuId) && { dichVuId }),
    };
    guiDataSangVitimes(params).finally(() => {
      hideLoading();
    });
  };

  const columns = useMemo(() => {
    return [
      {
        title: (
          <HeaderSearch
            title={
              <>
                {t("common.stt")}
                <Setting refTable={refSettings} />
              </>
            }
          />
        ),
        width: 60,
        dataIndex: "index",
        key: "index",
        fixed: "left",
        align: "center",
        ignore: true,
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.maThuoc")}
            sort_key="dichVu.ma"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.ma"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timMaThuoc")}
                onChange={onSearchInput("dichVu.ma")}
              />
            }
          />
        ),
        width: 150,
        dataIndex: "dichVu",
        key: "dichVu.ma",
        fixed: "left",
        i18Name: "danhMuc.maThuoc",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item.ma}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.tenThuoc")}
            sort_key="dichVu.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.ten"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timTenThuoc")}
                onChange={onSearchInput("dichVu.ten")}
              />
            }
          />
        ),
        width: 180,
        dataIndex: "dichVu",
        key: "dichVu.ten",
        fixed: "left",
        i18Name: "danhMuc.tenThuoc",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item.ten}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.maHoatChat")}
            sort_key="hoatChat.ma"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["hoatChat.ma"] || 0}
            searchSelect={
              <Select
                data={listHoatChat.map((item) => ({
                  id: item.id,
                  ten: item.ma,
                }))}
                placeholder={t("danhMuc.chonMaHoatChat")}
                onChange={onSearchInput("hoatChatId")}
              />
            }
          />
        ),
        width: 130,
        dataIndex: "hoatChat",
        key: "hoatChat.ma",
        i18Name: "danhMuc.maHoatChat",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item.ma}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="tenHoatChat"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.tenHoatChat || 0}
            search={
              <Input
                style={{ width: "100%" }}
                placeholder={t("danhMuc.timTenHoatChat")}
                onChange={onSearchInput("tenHoatChat")}
              />
            }
            title={t("danhMuc.tenHoatChat")}
          />
        ),
        width: 170,
        dataIndex: "tenHoatChat",
        key: "tenHoatChat",
        i18Name: "danhMuc.tenHoatChat",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="hamLuong"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.hamLuong || 0}
            search={
              <Input
                placeholder={t("danhMuc.timHamLuong")}
                onChange={onSearchInput("hamLuong")}
              />
            }
            title={t("danhMuc.hamLuong")}
          />
        ),
        width: 150,
        dataIndex: "hamLuong",
        key: "hamLuong",
        i18Name: "danhMuc.hamLuong",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="nhomDvKhoCap1.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["nhomDvKhoCap1.ten"] || 0}
            searchSelect={
              <Select
                data={listMeGrLv1}
                placeholder={t("danhMuc.chonNhomThuocCap1")}
                onChange={onSearchInput("nhomDvKhoCap1Id")}
              />
            }
            title={t("danhMuc.nhomThuocCap1")}
          />
        ),
        width: 200,
        dataIndex: "nhomDvKhoCap1",
        key: "nhomDvKhoCap1.ten",
        i18Name: "danhMuc.nhomThuocCap1",
        show: true,
        render: (item) => {
          return item && item.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="nhomDvKhoCap2.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["nhomDvKhoCap2.ten"] || 0}
            searchSelect={
              <Select
                data={listMeGrLv2}
                placeholder={t("danhMuc.chonNhomThuocCap2")}
                onChange={onSearchInput("nhomDvKhoCap2Id")}
              />
            }
            title={t("danhMuc.nhomThuocCap2")}
          />
        ),
        width: 200,
        dataIndex: "nhomDvKhoCap2",
        key: "nhomDvKhoCap2.ten",
        i18Name: "danhMuc.nhomThuocCap2",
        show: true,
        render: (item) => {
          return item && item.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.nhomThuocCap3")}
            sort_key="nhomDvKhoCap3.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["nhomDvKhoCap3.ten"] || 0}
            searchSelect={
              <Select
                data={listMeGrLv3}
                placeholder={`${t("danhMuc.chonTitle", {
                  title: t("danhMuc.nhomThuocCap3").toLowerCase(),
                })}`}
                onChange={onSearchInput("nhomDvKhoCap3Id")}
              />
            }
          />
        ),
        width: "200px",
        dataIndex: "nhomDvKhoCap3",
        key: "nhomDvKhoCap3Id",
        i18Name: "danhMuc.nhomThuocCap3",
        show: true,
        render: (item) => item?.ten,
      },
      {
        title: (
          <HeaderSearch
            sort_key="phanNhomDvKho.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["phanNhomDvKho.ten"] || 0}
            searchSelect={
              <Select
                data={listAllNhomDichVuKho}
                placeholder={t("danhMuc.chonPhanNhomThuoc")}
                onChange={onSearchInput("phanNhomDvKhoId")}
              />
            }
            title={t("danhMuc.phanNhomThuoc")}
          />
        ),
        width: 150,
        dataIndex: "phanNhomDvKho",
        key: "phanNhomDvKho",
        i18Name: "danhMuc.phanNhomThuoc",
        show: true,
        render: (item, _, __) => item && item?.ten,
      },
      {
        title: (
          <HeaderSearch
            sort_key="phanLoaiDvKho.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["phanLoaiDvKho.ten"] || 0}
            searchSelect={
              <Select
                data={listAllPhanLoaiThuoc}
                placeholder={t("danhMuc.chonPhanLoaiThuoc")}
                onChange={onSearchInput("dsPhanLoaiDvKhoId")}
                mode="multiple"
              />
            }
            title={t("danhMuc.phanLoaiThuoc")}
          />
        ),
        width: 150,
        dataIndex: "phanLoaiDvKho",
        key: "phanLoaiDvKho",
        i18Name: "danhMuc.phanLoaiThuoc",
        show: true,
        render: (item, _, __) => item && item?.ten,
      },
      {
        title: (
          <HeaderSearch
            sort_key="dvtSoCap.ten"
            defaultValue=""
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dvtSoCap.ten"] || 0}
            searchSelect={
              <Select
                data={listAllDonViTinh}
                placeholder={t("danhMuc.chonDonViSoCap")}
                onChange={onSearchInput("dvtSoCapId")}
              />
            }
            title={t("danhMuc.donViSoCap")}
          />
        ),
        width: 150,
        dataIndex: "dvtSoCap",
        key: "dvtSoCap",
        i18Name: "danhMuc.donViSoCap",
        show: true,
        render: (item) => {
          return item?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.donViTinh.ten"
            defaultValue=""
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.donViTinh.ten"] || 0}
            searchSelect={
              <Select
                data={listAllDonViTinh}
                placeholder={t("danhMuc.chonDonViThuCap")}
                onChange={onSearchInput("dichVu.donViTinhId")}
              />
            }
            title={t("danhMuc.donViThuCap")}
          />
        ),
        width: 150,
        dataIndex: "dichVu",
        key: "dvtThuCap",
        i18Name: "danhMuc.donViThuCap",
        show: true,
        render: (item) => {
          return item?.donViTinh?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dvtSuDung.ten"
            defaultValue=""
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dvtSuDung.ten"] || 0}
            searchSelect={
              <Select
                data={listAllDonViTinh}
                placeholder={t("danhMuc.chonDonViSuDung")}
                onChange={onSearchInput("dvtSuDungId")}
              />
            }
            title={t("danhMuc.donViSuDung")}
          />
        ),
        width: 150,
        dataIndex: "dvtSuDung",
        key: "dvtSuDung",
        i18Name: "danhMuc.donViSuDung",
        show: true,
        render: (item) => {
          return item?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="heSoDinhMuc"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.heSoDinhMuc || 0}
            search={
              <Input
                placeholder={t("danhMuc.timHeSoDinhMuc")}
                onChange={onSearchInput("heSoDinhMuc")}
              />
            }
            title={t("danhMuc.heSoDinhMuc")}
          />
        ),
        width: 150,
        dataIndex: "heSoDinhMuc",
        key: "heSoDinhMuc",
        align: "right",
        i18Name: "danhMuc.heSoDinhMuc",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="quyCach"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.quyCach || 0}
            search={
              <Input
                placeholder={t("danhMuc.timQuyCach")}
                onChange={onSearchInput("quyCach")}
              />
            }
            title={t("danhMuc.quyCach")}
          />
        ),
        width: 130,
        dataIndex: "quyCach",
        key: "quyCach",
        i18Name: "danhMuc.quyCach",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="xuatXu.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["xuatXu.ten"] || 0}
            searchSelect={
              <Select
                // data={listQuocGia}
                data={listAllXuatXu}
                placeholder={t("danhMuc.chonNuocSanXuat")}
                onChange={onSearchInput("xuatXuId")}
              />
            }
            title={t("danhMuc.nuocSanXuat")}
          />
        ),
        width: 130,
        dataIndex: "xuatXu",
        key: "xuatXu",
        i18Name: "danhMuc.nuocSanXuat",
        show: true,
        render: (item) => {
          return item && item.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="nhaSanXuat.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["nhaSanXuat.ten"] || 0}
            searchSelect={
              <Select
                data={listAllNhaSanXuat}
                placeholder={t("danhMuc.chonNhaSanXuat")}
                onChange={onSearchInput("nhaSanXuatId")}
              />
            }
            title={t("common.nhaSanXuat")}
          />
        ),
        width: 130,
        dataIndex: "nhaSanXuat",
        key: "nhaSanXuat",
        i18Name: "common.nhaSanXuat",
        show: true,
        render: (item) => {
          return item && item.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="nhaCungCap.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["nhaCungCap.ten"] || 0}
            searchSelect={
              <Select
                data={listAllNhaCungCap}
                placeholder={t("danhMuc.chonNhaCungCap")}
                onChange={onSearchInput("nhaCungCapId")}
              />
            }
            title={t("danhMuc.nhaCungCap")}
          />
        ),
        width: 130,
        dataIndex: "nhaCungCap",
        key: "nhaCungCap",
        i18Name: "danhMuc.nhaCungCap",
        show: true,
        render: (item) => {
          return item && item.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="giaNhapSauVat"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.giaNhapSauVat || 0}
            search={
              <Input
                placeholder={t("danhMuc.timGiaNhap")}
                onChange={onSearchInput("giaNhapSauVat")}
              />
            }
            title={t("danhMuc.giaNhap")}
          />
        ),
        width: 130,
        dataIndex: "giaNhapSauVat",
        key: "giaNhapSauVat",
        align: "right",
        i18Name: "danhMuc.giaNhap",
        show: true,
        render: (field, _, __) => (field && formatNumber(field)) || "",
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.tyLeBhTt"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.tyLeBhTt"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timTyLeBhtt")}
                onChange={onSearchInput("dichVu.tyLeBhTt")}
              />
            }
            title={t("danhMuc.tyLeBhThanhToan")}
          />
        ),
        width: 170,
        dataIndex: "dichVu",
        key: "dichVu.tyLeBhTt",
        align: "right",
        i18Name: "danhMuc.tyLeBhThanhToan",
        show: true,
        render: (item) => {
          return item?.tyLeBhTt;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.nhomChiPhiBh"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.nhomChiPhiBh"] || 0}
            searchSelect={
              <Select
                data={listNhomChiPhiBh}
                placeholder={t("danhMuc.chonNhomChiPhi")}
                onChange={onSearchInput("dichVu.nhomChiPhiBh")}
              />
            }
            title={t("danhMuc.nhomChiPhi")}
          />
        ),
        width: 150,
        dataIndex: "dichVu",
        key: "dichVu.nhomChiPhiBh",
        i18Name: "danhMuc.nhomChiPhi",
        show: true,
        render: (item) => {
          return getNhomChiPhiBh(item.nhomChiPhiBh)?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.nhomDichVuCap1.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.nhomDichVuCap1.ten"] || 0}
            searchSelect={
              <Select
                data={listAllNhomDichVuCap1}
                placeholder={t("danhMuc.chonNhomDvCap1")}
                onChange={onSearchInput("dichVu.nhomDichVuCap1Id")}
              />
            }
            title={t("danhMuc.nhomDVCap1")}
          />
        ),
        width: 150,
        dataIndex: "dichVu",
        key: "dichVu.nhomDichVuCap1Id",
        i18Name: "danhMuc.nhomDVCap1",
        show: true,
        render: (item) => {
          return item && item?.nhomDichVuCap1?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.nhomDichVuCap2.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.nhomDichVuCap2.ten"] || 0}
            searchSelect={
              <Select
                data={listNhomDvCap2}
                placeholder={t("danhMuc.chonNhomDvCap2")}
                onChange={onSearchInput("dichVu.nhomDichVuCap2Id")}
              />
            }
            title={t("danhMuc.nhomDVCap2")}
          />
        ),
        width: 150,
        dataIndex: "dichVu",
        key: "dichVu.nhomDichVuCap2Id",
        i18Name: "danhMuc.nhomDVCap2",
        show: true,
        render: (item) => {
          return item && item?.nhomDichVuCap2?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.nhomDichVuCap3.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.nhomDichVuCap3.ten"] || 0}
            searchSelect={
              <Select
                data={listNhomDvCap3}
                placeholder={`${t("danhMuc.chonTitle", {
                  title: t("danhMuc.nhomDVCap3").toLowerCase(),
                })}`}
                onChange={onSearchInput("dichVu.nhomDichVuCap3Id")}
              />
            }
            title={t("danhMuc.nhomDVCap3")}
          />
        ),
        width: 150,
        dataIndex: "dichVu",
        key: "dichVu.nhomDichVuCap3Id",
        i18Name: "danhMuc.nhomDVCap3",
        show: true,
        render: (item) => {
          return item && item?.nhomDichVuCap3?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.maTuongDuong"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.maTuongDuong"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timMaTuongDuong")}
                onChange={onSearchInput("dichVu.maTuongDuong")}
              />
            }
            title={t("danhMuc.maTuongDuong")}
          />
        ),
        width: 160,
        dataIndex: "dichVu",
        key: "dichVu.maTuongDuong",
        i18Name: "danhMuc.maTuongDuong",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item.maTuongDuong}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.tenTuongDuong"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.tenTuongDuong"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timTenTuongDuong")}
                onChange={onSearchInput("dichVu.tenTuongDuong")}
              />
            }
            title={t("danhMuc.tenTuongDuong")}
          />
        ),
        width: 160,
        dataIndex: "dichVu",
        key: "dichVu.tenTuongDuong",
        i18Name: "danhMuc.tenTuongDuong",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item.tenTuongDuong}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="soVisa"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.soVisa || 0}
            search={
              <Input
                placeholder={t("danhMuc.timSoVisa")}
                onChange={onSearchInput("soVisa")}
              />
            }
            title={t("danhMuc.soVisa")}
          />
        ),
        width: 150,
        dataIndex: "soVisa",
        key: "soVisa",
        i18Name: "danhMuc.soVisa",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.maThuocThayThe")}
            sort_key="dsDichVuThayTheId"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dsDichVuThayTheId"] || 0}
            searchSelect={
              <SelectLoadMore
                placeholder={t("common.timKiem")}
                api={dmDichVuKhoProvider.searchAll}
                mapData={(i) => ({
                  value: i.id,
                  label: `${i.ma} - ${i.ten}`,
                  ten: `${i.ma} - ${i.ten}`,
                })}
                onChange={onSearchInput("dsDichVuThayTheId")}
                uniqByKey="value"
                addParam={{ dsLoaiDichVu: [LOAI_DICH_VU.THUOC] }}
                keySearch={"ma"}
                mode="multiple"
              />
            }
          />
        ),
        width: "150px",
        dataIndex: "dsDichVuThayTheId",
        key: "dsDichVuThayTheId",
        i18Name: "danhMuc.maThuocThayThe",
        show: true,
        render: (item, record) => {
          return record?.dsDichVuThayThe
            ?.map((el) => `${el.ma} - ${el.ten}`)
            .join(", ");
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="maLienThong"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.maLienThong || 0}
            search={
              <Input
                placeholder={t("danhMuc.timMaLienThongDuocQuocGia")}
                onChange={onSearchInput("maLienThong")}
              />
            }
            title={t("danhMuc.maLienThongDuocQuocGia")}
          />
        ),
        width: 200,
        dataIndex: "maLienThong",
        key: "maLienThong",
        i18Name: "danhMuc.maLienThongDuocQuocGia",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            searchSelect={
              <Select
                data={listNguonKhacChiTra}
                placeholder={t("danhMuc.timNguonChiTraKhac")}
                onChange={onSearchInput("dichVu.nguonKhacId")}
              />
            }
            title={t("danhMuc.nguonChiTraKhac")}
          />
        ),
        width: 170,
        dataIndex: "dichVu",
        key: "nguonKhacId",
        i18Name: "danhMuc.nguonChiTraKhac",
        show: true,
        render: (item) => getNguonKhacChiTra(item.nguonKhacId)?.ten,
      },
      {
        title: (
          <HeaderSearch
            searchSelect={
              <Select
                data={listAllKhoa}
                placeholder={t("danhMuc.timKhoaChiDinhTheoDvtSoCap")}
                mode="multiple"
                onChange={onSearchInput("dsKhoaCdDvtSoCapId")}
              />
            }
            title={t("danhMuc.khoaChiDinhTheoDvtSoCap")}
          />
        ),
        width: 180,
        dataIndex: "dsKhoaCdDvtSoCapId",
        key: "dsKhoaCdDvtSoCapId",
        i18Name: "danhMuc.khoaChiDinhTheoDvtSoCap",
        show: true,
        render: (item) => item?.map((id) => getKhoa(id)?.ten).join(", "),
      },
      {
        title: (
          <HeaderSearch
            sort_key="dungTich"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.dungTich || 0}
            search={
              <Input
                placeholder={t("danhMuc.timDungTich")}
                onChange={onSearchInput("dungTich")}
              />
            }
            title={t("danhMuc.dungTich")}
          />
        ),
        width: 200,
        dataIndex: "dungTich",
        key: "dungTich",
        align: "right",
        i18Name: "danhMuc.dungTich",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="duongDung.ten"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["duongDung.ten"] || 0}
            searchSelect={
              <Select
                data={listAllDuongDung}
                placeholder={t("danhMuc.timDuongDung")}
                onChange={onSearchInput("duongDung.ten")}
              />
            }
            title={t("danhMuc.duongDung")}
          />
        ),
        width: 170,
        dataIndex: "duongDung",
        key: "duongDung",
        i18Name: "danhMuc.duongDung",
        show: true,
        render: (item) => {
          return item?.ten;
        },
      },

      {
        title: (
          <HeaderSearch
            sort_key="dsPhuongPhapCheBienId"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dsPhuongPhapCheBienId"] || 0}
            searchSelect={
              <Select
                mode="multiple"
                data={listAllPhuongPhapCheBien}
                placeholder={t("danhMuc.chonPhuongPhapCheBien")}
                onChange={onSearchInput("dsPhuongPhapCheBienId")}
              />
            }
            title={t("danhMuc.phuongPhapCheBien")}
          />
        ),
        width: 160,
        dataIndex: "dsPhuongPhapCheBien",
        key: "dsPhuongPhapCheBienId",
        i18Name: "danhMuc.phuongPhapCheBien",
        show: true,
        render: (item) => {
          return item?.map((e) => e && e.ten?.length > 0 && e.ten).join(", ");
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dangBaoChe"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dangBaoChe"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timDangBaoChe")}
                onChange={onSearchInput("dangBaoChe")}
              />
            }
            title={t("danhMuc.dangBaoChe")}
          />
        ),
        width: 150,
        dataIndex: "dangBaoChe",
        key: "dangBaoChe",
        i18Name: "danhMuc.dangBaoChe",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.chiDinhSlLe"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.chiDinhSlLe"] || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonKeSlLe")}
                onChange={onSearchInput("dichVu.chiDinhSlLe")}
                hasAllOption={true}
              />
            }
            title={t("danhMuc.choPhepKeSlLe")}
          />
        ),
        width: 130,
        dataIndex: "dichVu",
        key: "dichVu.chiDinhSlLe",
        align: "center",
        i18Name: "danhMuc.choPhepKeSlLe",
        show: true,
        render: (item) => {
          return <Checkbox checked={item?.chiDinhSlLe} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="theoDoiNgaySd"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.theoDoiNgaySd || 0}
            searchSelect={
              <Select
                data={HIEU_LUC}
                placeholder={t("danhMuc.chonTheoDoiNgaySuDung")}
                onChange={onSearchInput("theoDoiNgaySd")}
                hasAllOption={true}
              />
            }
            title={t("danhMuc.theoDoiNgaySd")}
          />
        ),
        width: 130,
        dataIndex: "theoDoiNgaySd",
        key: "theoDoiNgaySd",
        align: "center",
        i18Name: "danhMuc.theoDoiNgaySd",
        show: true,
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.khongTinhTien"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.khongTinhTien"] || 0}
            searchSelect={
              <Select
                data={KHONG_TINH_TIEN}
                placeholder={t("danhMuc.chonTinhTien")}
                onChange={onSearchInput("dichVu.khongTinhTien")}
                hasAllOption={true}
              />
            }
            title={t("danhMuc.khongTinhTien")}
          />
        ),
        align: "center",
        width: 160,
        dataIndex: "dichVu",
        key: "dichVu.khongTinhTien",
        i18Name: "danhMuc.khongTinhTien",
        show: true,
        render: (item) => {
          return <Checkbox checked={!!item?.khongTinhTien} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="nhomThau"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["nhomThau"] || 0}
            searchSelect={
              <Select
                data={listNhomThau}
                placeholder={t("danhMuc.chonNhomThau")}
                onChange={onSearchInput("nhomThau")}
              />
            }
            title={t("danhMuc.nhomThau")}
          />
        ),
        align: "center",
        width: 160,
        dataIndex: "nhomThau",
        key: "nhomThau",
        i18Name: "danhMuc.nhomThau",
        show: true,
        render: (item) => {
          return getNhomThau(item)?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="goiThau"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["goiThau"] || 0}
            searchSelect={
              <Select
                data={listGoiThau}
                placeholder={t("danhMuc.chonGoiThau")}
                onChange={onSearchInput("goiThau")}
              />
            }
            title={t("danhMuc.goiThau")}
          />
        ),
        align: "center",
        width: 160,
        dataIndex: "goiThau",
        key: "goiThau",
        i18Name: "danhMuc.goiThau",
        show: true,
        render: (item) => {
          return getGoiThau(item)?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="thongTinThau"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["thongTinThau"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.nhapThongTinThau")}
                onChange={onSearchInput("thongTinThau")}
              />
            }
            title={t("danhMuc.thongTinThau")}
          />
        ),
        align: "center",
        width: 160,
        dataIndex: "thongTinThau",
        key: "thongTinThau",
        i18Name: "danhMuc.thongTinThau",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="quyetDinhThau"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["quyetDinhThau"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.nhapQuyetDinhThau")}
                onChange={onSearchInput("quyetDinhThau")}
              />
            }
            title={t("danhMuc.quyetDinhThau")}
          />
        ),
        align: "center",
        width: 160,
        dataIndex: "quyetDinhThau",
        key: "quyetDinhThau",
        i18Name: "danhMuc.quyetDinhThau",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="tenTrungThau"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["tenTrungThau"] || 0}
            search={
              <Input
                placeholder={t("kho.quyetDinhThau.nhapTenTrungThau")}
                onChange={onSearchInput("tenTrungThau")}
              />
            }
            title={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
          />
        ),
        align: "center",
        width: 160,
        dataIndex: "tenTrungThau",
        key: "tenTrungThau",
        i18Name: "kho.quyetDinhThau.tenHangHoaTrungThau",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="tenMoiThau"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["tenMoiThau"] || 0}
            search={
              <Input
                placeholder={`${t("danhMuc.nhapTitle", {
                  title: t("danhMuc.tenMoiThau").toLowerCase(),
                })}`}
                onChange={onSearchInput("tenMoiThau")}
              />
            }
            title={t("danhMuc.tenMoiThau")}
          />
        ),
        align: "left",
        width: 160,
        dataIndex: "tenMoiThau",
        key: "tenMoiThau",
        i18Name: "danhMuc.tenMoiThau",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.mucDichSuDung")}
            sort_key="dsMucDichSuDung"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dsMucDichSuDung"] || 0}
            searchSelect={
              <Select
                data={listDsMucDichSuDung}
                placeholder={t("danhMuc.chonMucDichSuDung")}
                onChange={onSearchInput("dsMucDichSuDung")}
                hasAllOption={true}
                mode="multiple"
              />
            }
          />
        ),
        width: "150px",
        dataIndex: "dsMucDichSuDung",
        key: "dsMucDichSuDung",
        i18Name: "danhMuc.mucDichSuDung",
        show: true,
        render: (item) => {
          if (!item?.length || !listDsMucDichSuDung) return "";

          return item
            .map((mucDichSuDungItem) => {
              const tenMucDichSuDung = getMucDichSuDung(mucDichSuDungItem);
              return tenMucDichSuDung?.ten;
            })
            .filter(Boolean)
            .join(", ");
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.maPhieuLinh")}
            sort_key="phieuLinhId"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["phieuLinhId"] || 0}
            searchSelect={
              <Select
                data={listAllMaPhieuLinh}
                placeholder={t("danhMuc.maPhieuLinh")}
                onChange={onSearchInput("phieuLinhId")}
              />
            }
          />
        ),
        width: "150px",
        dataIndex: "phieuLinh",
        key: "phieuLinh",
        i18Name: "danhMuc.maPhieuLinh",
        show: true,
        render: (item) => {
          return item?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="thuocDauSao"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.thuocDauSao || 0}
            searchSelect={
              <Select
                data={HIEU_LUC}
                onChange={onSearchInput("thuocDauSao")}
                hasAllOption={true}
                placeholder={t("common.timKiem")}
              />
            }
            title={t("danhMuc.thuocDauSao")}
          />
        ),
        width: 130,
        dataIndex: "thuocDauSao",
        key: "thuocDauSao",
        align: "center",
        i18Name: "danhMuc.thuocDauSao",
        show: true,
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="kyHieu"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.kyHieu || 0}
            searchSelect={
              <Select
                data={listKyHieu}
                onChange={onSearchInput("kyHieu")}
                hasAllOption={true}
                placeholder={t("common.timKiem")}
              />
            }
            title={t("danhMuc.corticoid")}
          />
        ),
        width: 120,
        dataIndex: "kyHieu",
        key: "kyHieu",
        align: "center",
        i18Name: "danhMuc.corticoid",
        show: true,
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="mucDichSuDung"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.mucDichSuDung || 0}
            searchSelect={
              <Select
                data={YES_NO}
                onChange={onSearchInput("mucDichSuDung")}
                hasAllOption={true}
                placeholder={t("common.timKiem")}
              />
            }
            title={t("danhMuc.apDungTt20")}
          />
        ),
        width: 130,
        dataIndex: "mucDichSuDung",
        key: "mucDichSuDung",
        align: "center",
        i18Name: "danhMuc.apDungTt20",
        show: true,
        render: (item, data) => {
          return <Checkbox checked={data?.mucDichSuDung} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="trongPtTt"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.trongPtTt || 0}
            searchSelect={
              <Select
                data={HIEU_LUC}
                onChange={onSearchInput("trongPtTt")}
                hasAllOption={true}
                placeholder={t("common.timKiem")}
              />
            }
            title={t("danhMuc.thuocPhauThuat")}
          />
        ),
        width: 130,
        dataIndex: "trongPtTt",
        key: "trongPtTt",
        align: "center",
        i18Name: "danhMuc.thuocPhauThuat",
        show: true,
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="trungTenThuongMai"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.trungTenThuongMai || 0}
            searchSelect={
              <Select
                placeholder={concatString(
                  t("common.chon"),
                  t("danhMuc.trungTenThuongMai").toLowerCase()
                )}
                data={YES_NO}
                onChange={onSearchInput("trungTenThuongMai")}
                hasAllOption={true}
              />
            }
            title={t("danhMuc.trungTenThuongMai")}
          />
        ),
        width: 150,
        dataIndex: "trungTenThuongMai",
        key: "trungTenThuongMai",
        align: "center",
        i18Name: "danhMuc.trungTenThuongMai",
        show: true,
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="maAtc"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.maAtc || 0}
            search={
              <Input
                placeholder={t("danhMuc.maAtc")}
                onChange={onSearchInput("maAtc")}
              />
            }
            title={t("danhMuc.maAtc")}
          />
        ),
        width: 130,
        dataIndex: "maAtc",
        key: "maAtc",
        i18Name: "danhMuc.maAtc",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="hamLuongDdd"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.hamLuongDdd || 0}
            search={
              <Input
                placeholder={t("danhMuc.hamLuongDdd")}
                onChange={onSearchInput("hamLuongDdd")}
              />
            }
            title={t("danhMuc.hamLuongDdd")}
          />
        ),
        width: 130,
        dataIndex: "hamLuongDdd",
        key: "hamLuongDdd",
        i18Name: "danhMuc.hamLuongDdd",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="whoDdd"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.whoDdd || 0}
            search={
              <Input
                placeholder={t("danhMuc.whoDdd")}
                onChange={onSearchInput("whoDdd")}
              />
            }
            title={t("danhMuc.whoDdd")}
          />
        ),
        width: 130,
        dataIndex: "whoDdd",
        key: "whoDdd",
        i18Name: "danhMuc.whoDdd",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="donViDdd"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.donViDdd || 0}
            search={
              <Input
                placeholder={t("danhMuc.donViDddWho")}
                onChange={onSearchInput("donViDdd")}
              />
            }
            title={t("danhMuc.donViDddWho")}
          />
        ),
        width: 130,
        dataIndex: "donViDdd",
        key: "donViDdd",
        i18Name: "danhMuc.donViDddWho",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="loaiLamTron"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.loaiLamTron || 0}
            title={t("danhMuc.loaiLamTron")}
            searchSelect={
              <Select
                placeholder={t("danhMuc.chonLoaiLamTron")}
                data={listLoaiLamTron}
                onChange={onSearchInput("loaiLamTron")}
              />
            }
          />
        ),
        width: 130,
        dataIndex: "loaiLamTron",
        key: "loaiLamTron",
        i18Name: "danhMuc.loaiLamTron",
        show: true,
        render: (item) => {
          return getLoaiLamTron(item)?.ten;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="soNgayCanhBaoHsd"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.soNgayCanhBaoHsd || 0}
            search={
              <Input
                placeholder={t("danhMuc.soNgayCanhBaoHsd")}
                onChange={onSearchInput("soNgayCanhBaoHsd")}
              />
            }
            title={t("danhMuc.soNgayCanhBaoHsd")}
          />
        ),
        width: 170,
        dataIndex: "soNgayCanhBaoHsd",
        key: "soNgayCanhBaoHsd",
        align: "center",
        i18Name: "danhMuc.soNgayCanhBaoHsd",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="soGioCanhBaoHuy"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.soGioCanhBaoHuy || 0}
            search={
              <Input
                placeholder={t("danhMuc.soGioCanhBaoThuocHuy")}
                onChange={onSearchInput("soGioCanhBaoHuy")}
              />
            }
            title={t("danhMuc.soGioCanhBaoThuocHuy")}
          />
        ),
        width: 180,
        dataIndex: "soGioCanhBaoHuy",
        key: "soGioCanhBaoHuy",
        align: "center",
        i18Name: "danhMuc.soGioCanhBaoThuocHuy",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="soLuongToiThieu"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.soLuongToiThieu || 0}
            search={
              <InputNumber
                min={0}
                placeholder={t("danhMuc.soLuongTonToiThieu")}
                onChange={onSearchInput("soLuongToiThieu")}
                type="number"
              />
            }
            title={t("danhMuc.soLuongTonToiThieu")}
          />
        ),
        width: 150,
        dataIndex: "soLuongToiThieu",
        key: "soLuongToiThieu",
        align: "center",
        i18Name: "danhMuc.soLuongTonToiThieu",
        show: true,
      },
      {
        title: (
          <HeaderSearch
            sort_key="maSinhPham"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.maSinhPham || 0}
            search={
              <Input
                placeholder={t("danhMuc.maSinhPham")}
                onChange={onSearchInput("maSinhPham")}
              />
            }
            title={t("danhMuc.maSinhPham")}
          />
        ),
        width: 120,
        dataIndex: "maSinhPham",
        key: "maSinhPham",
        i18Name: "danhMuc.maSinhPham",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="maSinhHieu"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.maSinhHieu || 0}
            search={
              <Input
                placeholder={t("danhMuc.maHieuSinhPham")}
                onChange={onSearchInput("maSinhHieu")}
              />
            }
            title={t("danhMuc.maHieuSinhPham")}
          />
        ),
        width: 150,
        dataIndex: "maSinhHieu",
        key: "maSinhHieu",
        i18Name: "danhMuc.maHieuSinhPham",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="maVitimes"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.maVitimes || 0}
            search={
              <Input
                placeholder={t("danhMuc.maGuiVitimes")}
                onChange={onSearchInput("maVitimes")}
              />
            }
            title={t("danhMuc.maGuiVitimes")}
          />
        ),
        width: 150,
        dataIndex: "maVitimes",
        key: "maVitimes",
        i18Name: "danhMuc.maGuiVitimes",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="tuoiTho"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.tuoiTho || 0}
            search={
              <Input
                placeholder={t("danhMuc.tuoiThoCuaThuoc")}
                onChange={onSearchInput("tuoiTho")}
              />
            }
            title={t("danhMuc.tuoiThoCuaThuoc")}
          />
        ),
        width: 150,
        dataIndex: "tuoiTho",
        key: "tuoiTho",
        i18Name: "danhMuc.tuoiThoCuaThuoc",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="canhBao"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.canhBao || 0}
            search={
              <Input
                placeholder={t("danhMuc.canhBaoSuDungThuoc")}
                onChange={onSearchInput("canhBao")}
              />
            }
            title={t("danhMuc.canhBaoSuDungThuoc")}
          />
        ),
        width: 180,
        dataIndex: "canhBao",
        key: "canhBao",
        i18Name: "danhMuc.canhBaoSuDungThuoc",
        show: true,
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: <HeaderSearch title={t("danhMuc.soLuongGioiHan1Ngay")} />,
        width: 120,
        dataIndex: "soLuongGioiHan1Ngay",
        key: "soLuongGioiHan1Ngay",
        i18Name: "danhMuc.soLuongGioiHan1Ngay",
        show: true,
        align: "center",
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.maTckt"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.maTckt"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timMaTckt")}
                onChange={onSearchInput("dichVu.maTckt")}
              />
            }
            title={t("danhMuc.maTckt")}
          />
        ),
        width: 120,
        dataIndex: "dichVu",
        key: "dichVu.maTckt",
        i18Name: "danhMuc.maTckt",
        show: true,
        align: "center",
        render: (item) => {
          return item && <div className="break-word">{item.maTckt}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="dichVu.tenTckt"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.tenTckt"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.timTenTckt")}
                onChange={onSearchInput("dichVu.tenTckt")}
              />
            }
            title={t("danhMuc.tenTckt")}
          />
        ),
        width: 120,
        dataIndex: "dichVu",
        key: "dichVu.tenTckt",
        i18Name: "danhMuc.tenTckt",
        show: true,
        align: "center",
        render: (item) => {
          return item && <div className="break-word">{item.tenTckt}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="soLuongGioiHan1LanKham"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["soLuongGioiHan1LanKham"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.nhapSLGioiHanLuotKcbNgoaiTruBhyt")}
                onChange={onSearchInput("soLuongGioiHan1LanKham")}
              />
            }
            title={t("danhMuc.slGioiHanLuotKcbNgoaiTruBhyt")}
          />
        ),
        width: 120,
        dataIndex: "soLuongGioiHan1LanKham",
        key: "soLuongGioiHan1LanKham",
        i18Name: "danhMuc.slGioiHanLuotKcbNgoaiTruBhyt",
        show: true,
        align: "center",
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="tuChua"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["tuChua"] || 0}
            search={
              <Input
                placeholder={t("danhMuc.nhapTuChua")}
                onChange={onSearchInput("tuChua")}
              />
            }
            title={t("danhMuc.tuChua")}
          />
        ),
        width: 120,
        dataIndex: "tuChua",
        key: "tuChua",
        i18Name: "danhMuc.tuChua",
        show: true,
        align: "center",
        render: (item) => {
          return item && <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.kiemTraHangNgay")}
            sort_key="kiemTraHangNgay"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["kiemTraHangNgay"] || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: t("danhMuc.kiemTraHangNgay").toLowerCase(),
                })}
                onChange={onSearchInput("kiemTraHangNgay")}
                defaultValue=""
                hasAllOption={true}
              />
            }
          />
        ),
        width: 120,
        dataIndex: "kiemTraHangNgay",
        key: "kiemTraHangNgay",
        i18Name: "danhMuc.kiemTraHangNgay",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.huongDanSuDung")}
            searchSelect={
              <Select
                defaultValue=""
                data={HDSD}
                placeholder={t("danhMuc.chonHieuLuc")}
                onChange={onSearchInput("tonTaiTaiLieuHdsd")}
                hasAllOption={true}
              />
            }
          />
        ),
        width: "120px",
        dataIndex: "dsTaiLieuHdsd",
        key: "dsTaiLieuHdsd",
        i18Name: "danhMuc.huongDanSuDung",
        show: true,
        align: "center",
        render: (item) => <Checkbox checked={!!item} />,
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.guiVitimes")}
            sort_key="dichVu.guiVitimes"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.guiVitimes"] || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: t("danhMuc.guiVitimes").toLowerCase(),
                })}
                onChange={onSearchInput("dichVu.guiVitimes")}
                defaultValue=""
                hasAllOption={true}
              />
            }
          />
        ),
        width: 120,
        dataIndex: "dichVu",
        key: "dichVu.guiVitimes",
        i18Name: "danhMuc.guiVitimes",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item?.guiVitimes} />;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.guiISC")}
            sort_key="dichVu.online"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["dichVu.online"] || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: lowerFirst(t("danhMuc.guiISC")),
                })}
                onChange={onSearchInput("dichVu.online")}
                defaultValue=""
                hasAllOption={true}
              />
            }
          />
        ),
        width: 120,
        dataIndex: "dichVu",
        key: "dichVu.online",
        i18Name: "danhMuc.guiISC",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item?.online} />;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.yeuCauTheoDoi")}
            sort_key="lieuDung"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["lieuDung"] || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: lowerFirst(t("danhMuc.yeuCauTheoDoi")),
                })}
                onChange={onSearchInput("lieuDung")}
                defaultValue=""
                hasAllOption={true}
              />
            }
          />
        ),
        width: 150,
        dataIndex: "lieuDung",
        key: "lieuDung",
        i18Name: "danhMuc.yeuCauTheoDoi",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.yeuCauPhaChe")}
            sort_key="phaChe"
            onClickSort={onClickSort}
            dataSort={dataSortColumn["phaChe"] || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: lowerFirst(t("danhMuc.yeuCauPhaChe")),
                })}
                onChange={onSearchInput("phaChe")}
                defaultValue=""
                hasAllOption={true}
              />
            }
          />
        ),
        width: 150,
        dataIndex: "phaChe",
        key: "phaChe",
        i18Name: "danhMuc.yeuCauPhaChe",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="lasa"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.lasa || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: lowerFirst(t("danhMuc.thuocLasa")),
                })}
                onChange={onSearchInput("lasa")}
                defaultValue=""
                hasAllOption={true}
              />
            }
            title={t("danhMuc.thuocLasa")}
          />
        ),
        width: 130,
        dataIndex: "lasa",
        i18Name: "danhMuc.thuocLasa",
        key: "lasa",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="nguyCoCao"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.nguyCoCao || 0}
            searchSelect={
              <Select
                data={YES_NO}
                placeholder={t("danhMuc.chonTitle", {
                  title: lowerFirst(t("danhMuc.thuocNguyCoCao")),
                })}
                onChange={onSearchInput("nguyCoCao")}
                defaultValue=""
                hasAllOption={true}
              />
            }
            title={t("danhMuc.thuocNguyCoCao")}
          />
        ),
        width: 130,
        dataIndex: "nguyCoCao",
        key: "nguyCoCao",
        i18Name: "danhMuc.thuocNguyCoCao",
        show: true,
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="mimsGuid"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.mimsGuid || 0}
            search={
              <Input
                placeholder={t("danhMuc.timMimsGuid")}
                onChange={onSearchInput("mimsGuid")}
              />
            }
            title={t("danhMuc.mimsGuid")}
          />
        ),
        width: 130,
        dataIndex: "mimsGuid",
        key: "mimsGuid",
        i18Name: "danhMuc.mimsGuid",
        align: "center",
        show: true,
        render: (item) => {
          return <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="mimsType"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.mimsType || 0}
            searchSelect={
              <Select
                data={dataMIMS_TYPE}
                placeholder={t("danhMuc.timMimsType")}
                onChange={onSearchInput("mimsType")}
              />
            }
            title={t("danhMuc.mimsType")}
          />
        ),
        width: 130,
        dataIndex: "mimsType",
        key: "mimsType",
        i18Name: "danhMuc.mimsType",
        align: "center",
        show: true,
        render: (item) => {
          return (
            <div className="break-word">
              {dataMIMS_TYPE.find((i) => i.id === item)?.ten}
            </div>
          );
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="maByt"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.maByt || 0}
            search={
              <Input
                placeholder={t("danhMuc.timMaByt")}
                onChange={onSearchInput("maByt")}
              />
            }
            title={t("danhMuc.maThuocByt")}
          />
        ),
        width: 130,
        dataIndex: "maByt",
        key: "maByt",
        i18Name: "danhMuc.maThuocByt",
        align: "center",
        show: true,
        render: (item) => {
          return <div className="break-word">{item}</div>;
        },
      },
      {
        title: (
          <HeaderSearch
            sort_key="active"
            onClickSort={onClickSort}
            dataSort={dataSortColumn.active || 0}
            searchSelect={
              <Select
                data={HIEU_LUC}
                placeholder={t("danhMuc.chonHieuLuc")}
                onChange={onSearchInput("active")}
                hasAllOption={true}
              />
            }
            title={t("danhMuc.coHieuLuc")}
          />
        ),
        width: 130,
        dataIndex: "active",
        key: "active",
        align: "center",
        ignore: true,
        render: (item) => {
          return <Checkbox checked={item} />;
        },
      },
    ];
  }, [
    t,
    dataSortColumn,
    onClickSort,
    onSearchInput,
    listHoatChat,
    listMeGrLv1,
    listMeGrLv2,
    listAllNhomDichVuKho,
    listAllPhanLoaiThuoc,
    listAllDonViTinh,
    listAllXuatXu,
    listAllNhaSanXuat,
    listAllNhaCungCap,
    listNhomChiPhiBh,
    listNguonKhacChiTra,
    listAllNhomDichVuCap1,
    listNhomDvCap2,
    listNhomDvCap3,
    listAllDuongDung,
    listLoaiLamTron,
    listAllPhuongPhapCheBien,
    listAllMaPhieuLinh,
    listAllKhoa,
    listLoaiLamTron,
    listNhomChiPhiBh,
    listNguonKhacChiTra,
    listDsMucDichSuDung,
    listGoiThau,
    listNhomThau,
    refSettings,
  ]);

  const listPanel = [
    {
      title: t("danhMuc.thongTinDichVu"),
      key: 1,
      show: true,
      render: () => {
        return (
          <LoadingWrapper
            cacheKey={CACHE_KEY.DM_THUOC_THONG_TIN_DICH_VU_CONFIG_FIELDS}
          >
            <FormServiceInfo
              listAllKhoa={listAllKhoa}
              listHoatChat={listHoatChat}
              listMeGrLv1={listMeGrLv1}
              listMeGrLv2={listMeGrLv2}
              listMeGrLv3={listMeGrLv3}
              listAllNhomDichVuKho={listAllNhomDichVuKho}
              listAllPhanLoaiThuoc={listAllPhanLoaiThuoc}
              listAllDonViTinh={listAllDonViTinh}
              listAllXuatXu={listAllXuatXu}
              listNSX={listAllNhaSanXuat}
              listNCC={listAllNhaCungCap}
              listNhomChiPhiBh={listNhomChiPhiBh}
              listNguonKhacChiTra={listNguonKhacChiTra}
              listAllNhomDichVuCap1={listAllNhomDichVuCap1}
              listAllNhomDichVuCap2={listAllNhomDichVuCap2}
              listAllNhomDichVuCap3={listAllNhomDichVuCap3}
              listAllDuongDung={listAllDuongDung}
              listLoaiLamTron={listLoaiLamTron}
              getListMeGrLv2TongHop={getListMeGrLv2TongHop}
              getListMeGrLv3TongHop={getListMeGrLv3TongHop}
              defaultParams={defaultParams}
              getAllTongHopDichVuCap2={getAllTongHopDichVuCap2}
              getAllTongHopDichVuCap3={getAllTongHopDichVuCap3}
              roleSave={[ROLES["DANH_MUC"].THUOC_THEM]}
              roleEdit={[ROLES["DANH_MUC"].THUOC_SUA]}
              editStatus={
                editStatus
                  ? !checkRole([ROLES["DANH_MUC"].THUOC_SUA])
                  : !checkRole([ROLES["DANH_MUC"].THUOC_THEM])
              }
              currentItemRowParent={props?.currentItem}
              onDayThuocSangVitimecs={onDayThuocSangVitimecs}
              layerId={layerId}
              handleClickedBtnAdded={handleClickedBtnAdded}
            />
          </LoadingWrapper>
        );
      },
    },
    {
      title: t("danhMuc.lieuDung"),
      key: 2,
      show: true,
      render: () => {
        return (
          <LieuDungThuoc
            dichVuId={currentItem?.id}
            roleSave={[ROLES["DANH_MUC"].THUOC_THEM]}
            roleEdit={[ROLES["DANH_MUC"].THUOC_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].THUOC_SUA])
                : !checkRole([ROLES["DANH_MUC"].THUOC_THEM])
            }
          />
        );
      },
    },
    {
      title: t("danhMuc.nhomChiPhi"),
      key: 3,
      show: true,
      render: () => {
        return (
          <NhomChiPhi
            dichVuId={currentItem?.id}
            roleSave={[ROLES["DANH_MUC"].THUOC_THEM]}
            roleEdit={[ROLES["DANH_MUC"].THUOC_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].THUOC_SUA])
                : !checkRole([ROLES["DANH_MUC"].THUOC_THEM])
            }
          />
        );
      },
    },
    {
      key: 4,
      title: `${t("danhMuc.mucDichSuDung")}(TT20)`,
      show: currentItem?.dichVu?.mucDichSuDung,
      render: () => {
        return (
          <MucDichSuDung
            dichVuId={currentItem?.id}
            currentItem={currentItem}
            title={`${t("danhMuc.mucDichSuDung")}(TT20)`}
          />
        );
      },
    },
    {
      key: 5,
      title: t("danhMuc.dichVuKemTheo"),
      show: true,
      render: () => {
        return (
          <DichVuKemTheo
            dichVuId={currentItem?.id}
            allowDecimal={true}
            dsLoaiDichVu={[LOAI_DICH_VU.VAT_TU]}
            loaiDichVu={ID_LOAI_DICH_VU}
            roleSave={[ROLES["DANH_MUC"].THUOC_THEM]}
            roleEdit={[ROLES["DANH_MUC"].THUOC_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].THUOC_SUA])
                : !checkRole([ROLES["DANH_MUC"].THUOC_THEM])
            }
          />
        );
      },
    },
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const onHandleSizeChange = (size) => {
    onSizeChange({ size: size });
  };

  const onShowAndHandleUpdate = (data = {}) => {
    setEditStatus(true);
    updateData({
      currentItem: { ...data },
    });
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        setState({
          dataSelected: record,
        });
        onShowAndHandleUpdate(record);
        getListMeGrLv2TongHop({
          page: "",
          size: "",
          active: true,
          sort: "ten,asc",
          loaiDichVu: ID_LOAI_DICH_VU,
        });
      },
    };
  };

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    updateData({
      currentItem: {},
    });
  };

  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };
  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };
  const setRowClassName = (record) => {
    let idDiff = state.dataSelected?.id;
    return record.id === idDiff ? "row-actived" : "";
  };

  const onImportDuLieuDMThuoc = () => {
    refModalImportDMThuoc.current &&
      refModalImportDMThuoc.current.show({ isModalVisible: true });
  };

  const onExportDuLieuDMThuoc = async () => {
    try {
      showLoading();

      const keysToRemove = [
        "dichVu.loaiDichVu",
        "dichVu.ma",
        "dichVu.ten",
        "dichVu.donViTinhId",
        "dichVu.giaKhongBaoHiem",
        "dichVu.giaBaoHiem",
        "dichVu.giaPhuThu",
        "dichVu.tyLeBhTt",
        "dichVu.tyLeTtDv",
        "dichVu.nhomChiPhiBh",
        "dichVu.nhomDichVuCap1Id",
        "dichVu.nhomDichVuCap2Id",
        "dichVu.nhomDichVuCap3Id",
        "dichVu.maTuongDuong",
        "dichVu.tenTuongDuong",
        "dichVu.nguonKhacId",
        "dichVu.maTckt",
        "dichVu.tenTckt",
        "dichVu.khongTinhTien",
        "dichVu.chiDinhSlLe",
      ];

      const stringToBoolean = (value) => {
        if (value === "true") return true;
        if (value === "false") return false;
        return value;
      };

      const filteredData = Object.fromEntries(
        Object.entries(dataSearch)
          .filter(([key]) => !keysToRemove.includes(key))
          .map(([key, value]) => [key, stringToBoolean(value)])
      );

      const dichVu = Object.fromEntries(
        Object.entries({
          loaiDichVu: ID_LOAI_DICH_VU,
          ma: dataSearch["dichVu.ma"],
          ten: dataSearch["dichVu.ten"],
          donViTinhId: dataSearch["dichVu.donViTinhId"],
          giaKhongBaoHiem: dataSearch["dichVu.giaKhongBaoHiem"],
          giaBaoHiem: dataSearch["dichVu.giaBaoHiem"],
          giaPhuThu: dataSearch["dichVu.giaPhuThu"],
          tyLeBhTt: dataSearch["dichVu.tyLeBhTt"],
          tyLeTtDv: dataSearch["dichVu.tyLeTtDv"],
          nhomChiPhiBh: dataSearch["dichVu.nhomChiPhiBh"],
          nhomDichVuCap1Id: dataSearch["dichVu.nhomDichVuCap1Id"],
          nhomDichVuCap2Id: dataSearch["dichVu.nhomDichVuCap2Id"],
          nhomDichVuCap3Id: dataSearch["dichVu.nhomDichVuCap3Id"],
          maTuongDuong: dataSearch["dichVu.maTuongDuong"],
          tenTuongDuong: dataSearch["dichVu.tenTuongDuong"],
          nguonKhacId: dataSearch["dichVu.nguonKhacId"],
          maTckt: dataSearch["dichVu.maTckt"],
          tenTckt: dataSearch["dichVu.tenTckt"],
          khongTinhTien: stringToBoolean(dataSearch["dichVu.khongTinhTien"]),
          chiDinhSlLe: stringToBoolean(dataSearch["dichVu.chiDinhSlLe"]),
        }).filter(([_, value]) => value !== "" && value !== null)
      );

      const payload = Object.fromEntries(
        Object.entries({
          ...filteredData,
          ...(Object.keys(dichVu).length > 0 && { dichVu }),
        }).filter(([_, value]) => value !== "" && value !== null)
      );
      await onExportDmThuoc(payload);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onImportDuLieuLieuDung = () => {
    refModalImportLieuDung.current &&
      refModalImportLieuDung.current.show({ isModalVisible: true });
  };

  const onExportDuLieuLieuDung = () => {
    showLoading();
    onExportLieuDung().finally(() => {
      hideLoading();
    });
  };

  const onImportDuLieuNhomChiPhi = () => {
    refModalImportNhomChiPhi.current &&
      refModalImportNhomChiPhi.current.show({ isModalVisible: true });
  };

  const onExportDuLieuNhomChiPhi = () => {
    showLoading();
    onExportNhomChiPhi().finally(() => {
      hideLoading();
    });
  };

  const onImportDuLieuMucDichSuDung = () => {
    refModalImportMucDichSuDung.current &&
      refModalImportMucDichSuDung.current.show({ isModalVisible: true });
  };

  const onExportDuLieuMucDichSuDung = () => {
    showLoading();
    onExportMucDichSuDung().finally(() => {
      hideLoading();
    });
  };

  const onExportThuocMoi = () => {
    refModalExportThuocMoi.current &&
      refModalExportThuocMoi.current.show(
        MAU_XUAT_DU_LIEU_XML.XUAT_DU_LIEU_DM_THUOC
      );
  };

  const menuMultiTab = () => (
    <Menu
      items={[
        ...(checkRole([ROLES["DANH_MUC"].XUAT_DANH_SACH_THUOC_MOI])
          ? [
              {
                key: 1,
                label: (
                  <a onClick={onExportThuocMoi}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>{t("danhMuc.xuatDanhSachThuocDayCong")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportDMThuoc
          ? [
              {
                key: 2,
                label: (
                  <a onClick={onImportDuLieuDMThuoc}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>{t("danhMuc.nhapDuLieuDanhMucThuoc")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportDmThuoc
          ? [
              {
                key: 3,
                label: (
                  <a onClick={onExportDuLieuDMThuoc}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>{t("danhMuc.xuatDuLieuDanhMucThuoc")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportLieuDung
          ? [
              {
                key: 4,
                label: (
                  <a onClick={onImportDuLieuLieuDung}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.lieuDung"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportLieuDung
          ? [
              {
                key: 5,
                label: (
                  <a onClick={onExportDuLieuLieuDung}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.lieuDung"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportNhomChiPhi
          ? [
              {
                key: 6,
                label: (
                  <a onClick={onImportDuLieuNhomChiPhi}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.nhomChiPhi"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportNhomChiPhi
          ? [
              {
                key: 7,
                label: (
                  <a onClick={onExportDuLieuNhomChiPhi}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.nhomChiPhi"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportMucDichSuDung
          ? [
              {
                key: 8,
                label: (
                  <a onClick={onImportDuLieuMucDichSuDung}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: `${t("danhMuc.mucDichSuDung")}(TT20)`,
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportMucDichSuDung
          ? [
              {
                key: 9,
                label: (
                  <a onClick={onExportDuLieuMucDichSuDung}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: `${t("danhMuc.mucDichSuDung")}(TT20)`,
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        {
          key: 10,
          label: (
            <a onClick={onDayThuocSangVitimecs}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcLogin />
                <span>{t("danhMuc.dayDmThuocSangVitimes")}</span>
              </div>
            </a>
          ),
        },
      ]}
    />
  );

  return (
    <BaseDm3
      breadcrumb={[
        { title: t("danhMuc.danhMuc"), link: "/danh-muc" },
        {
          title: t("danhMuc.danhMucThuoc"),
          link: "/danh-muc/thuoc",
        },
      ]}
    >
      <Col
        {...(!state.showFullTable
          ? collapseStatus
            ? TABLE_LAYOUT_COLLAPSE
            : TABLE_LAYOUT
          : null)}
        span={state.showFullTable ? 24 : null}
        className={`pr-3 ${state.changeShowFullTbale ? "" : "transition-ease"}`}
        style={{
          background: "#ffffff",
        }}
      >
        <TableWrapper
          title={TEN_LOAI_DICH_VU}
          classNameRow={"custom-header"}
          styleMain={{ marginTop: 0 }}
          styleContainerButtonHeader={{
            display: "flex",
            width: "100%",
            justifyContent: "flex-end",
            alignItems: "center",
            paddingRight: 35,
          }}
          menuMultiTab={menuMultiTab}
          buttonHeader={
            checkRole([ROLES["DANH_MUC"].THUOC_THEM])
              ? [
                  {
                    content: (
                      <Button
                        type="success"
                        onClick={handleClickedBtnAdded}
                        rightIcon={<SVG.IcAdd />}
                      >
                        {t("common.themMoiF1")}
                      </Button>
                    ),
                  },
                  {
                    className: `btn-change-full-table ${
                      state.showFullTable ? "small" : "large"
                    }`,
                    title: state.showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },
                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
              : [
                  {
                    className: `btn-change-full-table ${
                      state.showFullTable ? "small" : "large"
                    }`,
                    title: state.showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },
                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
          }
          columns={columns}
          dataSource={listData}
          onRow={onRow}
          rowClassName={setRowClassName}
          ref={refSettings}
          tableName="table_DANHMUC_DichVuThuoc"
          virtual={true}
        />
        {!!totalElements && (
          <Pagination
            listData={listData}
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            onShowSizeChange={onHandleSizeChange}
            style={{ flex: 1, justifyContent: "flex-end" }}
            pageSizeOptions={["10", "20", "50", "100", "200", "500", "1000"]}
          />
        )}
      </Col>
      {!state.showFullTable && (
        <Col
          {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
          className={`mt-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
          style={
            state.isSelected
              ? { border: "2px solid #c1d8fd", borderRadius: 20 }
              : {}
          }
        >
          <MultiLevelTab
            defaultActiveKey={1}
            listPanel={listPanel.filter((x) => x.show)}
            isBoxTabs={true}
          />
        </Col>
      )}
      <ModalImport onImport={onImportDMThuoc} ref={refModalImportDMThuoc} />
      <ModalImport onImport={onImportLieuDung} ref={refModalImportLieuDung} />
      <ModalImport
        onImport={onImportNhomChiPhi}
        ref={refModalImportNhomChiPhi}
      />
      <ModalImport
        onImport={onImportMucDichSuDung}
        ref={refModalImportMucDichSuDung}
      />
      <ModalExportDanhMucMoi ref={refModalExportThuocMoi} />
    </BaseDm3>
  );
};

export default DanhMucThuoc;
