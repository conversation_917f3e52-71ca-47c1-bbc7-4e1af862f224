import React, { useState, useEffect, useMemo, useRef } from "react";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import {
  Checkbox,
  Button,
  Select,
  InputTimeout,
  SelectLoadMore,
} from "components";
import {
  Form,
  Input,
  InputNumber,
  Select as AntSelect,
  Upload,
  Spin,
} from "antd"; // Xài Select của antd vì custom hiển thị giá trị khi chọn theo ticket https://jira.isofh.com.vn/browse/SAKURA-53531
import { useDispatch } from "react-redux";
import {
  ENUM,
  HOTKEY,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  ROLES,
  CACHE_KEY,
} from "constants/index";
import { openInNewTab, parseFloatNumberFromString } from "utils/index";
import { checkRole } from "lib-utils/role-utils";
import TabPanel from "components/MultiLevelTab/TabPanel";
import {
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import { useTranslation } from "react-i18next";
import { Main } from "./styled";
import { InputNumberFormat } from "components/common";
import { lowerFirst, pick } from "lodash";
import UploadForm from "components/UploadForm";
import { selectMaTen } from "redux-store/selectors";
import { listAllFields } from "pages/application/TuyChinhGiaoDienPhamMem/ThietLapDanhMuc/config";
import ModalSapXepTruong from "./ModalSapXepTruong";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";
const MUC_DICH_SU_DUNG = {
  DIEU_TRI_TRONG_VIEN: 10,
  KINH_DOANH_NHA_THUOC: 20,
};

function FormServiceInfo(props) {
  const {
    listHoatChat,
    listMeGrLv1,
    listMeGrLv2,
    listMeGrLv3,
    listAllNhomDichVuKho,
    listAllPhanLoaiThuoc,
    listAllDonViTinh,
    listAllXuatXu,
    listNSX,
    listNCC,
    listNguonKhacChiTra,
    listAllNhomDichVuCap1,
    listAllNhomDichVuCap2,
    listAllNhomDichVuCap3,
    listAllDuongDung,
    listLoaiLamTron,
    getListMeGrLv2TongHop,
    getListMeGrLv3TongHop,
    currentItemRowParent,
    roleSave,
    roleEdit,
    editStatus,
    onDayThuocSangVitimecs,
    defaultParams,
    getAllTongHopDichVuCap2,
    getAllTongHopDichVuCap3,
    layerId,
    handleClickedBtnAdded,
    listAllKhoa,
    configFields,
    setUserConfigFields,
    userConfigFields,
  } = props;
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const [listGoiThau] = useEnum(ENUM.GOI_THAU);
  const [listnhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listDsMucDichSuDung] = useEnum(ENUM.MUC_DICH_SU_DUNG);
  const [dataMIMS_TYPE] = useEnum(ENUM.MIMS_TYPE);

  const [listDuyetKhangSinh] = useEnum(ENUM.DUYET_KHANG_SINH);
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);
  const [listAllPhuongPhapCheBien] = useListAll("phuongPhapCheBien", {}, true);
  const [listVen] = useEnum(ENUM.VEN);

  const currentItem = useStore("danhMucThuoc.currentItem");

  const [dataPHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT] = useThietLap(
    THIET_LAP_CHUNG.PHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT
  );
  const dataBAT_BUOC_NHAP_DU_LIEU_DANH_MUC_THUOC =
    useThietLap(
      THIET_LAP_CHUNG.BAT_BUOC_NHAP_DU_LIEU_DANH_MUC_THUOC
    )[0].toLowerCase() === "true";

  const [dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT
  );

  const {
    danhMucThuoc: { createOrEdit, onUpdate, updateData },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
  } = useDispatch();

  const [state, _setState] = useState({ bhyt: true, listFile: [] });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [form] = Form.useForm();
  const phanLoaiDvKhoId = Form.useWatch("phanLoaiDvKhoId", form);
  const heSoDinhMuc = Form.useWatch("heSoDinhMuc", form);

  const isRequiredMaHoatChat = useMemo(() => {
    if (!dataPHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT || !phanLoaiDvKhoId) {
      return false;
    }
    return (
      (listAllPhanLoaiThuoc || []).find((x) => x.id == phanLoaiDvKhoId)?.ma ==
      dataPHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT
    );
  }, [
    listAllPhanLoaiThuoc,
    phanLoaiDvKhoId,
    dataPHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT,
  ]);

  const refClickBtnAdd = useRef(null);
  const refClickBtnSave = useRef(null);
  const refModalSapXepTruong = useRef(null);

  useEffect(() => {
    loadCurrentItem(currentItem);
  }, [currentItem]);

  useEffect(() => {
    if (layerId) {
      onAddLayer({ layerId });
      onRegisterHotkey({
        layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F1, //F1
            onEvent: () => {
              refClickBtnAdd.current && refClickBtnAdd.current();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: (e) => {
              refClickBtnSave.current && refClickBtnSave.current(e);
            },
          },
        ],
      });
      return () => {
        onRemoveLayer({ layerId });
      };
    }
  }, [layerId]);

  const loadCurrentItem = (danhMucThuoc) => {
    if (danhMucThuoc && Object.keys(danhMucThuoc).length) {
      const {
        dichVu: {
          ma,
          ten,
          donViTinhId: dvtThuCapId,
          khongTinhTien,
          nguonKhacId,
          nhomChiPhiBh,
          tyLeBhTt,
          tyLeTtDv,
          maTuongDuong,
          tenTuongDuong,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          duongDungId,
          mucDichSuDung,
          chiDinhSlLe,
          guiVitimes,
          mienPhiGiamDocDuyet,
          online,
          maTckt,
          tenTckt,
        } = {},
        active,
        id,
        giaNhapSauVat,
        dsKhoaCdDvtSoCapId,
        dsMucDichSuDung,
        phieuLinhId,
        huongDanSuDung,
        soNgayCanhBaoHsd,
        soGioCanhBaoHuy,
        dsPhuongPhapCheBienId,
        dangBaoChe,
        tenTrungThau,
        nhaNhapKhau,
        dieuKienBaoQuan,
        chatLuongCamQuan,
        lieuDung,
        phaChe,
        soLuongGioiHan1LanKham,
        tuChua,
        kiemTraHangNgay,
        kyHieu,
        dsDichVuThayTheId,
      } = danhMucThuoc || {};
      const data = {
        id,
        ma,
        ten,
        khongTinhTien,
        nguonKhacId: nguonKhacId || null,
        nhomChiPhiBh,
        tyLeBhTt:
          tyLeBhTt !== null
            ? tyLeBhTt
            : dsMucDichSuDung?.length === 1 &&
              dsMucDichSuDung[0] === MUC_DICH_SU_DUNG.KINH_DOANH_NHA_THUOC
            ? 0
            : null,
        tyLeTtDv,
        dvtThuCapId,
        maTuongDuong,
        tenTuongDuong,
        nhomDichVuCap1Id,
        nhomDichVuCap2Id,
        nhomDichVuCap3Id,
        duongDungId,
        active,
        giaNhapSauVat,
        ...danhMucThuoc,
        dsKhoaCdDvtSoCapId: dsKhoaCdDvtSoCapId || [],
        dsMucDichSuDung: dsMucDichSuDung || [],
        heSoDinhMuc: danhMucThuoc?.heSoDinhMuc || 1,
        dsDichVuThayTheId: dsDichVuThayTheId || [],
        mucDichSuDung,
        phieuLinhId,
        huongDanSuDung,
        soNgayCanhBaoHsd,
        soGioCanhBaoHuy,
        dsPhuongPhapCheBienId,
        dangBaoChe,
        chiDinhSlLe,
        tenTrungThau,
        guiVitimes,
        mienPhiGiamDocDuyet,
        online,
        nhaNhapKhau,
        dieuKienBaoQuan,
        chatLuongCamQuan,
        lieuDung,
        phaChe,
        maTckt,
        tenTckt,
        soLuongGioiHan1LanKham,
        tuChua,
        kiemTraHangNgay,
        kyHieu: kyHieu === 10,
      };
      form.setFieldsValue(data);
      setState({
        data: data,
        bhyt: data.bhyt,
        listFile: [],
      });
    } else {
      form.resetFields();
      setState({
        data: null,
        bhyt: true,
      });
    }
  };

  const onCancel = () => {
    loadCurrentItem(currentItem);
  };

  const onSave = (e) => {
    e.preventDefault();
    form
      .validateFields()
      .then((values) => {
        // Lấy tất cả giá trị có trong configForms kể cả bị ẩn đi
        const mergeValues = pick(
          { ...state.data, ...values },
          listAllFields["dmThuoc.thongTinDichVu"].map((i) => i.name)
        );

        const {
          ma,
          ten,
          active,
          nguonKhacId,
          nhomChiPhiBh,
          tyLeBhTt,
          tyLeTtDv,
          dvtThuCapId,
          maTuongDuong,
          tenTuongDuong,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          khongTinhTien,
          dsMucDichSuDung,
          mucDichSuDung,
          huongDanSuDung,
          soNgayCanhBaoHsd,
          soGioCanhBaoHuy,
          dsPhuongPhapCheBienId,
          dangBaoChe,
          chiDinhSlLe,
          guiVitimes,
          mienPhiGiamDocDuyet,
          online,
          nhaNhapKhau,
          dieuKienBaoQuan,
          chatLuongCamQuan,
          lieuDung,
          phaChe,
          maTckt,
          tenTckt,
          soLuongGioiHan1LanKham,
          kyHieu,
          mimsGuid,
          mimsType,
          maByt,
          ...rest
        } = mergeValues;

        values = {
          dichVu: {
            ten,
            ma,
            maTuongDuong,
            tenTuongDuong,
            nhomChiPhiBh,
            nguonKhacId,
            tyLeBhTt,
            tyLeTtDv,
            donViTinhId: dvtThuCapId,
            nhomDichVuCap1Id,
            nhomDichVuCap2Id,
            nhomDichVuCap3Id,
            khongTinhTien,
            loaiDichVu: LOAI_DICH_VU.THUOC,
            mucDichSuDung,
            chiDinhSlLe,
            guiVitimes,
            mienPhiGiamDocDuyet,
            online,
            maTckt,
            tenTckt,
          },

          active,
          ...rest,
          giaNhapSauVat: parseFloatNumberFromString(rest.giaNhapSauVat),
          id: state.data?.id,
          dsMucDichSuDung:
            dsMucDichSuDung?.length > 0 ? dsMucDichSuDung : [10, 20],
          huongDanSuDung,
          soNgayCanhBaoHsd,
          soGioCanhBaoHuy,
          dsPhuongPhapCheBienId,
          dangBaoChe,
          bhyt: state.bhyt,
          nhaNhapKhau,
          dieuKienBaoQuan,
          chatLuongCamQuan,
          lieuDung,
          phaChe,
          soLuongGioiHan1LanKham: parseFloatNumberFromString(
            soLuongGioiHan1LanKham
          ),
          soLuongGioiHan1Ngay: parseFloatNumberFromString(
            rest.soLuongGioiHan1Ngay
          ),
          dungTich: parseFloatNumberFromString(rest.dungTich),
          kyHieu: kyHieu === true ? 10 : null,
          mimsGuid,
          mimsType,
          maByt,
        };
        showLoading();
        createOrEdit(values)
          .then(() => {
            if (state.data?.id) {
              return;
            }
            form.resetFields();
          })
          .catch((err) => {})
          .finally(() => {
            hideLoading();
          });
      })
      .catch((error) => {});
  };

  refClickBtnSave.current = onSave;
  refClickBtnAdd.current = handleClickedBtnAdded;

  const refAutoFocus = useRef(null);
  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [currentItem]);
  const onChangeFields = (key) => (e) => {
    let ten = listHoatChat.find((x) => x.id === e)?.ten;
    form.setFieldsValue({ tenHoatChat: ten });
  };

  const onHuongBaoHiem = (isOk) => () => {
    showConfirm(
      {
        title: t("common.thongBao"),
        content: isOk
          ? t("danhMuc.banCoChacChanBatXacNhanHuongBaoHiemKhong")
          : t("danhMuc.banCoChacChanTatXacNhanHuongBaoHiemKhong"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        if (currentItem.id) {
          onUpdate({ id: currentItem.id, bhyt: isOk }).then(() => {
            setState({ bhyt: !state.bhyt });
          });
        } else {
          setState({ bhyt: !state.bhyt });
        }
      }
    );
  };

  const onChange = (key1, key2) => (e, list) => {
    setState({ data: { ...state.data, [key1]: e, [key2]: list } });
  };

  console.log("state", state);

  const handleChange = useRefFunc((key) => (value) => {
    if (key === "nhomDichVuCap1Id") {
      form.setFieldsValue({
        nhomDichVuCap2Id: null,
        nhomDichVuCap3Id: null,
      });
      if (value) {
        getAllTongHopDichVuCap2({
          ...defaultParams,
          nhomDichVuCap1Id: value,
        });
        getAllTongHopDichVuCap3({
          ...defaultParams,
          nhomDichVuCap1Id: value,
        });
      } else {
        getAllTongHopDichVuCap2(defaultParams);
        getAllTongHopDichVuCap3(defaultParams);
      }
    } else if (key === "nhomDichVuCap2Id") {
      form.setFieldsValue({
        nhomDichVuCap3Id: null,
      });
      if (value) {
        getAllTongHopDichVuCap3({
          ...defaultParams,
          nhomDichVuCap2Id: value,
        });
      } else {
        getAllTongHopDichVuCap3(defaultParams);
      }
    }

    if (key === "dsMucDichSuDung") {
      if (
        value?.length === 1 &&
        value[0] === MUC_DICH_SU_DUNG.KINH_DOANH_NHA_THUOC
      ) {
        form.setFieldsValue({
          tyLeBhTt: 0,
        });
      }
    }
  });

  const FormBatBuoc = ({ title, rules, ...restProps }) => {
    const requireRule = {
      required: dataBAT_BUOC_NHAP_DU_LIEU_DANH_MUC_THUOC,
      message: t("danhMuc.vuiLongNhapTitle", {
        title: lowerFirst(title),
      }),
    };
    rules = rules ? rules : [requireRule];

    return <Form.Item rules={rules} {...restProps} />;
  };

  const fieldOrderMap = useMemo(() => {
    const map = {};
    if (userConfigFields && userConfigFields.length > 0) {
      userConfigFields.forEach((fieldName, index) => {
        map[fieldName] = index;
      });
    }
    return map;
  }, [userConfigFields]);

  const isFieldVisible = useMemo(() => {
    return (fieldName) => {
      if (!userConfigFields || userConfigFields.length === 0) return true;
      return fieldName in fieldOrderMap;
    };
  }, [userConfigFields, fieldOrderMap]);

  const getFieldOrder = (fieldName) => {
    return fieldName in fieldOrderMap ? fieldOrderMap[fieldName] : 999;
  };

  const formFields = useMemo(() => {
    const allFields = [
      {
        name: "ma",
        component: (
          <Form.Item
            key="ma"
            label={t("danhMuc.maThuoc")}
            name="ma"
            rules={
              !!dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT?.eval()
                ? []
                : [
                    {
                      required: true,
                      message: t("danhMuc.vuiLongNhapMaThuoc"),
                    },
                    {
                      max: 200,
                      message: t("danhMuc.vuiLongNhapMaThuocKhongQua200KyTu"),
                    },
                    {
                      whitespace: true,
                      message: t("danhMuc.vuiLongNhapMaThuoc"),
                    },
                  ]
            }
          >
            <Input
              ref={refAutoFocus}
              autoFocus={true}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapMaThuoc")}
              disabled={!!dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT?.eval()}
            />
          </Form.Item>
        ),
      },
      {
        name: "ten",
        component: (
          <Form.Item
            key="ten"
            label={t("danhMuc.tenThuoc")}
            name="ten"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTenThuoc"),
              },
              {
                max: 1024,
                message: t("danhMuc.vuiLongNhapTenThuocKhongQua1024KyTu"),
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapTenThuoc"),
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenThuoc")}
            />
          </Form.Item>
        ),
      },
      {
        name: "goiThau",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.goiThau")} </div>}
            name="goiThau"
          >
            <Select placeholder={t("danhMuc.goiThau")} data={listGoiThau} />
          </Form.Item>
        ),
      },
      {
        name: "hoatChatId",
        component: (
          <FormBatBuoc
            key="hoatChatId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/hoat-chat")}
                style={{ color: "#0000FF" }}
              >
                {t("danhMuc.maHoatChat")}
              </div>
            }
            name="hoatChatId"
            rules={[
              {
                required: !isRequiredMaHoatChat,
                message: t("danhMuc.vuiLongNhapMaHoatChat"),
              },
            ]}
          >
            <Select
              onChange={onChangeFields("hoatChatId")}
              placeholder={t("danhMuc.chonMaHoatChat")}
              optionLabelProp="label"
              data={listHoatChat}
              getLabel={selectMaTen}
            />
          </FormBatBuoc>
        ),
      },
      {
        name: "tenHoatChat",
        component: (
          <FormBatBuoc
            key="tenHoatChat"
            title={t("danhMuc.tenHoatChat")}
            label={<div className="color">{t("danhMuc.tenHoatChat")}</div>}
            name="tenHoatChat"
            rules={[
              {
                required: !isRequiredMaHoatChat,
                message: t("danhMuc.vuiLongNhapTitle", {
                  title: lowerFirst(t("danhMuc.tenHoatChat")),
                }),
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTenHoatChat")}
            />
          </FormBatBuoc>
        ),
      },
      {
        name: "hamLuong",
        component: (
          <FormBatBuoc
            key="hamLuong"
            title={t("danhMuc.hamLuong")}
            label={<div className="color">{t("danhMuc.hamLuong")} </div>}
            name="hamLuong"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapHamLuong")}
            />
          </FormBatBuoc>
        ),
      },
      {
        name: "nhomDvKhoCap1Id",
        component: (
          <Form.Item
            key="nhomDvKhoCap1Id"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-thuoc")}
              >
                {t("danhMuc.nhomThuocCap1")}
              </div>
            }
            name="nhomDvKhoCap1Id"
          >
            <Select
              data={listMeGrLv1}
              placeholder={t("danhMuc.chonNhomThuocCap1")}
              onChange={(e) => {
                form.setFieldsValue({
                  nhomDvKhoCap2Id: null,
                  nhomDvKhoCap3Id: null,
                });
                const params = {
                  page: "",
                  size: "",
                  active: true,
                  sort: "ten,asc",
                  loaiDichVu: LOAI_DICH_VU.THUOC,
                };
                if (e) {
                  getListMeGrLv2TongHop({
                    ...params,
                    nhomDvKhoCap1Id: e,
                  });
                } else {
                  getListMeGrLv2TongHop({ ...params });
                }
              }}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDvKhoCap2Id",
        component: (
          <Form.Item
            key="nhomDvKhoCap2Id"
            label={t("danhMuc.nhomThuocCap2")}
            name="nhomDvKhoCap2Id"
          >
            <Select
              data={listMeGrLv2}
              placeholder={t("danhMuc.chonNhomThuocCap2")}
              onChange={(e) => {
                form.setFieldsValue({
                  nhomDvKhoCap3Id: null,
                });
                const params = {
                  page: "",
                  size: "",
                  active: true,
                  sort: "ten,asc",
                  loaiDichVu: LOAI_DICH_VU.THUOC,
                };
                if (e) {
                  getListMeGrLv3TongHop({
                    ...params,
                    nhomDvKhoCap2Id: e,
                  });
                } else {
                  getListMeGrLv3TongHop({ ...params });
                }
              }}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDvKhoCap3Id",
        component: (
          <Form.Item label={t("danhMuc.nhomThuocCap3")} name="nhomDvKhoCap3Id">
            <Select
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomThuocCap3").toLowerCase(),
              })}`}
              data={listMeGrLv3}
            />
          </Form.Item>
        ),
      },
      {
        name: "phanNhomDvKhoId",
        component: (
          <Form.Item
            key="phanNhomDvKhoId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/phan-nhom-thuoc")}
              >
                {t("danhMuc.phanNhomThuoc")}
              </div>
            }
            name="phanNhomDvKhoId"
          >
            <Select
              data={listAllNhomDichVuKho}
              placeholder={t("danhMuc.chonPhanNhomThuoc")}
            />
          </Form.Item>
        ),
      },
      {
        name: "phanLoaiDvKhoId",
        component: (
          <Form.Item
            key="phanLoaiDvKhoId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/phan-loai-thuoc")}
              >
                {t("danhMuc.phanLoaiThuoc")}
              </div>
            }
            name="phanLoaiDvKhoId"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonPhanLoaiThuoc"),
              },
            ]}
          >
            <Select
              data={listAllPhanLoaiThuoc}
              placeholder={t("danhMuc.chonPhanLoaiThuoc")}
              onChange={(e) => {
                if (
                  (listAllPhanLoaiThuoc || []).find((x) => x.id == e)?.ma ==
                  dataPHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT
                ) {
                  form.setFields([
                    {
                      name: "hoatChatId",
                      errors: [],
                    },
                  ]);
                }
              }}
            />
          </Form.Item>
        ),
      },
      {
        name: "dvtSoCapId",
        component: (
          <Form.Item
            key="dvtSoCapId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/don-vi-tinh")}
              >
                {t("danhMuc.donViSoCap")}
              </div>
            }
            name="dvtSoCapId"
            rules={[
              {
                required: heSoDinhMuc > 1,
                message: t(
                  "danhMuc.batBuocDienDonViSoCapKhiHeSoDinhMucLonHon1"
                ),
              },
            ]}
          >
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDonViSoCap")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dvtThuCapId",
        component: (
          <Form.Item
            key="dvtThuCapId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/don-vi-tinh")}
              >
                {t("danhMuc.donViThuCap")}
              </div>
            }
            name="dvtThuCapId"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonDonViThuCap"),
              },
            ]}
          >
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDonViThuCap")}
            />
          </Form.Item>
        ),
      },
      {
        name: "heSoDinhMuc",
        component: (
          <Form.Item
            key="heSoDinhMuc"
            label={t("danhMuc.heSoDinhMuc")}
            name="heSoDinhMuc"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapHeSoDinhMuc"),
              },
              {
                pattern: new RegExp(
                  /^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$/
                ),
                message: t("danhMuc.vuiLongNhapHeSoDinhMucLonHon0"),
              },
              {
                validator: (rule, value, callback) => {
                  if (value) {
                    if (Number(value) <= 0) {
                      let dvtSoCapId = form.getFieldValue("dvtSoCapId");
                      let dvtThuCapId = form.getFieldValue("dvtThuCapId");
                      if (dvtSoCapId && dvtSoCapId !== dvtThuCapId) {
                        callback(
                          new Error(
                            t(
                              "danhMuc.hangHoaCoDonViSoCapBatBuocDienHeSoDinhMucLonHon0"
                            )
                          )
                        );
                      } else {
                        callback();
                      }
                    } else {
                      callback();
                    }
                  } else {
                    callback();
                  }
                },
              },
            ]}
          >
            <Input
              style={{ width: "100%" }}
              maxLength={10}
              className="input-option"
              placeholder={t("danhMuc.nhapHeSoDinhMuc")}
              disabled={
                Object.keys(currentItem).length > 0 &&
                !checkRole([ROLES["DANH_MUC"].XEM_SUA_HE_SO_DINH_MUC])
              }
            />
          </Form.Item>
        ),
      },
      {
        name: "dvtSuDungId",
        component: (
          <Form.Item
            key="dvtSuDungId"
            label={<div>{t("danhMuc.donViSuDung")}</div>}
            name="dvtSuDungId"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonDonViSuDung"),
              },
            ]}
          >
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDonViSuDung")}
            />
          </Form.Item>
        ),
      },
      {
        name: "loaiLamTron",
        component: (
          <Form.Item label={t("danhMuc.loaiLamTron")} name="loaiLamTron">
            <Select
              placeholder={t("danhMuc.loaiLamTron")}
              data={listLoaiLamTron}
            />
          </Form.Item>
        ),
      },
      {
        name: "dungTich",
        component: (
          <Form.Item
            key="dungTich"
            label={t("danhMuc.dungTich")}
            name="dungTich"
          >
            <InputNumberFormat
              style={{ width: "100%" }}
              maxLength={10}
              className="input-option"
              placeholder={t("danhMuc.nhapDungTich")}
              allowNegative={false}
            />
          </Form.Item>
        ),
      },
      {
        name: "quyCach",
        component: (
          <FormBatBuoc
            key="quyCach"
            title={t("danhMuc.quyCach")}
            label={t("danhMuc.quyCach")}
            name="quyCach"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapQuyCach")}
            />
          </FormBatBuoc>
        ),
      },
      {
        name: "sttTt20",
        component: (
          <Form.Item label={t("danhMuc.sttTrongTt20")} name="sttTt20">
            <Input
              className="input-option"
              placeholder={t("danhMuc.sttTrongTt20")}
            />
          </Form.Item>
        ),
      },
      {
        name: "maTckt",
        component: (
          <Form.Item label={t("danhMuc.maTckt")} name="maTckt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaTckt")}
            />
          </Form.Item>
        ),
      },
      {
        name: "xuatXuId",
        component: (
          <Form.Item
            key="xuatXuId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/xuat-xu")}
              >
                {t("danhMuc.nuocSanXuat")}
              </div>
            }
            name="xuatXuId"
          >
            <Select
              data={listAllXuatXu}
              placeholder={t("danhMuc.chonNuocSanXuat")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tuChua",
        component: (
          <Form.Item label={t("danhMuc.tuChua")} name="tuChua">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTuChua")}
            />
          </Form.Item>
        ),
      },
      {
        name: "soLuongGioiHan1LanKham",
        component: (
          <Form.Item
            label={t("danhMuc.slGioiHanLuotKcbNgoaiTruBhyt")}
            name="soLuongGioiHan1LanKham"
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapSLGioiHanLuotKcbNgoaiTruBhyt")}
              allowNegative={false}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenTckt",
        component: (
          <Form.Item label={t("danhMuc.tenTckt")} name="tenTckt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTenTckt")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhaSanXuatId",
        component: (
          <Form.Item
            key="nhaSanXuatId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/doi-tac")}
              >
                {t("common.nhaSanXuat")}
              </div>
            }
            name="nhaSanXuatId"
          >
            <Select data={listNSX} placeholder={t("danhMuc.chonNhaSanXuat")} />
          </Form.Item>
        ),
      },
      {
        name: "nhaCungCapId",
        component: (
          <Form.Item
            key="nhaCungCapId"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/doi-tac")}
              >
                {t("danhMuc.nhaCungCap")}
              </div>
            }
            name="nhaCungCapId"
          >
            <Select data={listNCC} placeholder={t("danhMuc.chonNhaCungCap")} />
          </Form.Item>
        ),
      },
      {
        name: "giaNhapSauVat",
        component: (
          <Form.Item
            key="giaNhapSauVat"
            label={t("danhMuc.giaSauVAT1DvtSoCap")}
            name="giaNhapSauVat"
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapGiaSauVAT1DvtSoCap")}
              decimalScale={3}
              allowNegative={false}
            />
          </Form.Item>
        ),
      },
      {
        name: "tyLeBhTt",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.tyLeBhThanhToan")} </div>}
            name="tyLeBhTt"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTyLeBhThanhToan"),
              },
            ]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapTyLeBhThanhToan")}
              allowNegative={false}
              decimalScale={0}
              isAllowed={(values) => {
                const { floatValue, formattedValue } = values;
                if (formattedValue && formattedValue.includes(","))
                  return false;

                return floatValue ? floatValue <= 100 : true;
              }}
              disabled={
                Object.keys(currentItem).length > 0 &&
                !checkRole([
                  ROLES["DANH_MUC"].XEM_SUA_TY_LE_THANH_TOAN_BAO_HIEM,
                ])
              }
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomThau",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.nhomThau")} </div>}
            name="nhomThau"
          >
            <Select placeholder={t("danhMuc.nhomThau")} data={listnhomThau} />
          </Form.Item>
        ),
      },
      {
        name: "chiDinhSlLe",
        component: (
          <Form.Item
            key="chiDinhSlLe"
            label=" "
            name="chiDinhSlLe"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.choPhepKeSlLe")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "theoDoiNgaySd",
        component: (
          <Form.Item
            key="theoDoiNgaySd"
            label=" "
            name="theoDoiNgaySd"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.theoDoiNgaySd")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "soLuongGioiHan1Ngay",
        component: (
          <Form.Item
            label={t("danhMuc.soLuongGioiHan1Ngay")}
            name="soLuongGioiHan1Ngay"
            rules={[
              {
                validator: (_, value, callback) => {
                  if (value && value <= 0) {
                    callback(t("pttt.yeuCauNhapSoLuongLonHon0"));
                  } else {
                    callback();
                  }
                },
              },
            ]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapSoLuongGioiHan1Ngay")}
              allowNegative={false}
            />
          </Form.Item>
        ),
      },
      {
        name: "khongTinhTien",
        component: (
          <Form.Item
            key="khongTinhTien"
            label=" "
            name="khongTinhTien"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.khongTinhTien")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "ven",
        component: (
          <Form.Item label={t("danhMuc.ven")} name="ven">
            <Select
              className="input-option"
              placeholder={t("danhMuc.nhapVen")}
              data={listVen}
            />
          </Form.Item>
        ),
      },
      {
        name: "thuocDauSao",
        component: (
          <Form.Item
            key="thuocDauSao"
            label=" "
            name="thuocDauSao"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.thuocDauSao")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "kyHieu",
        component: (
          <Form.Item
            key="kyHieu"
            label=" "
            name="kyHieu"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.corticoid")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "mucDichSuDung",
        component: (
          <Form.Item
            key="mucDichSuDung"
            label=" "
            name="mucDichSuDung"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.apDungTt20")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "trongPtTt",
        component: (
          <Form.Item
            key="trongPtTt"
            label=" "
            name="trongPtTt"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.thuocPhauThuat")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "trungTenThuongMai",
        component: (
          <Form.Item
            key="trungTenThuongMai"
            label=" "
            name="trungTenThuongMai"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.trungTenThuongMai")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "nhaNhapKhau",
        component: (
          <Form.Item label={t("danhMuc.nhaNhapKhau")} name="nhaNhapKhau">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapNhaNhapKhau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tuoiTho",
        component: (
          <Form.Item label={t("danhMuc.tuoiThoCuaThuoc")} name="tuoiTho">
            <Input.TextArea
              className="input-option"
              placeholder={t("danhMuc.nhapTitle", {
                title: lowerFirst(t("danhMuc.tuoiThoCuaThuoc")),
              })}
            />
          </Form.Item>
        ),
      },
      {
        name: "guiVitimes",
        component: (
          <Form.Item
            key="guiVitimes"
            label=" "
            name="guiVitimes"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.guiVitimes")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "mienPhiGiamDocDuyet",
        component: (
          <Form.Item
            key="mienPhiGiamDocDuyet"
            label=" "
            name="mienPhiGiamDocDuyet"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.mienPhiGiamDocDuyet")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "online",
        component: (
          <Form.Item
            key="online"
            label=" "
            name="online"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.guiISC")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "chatLuongCamQuan",
        component: (
          <Form.Item
            key="chatLuongCamQuan"
            label=" "
            name="chatLuongCamQuan"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.chatLuongCamQuan")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "lieuDung",
        component: (
          <Form.Item
            key="lieuDung"
            label=" "
            name="lieuDung"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.yeuCauTheoDoi")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "phaChe",
        component: (
          <Form.Item
            key="phaChe"
            label=" "
            name="phaChe"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.yeuCauPhaChe")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "lasa",
        component: (
          <Form.Item key="lasa" label=" " name="lasa" valuePropName="checked">
            <Checkbox>{t("danhMuc.thuocLasa")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "ghiChuHoatChat",
        component: (
          <Form.Item label={t("danhMuc.ghiChuHoatChat")} name="ghiChuHoatChat">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapGhiChuHoatChat")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dieuKienBaoQuan",
        component: (
          <Form.Item
            label={t("danhMuc.dieuKienBaoQuan")}
            name="dieuKienBaoQuan"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapDieuKienBaoQuan")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nguyCoCao",
        component: (
          <Form.Item
            key="nguyCoCao"
            label=" "
            name="nguyCoCao"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.thuocNguyCoCao")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "kiemTraHangNgay",
        component: (
          <Form.Item
            key="kiemTraHangNgay"
            label=" "
            name="kiemTraHangNgay"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.kiemTraHangNgay")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "active",
        component: currentItem && (
          <Form.Item
            key="active"
            label=" "
            name="active"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "nhomDichVuCap1Id",
        component: (
          <Form.Item
            key="nhomDichVuCap1Id"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=1")}
              >
                {t("danhMuc.nhomDichVuCap1")}
              </div>
            }
            name="nhomDichVuCap1Id"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonNhomDichVuCap1"),
              },
            ]}
          >
            <Select
              data={listAllNhomDichVuCap1}
              placeholder={t("danhMuc.chonNhomdichVuCap1")}
              onChange={handleChange("nhomDichVuCap1Id")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDichVuCap2Id",
        component: (
          <Form.Item
            key="nhomDichVuCap2Id"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=2")}
              >
                {t("danhMuc.nhomDichVuCap2")}
              </div>
            }
            name="nhomDichVuCap2Id"
          >
            <Select
              data={listAllNhomDichVuCap2}
              placeholder={t("danhMuc.chonNhomdichVuCap2")}
              onChange={handleChange("nhomDichVuCap2Id")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDichVuCap3Id",
        component: (
          <Form.Item
            key="nhomDichVuCap3Id"
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=3")}
              >
                {t("danhMuc.nhomDichVuCap3")}
              </div>
            }
            name="nhomDichVuCap3Id"
          >
            <Select
              data={listAllNhomDichVuCap3}
              placeholder={t("danhMuc.chonNhomdichVuCap3")}
            />
          </Form.Item>
        ),
      },
      {
        name: "soVisa",
        component: (
          <FormBatBuoc
            key="soVisa"
            title={t("danhMuc.soVisa")}
            label={<div className="color">{t("danhMuc.soVisa")} </div>}
            name="soVisa"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapSoVisa")}
            />
          </FormBatBuoc>
        ),
      },
      {
        name: "dsDichVuThayTheId",
        component: (
          <Form.Item
            label={t("danhMuc.maThuocThayThe")}
            name="dsDichVuThayTheId"
          >
            <SelectLoadMore
              placeholder={t("danhMuc.maThuocThayThe")}
              api={dmDichVuKhoProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma} - ${i.ten}`,
                ten: `${i.ma} - ${i.ten}`,
              })}
              onChange={onChange("dsDichVuThayTheId", "dsDichVuThayThe")}
              uniqByKey="value"
              addValue={
                state.data?.dsDichVuThayThe?.length > 0
                  ? state.data?.dsDichVuThayThe.map((i) => ({
                      value: i.id,
                      label: `${i.ma} - ${i.ten}`,
                    }))
                  : null
              }
              value={state.data?.dsDichVuThayThe?.map((i) => i.id)}
              addParam={{ dsLoaiDichVu: [LOAI_DICH_VU.THUOC] }}
              keySearch={"ma"}
              mode="multiple"
            ></SelectLoadMore>
          </Form.Item>
        ),
      },
      {
        name: "maLienThong",
        component: (
          <Form.Item
            key="maLienThong"
            label={t("danhMuc.maLienThongDuocQuocGia")}
            name="maLienThong"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaLienThongDuocQuocGia")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nguonKhacId",
        component: (
          <Form.Item
            key="nguonKhacId"
            label={t("danhMuc.nguonChiTraKhac")}
            name="nguonKhacId"
          >
            <Select
              data={listNguonKhacChiTra}
              placeholder={t("danhMuc.nguonChiTraKhac")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dsPhuongPhapCheBienId",
        component: (
          <Form.Item
            label={t("danhMuc.phuongPhapCheBien")}
            name="dsPhuongPhapCheBienId"
          >
            <Select
              placeholder={t("danhMuc.phuongPhapCheBien")}
              data={listAllPhuongPhapCheBien}
              mode="multiple"
            />
          </Form.Item>
        ),
      },
      {
        name: "tyLeTtDv",
        component: (
          <Form.Item
            label={t("danhMuc.tyLeThanhToanDichVu")}
            name="tyLeTtDv"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTyLeThanhToanDichVu"),
              },
              {
                pattern: new RegExp(
                  /^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$/
                ),
                message: t("quanLyNoiTru.yeuCauNhapTitleLonHon0", {
                  title: lowerFirst(t("danhMuc.tyLeThanhToanDichVu")),
                }),
              },
            ]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapTyLeThanhToanDichVu")}
              allowNegative={false}
              decimalScale={0}
              isAllowed={(values) => {
                const { floatValue, formattedValue } = values;
                if (formattedValue && formattedValue.includes(","))
                  return false;

                return floatValue ? floatValue <= 100 : true;
              }}
              disabled={
                Object.keys(currentItem).length > 0 &&
                !checkRole([ROLES["DANH_MUC"].XEM_SUA_TY_LE_THANH_TOAN_DICH_VU])
              }
            />
          </Form.Item>
        ),
      },
      {
        name: "canhBao",
        component: (
          <Form.Item label={t("danhMuc.canhBaoSuDungThuoc")} name="canhBao">
            <Input.TextArea
              className="input-option"
              placeholder={t("danhMuc.nhapTitle", {
                title: lowerFirst(t("danhMuc.canhBaoSuDungThuoc")),
              })}
            />
          </Form.Item>
        ),
      },
      {
        name: "dsKhoaCdDvtSoCapId",
        component: (
          <Form.Item
            key="dsKhoaCdDvtSoCapId"
            label={
              <div className="pointer">
                {t("danhMuc.khoaChiDinhTheoDvtSoCap")}
              </div>
            }
            name="dsKhoaCdDvtSoCapId"
          >
            <Select
              mode="multiple"
              data={listAllKhoa}
              placeholder={t("danhMuc.chonKhoaChiDinhTheoDvtSoCap")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dsMucDichSuDung",
        component: (
          <Form.Item
            key="dsMucDichSuDung"
            label={t("danhMuc.mucDichSuDung")}
            name="dsMucDichSuDung"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonMucDichSuDung"),
              },
            ]}
          >
            <Select
              mode="multiple"
              data={listDsMucDichSuDung}
              placeholder={t("danhMuc.chonMucDichSuDung")}
              onChange={handleChange("dsMucDichSuDung")}
            />
          </Form.Item>
        ),
      },
      {
        name: "duongDungId",
        component: (
          <FormBatBuoc
            key="duongDungId"
            title={t("danhMuc.duongDung")}
            label={<div className="color">{t("danhMuc.duongDung")} </div>}
            name="duongDungId"
          >
            <Select
              placeholder={t("danhMuc.duongDung")}
              data={listAllDuongDung}
            />
          </FormBatBuoc>
        ),
      },
      {
        name: "huongDanSuDung",
        component: (
          <Form.Item
            key="huongDanSuDung"
            label={t("danhMuc.huongDanSuDung")}
            name="huongDanSuDung"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.huongDanSuDung")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dsTaiLieuHdsd",
        component: (
          <Form.Item
            key="dsTaiLieuHdsd"
            name="dsTaiLieuHdsd"
            label={t("danhMuc.fileHdsdDungThuoc")}
          >
            <UploadForm
              accept=".html,.pdf"
              afterUpload={(dsTaiLieuHdsd) => {
                form.setFieldsValue({ dsTaiLieuHdsd });
                setState({ dsTaiLieuHdsd });
              }}
              provider="dvKhoHuongDanSuDung"
              textBtn={t("danhMuc.taiFileHdsdDangHtmlPdf")}
              multiple
              previewInNewTab
            />
          </Form.Item>
        ),
      },
      {
        name: "duyetKhangSinh",
        component: (
          <Form.Item
            label={t("danhMuc.mucDoPheDuyetThuocKhangSinh")}
            name="duyetKhangSinh"
          >
            <Select
              placeholder={t("danhMuc.mucDoPheDuyetThuocKhangSinh")}
              data={listDuyetKhangSinh}
            />
          </Form.Item>
        ),
      },
      {
        name: "phieuLinhId",
        component: (
          <Form.Item label={t("danhMuc.maPhieuLinh")} name="phieuLinhId">
            <Select
              placeholder={t("danhMuc.maPhieuLinh")}
              data={listAllMaPhieuLinh}
            />
          </Form.Item>
        ),
      },
      {
        name: "quyetDinhThau",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.quyetDinhThau")} </div>}
            name="quyetDinhThau"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.quyetDinhThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "thongTinThau",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.thongTinThau")} </div>}
            name="thongTinThau"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.thongTinThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenTrungThau",
        component: (
          <Form.Item
            label={
              <div className="color">
                {t("kho.quyetDinhThau.tenHangHoaTrungThau")}{" "}
              </div>
            }
            name="tenTrungThau"
          >
            <Input
              className="input-option"
              placeholder={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenMoiThau",
        component: (
          <Form.Item label={t("danhMuc.tenMoiThau")} name="tenMoiThau">
            <Input
              className="input-option"
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.tenMoiThau").toLowerCase(),
              })}`}
            />
          </Form.Item>
        ),
      },
      {
        name: "soNgayCanhBaoHsd",
        component: (
          <Form.Item
            label={t("danhMuc.soNgayCanhBaoHsd")}
            name="soNgayCanhBaoHsd"
          >
            <InputNumber
              min={0}
              className="input-option"
              placeholder={t("danhMuc.soNgayCanhBaoHsd")}
            />
          </Form.Item>
        ),
      },
      {
        name: "maVitimes",
        component: (
          <Form.Item
            label={t("danhMuc.maGuiVitimes")}
            name="maVitimes"
            rules={[
              {
                max: 300,
                message: t("danhMuc.khongDuocNhapQuaNumKyTu", {
                  num: 300,
                }),
              },
            ]}
          >
            <Input.TextArea
              className="input-option"
              placeholder={t("danhMuc.nhapTitle", {
                title: lowerFirst(t("danhMuc.maGuiVitimes")),
              })}
            />
          </Form.Item>
        ),
      },
      {
        name: "maSinhHieu",
        component: (
          <Form.Item
            label={t("danhMuc.maHieuSinhPham")}
            name="maSinhHieu"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maHieuSinhPham")}
            />
          </Form.Item>
        ),
      },
      {
        name: "maSinhPham",
        component: (
          <Form.Item
            label={t("danhMuc.maSinhPham")}
            name="maSinhPham"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maSinhPham")}
            />
          </Form.Item>
        ),
      },
      {
        name: "maTuongDuong",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.maTuongDuong")} </div>}
            name="maTuongDuong"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaTuongDuong")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenTuongDuong",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.tenTuongDuong")} </div>}
            name="tenTuongDuong"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTenTuongDuong")}
            />
          </Form.Item>
        ),
      },
      {
        name: "soLuongTonToiThieu",
        component: (
          <Form.Item
            label={t("danhMuc.soLuongTonToiThieu")}
            name="soLuongTonToiThieu"
          >
            <InputNumber
              min={0}
              className="input-option"
              placeholder={t("danhMuc.nhapSoLuongTonToiThieu")}
              type="number"
            />
          </Form.Item>
        ),
      },
      {
        name: "dangBaoChe",
        component: (
          <Form.Item
            label={t("danhMuc.dangBaoChe")}
            name="dangBaoChe"
            rules={[
              {
                max: 255,
                message: t("danhMuc.khongDuocNhapQuaNumKyTu", {
                  num: 255,
                }),
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.dangBaoChe")}
            />
          </Form.Item>
        ),
      },
      {
        name: "soLuongToiThieu",
        component: (
          <Form.Item
            label={t("danhMuc.soLuongTonToiThieu")}
            name="soLuongToiThieu"
          >
            <InputNumber
              min={0}
              className="input-option"
              placeholder={t("danhMuc.nhapSoLuongTonToiThieu")}
              type="number"
            />
          </Form.Item>
        ),
      },
      {
        name: "hamLuongDdd",
        component: (
          <Form.Item label={t("danhMuc.hamLuongDdd")} name="hamLuongDdd">
            <Input
              className="input-option"
              placeholder={t("danhMuc.hamLuongDdd")}
              type="number"
            />
          </Form.Item>
        ),
      },
      {
        name: "maAtc",
        component: (
          <Form.Item
            label={t("danhMuc.maAtc")}
            name="maAtc"
            rules={[
              {
                max: 250,
                message: t("danhMuc.khongDuocNhapQuaNumKyTu", {
                  num: 250,
                }),
              },
            ]}
          >
            <Input className="input-option" placeholder={t("danhMuc.maAtc")} />
          </Form.Item>
        ),
      },
      {
        name: "donViDdd",
        component: (
          <Form.Item label={t("danhMuc.donViDddWho")} name="donViDdd">
            <InputTimeout
              className="input-option"
              placeholder={t("danhMuc.donViDddWho")}
            />
          </Form.Item>
        ),
      },
      {
        name: "whoDdd",
        component: (
          <Form.Item label={t("danhMuc.whoDdd")} name="whoDdd">
            <Input
              className="input-option"
              placeholder={t("danhMuc.whoDdd")}
              type="number"
            />
          </Form.Item>
        ),
      },
      {
        name: "mimsGuid",
        component: (
          <Form.Item label={t("danhMuc.mimsGuid")} name="mimsGuid">
            <Input
              className="input-option"
              placeholder={t("danhMuc.mimsGuid")}
            />
          </Form.Item>
        ),
      },
      {
        name: "mimsType",
        component: (
          <Form.Item label={t("danhMuc.mimsType")} name="mimsType">
            <Select
              className="input-option"
              placeholder={t("danhMuc.mimsType")}
              data={dataMIMS_TYPE}
            />
          </Form.Item>
        ),
      },
      {
        name: "maByt",
        component: (
          <Form.Item label={t("danhMuc.maThuocByt")} name="maByt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.maThuocByt")}
            />
          </Form.Item>
        ),
      },
    ];

    return allFields
      .filter((field) => isFieldVisible(field.name))
      .sort((a, b) => getFieldOrder(a.name) - getFieldOrder(b.name));
  }, [
    userConfigFields,
    fieldOrderMap,
    isFieldVisible,
    t,
    refAutoFocus,
    heSoDinhMuc,
    currentItem,
    isRequiredMaHoatChat,
    dataPHAN_LOAI_THUOC_KHONG_NHAP_HOAT_CHAT,
    listAllDonViTinh,
    listHoatChat,
    listMeGrLv1,
    listMeGrLv2,
    listAllNhomDichVuKho,
    listAllPhanLoaiThuoc,
    listAllXuatXu,
    listNSX,
    listNCC,
    listAllNhomDichVuCap1,
    listAllNhomDichVuCap2,
    listAllNhomDichVuCap3,
    listAllDuongDung,
    listNguonKhacChiTra,
    listAllKhoa,
    listDsMucDichSuDung,
    getListMeGrLv2TongHop,
    handleChange,
    LOAI_DICH_VU,
    form,
    setState,
  ]);

  return (
    <Main>
      <TabPanel>
        <EditWrapper
          title={
            <div style={{ display: "flex" }}>
              <div>{t("danhMuc.thongTinGoiDichVu")}</div>
              <div style={{ marginLeft: "auto", display: "flex", gap: "8px" }}>
                {!state.bhyt && (
                  <Button onClick={onHuongBaoHiem(true)}>
                    {t("danhMuc.batHuongBh")}
                  </Button>
                )}
                {state.bhyt && (
                  <Button onClick={onHuongBaoHiem(false)}>
                    {t("danhMuc.tatHuongBh")}
                  </Button>
                )}
                {currentItem?.dichVu?.guiVitimes &&
                  currentItem?.id &&
                  onDayThuocSangVitimecs && (
                    <Button
                      onClick={() => onDayThuocSangVitimecs(currentItem.id)}
                    >
                      {t("danhMuc.dayThuocSangVitimes")}
                    </Button>
                  )}
                <Button onClick={() => refModalSapXepTruong.current?.show()}>
                  {t("danhMuc.sapXepTruong")}
                </Button>
              </div>
            </div>
          }
          onCancel={onCancel}
          onSave={onSave}
          showAdded={false}
          roleSave={roleSave}
          roleEdit={roleEdit}
          editStatus={editStatus}
          isHiddenButtonAdd={true}
          forceShowButtonSave={
            (currentItemRowParent && checkRole(roleEdit) && true) || false
          }
          forceShowButtonCancel={
            (currentItemRowParent && checkRole(roleEdit) && true) || false
          }
        >
          <Form
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom"
            initialValues={{ heSoDinhMuc: 1, tyLeTtDv: 100 }}
          >
            {formFields.map((field) => {
              return (
                <React.Fragment key={field.name}>
                  {field.component}
                </React.Fragment>
              );
            })}
          </Form>
        </EditWrapper>
      </TabPanel>
      <ModalSapXepTruong
        ref={refModalSapXepTruong}
        userConfigFields={userConfigFields}
        configFields={configFields}
        onSubmitSuccess={(newConfigFields) => {
          setUserConfigFields(newConfigFields);
        }}
      />
    </Main>
  );
}

export default FormServiceInfo;
