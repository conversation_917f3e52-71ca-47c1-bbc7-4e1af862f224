import React, { useEffect, useState, useRef, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { orderBy } from "lodash";
import FormServiceInfo from "./components/FormServiceInfo";
import <PERSON>homChiP<PERSON> from "components/DanhMuc/NhomChiPhi";
import {
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  Checkbox,
  InputTimeout,
  Button,
  SelectLoadMore,
} from "components";
import MultiLevelTab from "components/MultiLevelTab";
import BaseDm3 from "pages/danhMuc/BaseDm3";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  HIEU_LUC,
  CO_KHONG,
  YES_NO,
  ROLES,
  ENUM,
  LOAI_DICH_VU,
  LOAI_DOI_TAC,
  CACHE_KEY,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { Col, Input, Menu } from "antd";
import { formatNumber } from "utils";
import { useEnum, useGuid, useListAll, useLoading, useStore } from "hooks";
import { SVG } from "assets";
import ModalImport from "components/DanhMuc/ModalImport";
import ModalExportDanhMucMoi from "components/DanhMuc/ModalExportDanhMucMoi";
import { MAU_XUAT_DU_LIEU_XML } from "client/api";
import { Main } from "./styled";
import { LoadingWrapper } from "../danhMucThuoc/components/LoadingWrapper";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";

const { Setting } = TableWrapper;
let timer = null;
const ID_LOAI_DICH_VU = LOAI_DICH_VU.VAT_TU;

const params = { page: "", size: "", active: true, sort: "ten,asc" };

const DanhMucVatTu = (props) => {
  const { t } = useTranslation();
  const TEN_LOAI_DICH_VU = t("danhMuc.danhMucVatTu");
  const refSettings = useRef(null);
  const refModalImportDMVatTu = useRef(null);
  const refModalExportVatTuMoi = useRef(null);
  const refModalImportNhomChiPhi = useRef(null);
  const refModalImportKichCoVt = useRef(null);
  const [collapseStatus, setCollapseStatus] = useState(false);
  const [editStatus, setEditStatus] = useState(false);
  const { showLoading, hideLoading } = useLoading();
  const layerId = useGuid();

  useEffect(() => {
    onSizeChange({ size: 10 });
    getAllServices();
  }, []);

  const {
    danhMucVatTu: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData,
      getListVatTuBo,
    },
    kichCo: {
      getListAllKichCo,
      onExport: onExportKichCoVt,
      onImport: onImportKichCoVt,
    },
    donViTinh: { getListAllDonViTinh },
    xuatXu: { getListAllXuatXu },
    doiTac: { getListAllNhaSanXuat, getListAllNhaCungCap },
    nhomDichVuCap1: { searchTongHopDichVuCap1 },
    nhomDichVuCap2: { searchTongHopDichVuCap2, getAllTongHopDichVuCap2 },
    nhomDichVuCap3: { searchTongHopDichVuCap3, getAllTongHopDichVuCap3 },
    dichVuKho: { onExportDmVatTu, onImportDMVatTu },
    nhomDichVuKho: {
      getListMeGrLv1TongHop,
      getListMeGrLv2TongHop,
      getListMeGrLv3TongHop,
      getListMeGrLv4TongHop,
    },
    nhomChiPhi: { onExportNhomChiPhi, onImport: onImportNhomChiPhi },
  } = useDispatch();

  const {
    listData,
    totalElements,
    page,
    size,
    currentItem,
    dataSortColumn,
    dataSearch,
  } = useSelector((state) => state.danhMucVatTu);
  const listAllDonViTinh = useStore("donViTinh.listAllDonViTinh", []);
  const listAllXuatXu = useStore("xuatXu.listAllXuatXu", []);

  const listAllNhaSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);

  const [listGoiThau] = useEnum(ENUM.GOI_THAU);
  const [listNhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listDsMucDichSuDung] = useEnum(ENUM.MUC_DICH_SU_DUNG);
  const [listNhomChiPhiBh] = useEnum(ENUM.NHOM_CHI_PHI_BH);
  const [listLoaiLamTron] = useEnum(ENUM.LOAI_LAM_TRON);
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listAllPhanLoaiVatTu] = useListAll("phanLoaiVatTu", {}, true);
  const listMeGrLv1 = useStore("nhomDichVuKho.listMeGrLv1", []);
  const listMeGrLv2 = useStore("nhomDichVuKho.listMeGrLv2", []);
  const listMeGrLv3 = useStore("nhomDichVuKho.listMeGrLv3", []);
  const listMeGrLv4 = useStore("nhomDichVuKho.listMeGrLv4", []);

  // xài cho form
  const listNhomDvCap1 = useStore("nhomDichVuCap1.listGroupService1", []);
  const listNhomDvCap2 = useStore("nhomDichVuCap2.listGroupService2", []);
  const listNhomDvCap3 = useStore("nhomDichVuCap3.listGroupService3", []);
  // xài cho bộ lọc danh sách
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );

  const listNguonKhacChiTra = useMemo(() => {
    return orderBy(listAllNguonKhacChiTra || [], (a) => a.ma, "asc").map(
      (o) => ({
        ...o,
        ten: `${o.ma} - ${o.ten}`,
      })
    );
  }, [listAllNguonKhacChiTra]);

  const [state, _setState] = useState({
    showFullTable: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const getAllServices = () => {
    searchTongHopDichVuCap1({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    searchTongHopDichVuCap2({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    searchTongHopDichVuCap3({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListAllDonViTinh({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListAllXuatXu(params);
    getListAllNhaSanXuat({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: LOAI_DOI_TAC.NHA_SAN_XUAT,
      loaiDichVu: ID_LOAI_DICH_VU,
    });
    getListAllNhaCungCap({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: LOAI_DOI_TAC.NHA_CUNG_CAP,
      loaiDichVu: ID_LOAI_DICH_VU,
    });
    getListMeGrLv1TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListMeGrLv2TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListMeGrLv3TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
    getListMeGrLv4TongHop({ ...params, loaiDichVu: ID_LOAI_DICH_VU });
  };
  const searchTongHopDichVuCap2ByCap1 = (nhomDichVuCap1Id) => {
    searchTongHopDichVuCap2({
      ...params,
      nhomDichVuCap1Id,
    });
  };
  const searchTongHopDichVuCap3ByCap2 = (nhomDichVuCap2Id) => {
    searchTongHopDichVuCap3({
      ...params,
      nhomDichVuCap2Id,
    });
  };

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };
  const onSearchInput = (key) => (e) => {
    if (key === "nhomDvKhoCap1Id") {
      const params = {
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
        loaiDichVu: ID_LOAI_DICH_VU,
      };
      if (e) {
        getListMeGrLv2TongHop({
          ...params,
          nhomDvKhoCap1Id: e,
        });
      } else {
        getListMeGrLv2TongHop({ ...params });
      }
    }
    if (key === "nhomDvKhoCap2Id") {
      const params = {
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
        loaiDichVu: ID_LOAI_DICH_VU,
      };
      if (e) {
        getListMeGrLv3TongHop({
          ...params,
          nhomDvKhoCap2Id: e,
        });
      } else {
        getListMeGrLv3TongHop({ ...params });
      }
    }
    if (key === "nhomDvKhoCap3Id") {
      const params = {
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
        loaiDichVu: ID_LOAI_DICH_VU,
      };
      if (e) {
        getListMeGrLv4TongHop({
          ...params,
          nhomDvKhoCap3Id: e,
        });
      } else {
        getListMeGrLv4TongHop({ ...params });
      }
    }
    if (key === "dichVu.nhomDichVuCap1Id") {
      getAllTongHopDichVuCap2({
        ...params,
        ...(e && { nhomDichVuCap1Id: e }),
      });
    }
    if (key === "dichVu.nhomDichVuCap2Id") {
      getAllTongHopDichVuCap3({
        ...params,
        ...(e && { nhomDichVuCap2Id: e }),
      });
    }
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) {
        value = e.target.checked;
      } else {
        value = e.target.value;
      }
    } else value = e;
    clearTimeout(timer);
    timer = setTimeout(() => {
      onChangeInputSearch({
        [key]: value,
      });
    }, 500);
  };

  const columns = [
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.stt")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "60px",
      dataIndex: "index",
      key: "index",
      fixed: "left",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maVatTu")}
          sort_key="dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ma"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.maVaTu").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.ma")}
            />
          }
        />
      ),
      width: "130px",
      dataIndex: "dichVu",
      key: "dichVu.ma",
      fixed: "left",
      i18Name: "danhMuc.maVatTu",
      show: true,
      render: (item) => {
        return item && item.ma;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenVatTu")}
          sort_key="dichVu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ten"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.tenVatTu").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.ten")}
            />
          }
        />
      ),
      width: "180px",
      dataIndex: "dichVu",
      key: "dichVu.ten",
      fixed: "left",
      i18Name: "danhMuc.tenVatTu",
      show: true,
      render: (item) => {
        return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomVatTuCap1")}
          sort_key="nhomDvKhoCap1.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomDvKhoCap1.ten"] || 0}
          searchSelect={
            <Select
              data={listMeGrLv1}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap1").toLowerCase(),
              })}`}
              onChange={onSearchInput("nhomDvKhoCap1Id")}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "nhomDvKhoCap1",
      key: "nhomDvKhoCap1Id",
      i18Name: "danhMuc.nhomVatTuCap1",
      show: true,
      render: (item) => item?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomVatTuCap2")}
          sort_key="nhomDvKhoCap2.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomDvKhoCap2.ten"] || 0}
          searchSelect={
            <Select
              data={listMeGrLv2}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap2").toLowerCase(),
              })}`}
              onChange={onSearchInput("nhomDvKhoCap2Id")}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "nhomDvKhoCap2",
      key: "nhomDvKhoCap2Id",
      i18Name: "danhMuc.nhomVatTuCap2",
      show: true,
      render: (item) => item?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomVatTuCap3")}
          sort_key="nhomDvKhoCap3.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomDvKhoCap3.ten"] || 0}
          searchSelect={
            <Select
              data={listMeGrLv3}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap3").toLowerCase(),
              })}`}
              onChange={onSearchInput("nhomDvKhoCap3Id")}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "nhomDvKhoCap3",
      key: "nhomDvKhoCap3Id",
      i18Name: "danhMuc.nhomVatTuCap3",
      show: true,
      render: (item) => item?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomVatTuCap4")}
          sort_key="nhomDvKhoCap4.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomDvKhoCap4.ten"] || 0}
          searchSelect={
            <Select
              data={listMeGrLv4}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap4").toLowerCase(),
              })}`}
              onChange={onSearchInput("nhomDvKhoCap4Id")}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "nhomDvKhoCap4",
      key: "nhomDvKhoCap4Id",
      i18Name: "danhMuc.nhomVatTuCap4",
      show: true,
      render: (item) => item?.ten,
    },
    {
      title: (
        <HeaderSearch
          sort_key="maKyHieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maKyHieu || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.maKyHieu").toLowerCase(),
              })}`}
              onChange={onSearchInput("maKyHieu")}
            />
          }
          title={t("danhMuc.maKyHieu")}
        />
      ),
      width: "130px",
      dataIndex: "maKyHieu",
      key: "maKyHieu",
      i18Name: "danhMuc.maKyHieu",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="dvtSoCap.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dvtSoCap.ten"] || 0}
          searchSelect={
            <Select
              data={listAllDonViTinh}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.donViSoCap").toLowerCase(),
              })}`}
              onChange={onSearchInput("dvtSoCapId")}
            />
          }
          title={t("danhMuc.donViSoCap")}
        />
      ),
      width: "150px",
      dataIndex: "dvtSoCap",
      key: "dvtSoCap",
      i18Name: "danhMuc.donViSoCap",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.donViTinh.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.donViTinh.ten"] || 0}
          searchSelect={
            <Select
              data={listAllDonViTinh}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.donViThuCap").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.donViTinhId")}
            />
          }
          title={t("danhMuc.donViThuCap")}
        />
      ),
      width: "150px",
      dataIndex: "dichVu",
      key: "dichVu.donViTinhId",
      i18Name: "danhMuc.donViThuCap",
      show: true,
      render: (item) => {
        return item?.donViTinh?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="heSoDinhMuc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.heSoDinhMuc || 0}
          search={
            <InputTimeout
              type="number"
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.heSoDinhMuc").toLowerCase(),
              })}`}
              onChange={onSearchInput("heSoDinhMuc")}
            />
          }
          title={t("danhMuc.heSoDinhMuc")}
        />
      ),
      width: "150px",
      dataIndex: "heSoDinhMuc",
      key: "heSoDinhMuc",
      align: "right",
      i18Name: "danhMuc.heSoDinhMuc",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="thongSoKyThuat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thongSoKyThuat || 0}
          search={
            <InputTimeout
              type="number"
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.thongSoKyThuat").toLowerCase(),
              })}`}
              onChange={onSearchInput("thongSoKyThuat")}
            />
          }
          title={t("danhMuc.thongSoKyThuat")}
        />
      ),
      width: "160px",
      dataIndex: "thongSoKyThuat",
      key: "thongSoKyThuat",
      align: "right",
      i18Name: "danhMuc.thongSoKyThuat",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="quyCach"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.quyCach || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.quyCach").toLowerCase(),
              })}`}
              onChange={onSearchInput("quyCach")}
            />
          }
          title={t("danhMuc.quyCach")}
        />
      ),
      width: "100px",
      dataIndex: "quyCach",
      key: "quyCach",
      align: "right",
      i18Name: "danhMuc.quyCach",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="xuatXu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["xuatXu.ten"] || 0}
          searchSelect={
            <Select
              data={listAllXuatXu}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nuocSanXuat").toLowerCase(),
              })}`}
              onChange={onSearchInput("xuatXuId")}
            />
          }
          title={t("danhMuc.nuocSanXuat")}
        />
      ),
      width: 130,
      dataIndex: "xuatXu",
      key: "xuatXu",
      i18Name: "danhMuc.nuocSanXuat",
      show: true,
      render: (item) => {
        return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="nhaSanXuat.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhaSanXuat.ten"] || 0}
          searchSelect={
            <Select
              data={listAllNhaSanXuat}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("common.nhaSanXuat").toLowerCase(),
              })}`}
              onChange={onSearchInput("nhaSanXuatId")}
            />
          }
          title={t("common.nhaSanXuat")}
        />
      ),
      width: 130,
      dataIndex: "nhaSanXuat",
      key: "nhaSanXuat",
      i18Name: "common.nhaSanXuat",
      show: true,
      render: (item) => {
        return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="nhaCungCap.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhaCungCap.ten"] || 0}
          searchSelect={
            <Select
              data={listAllNhaCungCap}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhaCungCap").toLowerCase(),
              })}`}
              onChange={onSearchInput("nhaCungCapId")}
            />
          }
          title={t("danhMuc.nhaCungCap")}
        />
      ),
      width: 130,
      dataIndex: "nhaCungCap",
      key: "nhaCungCap",
      i18Name: "danhMuc.nhaCungCap",
      show: true,
      render: (item) => {
        return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="giaNhapSauVat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.giaNhapSauVat || 0}
          search={
            <InputTimeout
              type="number"
              formatPrice={true}
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.giaNhap").toLowerCase(),
              })}`}
              onChange={onSearchInput("giaNhapSauVat")}
            />
          }
          title={t("danhMuc.giaNhap")}
        />
      ),
      width: 130,
      dataIndex: "giaNhapSauVat",
      key: "giaNhapSauVat",
      align: "right",
      i18Name: "danhMuc.giaNhap",
      show: true,
      render: (field, _, __) => (field && formatNumber(field)) || "",
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.giaKhongBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaKhongBaoHiem"] || 0}
          search={
            <InputTimeout
              type="number"
              formatPrice={true}
              placeholder={t("danhMuc.timGia")}
              onChange={onSearchInput("dichVu.giaKhongBaoHiem")}
            />
          }
          title={t("danhMuc.giaKhongBaoHiem")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "giaKhongBaoHiem",
      align: "right",
      i18Name: "danhMuc.giaKhongBaoHiem",
      show: true,
      render: (field, _, __) =>
        field?.giaKhongBaoHiem ? field?.giaKhongBaoHiem.formatPrice() : "",
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.giaBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaBaoHiem"] || 0}
          search={
            <InputTimeout
              type="number"
              formatPrice={true}
              placeholder={t("danhMuc.timGia")}
              onChange={onSearchInput("dichVu.giaBaoHiem")}
            />
          }
          title={t("danhMuc.giaBaoHiem")}
        />
      ),
      width: 130,
      dataIndex: "dichVu",
      key: "giaBaoHiem",
      align: "right",
      i18Name: "danhMuc.giaBaoHiem",
      show: true,
      render: (field, _, __) =>
        field?.giaBaoHiem ? field?.giaBaoHiem.formatPrice() : "",
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.giaPhuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaPhuThu"] || 0}
          search={
            <InputTimeout
              type="number"
              formatPrice={true}
              placeholder={t("danhMuc.timGia")}
              onChange={onSearchInput("dichVu.giaPhuThu")}
            />
          }
          title={t("danhMuc.giaPhuThu")}
        />
      ),
      width: 130,
      dataIndex: "dichVu",
      key: "giaPhuThu",
      align: "right",
      i18Name: "danhMuc.giaPhuThu",
      show: true,
      render: (field, _, __) =>
        field?.giaPhuThu ? field?.giaPhuThu.formatPrice() : "",
    },
    // {
    //   title: (
    //     <HeaderSearch
    //       sort_key="giaTran"
    //       onClickSort={onClickSort}
    //       dataSort={dataSortColumn.giaTran || 0}
    //       search={
    //         <Input
    //           placeholder="Tìm giá trần"
    //           onChange={onSearchInput("giaTran")}
    //         />
    //       }
    //       title="Giá trần"
    //     />
    //   ),
    //   width: 130,
    //   dataIndex: "giaTran",
    //   key: "giaTran",
    //   align: "right",
    //   render: (field, _, __) => (field && formatNumber(field)) || "",
    // },
    {
      title: (
        <HeaderSearch
          sort_key="tranBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tranBaoHiem || 0}
          search={
            <InputTimeout
              type="number"
              formatPrice={true}
              placeholder={t("danhMuc.timGiaTranBH")}
              onChange={onSearchInput("tranBaoHiem")}
            />
          }
          title={t("danhMuc.tranBaoHiem")}
        />
      ),
      width: 130,
      dataIndex: "tranBaoHiem",
      key: "tranBaoHiem",
      align: "right",
      i18Name: "danhMuc.tranBaoHiem",
      show: true,
      render: (field, _, __) => (field && formatNumber(field)) || "",
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.tyLeBhTt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tyLeBhTt"] || 0}
          search={
            <InputTimeout
              type="number"
              placeholder={t("danhMuc.timTyLeBHTT")}
              onChange={onSearchInput("dichVu.tyLeBhTt")}
            />
          }
          title={t("danhMuc.tyLeBHThanhToan")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu",
      align: "right",
      i18Name: "danhMuc.tyLeBHThanhToan",
      show: true,
      render: (item) => {
        return item?.tyLeBhTt;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.tyLeTtDv"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tyLeTtDv"] || 0}
          search={
            <InputTimeout
              type="number"
              placeholder={t("danhMuc.timTyLeTTDV")}
              onChange={onSearchInput("dichVu.tyLeTtDv")}
            />
          }
          title={t("danhMuc.tyLeThanhToanDV")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.tyLeTtDv",
      align: "right",
      i18Name: "danhMuc.tyLeThanhToanDV",
      show: true,
      render: (item) => {
        return item?.tyLeTtDv;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.nhomChiPhiBh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomChiPhiBh"] || 0}
          searchSelect={
            <Select
              data={listNhomChiPhiBh}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomChiPhi").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.nhomChiPhiBh")}
            />
          }
          title={t("danhMuc.nhomChiPhi")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.nhomChiPhiBh",
      i18Name: "danhMuc.nhomChiPhi",
      show: true,
      render: (item) => {
        const index = listNhomChiPhiBh.findIndex(
          (chiphi) => chiphi.id === item.nhomChiPhiBh
        );
        if (index === -1) return;
        return listNhomChiPhiBh[index]?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.nhomDichVuCap1.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap1.ten"] || 0}
          searchSelect={
            <Select
              data={listNhomDvCap1}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomDVCap1").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.nhomDichVuCap1Id")}
            />
          }
          title={t("danhMuc.nhomDVCap1")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.nhomDichVuCap1Id",
      i18Name: "danhMuc.nhomDVCap1",
      show: true,
      render: (item) => {
        return item && item?.nhomDichVuCap1?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.nhomDichVuCap2.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap2.ten"] || 0}
          searchSelect={
            <Select
              data={listAllNhomDichVuCap2}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomDVCap2").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.nhomDichVuCap2Id")}
            />
          }
          title={t("danhMuc.nhomDVCap2")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.nhomDichVuCap2Id",
      i18Name: "danhMuc.nhomDVCap2",
      show: true,
      render: (item) => {
        return item && item?.nhomDichVuCap2?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.nhomDichVuCap3.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap3.ten"] || 0}
          searchSelect={
            <Select
              data={listAllNhomDichVuCap3}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomDVCap3").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.nhomDichVuCap3Id")}
            />
          }
          title={t("danhMuc.nhomDVCap3")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.nhomDichVuCap3Id",
      i18Name: "danhMuc.nhomDVCap3",
      show: true,
      render: (item) => {
        return item && item?.nhomDichVuCap3?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.maTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.maTuongDuong"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.maTuongDuong").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.maTuongDuong")}
            />
          }
          title={t("danhMuc.maTuongDuong")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.maTuongDuong",
      i18Name: "danhMuc.maTuongDuong",
      show: true,
      render: (item) => {
        return item && item.maTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.tenTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenTuongDuong"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.timTitle", {
                title: t("danhMuc.tenTuongDuong").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.tenTuongDuong")}
            />
          }
          title={t("danhMuc.tenTuongDuong")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.tenTuongDuong",
      i18Name: "danhMuc.tenTuongDuong",
      show: true,
      render: (item) => {
        return item && item.tenTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="vatTuBo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.vatTuBo || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.vatTuBo").toLowerCase(),
              })}`}
              onChange={onSearchInput("vatTuBo")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.vatTuBo")}
        />
      ),
      width: "130px",
      dataIndex: "vatTuBo",
      key: "vatTuBo",
      align: "center",
      i18Name: "danhMuc.vatTuBo",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="vatTuKichCo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["vatTuKichCo"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.vatTuTheoKichCo").toLowerCase(),
              })}`}
              onChange={onSearchInput("vatTuKichCo")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.vatTuTheoKichCo")}
        />
      ),
      width: "160px",
      dataIndex: "vatTuKichCo",
      key: "vatTuKichCo",
      align: "center",
      i18Name: "danhMuc.vatTuTheoKichCo",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="kyThuatCao"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["kyThuatCao"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.kyThuatCao").toLowerCase(),
              })}`}
              onChange={onSearchInput("kyThuatCao")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.kyThuatCao")}
        />
      ),
      width: "130px",
      dataIndex: "kyThuatCao",
      key: "kyThuatCao",
      align: "center",
      i18Name: "danhMuc.kyThuatCao",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          // sort_key="dichVu.khongTinhTien"
          // onClickSort={onClickSort}
          // dataSort={dataSortColumn["dichVu.khongTinhTien"] || 0}
          searchSelect={
            <Select
              data={listNguonKhacChiTra}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nguonKhacChiTra").toLowerCase(),
              })}`}
              onChange={onSearchInput("dichVu.nguonKhacId")}
            />
          }
          title={t("danhMuc.nguonKhacChiTra")}
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      key: "nguonKhacId",
      i18Name: "danhMuc.nguonKhacChiTra",
      show: true,
      render: (item) =>
        listAllNguonKhacChiTra?.find((o) => o.id === item.nguonKhacId)?.ten,
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.khongTinhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.khongTinhTien"] || 0}
          searchSelect={
            <Select
              data={CO_KHONG}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("dichVu.khongTinhTien")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.khongTinhTien")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      key: "dichVu.khongTinhTien",
      align: "center",
      i18Name: "danhMuc.khongTinhTien",
      show: true,
      render: (item) => {
        return <Checkbox checked={item?.khongTinhTien} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chuaGomTrongGiuong")}
          sort_key="chuaGomTrongGiuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chuaGomTrongGiuong"] || 0}
          searchSelect={
            <Select
              data={CO_KHONG}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("chuaGomTrongGiuong")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "chuaGomTrongGiuong",
      key: "chuaGomTrongGiuong",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="vatTuTaiSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["vatTuTaiSuDung"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.vatTuTaiSuDung")}
              onChange={onSearchInput("vatTuTaiSuDung")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.vatTuTaiSuDung")}
        />
      ),
      width: "130px",
      dataIndex: "vatTuTaiSuDung",
      key: "vatTuTaiSuDung",
      align: "center",
      i18Name: "danhMuc.vatTuTaiSuDung",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="chayMay"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chayMay"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.vatTuChayMay")}
              onChange={onSearchInput("chayMay")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.vatTuChayMay")}
        />
      ),
      width: "130px",
      dataIndex: "chayMay",
      key: "chayMay",
      align: "center",
      i18Name: "danhMuc.vatTuChayMay",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="trongPtTt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["trongPtTt"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.thuocPhauThuat")}
              data={HIEU_LUC}
              onChange={onSearchInput("trongPtTt")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.thuocPhauThuat")}
        />
      ),
      width: 130,
      dataIndex: "trongPtTt",
      key: "trongPtTt",
      align: "center",
      i18Name: "danhMuc.thuocPhauThuat",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="tenMoiThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenMoiThau"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.tenMoiThau").toLowerCase(),
              })}`}
              onChange={onSearchInput("tenMoiThau")}
            />
          }
          title={t("danhMuc.tenMoiThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "tenMoiThau",
      key: "tenMoiThau",
      i18Name: "danhMuc.tenMoiThau",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="quyetDinhThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinhThau"] || 0}
          search={
            <Input
              placeholder={`${t("danhMuc.nhapTitle", {
                title: t("danhMuc.quyetDinhThau").toLowerCase(),
              })}`}
              onChange={onSearchInput("quyetDinhThau")}
            />
          }
          title={t("danhMuc.quyetDinhThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "quyetDinhThau",
      key: "quyetDinhThau",
      i18Name: "danhMuc.quyetDinhThau",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="nhomThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["nhomThau"] || 0}
          searchSelect={
            <Select
              data={listNhomThau}
              placeholder={t("danhMuc.chonNhomThau")}
              onChange={onSearchInput("nhomThau")}
            />
          }
          title={t("danhMuc.nhomThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "nhomThau",
      key: "nhomThau",
      i18Name: "danhMuc.nhomThau",
      show: true,
      render: (item) => {
        return (listNhomThau || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="goiThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["goiThau"] || 0}
          searchSelect={
            <Select
              data={listGoiThau}
              placeholder={t("danhMuc.chonGoiThau")}
              onChange={onSearchInput("goiThau")}
            />
          }
          title={t("danhMuc.goiThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "goiThau",
      key: "goiThau",
      i18Name: "danhMuc.goiThau",
      show: true,
      render: (item) => {
        return (listGoiThau || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="thongTinThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["thongTinThau"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapThongTinThau")}
              onChange={onSearchInput("thongTinThau")}
            />
          }
          title={t("danhMuc.thongTinThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "thongTinThau",
      key: "thongTinThau",
      i18Name: "danhMuc.thongTinThau",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="tenTrungThau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenTrungThau"] || 0}
          search={
            <Input
              placeholder={t("kho.quyetDinhThau.nhapTenTrungThau")}
              onChange={onSearchInput("tenTrungThau")}
            />
          }
          title={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
        />
      ),
      align: "center",
      width: 160,
      dataIndex: "tenTrungThau",
      key: "tenTrungThau",
      i18Name: "kho.quyetDinhThau.tenHangHoaTrungThau",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.mucDichSuDung")}
          sort_key="dsMucDichSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dsMucDichSuDung"] || 0}
          searchSelect={
            <Select
              data={listDsMucDichSuDung}
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.mucDichSuDung").toLowerCase(),
              })}`}
              onChange={onSearchInput("dsMucDichSuDung")}
              hasAllOption={true}
              mode="multiple"
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "dsMucDichSuDung",
      key: "dsMucDichSuDung",
      i18Name: "danhMuc.mucDichSuDung",
      show: true,
      render: (item) => {
        let arr = [];
        if (item?.length && listDsMucDichSuDung) {
          item.forEach((mucDichSuDungItem) => {
            const tenMucDIchSuDung = listDsMucDichSuDung.find(
              (item) => item.id === mucDichSuDungItem
            );
            if (tenMucDIchSuDung) arr.push(tenMucDIchSuDung.ten);
          });
        }
        return arr.join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="loaiLamTron"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiLamTron || 0}
          title={t("danhMuc.loaiLamTron")}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonLoaiLamTron")}
              data={listLoaiLamTron}
              onChange={onSearchInput("loaiLamTron")}
            />
          }
        />
      ),
      width: 130,
      dataIndex: "loaiLamTron",
      key: "loaiLamTron",
      i18Name: "danhMuc.loaiLamTron",
      show: true,
      render: (item) => {
        return (listLoaiLamTron || []).find((i) => i.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="soNgayCanhBaoHsd"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soNgayCanhBaoHsd || 0}
          search={
            <Input
              placeholder={t("danhMuc.soNgayCanhBaoHsd")}
              onChange={onSearchInput("soNgayCanhBaoHsd")}
            />
          }
          title={t("danhMuc.soNgayCanhBaoHsd")}
        />
      ),
      width: 170,
      dataIndex: "soNgayCanhBaoHsd",
      key: "soNgayCanhBaoHsd",
      align: "center",
      i18Name: "danhMuc.soNgayCanhBaoHsd",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maPhieuLinh")}
          sort_key="phieuLinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phieuLinhId"] || 0}
          searchSelect={
            <Select
              data={listAllMaPhieuLinh}
              placeholder={t("danhMuc.maPhieuLinh")}
              onChange={onSearchInput("phieuLinhId")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "phieuLinh",
      key: "phieuLinh",
      i18Name: "danhMuc.maPhieuLinh",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.maTckt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.maTckt"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaTckt")}
              onChange={onSearchInput("dichVu.maTckt")}
            />
          }
          title={t("danhMuc.maTckt")}
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.maTckt",
      i18Name: "danhMuc.maTckt",
      show: true,
      align: "center",
      render: (item) => item?.maTckt,
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.tenTckt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenTckt"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenTckt")}
              onChange={onSearchInput("dichVu.tenTckt")}
            />
          }
          title={t("danhMuc.tenTckt")}
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.tenTckt",
      i18Name: "danhMuc.tenTckt",
      show: true,
      align: "center",
      render: (item) => item?.tenTckt,
    },
    {
      title: (
        <HeaderSearch
          sort_key="soVisa"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soVisa || 0}
          search={
            <Input
              placeholder={t("danhMuc.timSoVisa")}
              onChange={onSearchInput("soVisa")}
            />
          }
          title={t("danhMuc.soVisa")}
        />
      ),
      width: 150,
      dataIndex: "soVisa",
      key: "soVisa",
      i18Name: "danhMuc.soVisa",
      show: true,
      render: (item) => {
        return item && <div className="break-word">{item}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maVatTuThayThe")}
          sort_key="dsDichVuThayTheId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dsDichVuThayTheId"] || 0}
          searchSelect={
            <SelectLoadMore
              placeholder={t("common.timKiem")}
              api={dmDichVuKhoProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma} - ${i.ten}`,
                ten: `${i.ma} - ${i.ten}`,
              })}
              onChange={onSearchInput("dsDichVuThayTheId")}
              uniqByKey="value"
              // addValue={
              //   state.data?.dsDichVuThayThe?.length > 0
              //     ? state.data?.dsDichVuThayThe.map((i) => ({
              //         value: i.id,
              //         label: `${i.ma} - ${i.ten}`,
              //       }))
              //     : null
              // }
              // value={state.data?.dsDichVuThayThe?.map((i) => i.id)}
              addParam={{ dsLoaiDichVu: [LOAI_DICH_VU.VAT_TU] }}
              keySearch={"ma"}
              mode="multiple"
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "dsDichVuThayTheId",
      key: "dsDichVuThayTheId",
      i18Name: "danhMuc.maVatTuThayThe",
      show: true,
      render: (item, record) => {
        return record?.dsDichVuThayThe
          ?.map((el) => `${el.ma} - ${el.ten}`)
          .join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.chiDinhSlLe"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.chiDinhSlLe"] || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonKeSlLe")}
              onChange={onSearchInput("dichVu.chiDinhSlLe")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.choPhepKeSlLe")}
        />
      ),
      width: 130,
      dataIndex: "dichVu",
      key: "dichVu.chiDinhSlLe",
      align: "center",
      i18Name: "danhMuc.choPhepKeSlLe",
      show: true,
      render: (item) => {
        return <Checkbox checked={!!item?.chiDinhSlLe} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="maSinhPham"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maSinhPham || 0}
          search={
            <Input
              placeholder={t("danhMuc.maSinhPham")}
              onChange={onSearchInput("maSinhPham")}
            />
          }
          title={t("danhMuc.maSinhPham")}
        />
      ),
      width: 120,
      dataIndex: "maSinhPham",
      key: "maSinhPham",
      i18Name: "danhMuc.maSinhPham",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="maSinhHieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maSinhHieu || 0}
          search={
            <Input
              placeholder={t("danhMuc.maHieuSinhPham")}
              onChange={onSearchInput("maSinhHieu")}
            />
          }
          title={t("danhMuc.maHieuSinhPham")}
        />
      ),
      width: 150,
      dataIndex: "maSinhHieu",
      key: "maSinhHieu",
      i18Name: "danhMuc.maHieuSinhPham",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="phanLoaiDvKho"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phanLoaiDvKho"] || 0}
          searchSelect={
            <Select
              data={listAllPhanLoaiVatTu}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("phanLoaiDvKhoId")}
            />
          }
          title={t("danhMuc.phanLoaiVTYT")}
        />
      ),
      align: "center",
      width: 120,
      dataIndex: "phanLoaiDvKho",
      key: "phanLoaiDvKho",
      i18Name: "danhMuc.phanLoaiVTYT",
      show: true,
      render: (item) => {
        return item && item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["active"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonTinhTien")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.coHieuLuc")}
        />
      ),
      width: "130px",
      dataIndex: "active",
      key: "active",
      align: "center",
      ignore: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
  ];

  const listPanel = [
    {
      title: t("danhMuc.thongTinVatTu"),
      key: 1,
      render: () => {
        return (
          <LoadingWrapper
            configKey="dmVatTu.thongTinVatTu"
            cacheKey={CACHE_KEY.DM_VAT_TU_THONG_TIN_VAT_TU_CONFIG_FIELDS}
          >
            <FormServiceInfo
              listMeGrLv1={listMeGrLv1}
              listMeGrLv2={listMeGrLv2}
              listMeGrLv3={listMeGrLv3}
              listMeGrLv4={listMeGrLv4}
              listAllDonViTinh={listAllDonViTinh}
              // listQuocGia={listQuocGia}
              listAllXuatXu={listAllXuatXu}
              listNSX={listAllNhaSanXuat}
              listNCC={listAllNhaCungCap}
              listNhomChiPhiBh={listNhomChiPhiBh}
              listNguonKhacChiTra={listNguonKhacChiTra}
              listNhomDvCap1={listNhomDvCap1}
              listNhomDvCap2={listNhomDvCap2}
              listNhomDvCap3={listNhomDvCap3}
              getListMeGrLv2TongHop={getListMeGrLv2TongHop}
              getListMeGrLv3TongHop={getListMeGrLv3TongHop}
              roleSave={[ROLES["DANH_MUC"].VAT_TU_THEM]}
              roleEdit={[ROLES["DANH_MUC"].VAT_TU_SUA]}
              editStatus={
                editStatus
                  ? !checkRole([ROLES["DANH_MUC"].VAT_TU_SUA])
                  : !checkRole([ROLES["DANH_MUC"].VAT_TU_THEM])
              }
              currentItemRowParent={currentItem}
              optionalField={["nhomDichVuCap2Id"]}
              layerId={layerId}
              handleClickedBtnAdded={handleClickedBtnAdded}
              searchTongHopDichVuCap2ByCap1={searchTongHopDichVuCap2ByCap1}
              searchTongHopDichVuCap3ByCap2={searchTongHopDichVuCap3ByCap2}
            />
          </LoadingWrapper>
        );
      },
    },
    {
      title: t("danhMuc.nhomChiPhi"),
      key: 2,
      render: () => <NhomChiPhi dichVuId={currentItem?.id} />,
    },
  ];

  const onImportDuLieu = () => {
    refModalImportDMVatTu.current &&
      refModalImportDMVatTu.current.show({ isModalVisible: true });
  };

  const onExportDuLieu = async () => {
    try {
      showLoading();

      const keysToRemove = [
        "dichVu.loaiDichVu",
        "dichVu.ma",
        "dichVu.ten",
        "dichVu.donViTinhId",
        "dichVu.giaKhongBaoHiem",
        "dichVu.giaBaoHiem",
        "dichVu.giaPhuThu",
        "dichVu.tyLeBhTt",
        "dichVu.tyLeTtDv",
        "dichVu.nhomChiPhiBh",
        "dichVu.nhomDichVuCap1Id",
        "dichVu.nhomDichVuCap2Id",
        "dichVu.nhomDichVuCap3Id",
        "dichVu.maTuongDuong",
        "dichVu.tenTuongDuong",
        "dichVu.nguonKhacId",
        "dichVu.maTckt",
        "dichVu.tenTckt",
        "dichVu.khongTinhTien",
        "dichVu.chiDinhSlLe",
      ];

      const stringToBoolean = (value) => {
        if (value === "true") return true;
        if (value === "false") return false;
        return value;
      };

      const filteredData = Object.fromEntries(
        Object.entries(dataSearch)
          .filter(([key]) => !keysToRemove.includes(key))
          .map(([key, value]) => [key, stringToBoolean(value)])
      );

      const dichVu = Object.fromEntries(
        Object.entries({
          loaiDichVu: LOAI_DICH_VU.VAT_TU,
          ma: dataSearch["dichVu.ma"],
          ten: dataSearch["dichVu.ten"],
          donViTinhId: dataSearch["dichVu.donViTinhId"],
          giaKhongBaoHiem: dataSearch["dichVu.giaKhongBaoHiem"],
          giaBaoHiem: dataSearch["dichVu.giaBaoHiem"],
          giaPhuThu: dataSearch["dichVu.giaPhuThu"],
          tyLeBhTt: dataSearch["dichVu.tyLeBhTt"],
          tyLeTtDv: dataSearch["dichVu.tyLeTtDv"],
          nhomChiPhiBh: dataSearch["dichVu.nhomChiPhiBh"],
          nhomDichVuCap1Id: dataSearch["dichVu.nhomDichVuCap1Id"],
          nhomDichVuCap2Id: dataSearch["dichVu.nhomDichVuCap2Id"],
          nhomDichVuCap3Id: dataSearch["dichVu.nhomDichVuCap3Id"],
          maTuongDuong: dataSearch["dichVu.maTuongDuong"],
          tenTuongDuong: dataSearch["dichVu.tenTuongDuong"],
          nguonKhacId: dataSearch["dichVu.nguonKhacId"],
          maTckt: dataSearch["dichVu.maTckt"],
          tenTckt: dataSearch["dichVu.tenTckt"],
          khongTinhTien: stringToBoolean(dataSearch["dichVu.khongTinhTien"]),
          chiDinhSlLe: stringToBoolean(dataSearch["dichVu.chiDinhSlLe"]),
        }).filter(([_, value]) => value !== "" && value !== null)
      );

      const payload = Object.fromEntries(
        Object.entries({
          ...filteredData,
          ...(Object.keys(dichVu).length > 0 && { dichVu }),
        }).filter(([_, value]) => value !== "" && value !== null)
      );

      await onExportDmVatTu(payload);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onExportVatTuMoi = () => {
    refModalExportVatTuMoi.current &&
      refModalExportVatTuMoi.current.show(
        MAU_XUAT_DU_LIEU_XML.XUAT_DU_LIEU_DM_VTYT
      );
  };

  const onImportDuLieuNhomChiPhi = () => {
    refModalImportNhomChiPhi.current &&
      refModalImportNhomChiPhi.current.show({ isModalVisible: true });
  };

  const onExportDuLieuNhomChiPhi = () => {
    showLoading();
    onExportNhomChiPhi({ dsBang: "dm_dv_nhom_chi_phi_100" }).finally(() => {
      hideLoading();
    });
  };

  const onImportDuLieuKichCoVt = () => {
    refModalImportKichCoVt.current &&
      refModalImportKichCoVt.current.show({
        isModalVisible: true,
        dichVuId: currentItem?.id,
      });
  };

  const onExportDuLieuKichCoVt = () => {
    showLoading();
    onExportKichCoVt({ dsBang: "dm_kich_co_vt" }).finally(() => {
      hideLoading();
    });
  };

  const importModalConfigs = [
    {
      key: "VatTu",
      onImport: onImportDMVatTu,
      ref: refModalImportDMVatTu,
    },
    {
      key: "NhomChiPhi",
      onImport: onImportNhomChiPhi,
      ref: refModalImportNhomChiPhi,
    },
    {
      key: "KichCoVt",
      onImport: onImportKichCoVt,
      ref: refModalImportKichCoVt,
    },
  ];

  const menuMultiTab = () => (
    <Menu
      items={[
        ...(checkRole([ROLES["DANH_MUC"].XUAT_DANH_SACH_VTYT_MOI])
          ? [
              {
                key: 1,
                label: (
                  <a onClick={onExportVatTuMoi}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>{t("danhMuc.xuatDanhSachVatTuDayCong")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        {
          key: 2,
          label: (
            <a onClick={onImportDuLieu}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcUpload />
                <span>
                  {t("danhMuc.nhapDuLieuTitle", {
                    title: TEN_LOAI_DICH_VU,
                  })}
                </span>
              </div>
            </a>
          ),
        },
        {
          key: 3,
          label: (
            <a onClick={onExportDuLieu}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcDownload />
                <span>
                  {t("danhMuc.xuatDuLieuTitle", {
                    title: TEN_LOAI_DICH_VU,
                  })}
                </span>
              </div>
            </a>
          ),
        },
        ...(onImportNhomChiPhi
          ? [
              {
                key: 6,
                label: (
                  <a onClick={onImportDuLieuNhomChiPhi}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.nhomChiPhi"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportNhomChiPhi
          ? [
              {
                key: 7,
                label: (
                  <a onClick={onExportDuLieuNhomChiPhi}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.nhomChiPhi"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportKichCoVt
          ? [
              {
                key: 8,
                label: (
                  <a onClick={onImportDuLieuKichCoVt}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.kichCoVatTu"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExportKichCoVt
          ? [
              {
                key: 9,
                label: (
                  <a onClick={onExportDuLieuKichCoVt}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.kichCoVatTu"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
      ]}
    />
  );

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const onHandleSizeChange = (size) => {
    onSizeChange({ size: size });
  };

  const onShowAndHandleUpdate = (data = {}) => {
    setEditStatus(true);
    getListAllKichCo(
      { page: "", size: "", active: true, dichVuId: data?.id },
      { saveCache: false }
    );
    getListVatTuBo({
      vatTuBoId: data.id,
      "dichVu.loaiDichVu": LOAI_DICH_VU.VAT_TU,
    });
    updateData({
      currentItem: data,
    });
  };
  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        onShowAndHandleUpdate(record);
        getListMeGrLv2TongHop({
          page: "",
          size: "",
          active: true,
          sort: "ten,asc",
          loaiDichVu: LOAI_DICH_VU.VAT_TU,
        });
      },
    };
  };

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    updateData({
      currentItem: {},
    });
  };
  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };

  return (
    <Main>
      <BaseDm3
        breadcrumb={[
          { title: t("danhMuc.danhMuc"), link: "/danh-muc" },
          {
            title: t("danhMuc.danhMucVatTu"),
            link: "/danh-muc/vat-tu",
          },
        ]}
      >
        <Col
          {...(!state.showFullTable
            ? collapseStatus
              ? TABLE_LAYOUT_COLLAPSE
              : TABLE_LAYOUT
            : null)}
          span={state.showFullTable ? 24 : null}
          className={`pr-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
        >
          <TableWrapper
            title={TEN_LOAI_DICH_VU}
            scroll={{ x: 1000 }}
            styleMain={{ marginTop: 0 }}
            classNameRow={"custom-header"}
            styleContainerButtonHeader={{
              display: "flex",
              width: "100%",
              justifyContent: "flex-end",
              alignItems: "center",
              paddingRight: 35,
            }}
            menuMultiTab={menuMultiTab}
            buttonHeader={
              checkRole([ROLES["DANH_MUC"].VAT_TU_THEM])
                ? [
                    {
                      content: (
                        <Button
                          type="success"
                          onClick={handleClickedBtnAdded}
                          rightIcon={<SVG.IcAdd />}
                        >
                          {t("common.themMoiF1")}
                        </Button>
                      ),
                    },
                    {
                      className: `btn-change-full-table ${
                        state.showFullTable ? "small" : "large"
                      }`,
                      title: state.showFullTable ? (
                        <SVG.IcShowThuNho />
                      ) : (
                        <SVG.IcShowFull />
                      ),
                      onClick: handleChangeshowTable,
                    },
                    {
                      className: "btn-collapse",
                      title: collapseStatus ? (
                        <SVG.IcExtend />
                      ) : (
                        <SVG.IcCollapse />
                      ),
                      onClick: handleCollapsePane,
                    },
                  ]
                : [
                    {
                      className: `btn-change-full-table ${
                        state.showFullTable ? "small" : "large"
                      }`,
                      title: state.showFullTable ? (
                        <SVG.IcShowThuNho />
                      ) : (
                        <SVG.IcShowFull />
                      ),
                      onClick: handleChangeshowTable,
                    },
                    {
                      className: "btn-collapse",
                      title: collapseStatus ? (
                        <SVG.IcExtend />
                      ) : (
                        <SVG.IcCollapse />
                      ),
                      onClick: handleCollapsePane,
                    },
                  ]
            }
            columns={columns}
            dataSource={listData}
            onRow={onRow}
            ref={refSettings}
            tableName="table_DANHMUC_DichVuVatTu"
            dataEditDefault={currentItem}
          />
          {totalElements ? (
            <Pagination
              listData={listData || []}
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              total={totalElements}
              onShowSizeChange={onHandleSizeChange}
              style={{ flex: 1, justifyContent: "flex-end" }}
            />
          ) : null}
        </Col>
        {!state.showFullTable && (
          <Col
            {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
            className={`mt-3 ${
              state.changeShowFullTbale ? "" : "transition-ease"
            }`}
            style={
              state.isSelected
                ? { border: "2px solid #c1d8fd", borderRadius: 20 }
                : {}
            }
          >
            <MultiLevelTab
              defaultActiveKey={1}
              listPanel={listPanel}
              isBoxTabs={true}
            ></MultiLevelTab>
          </Col>
        )}
        <ModalExportDanhMucMoi ref={refModalExportVatTuMoi} />
        {importModalConfigs.map(({ key, onImport, ref }) => (
          <ModalImport key={key} onImport={onImport} ref={ref} />
        ))}
      </BaseDm3>
    </Main>
  );
};

export default DanhMucVatTu;
