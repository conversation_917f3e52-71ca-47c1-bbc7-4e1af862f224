import React, { useState, useEffect, useRef, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { lowerFirst, pick } from "lodash";
import classNames from "classnames";
import { Form, Input, InputNumber, message } from "antd";
import {
  useEnum,
  useStore,
  useConfirm,
  useLoading,
  useListAll,
  useThietLap,
} from "hooks";
import { Select, Checkbox, Button, SelectLoadMore } from "components";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { isNumber, openInNewTab, parseFloatNumberFromString } from "utils";
import { checkRole } from "lib-utils/role-utils";
import TableChiTietKichCo from "./TableChiTietKichCo";
import TableChiTietBo from "./TableChiTietBo";
import TabPanel from "components/MultiLevelTab/TabPanel";
import { InputNumberFormat } from "components/common";
import {
  ENUM,
  HOTKEY,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import ModalSapXepTruong from "pages/danhMuc1n/danhMucThuoc/components/ModalSapXepTruong";
import { listAllFields } from "pages/application/TuyChinhGiaoDienPhamMem/ThietLapDanhMuc/config";
import { Main } from "../styled";
import ModalCapNhatGia from "pages/kho/components/ModalCapNhatGia";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";

function FormServiceInfo({
  hiddenField = [],
  optionalField = [],
  configFields,
  setUserConfigFields,
  userConfigFields,
  ...props
}) {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({ bhyt: true });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const fieldOrderMap = useMemo(() => {
    const map = {};
    if (userConfigFields && userConfigFields.length > 0) {
      userConfigFields.forEach((fieldName, index) => {
        map[fieldName] = index;
      });
    }
    return map;
  }, [userConfigFields]);

  const isFieldVisible = useMemo(() => {
    return (fieldName) => {
      if (!userConfigFields || userConfigFields.length === 0) return true;
      return fieldName in fieldOrderMap;
    };
  }, [userConfigFields, fieldOrderMap]);

  const getFieldOrder = (fieldName) => {
    return fieldName in fieldOrderMap ? fieldOrderMap[fieldName] : 999;
  };

  const {
    listMeGrLv1,
    listMeGrLv2,
    listMeGrLv3,
    listMeGrLv4,
    listAllDonViTinh,
    listAllXuatXu,
    listNSX,
    listNCC,
    listNguonKhacChiTra,
    listNhomDvCap1,
    listNhomDvCap2,
    listNhomDvCap3,
    roleSave,
    roleEdit,
    editStatus,
    currentItemRowParent,
    layerId,
    handleClickedBtnAdded,
    searchTongHopDichVuCap2ByCap1,
    searchTongHopDichVuCap3ByCap2,
  } = props;

  const [listGoiThau] = useEnum(ENUM.GOI_THAU);
  const [listnhomThau] = useEnum(ENUM.NHOM_THAU);
  const [listDsMucDichSuDung] = useEnum(ENUM.MUC_DICH_SU_DUNG);
  const [listLoaiLamTron] = useEnum(ENUM.LOAI_LAM_TRON);
  const currentItem = useStore("danhMucVatTu.currentItem");
  const listAllMaPhieuLinh = useStore("maPhieuLinh.listAllMaPhieuLinh");
  const [listAllPhanLoaiVatTu] = useListAll("phanLoaiVatTu", {}, true);
  const refClickBtnAdd = useRef(null);
  const refClickBtnSave = useRef(null);
  const refModalCapNhatGia = useRef(null);
  const refModalSapXepTruong = useRef(null);
  const [form] = Form.useForm();

  const [dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT
  );

  const {
    danhMucVatTu: { createOrEdit, updateData, createBatch, onUpdate },
    danhSachDichVuKho: { updateData: updateDataDvKho },
    maPhieuLinh: { getListAllMaPhieuLinh },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
  } = useDispatch();

  useEffect(() => {
    loadCurrentItem(currentItem);
    if (currentItem && Object.keys(currentItem).length <= 0) {
      form.resetFields();
    }
  }, [currentItem]);

  useEffect(() => {
    getListAllMaPhieuLinh({
      page: "",
      size: "",
      active: true,
      loaiDichVu: LOAI_DICH_VU.VAT_TU,
    });
  }, []);

  useEffect(() => {
    if (layerId) {
      onAddLayer({ layerId });
      onRegisterHotkey({
        layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F1, //F1
            onEvent: () => {
              refClickBtnAdd.current && refClickBtnAdd.current();
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: (e) => {
              refClickBtnSave.current && refClickBtnSave.current(e);
            },
          },
        ],
      });
      return () => {
        onRemoveLayer({ layerId });
      };
    }
  }, [layerId]);

  const nhomDvKhoCap1Id = Form.useWatch("nhomDvKhoCap1Id", form);
  const nhomDvKhoCap2Id = Form.useWatch("nhomDvKhoCap2Id", form);
  const nhomDvKhoCap3Id = Form.useWatch("nhomDvKhoCap3Id", form);

  const listMeGrLv2Memo = useMemo(() => {
    if (nhomDvKhoCap1Id) {
      return (listMeGrLv2 || []).filter(
        (i) => i.nhomDvKhoCap1Id === nhomDvKhoCap1Id
      );
    }
    return listMeGrLv2;
  }, [nhomDvKhoCap1Id, listMeGrLv2]);

  const listMeGrLv3Memo = useMemo(() => {
    if (nhomDvKhoCap2Id || nhomDvKhoCap1Id) {
      return (listMeGrLv3 || []).filter(
        (i) =>
          (nhomDvKhoCap2Id ? i.nhomDvKhoCap2Id === nhomDvKhoCap2Id : true) &&
          (nhomDvKhoCap1Id ? i.nhomDvKhoCap1Id === nhomDvKhoCap1Id : true)
      );
    }
    return listMeGrLv3;
  }, [nhomDvKhoCap2Id, nhomDvKhoCap1Id, listMeGrLv3]);

  const listMeGrLv4Memo = useMemo(() => {
    if (nhomDvKhoCap3Id || nhomDvKhoCap2Id || nhomDvKhoCap1Id) {
      return (listMeGrLv4 || []).filter(
        (i) =>
          (nhomDvKhoCap3Id ? i.nhomDvKhoCap3Id === nhomDvKhoCap3Id : true) &&
          (nhomDvKhoCap2Id ? i.nhomDvKhoCap2Id === nhomDvKhoCap2Id : true) &&
          (nhomDvKhoCap1Id ? i.nhomDvKhoCap1Id === nhomDvKhoCap1Id : true)
      );
    }
    return listMeGrLv4;
  }, [nhomDvKhoCap3Id, nhomDvKhoCap2Id, nhomDvKhoCap1Id, listMeGrLv4]);

  const nhomDichVuCap1Id = Form.useWatch("nhomDichVuCap1Id", form);
  const nhomDichVuCap2Id = Form.useWatch("nhomDichVuCap2Id", form);

  useEffect(() => {
    searchTongHopDichVuCap3ByCap2(nhomDichVuCap2Id);
  }, [nhomDichVuCap2Id]);

  useEffect(() => {
    searchTongHopDichVuCap2ByCap1(nhomDichVuCap1Id);
  }, [nhomDichVuCap1Id]);

  const handleValuesChange = (changeValues) => {
    if (changeValues.stentPhuThuoc) {
      form.setFieldsValue({ kyThuatCao: true });
    }
    if (changeValues.kyThuatCao === false) {
      form.setFieldsValue({ stentPhuThuoc: false });
    }
    if (changeValues.hasOwnProperty("giaBaoHiem") && !changeValues.giaBaoHiem) {
      form.setFields([
        {
          name: "tyLeBhTt",
          errors: [],
        },
      ]);
    }
    if (changeValues.hasOwnProperty("tyLeBhTt")) {
      const tyLeBhTt = changeValues.tyLeBhTt
        ? Number(changeValues.tyLeBhTt)
        : null;
      if (!tyLeBhTt) {
        form.setFields([
          {
            name: "nhomDvKhoCap2Id",
            errors: [],
          },
        ]);
      }
    }

    if (changeValues.hasOwnProperty("nhomDvKhoCap2Id")) {
      form.setFieldsValue({
        nhomDvKhoCap3Id: null,
        nhomDvKhoCap4Id: null,
      });
    }
    if (changeValues.hasOwnProperty("nhomDvKhoCap3Id")) {
      form.setFieldsValue({
        nhomDvKhoCap4Id: null,
      });
    }

    if (changeValues.hasOwnProperty("nhomDvKhoCap4Id") && !currentItem?.id) {
      const selectedNhomCap4 = listMeGrLv4.find(
        (i) => i.id === changeValues.nhomDvKhoCap4Id
      );

      form.setFieldsValue({
        khongTinhTien: selectedNhomCap4?.khongTinhTien ?? false,
        chuaGomTrongGiuong: selectedNhomCap4?.chuaGomTrongGiuong ?? false,
      });
    }
  };

  const loadCurrentItem = (itemVatTu) => {
    if (itemVatTu && Object.keys(itemVatTu).length) {
      const {
        dichVu: {
          ma,
          ten,
          donViTinhId: dvtThuCapId,
          nguonKhacId,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          tyLeBhTt,
          tyLeTtDv,
          maTuongDuong,
          tenTuongDuong,
          nhomChiPhiBh,
          giaKhongBaoHiem,
          giaBaoHiem,
          giaPhuThu,
          chiDinhSlLe,
          mienPhiGiamDocDuyet,
          maTckt,
          tenTckt,
          khongTinhTien,
        } = {},
        active,
        id,
        vatTuBo,
        vatTuKichCo,
        dsMucDichSuDung,
        soNgayCanhBaoHsd,
        phieuLinhId,
        tenTrungThau,
        maSinhHieu,
        maSinhPham,
        chuaGomTrongGiuong,
        dsDichVuThayTheId,
      } = itemVatTu || {};

      const data = {
        ...itemVatTu,
        id,
        ma,
        ten,
        dvtThuCapId,
        nguonKhacId: nguonKhacId || null,
        dsMucDichSuDung: dsMucDichSuDung || [],
        dsDichVuThayTheId: dsDichVuThayTheId || [],
        nhomDichVuCap1Id,
        nhomDichVuCap2Id,
        nhomDichVuCap3Id,
        maTuongDuong,
        tyLeBhTt,
        tyLeTtDv,
        tenTuongDuong,
        nhomChiPhiBh,
        khongTinhTien,
        chuaGomTrongGiuong,
        active,
        vatTuBo,
        vatTuKichCo,
        giaKhongBaoHiem,
        giaBaoHiem,
        giaPhuThu,
        soNgayCanhBaoHsd,
        phieuLinhId,
        chiDinhSlLe,
        tenTrungThau,
        maSinhHieu,
        maSinhPham,
        mienPhiGiamDocDuyet,
        maTckt,
        tenTckt,
      };
      form.setFieldsValue(data);
      setState({ vatTuBo, vatTuKichCo, bhyt: data.bhyt, data });
    } else {
      form.resetFields();

      setState({ vatTuBo: false, vatTuKichCo: false, bhyt: true, data: null });
    }
  };

  const onCancel = () => {
    loadCurrentItem(currentItem);
  };
  const onSave = (e) => {
    e.preventDefault();
    form
      .validateFields()
      .then((values) => {
        const mergeValues = pick(
          { ...state.data, ...values },
          listAllFields["dmVatTu.thongTinVatTu"].map((i) => i.name)
        );

        const {
          ma,
          ten,
          dvtThuCapId: donViTinhId,
          nguonKhacId,
          dsMucDichSuDung,
          maTuongDuong,
          nhomChiPhiBh,
          nhomDichVuCap1Id,
          nhomDichVuCap2Id,
          nhomDichVuCap3Id,
          tenTuongDuong,
          tyLeBhTt,
          tyLeTtDv,
          active,
          khongTinhTien,
          giaKhongBaoHiem,
          giaBaoHiem,
          giaPhuThu,
          soNgayCanhBaoHsd,
          giaNhapSauVat,
          phieuLinhId,
          chiDinhSlLe,
          mienPhiGiamDocDuyet,
          maTckt,
          tenTckt,
          chuaGomTrongGiuong,
          ...rest
        } = mergeValues;
        const data = {
          dichVu: {
            ten,
            ma,
            donViTinhId,
            nguonKhacId,
            maTuongDuong,
            nhomChiPhiBh,
            nhomDichVuCap1Id,
            nhomDichVuCap2Id,
            nhomDichVuCap3Id,
            tenTuongDuong,
            tyLeBhTt: parseFloatNumberFromString(tyLeBhTt),
            tyLeTtDv: parseFloatNumberFromString(tyLeTtDv),
            khongTinhTien,
            loaiDichVu: LOAI_DICH_VU.VAT_TU,
            giaKhongBaoHiem: parseFloatNumberFromString(giaKhongBaoHiem),
            giaBaoHiem: parseFloatNumberFromString(giaBaoHiem),
            giaPhuThu: parseFloatNumberFromString(giaPhuThu),
            chiDinhSlLe,
            mienPhiGiamDocDuyet,
            maTckt,
            tenTckt,
          },
          active,
          ...rest,
          id: currentItem?.id,
          dsMucDichSuDung:
            dsMucDichSuDung?.length > 0 ? dsMucDichSuDung : [10, 20],
          soNgayCanhBaoHsd,
          phieuLinhId,
          giaNhapSauVat: parseFloatNumberFromString(giaNhapSauVat),
          bhyt: state.bhyt,
          chuaGomTrongGiuong,
        };

        showLoading();
        createOrEdit(data)
          .then((res) => {
            if (currentItem?.id) {
              updateData({ currentItem: { ...res } });
            } else {
              form.resetFields();
              let bodyVatTuCon = (state?.listVatTuCon || []).map((item) => {
                return { ...item, vatTuBoId: res?.id };
              });
              bodyVatTuCon.length && createBatch(bodyVatTuCon);
            }
          })
          .catch((err) => {})
          .finally(() => {
            hideLoading();
          });
      })
      .catch((error) => {});
  };

  refClickBtnSave.current = onSave;
  refClickBtnAdd.current = handleClickedBtnAdded;

  const refAutoFocus = useRef(null);
  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [currentItem]);

  const onChange = (key1, key2) => (e, list) => {
    setState({ data: { ...state.data, [key1]: e, [key2]: list } });
  };

  const onChangeCheckBox = (key) => (e) => {
    setState({ [key]: e?.target?.checked });
  };
  const selectedVatTuCon = (item) => {
    setState({ listVatTuCon: item });
  };

  const onShowCapNhatGia = () => {
    const dichVu = currentItem?.dichVu;
    dichVu.dichVuId = dichVu.id;
    updateDataDvKho({ selectedHangHoa: dichVu });
    refModalCapNhatGia.current?.show(dichVu);
  };

  const onHuongBaoHiem = (isOk) => () => {
    showConfirm(
      {
        title: t("common.thongBao"),
        content: isOk
          ? t("danhMuc.banCoChacChanBatXacNhanHuongBaoHiemKhong")
          : t("danhMuc.banCoChacChanTatXacNhanHuongBaoHiemKhong"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        if (currentItem.id) {
          onUpdate({ id: currentItem.id, bhyt: isOk }).then(() => {
            setState({ bhyt: !state.bhyt });
          });
        } else {
          setState({ bhyt: !state.bhyt });
        }
      }
    );
  };

  const formFields = useMemo(() => {
    const allFields = [
      {
        name: "ma",
        component: (
          <Form.Item
            label={t("danhMuc.maVatTu")}
            name="ma"
            rules={
              dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT?.eval()
                ? []
                : [
                    {
                      required: true,
                      message: t("danhMuc.vuiLongNhapMaVatTu"),
                    },
                    {
                      max: 200,
                      message: t("danhMuc.vuiLongNhapMaVatTuKhongQua200KyTu"),
                    },
                    {
                      whitespace: true,
                      message: t("danhMuc.vuiLongNhapMaVatTu"),
                    },
                  ]
            }
          >
            <Input
              ref={refAutoFocus}
              autoFocus={true}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapMaVatTu")}
              disabled={!!dataTU_DONG_TAO_MA_THUOC_VAT_TU_HOA_CHAT?.eval()}
            />
          </Form.Item>
        ),
      },
      {
        name: "ten",
        component: (
          <Form.Item
            label={t("danhMuc.tenVatTu")}
            name="ten"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTenVatTu"),
              },
              {
                max: 1000,
                message: t("danhMuc.vuiLongNhapTenVatTuKhongQua1000KyTu"),
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapTenVatTu"),
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenVatTu")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDvKhoCap1Id",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-vat-tu")}
              >
                {t("danhMuc.nhomVatTuCap1")}
              </div>
            }
            name="nhomDvKhoCap1Id"
          >
            <Select
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap1").toLowerCase(),
              })}`}
              data={listMeGrLv1}
              onChange={(e) => {
                form.setFieldsValue({
                  nhomDvKhoCap2Id: null,
                  nhomDvKhoCap3Id: null,
                  nhomDvKhoCap4Id: null,
                });
              }}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDvKhoCap2Id",
        component: (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.tyLeBhTt !== currentValues.tyLeBhTt
            }
          >
            {({ getFieldValue }) => {
              const tyLeBhTt = getFieldValue("tyLeBhTt");
              return (
                <Form.Item
                  label={
                    <div
                      className={classNames("cursor-pointer", {
                        color: tyLeBhTt && tyLeBhTt > 0,
                      })}
                    >
                      {t("danhMuc.nhomVatTuCap2")}
                    </div>
                  }
                  name="nhomDvKhoCap2Id"
                  rules={[
                    {
                      required: tyLeBhTt && tyLeBhTt > 0,
                      message: t("danhMuc.vuiLongChonTitle", {
                        title: lowerFirst(t("danhMuc.nhomVatTuCap2")),
                      }),
                    },
                  ]}
                >
                  <Select
                    placeholder={`${t("danhMuc.chonTitle", {
                      title: t("danhMuc.nhomVatTuCap2").toLowerCase(),
                    })}`}
                    data={listMeGrLv2Memo}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        ),
      },
      {
        name: "nhomDvKhoCap3Id",
        component: (
          <Form.Item label={t("danhMuc.nhomVatTuCap3")} name="nhomDvKhoCap3Id">
            <Select
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap3").toLowerCase(),
              })}`}
              data={listMeGrLv3Memo}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDvKhoCap4Id",
        component: (
          <Form.Item label={t("danhMuc.nhomVatTuCap4")} name="nhomDvKhoCap4Id">
            <Select
              placeholder={`${t("danhMuc.chonTitle", {
                title: t("danhMuc.nhomVatTuCap4").toLowerCase(),
              })}`}
              data={listMeGrLv4Memo}
            />
          </Form.Item>
        ),
      },
      {
        name: "maKyHieu",
        component: (
          <Form.Item label={t("danhMuc.maKyHieuTenThuongMai")} name="maKyHieu">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaKyHieu")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dvtSoCapId",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/don-vi-tinh")}
              >
                {t("danhMuc.donViSoCap")}
              </div>
            }
            name="dvtSoCapId"
          >
            <Select
              placeholder={t("danhMuc.chonDonViSoCap")}
              data={listAllDonViTinh}
            />
          </Form.Item>
        ),
      },
      {
        name: "dvtThuCapId",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/don-vi-tinh")}
              >
                {t("danhMuc.donViThuCap")}
              </div>
            }
            name="dvtThuCapId"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonDonViThuCap"),
              },
            ]}
          >
            <Select
              placeholder={t("danhMuc.chonDonViThuCap")}
              data={listAllDonViTinh}
            />
          </Form.Item>
        ),
      },
      {
        name: "heSoDinhMuc",
        component: (
          <Form.Item
            label={t("danhMuc.heSoDinhMuc")}
            name="heSoDinhMuc"
            initialValue={1}
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapHeSoDinhMuc"),
              },
            ]}
          >
            <InputNumber
              min={1}
              style={{ width: "100%" }}
              placeholder={t("danhMuc.nhapHeSoDinhMuc")}
              disabled={
                Object.keys(currentItem).length > 0 &&
                !checkRole([ROLES["DANH_MUC"].XEM_SUA_HE_SO_DINH_MUC])
              }
            />
          </Form.Item>
        ),
      },
      {
        name: "thongSoKyThuat",
        component: (
          <Form.Item label={t("danhMuc.thongSoKyThuat")} name="thongSoKyThuat">
            <Input
              className="input-option"
              style={{ width: "100%" }}
              placeholder={t("danhMuc.nhapThongSoKyThuat")}
            />
          </Form.Item>
        ),
      },
      {
        name: "quyCach",
        component: (
          <Form.Item label={t("danhMuc.quyCach")} name="quyCach">
            <Input
              className="input-option"
              style={{ width: "100%" }}
              placeholder={t("danhMuc.nhapQuyCach")}
            />
          </Form.Item>
        ),
      },
      {
        name: "xuatXuId",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/xuat-xu")}
              >
                {t("danhMuc.nuocSanXuat")}
              </div>
            }
            name="xuatXuId"
          >
            <Select
              data={listAllXuatXu}
              placeholder={t("danhMuc.chonNuocSanXuat")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhaSanXuatId",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/doi-tac")}
              >
                {t("common.nhaSanXuat")}
              </div>
            }
            name="nhaSanXuatId"
          >
            <Select data={listNSX} placeholder={t("danhMuc.chonNhaSanXuat")} />
          </Form.Item>
        ),
      },
      {
        name: "nhaCungCapId",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/doi-tac")}
              >
                {t("danhMuc.nhaCungCap")}
              </div>
            }
            name="nhaCungCapId"
          >
            <Select data={listNCC} placeholder={t("danhMuc.chonNhaCungCap")} />
          </Form.Item>
        ),
      },
      {
        name: "giaNhapSauVat",
        component: (
          <Form.Item label={t("danhMuc.giaNhap")} name="giaNhapSauVat">
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapGiaNhap")}
              decimalScale={3}
              allowNegative={false}
            />
          </Form.Item>
        ),
      },
      {
        name: "giaKhongBaoHiem",
        component: (
          <Form.Item
            label={t("danhMuc.giaKhongBaoHiem")}
            name="giaKhongBaoHiem"
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapGia")}
              decimalScale={3}
            />
          </Form.Item>
        ),
      },
      {
        name: "giaBaoHiem",
        component: (
          <Form.Item label={t("danhMuc.giaBaoHiem")} name="giaBaoHiem">
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapGia")}
              decimalScale={3}
            />
          </Form.Item>
        ),
      },
      {
        name: "giaPhuThu",
        component: (
          <Form.Item label={t("danhMuc.giaPhuThu")} name="giaPhuThu">
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.nhapGia")}
              decimalScale={3}
            />
          </Form.Item>
        ),
      },
      {
        name: "tranBaoHiem",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.tranBaoHiem")} </div>}
            name="tranBaoHiem"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTranBaoHiem")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tyLeBhTt",
        component: (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.giaBaoHiem !== currentValues.giaBaoHiem
            }
          >
            {({ getFieldValue }) => {
              return (
                <Form.Item
                  label={
                    <div className="color">{t("danhMuc.tyLeBhThanhToan")} </div>
                  }
                  name="tyLeBhTt"
                  rules={[
                    {
                      required: isNumber(getFieldValue("giaBaoHiem")),
                      message: t("danhMuc.vuiLongNhapTitle", {
                        title: lowerFirst(t("danhMuc.tyLeBhThanhToan")),
                      }),
                    },
                  ]}
                >
                  <InputNumberFormat
                    className="input-option"
                    placeholder={t("danhMuc.nhapTyLeBhThanhToan")}
                    disabled={
                      Object.keys(currentItem).length > 0 &&
                      !checkRole([
                        ROLES["DANH_MUC"].XEM_SUA_TY_LE_THANH_TOAN_BAO_HIEM,
                      ])
                    }
                    allowNegative={false}
                    decimalScale={0}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        ),
      },
      {
        name: "tyLeTtDv",
        component: (
          <Form.Item
            label={t("danhMuc.tyLeThanhToanDichVu")}
            name="tyLeTtDv"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTyLeThanhToanDichVu"),
              },
            ]}
          >
            <InputNumberFormat
              className="input-option"
              placeholder={t("danhMuc.tyLeThanhToanDichVu")}
              disabled={
                Object.keys(currentItem).length > 0 &&
                !checkRole([ROLES["DANH_MUC"].XEM_SUA_TY_LE_THANH_TOAN_DICH_VU])
              }
              allowNegative={false}
              decimalScale={0}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDichVuCap1Id",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=1")}
              >
                {t("danhMuc.nhomDVCap1")}
              </div>
            }
            name="nhomDichVuCap1Id"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonDichVuCap1"),
              },
            ]}
          >
            <Select
              data={listNhomDvCap1}
              placeholder={t("danhMuc.chonNhomDichVuCap1")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDichVuCap2Id",
        component: (
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=2")}
              >
                {t("danhMuc.nhomDVCap2")}
              </div>
            }
            name="nhomDichVuCap2Id"
            rules={
              optionalField.includes("nhomDichVuCap2Id")
                ? []
                : [
                    {
                      required: true,
                      message: t("danhMuc.vuiLongChonDichVuCap2"),
                    },
                  ]
            }
          >
            <Select
              data={listNhomDvCap2}
              placeholder={t("danhMuc.chonNhomDichVuCap2")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomDichVuCap3Id",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=3")}
              >
                {t("danhMuc.nhomDVCap3")}
              </div>
            }
            name="nhomDichVuCap3Id"
          >
            <Select
              data={listNhomDvCap3}
              placeholder={t("danhMuc.chonNhomDichVuCap3")}
            />
          </Form.Item>
        ),
      },
      {
        name: "maTuongDuong",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.maTuongDuong")} </div>}
            name="maTuongDuong"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaTuongDuong")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenTuongDuong",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.tenTuongDuong")} </div>}
            name="tenTuongDuong"
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTenTuongDuong")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dsMucDichSuDung",
        component: (
          <Form.Item
            label={t("danhMuc.mucDichSuDung")}
            name="dsMucDichSuDung"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonMucDichSuDung"),
              },
            ]}
          >
            <Select
              data={listDsMucDichSuDung}
              placeholder={t("danhMuc.chonMucDichSuDung")}
              mode="multiple"
            />
          </Form.Item>
        ),
      },
      {
        name: "nguonKhacId",
        component: (
          <Form.Item label={t("danhMuc.nguonChiTraKhac")} name="nguonKhacId">
            <Select
              data={listNguonKhacChiTra}
              placeholder={t("danhMuc.chonNguonChiTraKhac")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenMoiThau",
        component: (
          <Form.Item label={t("danhMuc.tenMoiThau")} name="tenMoiThau">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTitle", {
                title: t("danhMuc.tenMoiThau").toLowerCase(),
              })}
            />
          </Form.Item>
        ),
      },
      {
        name: "quyetDinhThau",
        component: (
          <Form.Item
            label={<div className="color">{t("danhMuc.quyetDinhThau")} </div>}
            name="quyetDinhThau"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.quyetDinhThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "nhomThau",
        component: (
          <Form.Item label={t("danhMuc.nhomThau")} name="nhomThau">
            <Select
              data={listnhomThau}
              placeholder={t("danhMuc.chonNhomThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "goiThau",
        component: (
          <Form.Item label={t("danhMuc.goiThau")} name="goiThau">
            <Select data={listGoiThau} placeholder={t("danhMuc.chonGoiThau")} />
          </Form.Item>
        ),
      },
      {
        name: "thongTinThau",
        component: (
          <Form.Item label={t("danhMuc.thongTinThau")} name="thongTinThau">
            <Input
              className="input-option"
              placeholder={t("danhMuc.thongTinThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenTrungThau",
        component: (
          <Form.Item
            label={
              <div className="color">
                {t("kho.quyetDinhThau.tenHangHoaTrungThau")}{" "}
              </div>
            }
            name="tenTrungThau"
          >
            <Input
              className="input-option"
              placeholder={t("kho.quyetDinhThau.tenHangHoaTrungThau")}
            />
          </Form.Item>
        ),
      },
      {
        name: "soNgayCanhBaoHsd",
        component: (
          <Form.Item
            label={t("danhMuc.soNgayCanhBaoHsd")}
            name="soNgayCanhBaoHsd"
          >
            <InputNumber
              min={0}
              className="input-option"
              placeholder={t("danhMuc.soNgayCanhBaoHsd")}
            />
          </Form.Item>
        ),
      },
      {
        name: "loaiLamTron",
        component: (
          <Form.Item label={t("danhMuc.loaiLamTron")} name="loaiLamTron">
            <Select
              placeholder={t("danhMuc.loaiLamTron")}
              data={listLoaiLamTron}
            />
          </Form.Item>
        ),
      },
      {
        name: "phieuLinhId",
        component: (
          <Form.Item label={t("danhMuc.maPhieuLinh")} name="phieuLinhId">
            <Select
              placeholder={t("danhMuc.maPhieuLinh")}
              data={listAllMaPhieuLinh}
            />
          </Form.Item>
        ),
      },
      {
        name: "maSinhPham",
        component: (
          <Form.Item
            label={t("danhMuc.maSinhPham")}
            name="maSinhPham"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maSinhPham")}
            />
          </Form.Item>
        ),
      },
      {
        name: "maSinhHieu",
        component: (
          <Form.Item
            label={t("danhMuc.maHieuSinhPham")}
            name="maSinhHieu"
            rules={[{ max: 50, message: t("danhMuc.khongDuocNhapQua50KyTu") }]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.maHieuSinhPham")}
            />
          </Form.Item>
        ),
      },
      {
        name: "phanLoaiDvKhoId",
        component: (
          <Form.Item
            label={
              <div
                className="cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/phan-loai-vat-tu")}
              >
                {t("danhMuc.phanLoaiVTYT")}
              </div>
            }
            name="phanLoaiDvKhoId"
          >
            <Select
              placeholder={t("danhMuc.chonPhanLoaiVTYT")}
              data={listAllPhanLoaiVatTu}
            />
          </Form.Item>
        ),
      },
      {
        name: "soVisa",
        component: (
          <Form.Item label={t("danhMuc.soVisa")} name="soVisa">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapSoVisa")}
            />
          </Form.Item>
        ),
      },
      {
        name: "dsDichVuThayTheId",
        component: (
          <Form.Item
            label={t("danhMuc.maVatTuThayThe")}
            name="dsDichVuThayTheId"
          >
            <SelectLoadMore
              placeholder={t("danhMuc.maVatTuThayThe")}
              api={dmDichVuKhoProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma} - ${i.ten}`,
                ten: `${i.ma} - ${i.ten}`,
              })}
              onChange={onChange("dsDichVuThayTheId", "dsDichVuThayThe")}
              uniqByKey="value"
              addValue={
                state.data?.dsDichVuThayThe?.length > 0
                  ? state.data?.dsDichVuThayThe.map((i) => ({
                      value: i.id,
                      label: `${i.ma} - ${i.ten}`,
                    }))
                  : null
              }
              value={state.data?.dsDichVuThayThe?.map((i) => i.id)}
              addParam={{ dsLoaiDichVu: [LOAI_DICH_VU.VAT_TU] }}
              keySearch={"ma"}
              mode="multiple"
            ></SelectLoadMore>
          </Form.Item>
        ),
      },
      {
        name: "maTckt",
        component: (
          <Form.Item label={t("danhMuc.maTckt")} name="maTckt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapMaTckt")}
            />
          </Form.Item>
        ),
      },
      {
        name: "tenTckt",
        component: (
          <Form.Item label={t("danhMuc.tenTckt")} name="tenTckt">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapTenTckt")}
            />
          </Form.Item>
        ),
      },
      {
        name: "khongTinhTien",
        component: (
          <Form.Item label=" " name="khongTinhTien" valuePropName="checked">
            <Checkbox onChange={onChangeCheckBox("khongTinhTien")}>
              {t("danhMuc.khongTinhTien")}
            </Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "chuaGomTrongGiuong",
        component: (
          <Form.Item
            label=" "
            name="chuaGomTrongGiuong"
            valuePropName="checked"
          >
            <Checkbox onChange={onChangeCheckBox("chuaGomTrongGiuong")}>
              {t("danhMuc.chuaGomTrongGiuong")}
            </Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "vatTuBo",
        component: (
          <Form.Item label=" " name="vatTuBo" valuePropName="checked">
            <Checkbox onChange={onChangeCheckBox("vatTuBo")}>
              {t("danhMuc.vatTuBo")}
            </Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "vatTuKichCo",
        component: (
          <Form.Item label=" " name="vatTuKichCo" valuePropName="checked">
            <Checkbox onChange={onChangeCheckBox("vatTuKichCo")}>
              {t("danhMuc.vatTuTheoKichCo")}
            </Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "stentPhuThuoc",
        component: (
          <Form.Item label=" " name="stentPhuThuoc" valuePropName="checked">
            <Checkbox>{t("danhMuc.stentPhuThuoc")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "kyThuatCao",
        component: (
          <Form.Item label=" " name="kyThuatCao" valuePropName="checked">
            <Checkbox>{t("danhMuc.kyThuatCao")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "vatTuTaiSuDung",
        component: (
          <Form.Item label=" " name="vatTuTaiSuDung" valuePropName="checked">
            <Checkbox>{t("danhMuc.vatTuTaiSuDung")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "chayMay",
        component: (
          <Form.Item label=" " name="chayMay" valuePropName="checked">
            <Checkbox>{t("danhMuc.vatTuChayMay")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "trongPtTt",
        component: (
          <Form.Item label=" " name="trongPtTt" valuePropName="checked">
            <Checkbox>{t("danhMuc.thuocPhauThuat")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "chiDinhSlLe",
        component: (
          <Form.Item label=" " name="chiDinhSlLe" valuePropName="checked">
            <Checkbox>{t("danhMuc.choPhepKeSlLe")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "mienPhiGiamDocDuyet",
        component: (
          <Form.Item
            label=" "
            name="mienPhiGiamDocDuyet"
            valuePropName="checked"
          >
            <Checkbox>{t("danhMuc.mienPhiGiamDocDuyet")}</Checkbox>
          </Form.Item>
        ),
      },
      {
        name: "active",
        component: currentItem &&
          currentItem.constructor === Object &&
          Object.keys(currentItem).length > 0 && (
            <Form.Item label=" " name="active" valuePropName="checked">
              <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
            </Form.Item>
          ),
      },
    ];
    return allFields
      .filter((field) => isFieldVisible(field.name))
      .sort((a, b) => getFieldOrder(a.name) - getFieldOrder(b.name));
  }, [t, refAutoFocus, isFieldVisible, getFieldOrder]);

  return (
    <Main>
      <TabPanel>
        <EditWrapper
          title={
            <div style={{ display: "flex" }}>
              <div>{t("danhMuc.thongTinVatTu")}</div>
              <div style={{ marginLeft: "auto", display: "flex" }}>
                {currentItem?.id &&
                  (!state.bhyt ? (
                    <Button onClick={onHuongBaoHiem(true)}>
                      {t("danhMuc.batHuongBh")}
                    </Button>
                  ) : (
                    <Button onClick={onHuongBaoHiem(false)}>
                      {t("danhMuc.tatHuongBh")}
                    </Button>
                  ))}
                {currentItem?.id && (
                  <Button onClick={onShowCapNhatGia}>
                    {t("kho.capNhatGia")}
                  </Button>
                )}
                <Button onClick={() => refModalSapXepTruong.current?.show()}>
                  {t("danhMuc.sapXepTruong")}
                </Button>
              </div>
            </div>
          }
          onCancel={onCancel}
          onSave={onSave}
          roleSave={roleSave}
          roleEdit={roleEdit}
          editStatus={editStatus}
          showAdded={false}
          forceShowButtonSave={
            (currentItemRowParent && checkRole(roleEdit) && true) || false
          }
          forceShowButtonCancel={
            (currentItemRowParent && checkRole(roleEdit) && true) || false
          }
          isHiddenButtonAdd={true}
        >
          <fieldset disabled={editStatus}>
            <Form
              form={form}
              layout="vertical"
              style={{ width: "100%" }}
              className="form-custom"
              initialValues={{
                tyLeTtDv: 100,
              }}
              onValuesChange={handleValuesChange}
            >
              {formFields.map((field) => {
                return (
                  <React.Fragment key={field.name}>
                    {field.component}
                  </React.Fragment>
                );
              })}
            </Form>
            {state?.vatTuKichCo && <TableChiTietKichCo />}
            {state?.vatTuBo && (
              <TableChiTietBo
                selectedVatTuCon={selectedVatTuCon}
                listVatTuCon={state?.listVatTuCon}
              />
            )}
          </fieldset>
        </EditWrapper>
      </TabPanel>
      <ModalSapXepTruong
        ref={refModalSapXepTruong}
        userConfigFields={userConfigFields}
        configFields={configFields}
        onSubmitSuccess={(newConfigFields) => {
          setUserConfigFields(newConfigFields);
        }}
        configKey="dmVatTu.thongTinVatTu"
      />
      <ModalCapNhatGia ref={refModalCapNhatGia} />
    </Main>
  );
}

export default FormServiceInfo;
