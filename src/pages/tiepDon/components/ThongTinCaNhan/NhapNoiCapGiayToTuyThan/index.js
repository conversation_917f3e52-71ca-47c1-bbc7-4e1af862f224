import React, { forwardRef, memo, useContext, useMemo } from "react";
import { Col } from "antd";
import { useDispatch } from "react-redux";
import { InputTimeout } from "components";
import { useTranslation } from "react-i18next";
import { useStore, useThietLap } from "hooks";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { THIET_LAP_CHUNG } from "constants";
import classNames from "classnames";
import ErrorValidateMessage from "../../ErrorValidateMessage";

const NhapNoiCapGiayToTuyThan = ({ fromSetting, ...props }, ref) => {
  const { disableTiepDon } = useContext(TiepDonContext);
  const { t } = useTranslation();
  const {
    tiepDon: { updateThongTinNb },
  } = useDispatch();
  const noiCap = useStore("tiepDon.nbGiayToTuyThan.noiCap", "");

  const dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_DIEN_GIAY_TO_TUY_THAN,
    ""
  )[0]?.eval();

  const batBuocGiayTo = useMemo(() => {
    return dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN || 0;
  }, [dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN]);

  const onChangeValue = (variables) => (value) => {
    updateThongTinNb({ [variables]: value?.trim() }, "nbGiayToTuyThan");
  };

  // Validate: bắt buộc khi BAT_BUOC_DIEN_GIAY_TO_TUY_THAN = 2
  const isRequired = useMemo(() => batBuocGiayTo === 2, [batBuocGiayTo]);
  const isError = useMemo(() => isRequired && !noiCap, [isRequired, noiCap]);

  return (
    <Col md={12} xl={12} xxl={12} {...props} ref={ref}>
      <div className="item-input">
        <label
          className={classNames("label", {
            "label-error": isError,
          })}
        >
          {t("tiepDon.noiCapGiayToTuyThan")}
          {isRequired && <span style={{ color: "red" }}> *</span>}
        </label>
        <InputTimeout
          className="input"
          placeholder={t("tiepDon.nhapNoiCapGiayToTuyThan")}
          value={noiCap}
          onChange={onChangeValue("noiCap")}
          disabled={disableTiepDon}
        />
        <ErrorValidateMessage
          isError={isError}
          message={t("tiepDon.vuiLongNhapNoiCapGiayToTuyThan")}
        />
      </div>
    </Col>
  );
};

export default memo(forwardRef(NhapNoiCapGiayToTuyThan));
