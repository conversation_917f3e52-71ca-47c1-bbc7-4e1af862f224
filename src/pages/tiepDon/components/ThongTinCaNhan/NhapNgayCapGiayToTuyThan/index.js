import React, {
  forwardRef,
  memo,
  useContext,
  useEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { Col } from "antd";
import { useDispatch } from "react-redux";
import { DOBInput } from "components";
import { useTranslation } from "react-i18next";
import { useStore, useThietLap } from "hooks";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { isEqual } from "lodash";
import moment from "moment";
import { THIET_LAP_CHUNG } from "constants";
import classNames from "classnames";
import ErrorValidateMessage from "../../ErrorValidateMessage";

const NhapNgayCapGiayToTuyThan = ({ fromSetting, ...props }, ref) => {
  const { disableTiepDon } = useContext(TiepDonContext);
  const { t } = useTranslation();
  const refNgayCap = useRef({});
  const {
    tiepDon: { updateThongTinNb },
  } = useDispatch();
  const ngayCap = useStore("tiepDon.nbGiayToTuyThan.ngayCap", "");

  const dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_DIEN_GIAY_TO_TUY_THAN,
    ""
  )[0]?.eval();

  const batBuocGiayTo = useMemo(() => {
    return dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN || 0;
  }, [dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN]);

  const [state, _setState] = useState({});
  const setState = (_state) => {
    _setState((state) => ({
      ...state,
      ...(_state || {}),
    }));
  };

  const onChange = (variables) => (value) => {
    updateThongTinNb({ [variables]: value }, "nbGiayToTuyThan");
  };

  useEffect(() => {
    if (!isEqual(refNgayCap.current, ngayCap)) {
      //nếu khác dữ liệu trước đó
      refNgayCap.current = ngayCap; //gán lại dữ liệu

      setState({
        ngayCapStr: ngayCap ? moment(ngayCap).format("DD/MM/yyyy") : "",
      });
    }
  }, [ngayCap]);

  // Validate: bắt buộc khi BAT_BUOC_DIEN_GIAY_TO_TUY_THAN = 2
  const isRequired = useMemo(() => batBuocGiayTo === 2, [batBuocGiayTo]);
  const isError = useMemo(() => isRequired && !ngayCap, [isRequired, ngayCap]);

  return (
    <Col md={12} xl={12} xxl={12} {...props} ref={ref}>
      <div className="item-input">
        <label
          className={classNames("label", {
            "label-error": isError,
          })}
        >
          {t("tiepDon.ngayCapGiayToTuyThan")}
          {isRequired && <span style={{ color: "red" }}> *</span>}
        </label>
        <DOBInput
          className="item-born"
          value={{ date: ngayCap, str: state.ngayCapStr }}
          onBlur={(e, nofi) => {
            onChange("ngayCap")(e.date);
          }}
          disabled={disableTiepDon}
          onChange={(e) => {
            setState({
              ngayCapStr: e.str,
            });
          }}
          placeholder={t("common.chonNgay")}
        />
        <ErrorValidateMessage
          isError={isError}
          message={t("tiepDon.vuiLongNhapNgayCapGiayToTuyThan")}
        />
      </div>
    </Col>
  );
};

export default memo(forwardRef(NhapNgayCapGiayToTuyThan));
