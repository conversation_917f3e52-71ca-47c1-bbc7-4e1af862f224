import React, { forwardRef, memo, useContext, useEffect, useMemo } from "react";
import { Col } from "antd";
import { useDispatch } from "react-redux";
import { InputTimeout } from "components";
import { useTranslation } from "react-i18next";
import { useStore, useThietLap } from "hooks";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { THIET_LAP_CHUNG } from "constants";
import classNames from "classnames";
import { VALIDATE_CASE_SDT } from "constants/index";
import ErrorValidateMessage from "../../ErrorValidateMessage";
import { select } from "redux-store/stores";
const NhapGiayToTuyThan = ({ fromSetting, ...props }, ref) => {
  const { nbDotDieuTriId, onCheckTrungThongTin, disableTiepDon } =
    useContext(TiepDonContext);
  const { t } = useTranslation();
  const {
    tiepDon: { updateThongTinNb },
  } = useDispatch();
  const maSo = useStore("tiepDon.nbGiayToTuyThan.maSo", "");
  const loaiGiayTo = useStore("tiepDon.nbGiayToTuyThan.loaiGiayTo");
  const { soDienThoai, quocTichId, maNb } = useStore(
    "tiepDon",
    {},
    { fields: "quocTichId,soDienThoai,maNb" }
  );
  const dataTemp = useStore("tiepDon.dataTemp", {});
  const [BAT_BUOC_NHAP_SDT] = useThietLap(THIET_LAP_CHUNG.BAT_BUOC_NHAP_SDT);
  const dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_DIEN_GIAY_TO_TUY_THAN,
    ""
  )[0]?.eval();

  const batBuocGiayTo = useMemo(() => {
    return dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN || 0;
  }, [dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN]);

  const getQuocGiaById = useStore(select.ttHanhChinh.listAllQuocGia)[1];

  const [QUOC_TICH_VIET_NAM] = useThietLap(THIET_LAP_CHUNG.QUOC_TICH_VIET_NAM);

  const validator = (value) => {
    switch (loaiGiayTo) {
      case 1:
        return {
          value: /^\d{12}$/.test(value),
          message: t("tiepDon.validateCanCuoc"),
        };
      case 2:
        return {
          value: /^\d{9}$|^\d{12}$/.test(value),
          message: t("tiepDon.validateChungMinh"),
        };
      case 3:
        if (value?.length === 8) {
          return {
            value: /^[A-Z][0-9]{7}$/.test(value),
            message: t("tiepDon.validateHoChieu8KyTu"),
          };
        }
        return {
          value: /^.{0,9}$/.test(value),
          message: t("tiepDon.validateHoChieu"),
        };
      case 8:
        return {
          value: /^\d{12}$/.test(value),
          message: t("tiepDon.validateMaDinhDanh"),
        };
      default:
        return {
          value: true,
        };
    }
  };

  const onChangeValue = (variables) => (value) => {
    // nếu trường thay đổi là maSo hoặc Email thì update vào nbGiayToTuyThan
    updateThongTinNb({ [variables]: value?.trim() }, "nbGiayToTuyThan");
    if (!nbDotDieuTriId && !maNb && value?.trim()) {
      //Đồng thời gọi check trùng thông tin nếu là maSo
      onCheckTrungThongTin && onCheckTrungThongTin(value?.trim(), variables);
    }
  };

  const hasValidate = useMemo(() => {
    return () => {
      if ([1, 2].includes(batBuocGiayTo) && !maSo) {
        return true;
      }

      if (
        [VALIDATE_CASE_SDT.VALIDATE_SDT_MA_GTTT].includes(BAT_BUOC_NHAP_SDT) &&
        batBuocGiayTo === 0 &&
        !soDienThoai &&
        !maSo
      ) {
        return true;
      }

      if (
        [VALIDATE_CASE_SDT.NO_VALIDATE, VALIDATE_CASE_SDT.VALIDATE].includes(
          BAT_BUOC_NHAP_SDT
        ) &&
        [1, 2].includes(batBuocGiayTo)
      ) {
        return true;
      }

      return false;
    };
  }, [BAT_BUOC_NHAP_SDT, batBuocGiayTo, soDienThoai, maSo]);

  const renderMessage =
    BAT_BUOC_NHAP_SDT === VALIDATE_CASE_SDT.VALIDATE_SDT_MA_GTTT
      ? t("tiepDon.vuiLongNhapSDThoacCCCD/CMND")
      : t("tiepDon.vuiLongNhapMaSoGiayToTuyThan");

  const isQuocTichVietNam = QUOC_TICH_VIET_NAM
    ? quocTichId && getQuocGiaById(quocTichId)?.ma === QUOC_TICH_VIET_NAM
    : true;

  const { value: isValueValid, message } = useMemo(() => {
    return isQuocTichVietNam
      ? validator(maSo)
      : { isValueValid: true, message: null };
  }, [isQuocTichVietNam, maSo]);

  useEffect(() => {
    if (dataTemp?.nbGiayToTuyThan?.maSo) {
      onChangeValue("maSo")(dataTemp?.nbGiayToTuyThan?.maSo);
      if (dataTemp.nbGiayToTuyThan.loaiGiayTo)
        updateThongTinNb(
          { loaiGiayTo: dataTemp.nbGiayToTuyThan.loaiGiayTo },
          "nbGiayToTuyThan"
        );
    }
  }, [dataTemp]);

  return (
    <Col md={12} xl={12} xxl={12} {...props} ref={ref}>
      <div className="item-input">
        <label
          className={classNames("label", {
            "label-error": hasValidate() && !maSo,
          })}
        >
          {t("tiepDon.maSoGiayToTuyThan")}
          {hasValidate() && <span style={{ color: "red" }}> *</span>}
        </label>
        <InputTimeout
          className="input"
          placeholder={t("tiepDon.nhapMaSoGiayToTuyThan")}
          value={maSo}
          onChange={onChangeValue("maSo")}
          disabled={disableTiepDon}
        />
        <ErrorValidateMessage
          validate={maSo && !isValueValid}
          message={message}
        />
        <ErrorValidateMessage
          isError={!maSo && hasValidate()}
          message={renderMessage}
        />
      </div>
    </Col>
  );
};

export default memo(forwardRef(NhapGiayToTuyThan));
