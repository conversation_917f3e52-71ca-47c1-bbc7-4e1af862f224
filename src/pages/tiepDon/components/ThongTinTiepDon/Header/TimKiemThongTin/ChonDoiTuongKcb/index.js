import React, { memo, forwardRef, useContext, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Col } from "antd";
import { Select } from "components";
import { useStore, useThietLap } from "hooks";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import useListDoiTuongKcb from "./hooks/useListDoiTuongKcb";
import { THIET_LAP_CHUNG } from "constants/index";
import classNames from "classnames";
import ErrorValidateMessage from "pages/tiepDon/components/ErrorValidateMessage";

const ChonDoiTuongKcb = ({ fromSetting, ...props }, ref) => {
  const { t } = useTranslation();
  const refSelect = useRef(null);
  const { disableTiepDon } = useContext(TiepDonContext);
  const maDoiTuongKcbId = useStore("tiepDon.maDoiTuongKcbId", null);

  const [listDoiTuongKcb] = useListDoiTuongKcb();

  const dataXML130_TRUONG_BAT_BUOC = useThietLap(
    THIET_LAP_CHUNG.XML130_TRUONG_BAT_BUOC
  )[0]?.eval();

  const {
    tiepDon: { updateData },
  } = useDispatch();

  useEffect(() => {
    if (!refSelect.current) {
      return;
    }

    if (
      listDoiTuongKcb.length &&
      maDoiTuongKcbId &&
      listDoiTuongKcb.findIndex((x) => x.id === maDoiTuongKcbId) < 0
    ) {
      onChange(null);
    }
    if (listDoiTuongKcb?.length === 1) {
      updateData({ maDoiTuongKcbId: listDoiTuongKcb[0]?.id });
    }
  }, [listDoiTuongKcb]);

  const onChange = (e) => updateData({ maDoiTuongKcbId: e ?? null });

  return (
    <Col sm={16} md={16} xl={16} xxl={16} {...props} ref={ref}>
      <div ref={refSelect} className="item-select" style={{ marginBottom: 0 }}>
        <label
          className={classNames("label pointer", {
            "label-error": dataXML130_TRUONG_BAT_BUOC && !maDoiTuongKcbId,
          })}
        >
          <span>{t("danhMuc.doiTuongKCB")}</span>
          {dataXML130_TRUONG_BAT_BUOC && (
            <span style={{ color: "red" }}> *</span>
          )}
        </label>
        <Select
          onChange={onChange}
          value={maDoiTuongKcbId}
          className="item__select"
          placeholder={t("common.chonDoiTuongKCB")}
          data={listDoiTuongKcb}
          disabled={disableTiepDon}
        />
        <ErrorValidateMessage
          isError={dataXML130_TRUONG_BAT_BUOC && !maDoiTuongKcbId}
          message={t("khamBenh.vuiLongChonDoiTuongKcb")}
        />
      </div>
    </Col>
  );
};

export default memo(forwardRef(ChonDoiTuongKcb));
