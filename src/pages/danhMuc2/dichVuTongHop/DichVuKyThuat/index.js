import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  DatePicker,
  TableWrapper,
  Pagination,
  HeaderSearch,
  Select,
  Checkbox,
  InputTimeout,
  EllipsisText,
} from "components";
import { Main } from "./styled";
import {
  ENUM,
  LOAI_DICH_VU,
  YES_NO,
  HAN_CHE_KHOA,
  HIEU_LUC,
  KHONG_TINH_TIEN,
  HOTKEY,
} from "constants/index";
import { Input, InputNumber } from "antd";
import moment from "moment";
import { useEnum, useListAll, useLazyKVMap } from "hooks";
import { useTranslation } from "react-i18next";
import { isArray } from "utils";
import { SVG } from "assets";
import { useHistory } from "react-router-dom";

const { Setting } = TableWrapper;

const DichVuKham = (props) => {
  const { t } = useTranslation();
  const history = useHistory();
  const {
    styleContainerButtonHeader,
    layerId,
    title,
    buttonHeader,
    classNameRow,
    setEditStatus,
  } = props;

  const { onRegisterHotkey } = useDispatch().phimTat;
  const refSelectRow = useRef();
  const refSettings = useRef(null);
  const [listKetNoiPacsLis] = useEnum(ENUM.KET_NOI_PASC_LIS);
  const [listPhanTuyenPtTt] = useEnum(ENUM.PHAN_TUYEN_PTTT);
  const [listLoaiPhieuThuDichVu] = useEnum(ENUM.LOAI_PHIEU_THU_DICH_VU);
  const [listloaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP, //up
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN, //down
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
      ],
    });
  }, []);

  const {
    danhMucDichVuTongHop: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData,
    },
  } = useDispatch();

  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const [listAllNhomDichVuCap2] = useListAll("nhomDichVuCap2", {}, true);
  const [listAllNhomDichVuCap3] = useListAll("nhomDichVuCap3", {}, true);
  const [listAllChuyenKhoa] = useListAll("chuyenKhoa", {}, true);
  const [listAllDonViTinh] = useListAll("donViTinh", {}, true);
  const [listAllBaoCao] = useListAll("baoCao", {}, true);
  const [listAllBenhVien] = useListAll("benhVien", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  const [listLoaiXnVitimes] = useEnum(ENUM.LOAI_XN_VITIMES);
  const [listNhomChiPhiBh] = useEnum(ENUM.NHOM_CHI_PHI);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listDoiTuongSuDung] = useEnum(ENUM.DOI_TUONG_SU_DUNG);
  const [listLoaiMau] = useEnum(ENUM.LOAI_MAU);
  const [listLoaiKetQuaXetNghiem] = useEnum(ENUM.LOAI_KET_QUA_XET_NGHIEM);
  const { listAllDuongDung } = useSelector((state) => state.duongDung);

  const {
    listData,
    totalElements,
    page,
    size,
    currentItem,
    dataSortColumn,
    dataSearch,
  } = useSelector((state) => state.danhMucDichVuTongHop);

  //getFunction

  const [getChuyenKhoa] = useLazyKVMap(listAllChuyenKhoa);
  const [getBaoCao] = useLazyKVMap(listAllBaoCao);
  const [getLoaiMau] = useLazyKVMap(listLoaiMau);
  const [getDuongDung] = useLazyKVMap(listAllDuongDung);
  const [getNguonChiTra] = useLazyKVMap(listAllNguonKhacChiTra);
  const [getLoaiKetQuaXetNghiem] = useLazyKVMap(listLoaiKetQuaXetNghiem);
  const [getNhomChiPhiBh] = useLazyKVMap(listNhomChiPhiBh);
  const [getGioiTinh] = useLazyKVMap(listGioiTinh);
  const [getDoiTuongSuDung] = useLazyKVMap(listDoiTuongSuDung);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (listData?.findIndex((item) => item.id === state.currentItem?.id) || 0) +
      index;
    if (-1 < indexNextItem && indexNextItem < listData.length) {
      onShowAndHandleUpdate(listData[indexNextItem]);
      setState({ currentItem: listData[indexNextItem] });
      document
        .getElementsByClassName("row-id-" + listData[indexNextItem]?.id)[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    updateData({ listData: [] });
    onSizeChange({
      size: 10,
      dataSortColumn: { active: 0 },
    });
  }, []);
  
  const listAllBV = useMemo(() => {
    let result = [];
    if (isArray(listAllBenhVien, true)) {
      result = listAllBenhVien.map((item) => ({
        ...item,
        ten: `${item.ma} - ${item.ten}`,
      }));
    }
    return result;
  }, [listAllBenhVien]);

  const [getBenhVien] = useLazyKVMap(listAllBV);

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const refTimeOut = useRef(null);
  const onSearchInputTimeout = (key) => (e) => {
    onChangeInputSearch({
      [key]: e,
    });
  };
  const onSearchInput = (key) => (e) => {
    if (
      key === "dichVu.nhomDichVuCap1Id" ||
      key === "dichVu.nhomDichVuCap2Id" ||
      key === "dichVu.nhomDichVuCap3Id" ||
      key === "dichVu.loaiDichVu"
    ) {
      onChangeInputSearch({
        [key]: e,
      });
      return null;
    }
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
      refTimeOut.current = null;
    }
    let value = "";
    if (e?.target) {
      if (e?.target?.hasOwnProperty("checked")) value = e?.target?.checked;
      else value = e?.target?.value;
    } else {
      if (e && typeof e === "object" && !Array.isArray(e)) {
        value = moment(e).format("YYYY-MM-DD");
      } else {
        value = e;
      }
    }
    onChangeInputSearch({
      [key]: value,
    });
  };

  const onViewDanhMuc = (record) => (e) => {
    e.stopPropagation();
    if (record?.dichVu?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
      history.push("/danh-muc/dich-vu-xet-nghiem");
    } else if (record?.dichVu?.loaiDichVu === LOAI_DICH_VU.KHAM) {
      history.push("/danh-muc/dich-vu-kham-benh");
    } else if (
      record?.dichVu?.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
    ) {
      history.push("/danh-muc/dich-vu-phau-thuat");
    } else if (record?.dichVu?.loaiDichVu === LOAI_DICH_VU.CDHA) {
      history.push("/danh-muc/dich-vu-cdha-tdcn");
    } else if (record?.dichVu?.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI) {
      history.push("/danh-muc/ngoai-dieu-tri");
    } else if (record?.dichVu?.loaiDichVu === LOAI_DICH_VU.SUAT_AN) {
      history.push("/danh-muc/suat-an");
    }
  };

  const columns = [
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.stt")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 60,
      dataIndex: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maDichVu")}
          sort_key="dichVu.ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ma"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.maDichVu")}
              onChange={onSearchInput("dichVu.ma")}
              value={dataSearch["dichVu.ma"]}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      i18Name: "danhMuc.maDichVu",
      key: "dichVu.ma",
      show: true,
      render: (item) => {
        return item?.ma;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenDichVu")}
          sort_key="dichVu.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.tenDichVu")}
              onChange={onSearchInput("dichVu.ten")}
              value={dataSearch["dichVu.ten"]}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dichVu",
      i18Name: "danhMuc.tenDichVu",
      key: "dichVu.ten",
      show: true,
      render: (item) => {
        return item?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiDichVu")}
          sort_key="dichVu.loaiDichVu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.loaiDichVu"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.loaiDichVu")}
              onChange={onSearchInput("dichVu.loaiDichVu")}
              data={listloaiDichVu?.filter((x) =>
                [
                  LOAI_DICH_VU.KHAM,
                  LOAI_DICH_VU.XET_NGHIEM,
                  LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                  LOAI_DICH_VU.CDHA,
                  LOAI_DICH_VU.NGOAI_DIEU_TRI,
                  LOAI_DICH_VU.SUAT_AN,
                ]?.includes(x.id)
              )}
              value={dataSearch["dichVu.loaiDichVu"]}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dichVu",
      i18Name: "danhMuc.loaiDichVu",
      key: "dichVu.loaiDichVu",
      show: true,
      render: (item) => {
        return listloaiDichVu?.find((x) => x.id === item?.loaiDichVu)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenVietTat")}
          sort_key="dichVu.vietTat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.vietTat"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.tenVietTat")}
              onChange={onSearchInput("dichVu.vietTat")}
              value={dataSearch["dichVu.vietTat"]}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "dichVu",
      i18Name: "danhMuc.tenVietTat",
      key: "dichVu.vietTat",
      show: true,
      render: (item) => {
        return item?.vietTat;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donGiaKhongBh")}
          sort_key="dichVu.giaKhongBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaKhongBaoHiem"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.donGiaKhongBh")}
              onChange={onSearchInput("dichVu.giaKhongBaoHiem")}
              type="number"
              value={dataSearch["dichVu.giaKhongBaoHiem"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      i18Name: "danhMuc.donGiaKhongBh",
      key: "dichVu.giaKhongBaoHiem",
      show: true,
      render: (item = []) => {
        return item?.giaKhongBaoHiem?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.donGiaBh")}
          sort_key="dichVu.giaBaoHiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaBaoHiem"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.donGiaBh")}
              onChange={onSearchInput("dichVu.giaBaoHiem")}
              type="number"
              value={dataSearch["dichVu.giaBaoHiem"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      i18Name: "danhMuc.donGiaBh",
      key: "dichVu.giaBaoHiem",
      show: true,
      render: (item, list, index) => {
        return item?.giaBaoHiem?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phuThu")}
          sort_key="dichVu.giaPhuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.giaPhuThu"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapPhuThu")}
              phuThu
              onChange={onSearchInput("dichVu.giaPhuThu")}
              type="number"
              value={dataSearch["dichVu.giaPhuThu"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      i18Name: "danhMuc.phuThu",
      key: "dichVu.giaPhuThu",
      show: true,
      render: (item, list, index) => {
        return item?.giaPhuThu?.formatPrice() || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tyLeBhThanhToan")}
          sort_key="dichVu.tyLeBhTt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tyLeBhTt"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapTyLeBhThanhToan")}
              onChange={onSearchInput("dichVu.tyLeBhTt")}
              type="number"
              value={dataSearch["dichVu.tyLeBhTt"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "right",
      i18Name: "danhMuc.tyLeBhThanhToan",
      key: "dichVu.tyLeBhTt",
      show: true,
      render: (item, list, index) => {
        return item?.tyLeBhTt;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiKetQua")}
          sort_key="loaiKetQua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiKetQua"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.nhapLoaiKetQua")}
              onChange={onSearchInput("loaiKetQua")}
              data={listLoaiKetQuaXetNghiem}
              hasAllOption={true}
              value={dataSearch?.loaiKetQua}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "loaiKetQua",
      i18Name: "danhMuc.loaiKetQua",
      key: "loaiKetQua",
      show: true,
      render: (item) => {
        return getLoaiKetQuaXetNghiem(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.ketQuaThamChieu")}
          sort_key="ketQuaThamChieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ketQuaThamChieu"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.ketQuaThamChieu")}
              onChange={onSearchInput("ketQuaThamChieu")}
              type="number"
              value={dataSearch?.ketQuaThamChieu}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "ketQuaThamChieu",
      i18Name: "danhMuc.ketQuaThamChieu",
      key: "ketQuaThamChieu",
      show: true,
      render: (item) => {
        return item && item.join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tyLeThanhToanDV")}
          sort_key="dichVu.tyLeTtDv"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tyLeTtDv"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapTyLeThanhToanDV")}
              onChange={onSearchInput("dichVu.tyLeTtDv")}
              type="number"
              value={dataSearch["dichVu.tyLeTtDv"]}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dichVu",
      align: "right",
      i18Name: "danhMuc.tyLeThanhToanDV",
      key: "dichVu.tyLeTtDv",
      show: true,
      render: (item, list, index) => {
        return item?.tyLeTtDv;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chuyenKhoa")}
          sort_key="chuyenKhoaId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chuyenKhoaId"] || 0}
          searchSelect={
            <Select
              data={listAllChuyenKhoa}
              placeholder={t("danhMuc.chonChuyenKhoa")}
              onChange={onSearchInput("chuyenKhoaId")}
              hasAllOption={true}
              value={dataSearch?.chuyenKhoaId}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "chuyenKhoaId",
      i18Name: "danhMuc.chuyenKhoa",
      key: "chuyenKhoaId",
      show: true,
      render: (item, list, index) => {
        return getChuyenKhoa(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tenTruong.maBieuMauChiDinh")}
          sort_key="phieuChiDinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phieuChiDinhId"] || 0}
          searchSelect={
            <Select
              data={listAllBaoCao}
              placeholder={t("danhMuc.chonBaoCao")}
              onChange={onSearchInput("phieuChiDinhId")}
              hasAllOption={true}
              value={dataSearch?.phieuChiDinhId}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "phieuChiDinhId",
      i18Name: "tenTruong.maBieuMauChiDinh",
      key: "phieuChiDinhId",
      show: true,
      render: (item) => {
        return getBaoCao(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tenTruong.maPhieuChiDinhPhu")}
          sort_key="maPhieuChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["maPhieuChiDinh"] || 0}
          search={
            <InputTimeout
              placeholder={t("tenTruong.maPhieuChiDinhPhu")}
              onChange={onSearchInputTimeout("maPhieuChiDinh")}
              value={dataSearch?.maPhieuChiDinh}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "maPhieuChiDinh",
      i18Name: "tenTruong.maPhieuChiDinhPhu",
      key: "maPhieuChiDinh",
      show: true,
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chiSoNuThap")}
          sort_key="chiSoNuThap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chiSoNuThap"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapChiSoNuThap")}
              onChange={onSearchInput("chiSoNuThap")}
              type="number"
              value={dataSearch?.chiSoNuThap}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "chiSoNuThap",
      align: "right",
      i18Name: "danhMuc.chiSoNuThap",
      key: "chiSoNuThap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chiSoNuCao")}
          sort_key="chiSoNuCao"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chiSoNuCao"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapChiSoNuCao")}
              onChange={onSearchInput("chiSoNuCao")}
              type="number"
              value={dataSearch?.chiSoNuCao}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "chiSoNuCao",
      align: "right",
      i18Name: "danhMuc.chiSoNuCao",
      key: "chiSoNuCao",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chiSoNamThap")}
          sort_key="chiSoNamThap"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chiSoNamThap"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapChiSoNamThap")}
              onChange={onSearchInput("chiSoNamThap")}
              type="number"
              value={dataSearch?.chiSoNamThap}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "chiSoNamThap",
      align: "right",
      i18Name: "danhMuc.chiSoNamThap",
      key: "chiSoNamThap",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chiSoNamCao")}
          sort_key="chiSoNamCao"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chiSoNamCao"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.nhapChiSoNamCao")}
              onChange={onSearchInput("chiSoNamCao")}
              type="number"
              value={dataSearch?.chiSoNamCao}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "chiSoNamCao",
      align: "right",
      i18Name: "danhMuc.chiSoNamCao",
      key: "chiSoNamCao",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiMau")}
          sort_key="loaiMau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiMau"] || 0}
          searchSelect={
            <Select
              data={listLoaiMau}
              placeholder={t("danhMuc.chonLoaiMau")}
              onChange={onSearchInput("loaiMau")}
              hasAllOption={true}
              value={dataSearch?.loaiMau}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "loaiMau",
      i18Name: "danhMuc.loaiMau",
      key: "loaiMau",
      show: true,
      render: (item) => {
        return getLoaiMau(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.theTich")}
          sort_key="theTich"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["theTich"] || 0}
          search={
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("danhMuc.nhapTheTich")}
              onChange={onSearchInput("theTich")}
              type="number"
              value={dataSearch?.theTich}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "theTich",
      align: "right",
      i18Name: "danhMuc.theTich",
      key: "theTich",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.soNgaySuDung")}
          sort_key="soNgaySuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["soNgaySuDung"] || 0}
          search={
            <InputNumber
              style={{ width: "100%" }}
              placeholder={t("danhMuc.nhapSoNgaySuDung")}
              onChange={onSearchInput("soNgaySuDung")}
              type="number"
              value={dataSearch?.soNgaySuDung}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "soNgaySuDung",
      align: "right",
      i18Name: "danhMuc.soNgaySuDung",
      key: "soNgaySuDung",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.duongDung")}
          sort_key="duongDungId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["duongDungId"] || 0}
          searchSelect={
            <Select
              data={listAllDuongDung}
              placeholder={t("danhMuc.chonDuongDung")}
              onChange={onSearchInput("duongDungId")}
              hasAllOption={true}
              value={dataSearch?.duongDungId}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "duongDungId",
      i18Name: "danhMuc.duongDung",
      key: "duongDungId",
      show: true,
      render: (item) => {
        return getDuongDung(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomChiPhi")}
          sort_key="dichVu.nhomChiPhiBh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomChiPhiBh"] || 0}
          searchSelect={
            <Select
              data={listNhomChiPhiBh}
              placeholder={t("danhMuc.chonNhomChiPhi")}
              onChange={onSearchInput("dichVu.nhomChiPhiBh")}
              hasAllOption={true}
              value={dataSearch["dichVu.nhomChiPhiBh"]}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dichVu",
      i18Name: "danhMuc.nhomChiPhi",
      key: "dichVu.nhomChiPhiBh",
      show: true,
      render: (item) => {
        return getNhomChiPhiBh(item?.nhomChiPhiBh)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.dvt")}
          sort_key="dichVu.donViTinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.donViTinhId"] || 0}
          searchSelect={
            <Select
              data={listAllDonViTinh}
              placeholder={t("danhMuc.chonDvt")}
              onChange={onSearchInput("dichVu.donViTinhId")}
              hasAllOption={true}
              value={dataSearch["dichVu.donViTinhId"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      i18Name: "danhMuc.dvt",
      key: "dichVu.donViTinhId",
      show: true,
      render: (item) => {
        return item?.donViTinh?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap1")}
          sort_key="dichVu.nhomDichVuCap1Id"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap1Id"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonNhomDVCap1")}
              data={listAllNhomDichVuCap1}
              onChange={onSearchInput("dichVu.nhomDichVuCap1Id")}
              style={{ width: "100%" }}
              hasAllOption={true}
              value={dataSearch["dichVu.nhomDichVuCap1Id"]}
            />
          }
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      i18Name: "danhMuc.nhomDVCap1",
      key: "dichVu.nhomDichVuCap1Id",
      show: true,
      render: (item) => {
        return item?.nhomDichVuCap1?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap2")}
          sort_key="dichVu.nhomDichVuCap2Id"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap2Id"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonNhomDVCap2")}
              data={listAllNhomDichVuCap2}
              onChange={onSearchInput("dichVu.nhomDichVuCap2Id")}
              style={{ width: "100%" }}
              hasAllOption={true}
              value={dataSearch["dichVu.nhomDichVuCap2Id"]}
            />
          }
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      i18Name: "danhMuc.nhomDVCap2",
      key: "dichVu.nhomDichVuCap2Id",
      show: true,
      render: (item) => {
        return item?.nhomDichVuCap2?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap3")}
          sort_key="dichVu.nhomDichVuCap3Id"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.nhomDichVuCap3Id"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonNhomDVCap3")}
              data={listAllNhomDichVuCap3}
              onChange={onSearchInput("dichVu.nhomDichVuCap3Id")}
              style={{ width: "100%" }}
              hasAllOption={true}
              value={dataSearch["dichVu.nhomDichVuCap3Id"]}
            />
          }
        />
      ),
      width: 170,
      dataIndex: "dichVu",
      i18Name: "danhMuc.nhomDVCap3",
      key: "dichVu.nhomDichVuCap3Id",
      show: true,
      render: (item) => {
        return item?.nhomDichVuCap3?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maTuongDuong")}
          sort_key="dichVu.maTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.maTuongDuong"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapMaTuongDuong")}
              onChange={onSearchInput("dichVu.maTuongDuong")}
              value={dataSearch["dichVu.maTuongDuong"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      i18Name: "danhMuc.maTuongDuong",
      key: "dichVu.maTuongDuong",
      show: true,
      render: (item) => {
        return item?.maTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenTuongDuong")}
          sort_key="dichVu.tenTuongDuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.tenTuongDuong"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapTenTuongDuong")}
              onChange={onSearchInput("dichVu.tenTuongDuong")}
              value={dataSearch["dichVu.tenTuongDuong"]}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      i18Name: "danhMuc.tenTuongDuong",
      key: "dichVu.tenTuongDuong",
      show: true,
      render: (item) => {
        return item?.tenTuongDuong;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.gioiTinh")}
          sort_key="gioiTinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.gioiTinh || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={listGioiTinh}
              placeholder={t("common.chonGioiTinh")}
              onChange={onSearchInput("gioiTinh")}
              hasAllOption={true}
              value={dataSearch?.gioiTinh}
            />
          }
        />
      ),
      width: 110,
      dataIndex: "gioiTinh",
      i18Name: "common.gioiTinh",
      key: "gioiTinh",
      show: true,
      render: (item, list, index) => {
        return getGioiTinh(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="hanCheKhoaChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.hanCheKhoaChiDinh || 0}
          searchSelect={
            <Select
              data={HAN_CHE_KHOA}
              placeholder={t("danhMuc.chonHanCheKhoaChiDinh")}
              onChange={onSearchInput("hanCheKhoaChiDinh")}
              hasAllOption={true}
              value={dataSearch?.hanCheKhoaChiDinh}
            />
          }
          title={t("danhMuc.hanCheKhoaChiDinh")}
        />
      ),
      width: 150,
      dataIndex: "hanCheKhoaChiDinh",
      align: "center",
      i18Name: "danhMuc.chonHanCheKhoaChiDinh",
      key: "hanCheKhoaChiDinh",
      show: true,
      render: (item) => {
        return <Checkbox checked={!!item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.truongHopKeDv")}
          sort_key="dsDoiTuongSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dsDoiTuongSuDung"] || 0}
          searchSelect={
            <Select
              data={listDoiTuongSuDung}
              placeholder={t("danhMuc.chonTruongHopKeDv")}
              onChange={onSearchInput("dsDoiTuongSuDung")}
              mode="multiple"
              value={dataSearch?.dsDoiTuongSuDung}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dsDoiTuongSuDung",
      i18Name: "danhMuc.truongHopKeDv",
      key: "dsDoiTuongSuDung",
      show: true,
      render: (item) => {
        return item
          ?.map((i) => getDoiTuongSuDung(i)?.ten)
          .filter(Boolean)
          .join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.tiepDonCls"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tiepDonCls"] || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonTiepDonCLS")}
              onChange={onSearchInput("tiepDonCls")}
              hasAllOption={true}
              value={dataSearch?.tiepDonCls}
            />
          }
          title={t("danhMuc.tiepDonCLS")}
        />
      ),
      width: 120,
      dataIndex: "tiepDonCls",
      i18Name: "danhMuc.tiepDonCLS",
      key: "dichVu.tiepDonCls",
      show: true,
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },

    {
      title: (
        <HeaderSearch
          sort_key="quyetDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["quyetDinh"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapMaSoQuyetDinh")}
              onChange={onSearchInput("quyetDinh")}
              value={dataSearch?.quyetDinh}
            />
          }
          title={t("danhMuc.maSoQuyetDinh")}
        />
      ),
      width: "150px",
      dataIndex: "quyetDinh",
      i18Name: "danhMuc.maSoQuyetDinh",
      key: "quyetDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="ngayCongBo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ngayCongBo"] || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("danhMuc.chonNgayQuyetDinh")}
              onChange={onSearchInput("ngayCongBo")}
              value={dataSearch?.ngayCongBo && moment(dataSearch?.ngayCongBo)}
            />
          }
          title={t("danhMuc.ngayQuyetDinh")}
        />
      ),
      width: "150px",
      dataIndex: "ngayCongBo",
      i18Name: "danhMuc.ngayQuyetDinh",
      key: "ngayCongBo",
      show: true,
      render: (item) => {
        return item && new Date(item).format("dd/MM/YYYY");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nguonKhacChiTra")}
          sort_key="dichVu.dsNguonKhacChiTra"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.dsNguonKhacChiTra"] || 0}
          searchSelect={
            <Select
              data={listAllNguonKhacChiTra}
              placeholder={t("danhMuc.chonNguonKhacChiTra")}
              onChange={onSearchInput("dichVu.nguonKhacId")}
              mode="multiple"
              value={dataSearch["dichVu.nguonKhacId"]}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "dichVu",
      i18Name: "danhMuc.nguonKhacChiTra",
      key: "dichVu.dsNguonKhacChiTra",
      show: true,
      render: (item) => {
        return getNguonChiTra(item.nguonKhacId)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="phiVanChuyen"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["phiVanChuyen"] || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonChiPhiVanChuyen")}
              onChange={onSearchInput("phiVanChuyen")}
              hasAllOption={true}
              value={dataSearch?.phiVanChuyen}
            />
          }
          title={t("danhMuc.chiPhiVanChuyen")}
        />
      ),
      width: 150,
      dataIndex: "phiVanChuyen",
      align: "center",
      i18Name: "danhMuc.chiPhiVanChuyen",
      key: "phiVanChuyen",
      show: true,
      render: (item) => {
        return <Checkbox checked={!!item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="csKcbChuyenGiaoId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["csKcbChuyenGiaoId"] || 0}
          searchSelect={
            <Select
              data={listAllBV}
              placeholder={t("tenTruong.cskcbChuyenGiaoDvkt")}
              onChange={onSearchInput("csKcbChuyenGiaoId")}
              hasAllOption
              value={dataSearch?.csKcbChuyenGiaoId}
            />
          }
          title={t("tenTruong.cskcbChuyenGiaoDvkt")}
        />
      ),
      width: 180,
      dataIndex: "csKcbChuyenGiaoId",
      align: "center",
      i18Name: "tenTruong.cskcbChuyenGiaoDvkt",
      key: "csKcbChuyenGiaoId",
      show: true,
      render: (item) => {
        return getBenhVien(item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="csKcbThucHienId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["csKcbThucHienId"] || 0}
          searchSelect={
            <Select
              data={listAllBV}
              placeholder={t("tenTruong.cskcbThucHienCls")}
              onChange={onSearchInput("csKcbThucHienId")}
              value={dataSearch?.csKcbThucHienId}
            />
          }
          title={t("tenTruong.cskcbThucHienCls")}
        />
      ),
      width: 150,
      dataIndex: "csKcbThucHienId",
      align: "center",
      i18Name: "tenTruong.cskcbThucHienCls",
      key: "csKcbThucHienId",
      show: true,
      render: (item) => {
        return listAllBV.find((i) => i.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.khongTinhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.khongTinhTien"] || 0}
          searchSelect={
            <Select
              data={KHONG_TINH_TIEN}
              placeholder={t("danhMuc.chonTinhTien")}
              onChange={onSearchInput("dichVu.khongTinhTien")}
              hasAllOption={true}
              value={dataSearch["dichVu.khongTinhTien"]}
            />
          }
          title={t("danhMuc.khongTinhTien")}
        />
      ),
      width: 150,
      dataIndex: "dichVu",
      align: "center",
      i18Name: "danhMuc.khongTinhTien",
      key: "dichVu.khongTinhTien",
      show: true,
      render: (item) => {
        return item && <Checkbox checked={!!item.khongTinhTien} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="maKetNoi"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["maKetNoi"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.nhapMaGuiLISPACS")}
              onChange={onSearchInput("maKetNoi")}
              value={dataSearch?.maKetNoi}
            />
          }
          title={t("danhMuc.maGuiLISPACS")}
        />
      ),
      width: 150,
      dataIndex: "maKetNoi",
      align: "center",
      i18Name: "danhMuc.maGuiLISPACS",
      key: "maKetNoi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="ketNoi"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ketNoi"] || 0}
          searchSelect={
            <Select
              placeholder={t("danhMuc.chonDonViKetNoi")}
              onChange={onSearchInput("ketNoi")}
              data={listKetNoiPacsLis}
              value={dataSearch?.ketNoi}
            />
          }
          title={t("danhMuc.donViKetNoi")}
        />
      ),
      width: 150,
      dataIndex: "ketNoi",
      align: "center",
      i18Name: "danhMuc.donViKetNoi",
      key: "ketNoi",
      show: true,
      render: (item) => listKetNoiPacsLis.find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          sort_key="stt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["stt"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.thuTuHienThi")}
              onChange={onSearchInput("stt")}
              type="number"
              value={dataSearch?.stt}
            />
          }
          title={t("danhMuc.thuTuHienThi")}
        />
      ),
      width: 120,
      dataIndex: "stt",
      align: "center",
      i18Name: "danhMuc.thuTuHienThi",
      key: "stt",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.trichCNC")}
          sort_key="trichCnc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["trichCnc"] || 0}
          search={
            <InputNumber
              min={0}
              placeholder={t("danhMuc.trichCNC")}
              onChange={onSearchInput("trichCnc")}
              type="number"
              value={dataSearch?.trichCnc}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "trichCnc",
      align: "right",
      key: "trichCnc",
      i18Name: "danhMuc.trichCNC",
      show: true,
      render: (item) => {
        return item?.formatPrice();
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiXnVitimes")}
          sort_key="loaiXnVitimes"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiXnVitimes || 0}
          searchSelect={
            <Select
              data={listLoaiXnVitimes}
              placeholder={t("danhMuc.chonLoaiXnVitimes")}
              onChange={onSearchInput("loaiXnVitimes")}
              value={dataSearch?.loaiXnVitimes}
            />
          }
        />
      ),
      width: 220,
      dataIndex: "loaiXnVitimes",
      key: "loaiXnVitimes",
      align: "center",
      i18Name: "danhMuc.loaiXnVitimes",
      show: true,
      render: (item) => listLoaiXnVitimes.find((loai) => loai.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.loaiPhieuThu")}
          sort_key="dichVu.loaiPhieuThu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiPhieuThu || 0}
          searchSelect={
            <Select
              data={listLoaiPhieuThuDichVu}
              placeholder={t("baoCao.chonLoaiPhieuThu")}
              onChange={onSearchInput("dichVu.loaiPhieuThu")}
              value={dataSearch["dichVu.loaiPhieuThu"]}
            />
          }
        />
      ),
      width: 220,
      dataIndex: "dichVu",
      key: "dichVu.loaiPhieuThu",
      align: "center",
      i18Name: "baoCao.loaiPhieuThu",
      show: true,
      render: (item) => {
        return listLoaiPhieuThuDichVu.find(
          (loai) => loai.id === item.loaiPhieuThu
        )?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="yeuCauBenhPham"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.yeuCauBenhPham || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonYeuCauBenhPham")}
              onChange={onSearchInput("yeuCauBenhPham")}
              hasAllOption={true}
              value={dataSearch?.yeuCauBenhPham}
            />
          }
          title={t("danhMuc.yeuCauBenhPham")}
        />
      ),
      width: 150,
      dataIndex: "yeuCauBenhPham",
      key: "yeuCauBenhPham",
      align: "center",
      i18Name: "danhMuc.yeuCauBenhPham",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="ketQuaLau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ketQuaLau || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonDvCoKetQuaLau")}
              onChange={onSearchInput("ketQuaLau")}
              hasAllOption={true}
              value={dataSearch?.ketQuaLau}
            />
          }
          title={t("danhMuc.dvCoKetQuaLau")}
        />
      ),
      width: 130,
      dataIndex: "ketQuaLau",
      key: "ketQuaLau",
      align: "center",
      i18Name: "danhMuc.dvCoKetQuaLau",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="covid"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.covid || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHanCheKhoaChiDinh")}
              onChange={onSearchInput("covid")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.covid}
            />
          }
          title={t("danhMuc.hanCheKhoaChiDinh")}
        />
      ),
      width: 150,
      dataIndex: "covid",
      key: "covid",
      i18Name: "danhMuc.hanCheKhoaChiDinh",
      show: true,
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="theoYeuCau"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.theoYeuCau || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonYeuCau")}
              onChange={onSearchInput("theoYeuCau")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.theoYeuCau}
            />
          }
          title={t("danhMuc.theoYeuCau")}
        />
      ),
      width: 120,
      dataIndex: "theoYeuCau",
      key: "theoYeuCau",
      i18Name: "danhMuc.theoYeuCau",
      show: true,
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="mucDichSuDung"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.mucDichSuDung || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonYeuCau")}
              onChange={onSearchInput("mucDichSuDung")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.mucDichSuDung}
            />
          }
          title={t("danhMuc.apDungTt35")}
        />
      ),
      width: 120,
      dataIndex: "mucDichSuDung",
      key: "mucDichSuDung",
      i18Name: "danhMuc.apDungTt35",
      show: true,
      align: "center",
      render: (item, data) => {
        return <Checkbox checked={data?.dichVu?.mucDichSuDung} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="taoChiSoCon"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.theoYeuCau || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonTuDongTaoChiSoCon")}
              onChange={onSearchInput("taoChiSoCon")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.taoChiSoCon}
            />
          }
          title={t("danhMuc.tuDongTaoChiSoCon")}
        />
      ),
      width: 160,
      dataIndex: "taoChiSoCon",
      key: "taoChiSoCon",
      i18Name: "danhMuc.tuDongTaoChiSoCon",
      show: true,
      align: "center",
      render: (item, data) => {
        return <Checkbox checked={data?.taoChiSoCon} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="soNgayCanhBaoHsd"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soNgayCanhBaoHsd || 0}
          search={
            <Input
              placeholder={t("danhMuc.soNgayCanhBaoHsd")}
              onChange={onSearchInput("soNgayCanhBaoHsd")}
              value={dataSearch?.soNgayCanhBaoHsd}
            />
          }
          title={t("danhMuc.soNgayCanhBaoHsd")}
        />
      ),
      width: 170,
      dataIndex: "soNgayCanhBaoHsd",
      key: "soNgayCanhBaoHsd",
      align: "center",
      i18Name: "danhMuc.soNgayCanhBaoHsd",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key={t("danhMuc.phanTuyenPttt")}
          onClickSort={onClickSort}
          dataSort={dataSortColumn.phanTuyenPtTt || 0}
          searchSelect={
            <Select
              data={listPhanTuyenPtTt}
              placeholder={t("danhMuc.phanTuyenPttt")}
              onChange={onSearchInput("phanTuyenPtTt")}
              value={dataSearch?.phanTuyenPtTt}
            />
          }
          title={t("danhMuc.phanTuyenPttt")}
        />
      ),
      width: 150,
      dataIndex: "phanTuyenPtTt",
      key: "phanTuyenPtTt",
      align: "center",
      i18Name: "danhMuc.phanTuyenPttt",
      show: true,
      render: (item) => listPhanTuyenPtTt.find((gt) => gt.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          sort_key="thoiGianThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianThucHien || 0}
          search={
            <Input
              placeholder={t("danhMuc.thoiGianThucHienDichVuPhut")}
              onChange={onSearchInput("thoiGianThucHien")}
              value={dataSearch?.thoiGianThucHien}
            />
          }
          title={t("danhMuc.thoiGianThucHienDichVuPhut")}
        />
      ),
      width: 200,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      align: "center",
      i18Name: "danhMuc.thoiGianThucHienDichVuPhut",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          sort_key="doiDichVuKhiCoKetQua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.doiDichVuKhiCoKetQua || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("tenTruong.doiDichVuKhiCoKetQua")}
              onChange={onSearchInput("doiDichVuKhiCoKetQua")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.doiDichVuKhiCoKetQua}
            />
          }
          title={t("tenTruong.doiDichVuKhiCoKetQua")}
        />
      ),
      width: 180,
      dataIndex: "doiDichVuKhiCoKetQua",
      key: "doiDichVuKhiCoKetQua",
      align: "center",
      i18Name: "tenTruong.doiDichVuKhiCoKetQua",
      show: true,
      render: (item, data) => {
        return <Checkbox checked={data?.doiDichVuKhiCoKetQua} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="khongThucHienCungThoiDiem"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.khongThucHienCungThoiDiem || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonKhongDuocThucHienSlNhieuCungLuc")}
              onChange={onSearchInput("khongThucHienCungThoiDiem")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.khongThucHienCungThoiDiem}
            />
          }
          title={t("danhMuc.khongDuocThucHienSlNhieuCungLuc")}
        />
      ),
      width: 250,
      dataIndex: "khongThucHienCungThoiDiem",
      key: "khongThucHienCungThoiDiem",
      align: "center",
      i18Name: "danhMuc.khongDuocThucHienSlNhieuCungLuc",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.chiDinhSlLe"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.chiDinhSlLe"] || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={YES_NO}
              placeholder={t("danhMuc.chonKeSlLe")}
              onChange={onSearchInput("dichVu.chiDinhSlLe")}
              hasAllOption={true}
              value={dataSearch["dichVu.chiDinhSlLe"]}
            />
          }
          title={t("danhMuc.choPhepKeSlLe")}
        />
      ),
      width: 130,
      dataIndex: "dichVu",
      key: "dichVu.chiDinhSlLe",
      align: "center",
      i18Name: "danhMuc.choPhepKeSlLe",
      show: true,
      render: (item) => {
        return <Checkbox checked={item?.chiDinhSlLe} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="dichVu.guiVitimes"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.guiVitimes"] || 0}
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("danhMuc.chonTitle", {
                title: t("danhMuc.guiVitimes").toLowerCase(),
              })}
              onChange={onSearchInput("dichVu.guiVitimes")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch["dichVu.guiVitimes"]}
            />
          }
          title={t("danhMuc.guiVitimes")}
        />
      ),
      width: 120,
      dataIndex: "dichVu",
      key: "dichVu.guiVitimes",
      i18Name: "danhMuc.guiVitimes",
      show: true,
      align: "center",
      render: (item) => {
        return <Checkbox checked={item?.guiVitimes} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={onSearchInput("active")}
              defaultValue=""
              hasAllOption={true}
              value={dataSearch?.active}
            />
          }
          title={t("danhMuc.coHieuLuc")}
        />
      ),
      width: 120,
      dataIndex: "active",
      key: "active",
      align: "center",
      ignore: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ghiChu")}
          sort_key="dichVu.ghiChu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichVu.ghiChu"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timGhiChu")}
              onChange={onSearchInput("dichVu.ghiChu")}
              value={dataSearch["dichVu.ghiChu"]}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "dichVu",
      i18Name: "common.ghiChu",
      key: "dichVu.ghiChu",
      show: true,
      render: (item) => {
        return item?.ghiChu && <EllipsisText content={item?.ghiChu} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.xemChiTiet")} />,
      width: 120,
      dataIndex: "action",
      key: "action",
      align: "center",
      ignore: true,
      fixed: "right",
      render: (item, record) => {
        return <SVG.IcEye onClick={onViewDanhMuc(record)} />;
      },
    },
  ];

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const onHandleSizeChange = (size) => {
    onSizeChange({ size: size });
  };

  const onShowAndHandleUpdate = (data = {}) => {
    setEditStatus && setEditStatus(true);
    updateData({
      currentItem: { ...data },
    });
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        onShowAndHandleUpdate(record);
        setState({ currentItem: record });
      },
    };
  };
  const setRowClassName = (record) => {
    let idDiff = currentItem?.id;
    let notValid = !record.active ? "row-gray" : "";

    return record.id == idDiff
      ? "row-actived row-id-" + record.id
      : `row-id-${record.id} ${notValid}`;
  };

  return (
    <Main>
      <TableWrapper
        title={title}
        scroll={{ x: 1000 }}
        buttonHeader={buttonHeader || []}
        classNameRow={classNameRow}
        columns={columns}
        dataSource={listData}
        onRow={onRow}
        rowClassName={setRowClassName}
        ref={refSettings}
        styleContainerButtonHeader={{ ...styleContainerButtonHeader }}
      />
      {!!totalElements ? (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          listData={listData}
          onShowSizeChange={onHandleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
      ) : null}
    </Main>
  );
};

export default DichVuKham;
