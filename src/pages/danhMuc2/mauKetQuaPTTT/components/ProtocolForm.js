import React, { useEffect, useMemo, memo } from "react";
import { useTranslation } from "react-i18next";
import { useQuery } from "@tanstack/react-query";
import { Form, Input } from "antd";
import { TextField, Checkbox } from "components";
import dmProtocolChiTietProvider from "data-access/categories/dm-protocol-chi-tiet-provider";
import styled, { css } from "styled-components";

const FormItem = styled(Form.Item)`
  margin-bottom: 0px;
  &.protocol-comment-item {
    margin-left: 0px !important;
    margin-top: 0px !important;
    flex: 1;
    .ant-form-item-row {
      flex-direction: row;
      .ant-form-item-label {
        display: flex;
        padding: 0 !important;
      }
      .ant-form-item-control {
        flex: 1;
      }
    }
  }
  &.hidden-form-item {
    .ant-form-item-control-input {
      display: none;
    }
  }
  .protocol-checkbox-wrapper {
    margin-left: 20px;
  }
  .ant-form-item-explain-error {
    margin-left: 20px;
  }
  .ant-form-item-label {
    label {
      align-items: start;
    }
  }
`;

const FieldRenderContainer = styled.div`
  margin-bottom: 0px;
  ${({ $hasComment }) =>
    $hasComment &&
    css`
      display: flex;
      textarea {
        top: 3px;
      }
    `}
`;

const ParentChildFormItem = ({
  children,
  parentItem,
  formValues,
  t,
  ...formItemProps
}) => {
  // Tạo field name cho parent group validation
  const parentFieldName = `parent_${parentItem?.id}_validation`;

  // Tạo danh sách dependencies từ child fields
  const childFieldNames = useMemo(() => {
    if (!parentItem?.children) return [];
    return parentItem.children.map((child) => `field_${child.id}`);
  }, [parentItem?.children]);

  // Custom validator cho parent group
  const validateParentGroup = () => {
    if (!parentItem?.batBuoc || !parentItem?.children) {
      return Promise.resolve();
    }

    // Kiểm tra có ít nhất một child được chọn
    const hasSelectedChild = parentItem.children.some((child) => {
      const fieldName = `field_${child.id}`;
      const fieldValue = formValues?.[fieldName];

      return (
        fieldValue === true ||
        (fieldValue && fieldValue.trim && fieldValue.trim() !== "")
      );
    });

    if (!hasSelectedChild) {
      return Promise.reject(new Error(t("danhMuc.vuiLongChonItNhatMotGiaTri")));
    }

    return Promise.resolve();
  };

  if (!parentItem?.batBuoc || !parentItem?.children) {
    return <div>{children}</div>;
  }

  return (
    <div>
      {children}
      <FormItem
        name={parentFieldName}
        dependencies={childFieldNames}
        rules={[{ validator: validateParentGroup }]}
        className="hidden-form-item"
        {...formItemProps}
      >
        <Input hidden />
      </FormItem>
    </div>
  );
};

const ProtocolFormContent = ({
  protocolId,
  disabled = false,
  formValues,
  onSingleSelection,
  style,
  className,
}) => {
  const { t } = useTranslation();

  const { data: listProtocolChiTiet } = useQuery({
    queryKey: ["protocolChiTiet", "listData", protocolId],
    queryFn: () => {
      return dmProtocolChiTietProvider.search({
        protocolId: protocolId,
        page: 0,
        size: 2000,
        active: true,
      });
    },
    enabled: !!protocolId,
    select: (res) => res.data,
  });

  const buildHierarchicalData = (data) => {
    if (!data || !Array.isArray(data)) return [];

    const sortedData = [...data].sort((a, b) => {
      const maA = a.protocolTenTruong?.ma || "";
      const maB = b.protocolTenTruong?.ma || "";
      return maA.localeCompare(maB);
    });

    const flatItems = sortedData.map((item) => {
      const ma = item.protocolTenTruong?.ma || "";
      const level = ma.split(".").length;
      let loaiHienThi = !item?.protocolTenTruong?.tenTruongChaId
        ? 10
        : item.loaiHienThi;

      return {
        ...item,
        key: item.id,
        level,
        children: [],
        ma,
        loaiHienThi,
        orgLoaiHienThi: item.loaiHienThi,
      };
    });

    const rootItems = [];
    const itemMap = new Map();

    flatItems.forEach((item) => {
      itemMap.set(item.ma, item);
    });

    flatItems.forEach((item) => {
      const maParts = item.ma.split(".");
      if (maParts.length > 1) {
        const parentMa = maParts.slice(0, -1).join(".");
        const parent = itemMap.get(parentMa);

        if (parent) {
          item.loaiHienThi = parent.orgLoaiHienThi ?? parent.loaiHienThi;
          parent.children.push(item);
        } else {
          rootItems.push(item);
        }
      } else {
        rootItems.push(item);
      }
    });

    return rootItems;
  };

  const hierarchicalData = useMemo(() => {
    return buildHierarchicalData(listProtocolChiTiet || []);
  }, [listProtocolChiTiet]);

  const flattenTree = (nodes) => {
    const result = [];
    const traverse = (node) => {
      result.push(node);
      if (Array.isArray(node.children) && node.children.length > 0) {
        node.children.forEach(traverse);
      }
    };
    nodes.forEach(traverse);
    return result;
  };

  const flatData = flattenTree(hierarchicalData);

  const handleSingleSelection = (item, checked) => {
    const siblings =
      flatData?.filter(
        (sibling) =>
          sibling.protocolTenTruong?.tenTruongChaId ===
            item.protocolTenTruong?.tenTruongChaId && sibling.loaiHienThi === 20
      ) || [];

    const newValues = { ...formValues };

    siblings.forEach((sibling) => {
      newValues[`field_${sibling.id}`] = false;
    });

    if (checked) {
      newValues[`field_${item.id}`] = true;
    }

    onSingleSelection?.(newValues);
  };

  const renderFormField = (item) => {
    const fieldName = `field_${item.id}`;
    const commentFieldName = `comment_${item.id}`;

    const hasComment = item.ghiChu;
    const showParentAsterisk = item.batBuoc && item.children;

    const labelStyle = {
      fontWeight: item.level === 1 ? "bold" : "normal",
      fontSize: item.level === 1 ? "16px" : "14px",
      color: item.level === 1 ? "#262626" : "#595959",
      marginLeft: 20,
    };

    const label = (
      <span style={labelStyle}>
        {item.protocolTenTruong?.ten}
        {showParentAsterisk && (
          <span style={{ color: "red", marginLeft: "4px" }}>*</span>
        )}
      </span>
    );

    switch (item.loaiHienThi) {
      case 10: // Chữ - TextField hoặc chỉ label
        if (hasComment) {
          // Hiển thị TextField inline với label "Chi tiết" khi có ghiChu = true
          return (
            <FieldRenderContainer key={item.id} $hasComment={hasComment}>
              <div
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  gap: "16px",
                }}
              >
                <div style={{ flex: 1 }}>
                  <FormItem
                    label={label}
                    name={fieldName}
                    $hasComment={hasComment}
                  >
                    <TextField disabled={disabled} />
                  </FormItem>
                </div>
                <div style={{ flex: 1 }}>
                  <FormItem
                    label={
                      <span
                        className="label-custom"
                        style={{
                          ...labelStyle,
                          fontWeight: "normal",
                          marginLeft: 0,
                        }}
                      >
                        {t("common.chiTiet")}:
                      </span>
                    }
                    name={commentFieldName}
                    $hasComment={hasComment}
                    className="protocol-comment-item"
                  >
                    <TextField disabled={disabled} />
                  </FormItem>
                </div>
              </div>
            </FieldRenderContainer>
          );
        } else {
          // Chỉ hiển thị label khi ghiChu = false
          return (
            <FieldRenderContainer key={item.id} $hasComment={hasComment}>
              <div style={labelStyle}>
                {item.protocolTenTruong?.ten}
                {showParentAsterisk && (
                  <span style={{ color: "red", marginLeft: "4px" }}>*</span>
                )}
              </div>
            </FieldRenderContainer>
          );
        }

      case 20: // Chọn một - Single selection checkbox
        return (
          <FieldRenderContainer key={item.id} $hasComment={hasComment}>
            <FormItem
              name={fieldName}
              valuePropName="checked"
              $hasComment={hasComment}
            >
              <Checkbox
                onChange={(e) => handleSingleSelection(item, e.target.checked)}
                className="protocol-checkbox-wrapper"
                disabled={disabled}
              >
                {item.protocolTenTruong?.ten}
              </Checkbox>
            </FormItem>
            {hasComment && formValues?.[fieldName] && (
              <FormItem
                label={
                  <span
                    style={{
                      ...labelStyle,
                      fontWeight: "normal",
                      marginTop: 5,
                    }}
                  >
                    {t("common.chiTiet")}:&nbsp;
                  </span>
                }
                name={commentFieldName}
                style={{
                  marginLeft: 20,
                  marginTop: "-8px",
                }}
                $hasComment={hasComment}
                className="protocol-comment-item"
              >
                <TextField disabled={disabled} />
              </FormItem>
            )}
          </FieldRenderContainer>
        );

      case 30: // Chọn nhiều - Multiple selection checkbox
        return (
          <FieldRenderContainer key={item.id} $hasComment={hasComment}>
            <FormItem
              name={fieldName}
              valuePropName="checked"
              $hasComment={hasComment}
            >
              <Checkbox
                className="protocol-checkbox-wrapper"
                disabled={disabled}
              >
                {item.protocolTenTruong?.ten}
              </Checkbox>
            </FormItem>
            {hasComment && formValues?.[fieldName] && (
              <FormItem
                label={
                  <span
                    style={{
                      ...labelStyle,
                      fontWeight: "normal",
                      marginTop: 5,
                    }}
                  >
                    {t("common.chiTiet")}:&nbsp;
                  </span>
                }
                name={commentFieldName}
                style={{
                  marginLeft: 20,
                  marginTop: "-8px",
                }}
                $hasComment={hasComment}
                className="protocol-comment-item"
              >
                <TextField disabled={disabled} />
              </FormItem>
            )}
          </FieldRenderContainer>
        );

      default:
        return null;
    }
  };

  const renderFormItems = (items, parentItem = null) => {
    return items.map((item) => (
      <div key={item.id}>
        {renderFormField(item, parentItem)}
        {item.children && item.children.length > 0 && (
          <ParentChildFormItem parentItem={item} formValues={formValues} t={t}>
            <div style={{ marginLeft: "20px" }}>
              {renderFormItems(item.children, item)}
            </div>
          </ParentChildFormItem>
        )}
      </div>
    ));
  };

  if (!protocolId) {
    return null;
  }

  return (
    <div style={style} className={className}>
      {hierarchicalData.length > 0 ? (
        renderFormItems(hierarchicalData)
      ) : (
        <div style={{ textAlign: "center", padding: "20px", color: "#999" }}>
          {t("common.khongCoDuLieu")}
        </div>
      )}
    </div>
  );
};

const getProtocolDataFromValues = (values, listProtocolChiTiet) => {
  const dsProtocol = [];

  if (listProtocolChiTiet?.length > 0) {
    listProtocolChiTiet.forEach((item) => {
      const fieldName = `field_${item.id}`;
      const commentFieldName = `comment_${item.id}`;

      dsProtocol.push({
        protocolChiTietId: item.id,
        protocolChiTiet: item,
        noiDung: values[fieldName] || null,
        ghiChu: values[commentFieldName] || null,
      });
    });
  }

  return dsProtocol;
};

const ProtocolForm = ({
  protocolId,
  currentData,
  disabled = false,
  onValuesChange,
  onFinish,
  style,
  className,
  form: externalForm,
  shouldReset = true,
}) => {
  const [internalForm] = Form.useForm();
  const form = externalForm || internalForm;
  const formValues = Form.useWatch([], form);

  const { data: listProtocolChiTiet } = useQuery({
    queryKey: ["protocolChiTiet", "listData", protocolId],
    queryFn: () => {
      return dmProtocolChiTietProvider.search({
        protocolId: protocolId,
        page: 0,
        size: 2000,
        active: true,
      });
    },
    enabled: !!protocolId,
    select: (res) => res.data,
  });

  // Set initial values when data changes
  useEffect(() => {
    if (!shouldReset) return;
    if (listProtocolChiTiet?.length > 0) {
      const initialValues = {};
      const initialComments = {};

      listProtocolChiTiet.forEach((item) => {
        const existingProtocolData = currentData?.find(
          (p) => p.protocolChiTietId === item.id
        );

        if (existingProtocolData) {
          initialValues[`field_${item.id}`] = existingProtocolData.noiDung;
          if (item.ghiChu) {
            initialComments[`comment_${item.id}`] =
              existingProtocolData.ghiChu || "";
          }
        } else {
          if (item.loaiHienThi === 20 || item.loaiHienThi === 30) {
            if (item.macDinh) {
              initialValues[`field_${item.id}`] = true;
            }
          }

          if (item.ghiChu) {
            initialComments[`comment_${item.id}`] = "";
          }
        }
      });
      form.resetFields();
      form.setFieldsValue({ ...initialValues, ...initialComments });
    } else {
      form.resetFields();
    }
  }, [listProtocolChiTiet, form, currentData, shouldReset]);

  const handleSingleSelection = (newValues) => {
    form.setFieldsValue(newValues);
    if (onValuesChange) {
      const protocolData = getProtocolDataFromValues(
        newValues,
        listProtocolChiTiet
      );
      onValuesChange(newValues, newValues, protocolData);
    }
  };

  const handleValuesChange = (changedValues, allValues) => {
    if (onValuesChange) {
      const protocolData = getProtocolDataFromValues(
        allValues,
        listProtocolChiTiet
      );
      onValuesChange(changedValues, allValues, protocolData);
    }
  };

  const handleFinish = (values) => {
    if (onFinish) {
      const protocolData = getProtocolDataFromValues(
        values,
        listProtocolChiTiet
      );

      onFinish(values, protocolData);
    }
  };

  if (!protocolId) {
    return null;
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onValuesChange={handleValuesChange}
      onFinish={handleFinish}
      style={style}
      className={className}
    >
      <ProtocolFormContent
        protocolId={protocolId}
        currentData={currentData}
        disabled={disabled}
        formValues={formValues}
        onSingleSelection={handleSingleSelection}
      />
    </Form>
  );
};

export default memo(ProtocolForm);
