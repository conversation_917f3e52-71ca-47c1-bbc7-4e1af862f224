import React, {
  forwardRef,
  useState,
  useImperative<PERSON><PERSON><PERSON>,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { useDispatch } from "react-redux";
import { cloneDeep } from "lodash";
import { Form, Row, Col, TimePicker, Input, message, Popover } from "antd";
import {
  Checkbox,
  Select,
  TableWrapper,
  ModalTemplate,
  Button,
} from "components";

import {
  DATA_TIME_QMS,
  ENUM,
  LIST_LOAI_QMS,
  TRANG_THAI_HIEN_THI,
  LOAI_PHONG,
  LIST_LOAI_DANH_SACH_QMS,
  LIST_KIEU_GOI_SO,
} from "constants/index";
import moment from "moment";
import {
  useEnum,
  useLazyKVMap,
  useListAll,
  useQueryString,
  useStore,
} from "hooks";
import { SVG } from "assets";
import isofhToolProvider from "data-access/isofh-tool-provider";
import { Main } from "./styled";
import { ISOFH_TOOL_HOST } from "client/request";

const { Column } = TableWrapper;

const Modal = forwardRef((props, ref) => {
  const [kioskId] = useQueryString("kioskId", "");
  const formatHour = "HH:mm";
  const history = useHistory();
  const { t } = useTranslation();
  const refVideo = useRef(null);
  const [state, _setState] = useState({
    value: [],
    data: [],
    dsPhong: [],
    dsTrangThai: [],
  });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [listTrangThaiHienThi] = useEnum(ENUM.TRANG_THAI_HIEN_THI);
  const [listLoaiHienThiQms] = useEnum(ENUM.LOAI_HIEN_THI_QMS);
  const [listLoaiDsQms] = useEnum(ENUM.LOAI_DANH_SACH_QMS);
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const [form] = Form.useForm();
  const mauQmsId = Form.useWatch("mauQmsId", form);
  const dsKhoaId = Form.useWatch("dsKhoaId", form);
  const refModal = useRef(null);
  const listPhong = useStore("phong.listPhong", []);
  const listTemplate = useStore("template.listTemplate", []);
  const listNhanVien = useStore("nhanVien.listNhanVien", []);
  const listDieuDuong = useStore("nhanVien.listDieuDuong", []);
  const listBacSi = useStore("nhanVien.listBacSi", []);
  const listQuayTiepDonTongHop = useStore(
    "quayTiepDon.listQuayTiepDonTongHop",
    []
  );
  const listAllPhong = useStore("phong.listAllPhong", []);
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon", []);
  const listAllKhuVuc = useStore("khuVuc.listAllKhuVuc", []);
  const loaiDanhSach = Form.useWatch("loaiDanhSach", form);
  const isLoadingTemplate = useStore("template.isLoading", false);

  const finishLoadUtils = useMemo(() => {
    return listTrangThaiHienThi.length && !isLoadingTemplate;
  }, [listTrangThaiHienThi, isLoadingTemplate]);

  const {
    phong: { getListPhongTongHop, getListAllPhong },
    nhanVien: { getListNhanVienTongHop },
    kiosk: { createOrEdit, postVideo },
    template: { onChangeInputSearch, updateData },
    kiosk: { getById: getByIdKiosk },
    quayTiepDon: {
      getListTongHop: getListQuayTiepDonTongHop,
      getListAllQuayTiepDon,
    },
    khuVuc: { getListAllKhuVuc },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: (options = {}) => {
      const { loaiQms, dsPhongId, item } = options;
      setState({
        show: true,
        currentItem: item,
        currentData: item,
        loaiQms,
        dsPhongId,
        qmsCdha3: window.location.pathname.includes("chan-doan-hinh-anh3"),
      });
    },
  }));

  const dataTable = useMemo(() => {
    return getListTrangThai(state.loaiQms, mauQmsId);
  }, [state.loaiQms, mauQmsId, listTrangThaiHienThi, listTemplate]);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    const params = { page: "", size: "", active: true };
    if (state?.loaiQms && state?.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON) {
      if (state.loaiQms === LIST_LOAI_QMS.QMS_KHAM_BENH) {
        getListPhongTongHop({
          dsLoaiPhong: [
            LOAI_PHONG.PHONG_KHAM,
            LOAI_PHONG.PHONG_KHAM_TIEM_CHUNG,
          ],
          ...params,
        });
      } else if (state.loaiQms === LIST_LOAI_QMS.QMS_CDHA_TDCN) {
        getListPhongTongHop({
          dsLoaiPhong: [LOAI_PHONG.CAN_LAM_SANG],
          ...params,
        });
      } else if (state.loaiQms === LIST_LOAI_QMS.QMS_PTTT) {
        getListPhongTongHop(params);
      } else {
        getListPhongTongHop({
          loaiPhong: state?.loaiQms,
          ...params,
        });
      }
      if (state.loaiQms === LIST_LOAI_QMS.QMS_PTTT)
        getListAllKhuVuc(
          {
            loai: 40, //loai PTTT
            ...params,
          },
          { saveCache: false }
        );
    }
    onChangeInputSearch({
      loaiQms: state?.loaiQms,
      active: true,
      size: 9999,
    });

    getListAllQuayTiepDon({
      ...params,
      dsLoai: [state.loaiQms === LIST_LOAI_QMS.QMS_THU_NGAN ? 20 : 10],
    });
    getListAllPhong(params);
  }, [state?.loaiQms]);

  useEffect(() => {
    if (state?.show && finishLoadUtils) {
      if (state?.currentItem?.id) {
        getByIdKiosk(state?.currentItem?.id).then((s) => {
          loadCurrentItem(s);
        });
      } else {
        loadCurrentItem();
      }
    }
  }, [state.currentItem, state?.show, finishLoadUtils]);

  const clearFunc = () => {
    isofhToolProvider.putDataCenter({ ma: "QMS_KIOSK_ID", value: null });
  };

  useEffect(() => {
    if (kioskId) {
      isofhToolProvider.putDataCenter({
        ma: "QMS_KIOSK_ID",
        value: parseInt(kioskId),
      });
    } else {
      clearFunc();
    }
  }, [kioskId]);

  const listPhongTrongKhoa = useMemo(() => {
    return dsKhoaId
      ? listPhong.filter((item) => {
          return Array.isArray(dsKhoaId)
            ? dsKhoaId.includes(item.khoaId)
            : item.khoaId === dsKhoaId;
        })
      : listPhong;
  }, [listPhong, dsKhoaId]);

  const [getPhongTrongKhoaById] = useLazyKVMap(listPhongTrongKhoa);
  const [getPhongById] = useLazyKVMap(listAllPhong);

  const renderTime = () => {
    return DATA_TIME_QMS.map((item, index) => {
      return (
        <Col span={12} key={index}>
          <Form.Item
            labelCol={{ span: 10 }}
            wrapperCol={{ span: 14 }}
            rules={[
              {
                required: false,
                message: t("qms.vuiLongChonThoiGianLamViec"),
              },
            ]}
            label={t(item.title)}
            name={item.value}
          >
            <TimePicker
              placeholder={t(item.title)}
              format="HH:mm"
              popupClassName="popup-time-picker"
            />
          </Form.Item>
        </Col>
      );
    });
  };

  function getListTrangThai(loaiQms, mauQmsId, isInitData = false) {
    const STATUS_GROUPS = {
      DEFAULT: [],
      _DEFAULT: [TRANG_THAI_HIEN_THI.CHO_KET_LUAN], // Exclude status
      BASIC: [
        TRANG_THAI_HIEN_THI.DANG_KHAM_THUC_HIEN,
        TRANG_THAI_HIEN_THI.GOI_NHO,
        TRANG_THAI_HIEN_THI.TIEP_THEO,
      ],
      KHAM_BENH: [
        TRANG_THAI_HIEN_THI.DANG_KHAM_THUC_HIEN,
        TRANG_THAI_HIEN_THI.GOI_NHO,
        TRANG_THAI_HIEN_THI.TIEP_THEO,
        TRANG_THAI_HIEN_THI.CHO_KET_LUAN,
      ],
    };

    const filterStatusByIds = (listIds, isExclude = false) => {
      if (!listIds || listIds.length === 0) {
        return [...listTrangThaiHienThi];
      }
      if (!Array.isArray(listIds)) return [];
      return listTrangThaiHienThi.filter((item) =>
        isExclude ? !listIds.includes(item.id) : listIds.includes(item.id)
      );
    };

    const listTrangThaiHienThiDefault = isInitData
      ? listTrangThaiHienThi
      : filterStatusByIds(STATUS_GROUPS._DEFAULT, true);
    const listTrangThaiBasic = filterStatusByIds(STATUS_GROUPS.BASIC);
    const listTrangThaiKhamBenh = filterStatusByIds(STATUS_GROUPS.KHAM_BENH);

    const currentTemplateUri = listTemplate.find((x) => x.id === mauQmsId)?.url;

    const isKhamBenh2 =
      currentTemplateUri === "kham-benh2" ||
      currentTemplateUri === "kham-benh2_1";
    const isChanDoanHinhAnh3 =
      currentTemplateUri === "chan-doan-hinh-anh3" ||
      currentTemplateUri === "chan-doan-hinh-anh3_1";

    switch (loaiQms) {
      case LIST_LOAI_QMS.QMS_TIEP_DON:
      case LIST_LOAI_QMS.QMS_THU_NGAN:
        return listTrangThaiBasic;
      case LIST_LOAI_QMS.QMS_KHAM_BENH:
        return isKhamBenh2
          ? listTrangThaiKhamBenh
          : listTrangThaiHienThiDefault;
      case LIST_LOAI_QMS.QMS_CDHA_TDCN:
        return isChanDoanHinhAnh3
          ? listTrangThaiKhamBenh
          : listTrangThaiHienThiDefault;
      default:
        return listTrangThaiHienThiDefault;
    }
  }

  const loadCurrentItem = async (item) => {
    if (item) {
      let dsKhoaId = item?.dsKhoaId || item?.khoaId;
      dsKhoaId =
        item?.loaiQms === LIST_LOAI_QMS.QMS_PTTT
          ? Array.isArray(dsKhoaId)
            ? dsKhoaId
            : dsKhoaId
            ? [dsKhoaId]
            : null
          : dsKhoaId;

      if (item?.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON) {
        getListQuayTiepDonTongHop({
          khoaId: dsKhoaId + "",
          active: true,
          page: "",
          size: "",
        });
      }

      const data = {
        ...item,
        dsPhongId: item?.dsPhongId || state?.dsPhongId,

        thoiGianChieuDen: item?.thoiGianChieuDen
          ? moment(item?.thoiGianChieuDen, formatHour)
          : null,
        thoiGianChieuTu: item?.thoiGianChieuTu
          ? moment(item?.thoiGianChieuTu, formatHour)
          : null,
        thoiGianSangDen: item?.thoiGianSangDen
          ? moment(item?.thoiGianSangDen, formatHour)
          : null,
        thoiGianSangTu: item?.thoiGianSangTu
          ? moment(item?.thoiGianSangTu, formatHour)
          : null,
        dsKhoaId,
      };
      let bacSi = (listNhanVien || []).filter((x) =>
        (item?.dsBacSiId || []).includes(x.id)
      );

      setState({
        currentId: item?.id,
        currentIndex: -1,
        fileName: item?.dsVideo?.map((item) => {
          return item?.split("/").pop();
        }),
        video: item?.dsVideo,
        bacSi,
        loaiQms: item?.loaiQms,
        dsPhong: item.dsPhong || [],
        goiSo: item?.goiSo,
        dsTrangThai:
          item?.loaiQms === LIST_LOAI_QMS.QMS_KHAM_BENH &&
          !item?.dsTrangThai?.includes(TRANG_THAI_HIEN_THI.DANG_KHAM_THUC_HIEN)
            ? [
                ...(item?.dsTrangThai || []),
                TRANG_THAI_HIEN_THI.DANG_KHAM_THUC_HIEN,
              ]
            : item?.dsTrangThai || [],
      });
      form.setFieldsValue(data);
    } else {
      form.resetFields();

      setState({
        dsTrangThai: getListTrangThai(state.loaiQms, _, true).map((x) => x.id),
        dsPhong: [],
        goiSo: "",
      });
    }
  };

  const generateLink = (path) => {
    let link = `/qms/qms-ngang/${path}`;
    history.push(link);
  };
  const onChangeCheckbox = (id) => (e) => {
    if (!state.loaiQms) return;

    if (
      id === TRANG_THAI_HIEN_THI.DANG_KHAM_THUC_HIEN &&
      state.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON
    ) {
      return;
    }

    if (e.target.checked) {
      setState({
        dsTrangThai: [...state.dsTrangThai, id],
      });
    } else {
      setState({
        dsTrangThai: state.dsTrangThai.filter((x) => x !== id),
      });
    }
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 30,
      dataIndex: "index",
      key: "index",
      align: "center",
      i18Name: "common.stt",
      render: (item, data, index) => {
        return index + 1;
      },
    }),
    Column({
      title: t("qms.tenTruongTrenMhKiosk"),
      width: 200,
      dataIndex: "name",
      key: "name",
      align: "left",
      i18Name: "qms.tenTruongTrenMhKiosk",
      render: (_, record) => {
        return (listTrangThaiHienThi || []).find((x) => x.id == record.id)?.ten;
      },
    }),
    Column({
      title: t("qms.hienThi"),
      width: 50,
      dataIndex: "active",
      key: "active",
      align: "center",
      i18Name: "qms.hienThi",
      render: (_, data) => {
        return (
          <Checkbox
            onChange={onChangeCheckbox(data.id)}
            checked={state.dsTrangThai.includes(data.id)}
          />
        );
      },
    }),
  ];

  const onCancel = () => {
    setState({ show: false });
  };

  const onChangeField = (key) => (e) => {
    if (key === "dsKhoaId") {
      const dsKhoaId = Array.isArray(e) ? e : [e];
      getListNhanVienTongHop({
        active: true,
        page: "",
        size: "",
        dsKhoaId,
      });
      if (state.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON) {
        getListQuayTiepDonTongHop({
          khoaId: dsKhoaId + "",
          active: true,
          page: "",
          size: "",
        });
        form.setFieldValue("dsQuayId", []);
      }
    }
    if (key === "loaiPhong") {
      getListPhongTongHop({ loaiPhong: e, active: true, page: "", size: "" });
    }
  };

  const onSave = () => {
    form.submit();
  };

  const onHanldeSubmit = (values) => {
    const {
      thoiGianChieuDen,
      thoiGianChieuTu,
      thoiGianSangDen,
      thoiGianSangTu,
      loaiDanhSach,
      khuVucId,
      dsKhoaId,
    } = values;

    if (state?.dsPhong?.length > 8) {
      message.warning(t("qms.vuiLongChonToiDa8Phong"));
      return;
    }

    const { video, goiSo } = state;
    const data = {
      ...values,
      dsPhongId: values?.dsPhongId,
      dsTrangThai: state.dsTrangThai.filter((x) =>
        dataTable.some((y) => y.id === x)
      ),
      loaiQms: state?.loaiQms || state?.loaiQms,
      thoiGianChieuDen: thoiGianChieuDen
        ? thoiGianChieuDen.format("HH:mm")
        : null,
      thoiGianChieuTu: thoiGianChieuTu ? thoiGianChieuTu.format("HH:mm") : null,
      thoiGianSangDen: thoiGianSangDen ? thoiGianSangDen.format("HH:mm") : null,
      thoiGianSangTu: thoiGianSangTu ? thoiGianSangTu.format("HH:mm") : null,
      dsVideo: Array.isArray(video) ? video : Array(video),
      id: state.currentId,
      dsPhong: state?.dsPhong,
      loaiDanhSach,
      khuVucId,
      dsKhoaId: Array.isArray(dsKhoaId)
        ? dsKhoaId
        : dsKhoaId
        ? [dsKhoaId]
        : null,
      goiSo: Array.isArray(goiSo) ? goiSo[0] : goiSo ? goiSo : null,
    };

    createOrEdit(data).then((s) => {
      isofhToolProvider.reloadVisualize();
      updateData({ currentKiosk: s });

      generateLink(
        `${listTemplate.find((x) => x.id === s.mauQmsId)?.url}?kioskId=${s?.id}`
      );
      setState({ dsTrangThai: [], show: false });
      if (kioskId) {
        getByIdKiosk(kioskId);
      }
    });
  };

  const handleUploadFile = (event) => {
    event.preventDefault();
    return refVideo.current.click();
  };

  const selectVideo = (data) => {
    let type =
      data.target &&
      data.target.files &&
      data.target.files[0] &&
      data.target.files[0].type;
    if (type === "video/mp4") {
      let fileUpload = "";
      let fileName = "";
      fileUpload = data.target.files[0] || {};
      fileName = fileUpload.name;
      setState({
        fileName,
        fileUpload,
      });
      postVideo(fileUpload).then((s) => {
        setState({ video: s?.data });
      });
    }
  };

  const onReset = () => {
    if (state.currentItem?.id) {
      getByIdKiosk(state?.currentItem?.id).then((s) => {
        loadCurrentItem(s);
      });
    } else {
      loadCurrentItem();

      setState({ currentIndex: -1 });
    }
  };

  const onChangeInput = (key, index) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
    } else value = e;
    state.dsPhong[index][key] = value;
    setState({
      dsPhong: state.dsPhong,
    });
  };

  const onDelete = (record, index) => () => {
    const data = cloneDeep(state.dsPhong);
    setState({ dsPhong: data.filter((x) => x !== data[index]) });
  };

  const columnsBacSi = [
    Column({
      title: t("common.phong"),
      width: 200,
      dataIndex: "phongId",
      key: "phongId",
      align: "left",
      render: (item, record, index) => {
        return (
          <Select
            value={item}
            data={listPhongTrongKhoa}
            onChange={onChangeInput("phongId", index)}
            setFallbackValue={(value) => {
              const phong = getPhongTrongKhoaById(value);
              return phong
                ? value
                : getPhongById(value)?.ten ?? record?.phong?.ten ?? value;
            }}
          />
        );
      },
    }),
    Column({
      title: t("qms.bacSi"),
      width: 200,
      dataIndex: "bacSiId",
      key: "bacSiId",
      align: "left",
      render: (item, _, index) => {
        return (
          <Select
            value={item}
            data={listBacSi}
            onChange={onChangeInput("bacSiId", index)}
          />
        );
      },
    }),
    Column({
      title: t("qms.hienThiLenQMS"),
      width: 100,
      dataIndex: "hienThiQms",
      key: "hienThiQms",
      align: "center",
      render: (item, _, index) => {
        return (
          <Checkbox
            onChange={onChangeInput("hienThiQms", index)}
            checked={item}
          ></Checkbox>
        );
      },
    }),

    Column({
      title: t("common.thaoTac"),
      width: 80,
      dataIndex: "action",
      key: "action",
      align: "center",
      render: (item, _, index) => {
        return (
          <SVG.IcDelete className="ic-action" onClick={onDelete(_, index)} />
        );
      },
    }),
  ];

  const onCreate = () => {
    let dsPhong = [
      ...state?.dsPhong,
      { bacSiId: null, phongId: null, hienThiQms: true },
    ];
    setState({ dsPhong });
  };

  const renderKhoa = (span = 12) => {
    return (
      <Col xs={span}>
        <Form.Item
          name="dsKhoaId"
          label={t("common.khoa")}
          {...(!(
            loaiDanhSach === LIST_LOAI_DANH_SACH_QMS.TOAN_VIEN ||
            loaiDanhSach === LIST_LOAI_DANH_SACH_QMS.KHU_VUC
          ) && {
            rules: [
              {
                required: true,
                message: t("qms.vuiLongChonKhoa"),
              },
            ],
          })}
        >
          <Select
            placeholder={t("qms.chonKhoa")}
            data={listAllKhoa}
            onChange={onChangeField("dsKhoaId")}
            {...(loaiDanhSach === LIST_LOAI_DANH_SACH_QMS.KHU_VUC &&
              state.loaiQms === LIST_LOAI_QMS.QMS_PTTT && {
                mode: "multiple",
              })}
          />
        </Form.Item>
      </Col>
    );
  };

  const contentPopover = (
    <div style={{ lineHeight: 1.2 }}>
      <p style={{ marginBottom: 4 }}>
        <strong>{"{STT}"}</strong>
        {`: ${t("qms.soThuTuBenhNhan")}`}
      </p>
      <p style={{ marginBottom: 4 }}>
        <strong>{"{TEN_NGUOI_BENH}"}</strong>
        {`: ${t("qms.tenBenhNhan")}`}
      </p>
      <p style={{ marginBottom: 4 }}>
        <strong>{"{TUOI}"}</strong>
        {`: ${t("qms.tuoiBenhNhan")}`}
      </p>
      <p style={{ marginBottom: 4 }}>
        <strong>{"{UU_TIEN}"}</strong>
        {`: ${t("common.uuTien")} (${t("common.neuCo").toLowerCase()})`}
      </p>
      <p style={{ marginBottom: 4 }}>
        <strong>{"{QUAY}"}</strong>
        {`: ${t("danhMuc.tenQuay")}`}
      </p>
      <p style={{ marginBottom: 0 }}>
        <strong>{"{PHONG}"}</strong>
        {`: ${t("danhMuc.tenPhong")}`}
      </p>
    </div>
  );

  return (
    <ModalTemplate
      ref={refModal}
      title={t("qms.thietLapThongSo")}
      width={1840}
      onCancel={onCancel}
      actionLeft={
        <Button onClick={onReset} className="btn-reset">
          {t("qms.datLai")}
        </Button>
      }
      actionRight={
        <Button
          className="btn-save"
          type="primary"
          onClick={onSave}
          rightIcon={<SVG.IcSave />}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          className="form-custom"
          onFinish={onHanldeSubmit}
        >
          <Row gutter={[12, 12]}>
            <Col xs={12}>
              <Row gutter={[12, 12]}>
                <Col xs={12}>
                  <Form.Item
                    name="ten"
                    label={t("qms.tenThietBi")}
                    rules={[
                      {
                        required: true,
                        message: t("qms.vuiLongNhapTenThietBi"),
                      },
                    ]}
                  >
                    <Input placeholder={t("qms.nhapTenThietBi")}></Input>
                  </Form.Item>
                </Col>
                {state.loaiQms === LIST_LOAI_QMS.QMS_PTTT && (
                  <Col xs={12}>
                    <Form.Item
                      name="loaiDanhSach"
                      label={t("qms.loaiDanhSach")}
                      rules={[
                        {
                          required: true,
                          message: t("qms.vuiLongChonLoaiDanhSach") + "!",
                        },
                      ]}
                    >
                      <Select
                        data={listLoaiDsQms || []}
                        placeholder={t("qms.vuiLongChonLoaiDanhSach")}
                      />
                    </Form.Item>
                  </Col>
                )}
                {loaiDanhSach == LIST_LOAI_DANH_SACH_QMS.KHU_VUC && (
                  <Col span={12}>
                    <Form.Item
                      name="khuVucId"
                      label={t("danhMuc.khuVuc")}
                      rules={[
                        {
                          required: true,
                          message: t("qms.vuiLongChonKhuVuc") + "!",
                        },
                      ]}
                    >
                      <Select
                        placeholder={t("danhMuc.chonKhuVuc")}
                        data={listAllKhuVuc}
                      />
                    </Form.Item>
                  </Col>
                )}
                {state?.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON &&
                  state?.loaiQms !== LIST_LOAI_QMS.QMS_THU_NGAN &&
                  renderKhoa()}

                {state?.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON &&
                  state?.loaiQms !== LIST_LOAI_QMS.QMS_THU_NGAN && (
                    <Col xs={12}>
                      <Form.Item name="hoTroId" label={t("qms.hoTro")}>
                        <Select
                          required={true}
                          message={t("qms.vuiLongChonHoTroHd")}
                          placeholder={t("qms.chonHoTroHd")}
                          data={listDieuDuong}
                        />
                      </Form.Item>
                    </Col>
                  )}

                <Col xs={12}>
                  <Form.Item name="mac" label={t("qms.diaChiMac")}>
                    <Input placeholder={t("qms.nhapDiaChiMac")}></Input>
                  </Form.Item>
                </Col>
                {state?.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON &&
                  state?.loaiQms !== LIST_LOAI_QMS.QMS_THU_NGAN && (
                    <Col xs={12}>
                      <Form.Item name="dieuDuongId" label={t("qms.yTa")}>
                        <Select
                          required={true}
                          message={t("qms.vuiLongChonYta")}
                          placeholder={t("qms.chonTroLyYta")}
                          data={listDieuDuong}
                        />
                      </Form.Item>
                    </Col>
                  )}
                {state?.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON &&
                  state?.loaiQms !== LIST_LOAI_QMS.QMS_THU_NGAN && (
                    <Col
                      span={24}
                      style={{ display: "flex", flexDirection: "column" }}
                    >
                      <div className="phong-bac-si">
                        <div className="left">
                          {t("qms.luaChonPhongKhamVaBacSi")}
                        </div>
                        <div className="right">
                          <Button
                            type="success"
                            onClick={onCreate}
                            rightIcon={<SVG.IcAdd />}
                          >
                            <span>{t("common.themMoi")}</span>
                          </Button>
                        </div>
                      </div>
                      <div className="table-phong">
                        <TableWrapper
                          columns={columnsBacSi}
                          dataSource={state?.dsPhong}
                          rowKey={(record) => record.key}
                          styleWrap={{ height: "300px" }}
                          rowClassName={() => {
                            return "";
                          }}
                          scroll={{ x: false, y: 300 }}
                        />
                      </div>
                    </Col>
                  )}
                {state?.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON &&
                  renderKhoa(24)}
                {(state?.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON ||
                  state?.loaiQms === LIST_LOAI_QMS.QMS_THU_NGAN) && (
                  <Col span={24}>
                    <Form.Item
                      name="dsQuayId"
                      label={
                        state?.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON
                          ? t("qms.quayTiepDon")
                          : t("qms.quayThuNgan")
                      }
                      rules={[
                        {
                          required: true,
                          message: t(
                            `qms.${
                              state?.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON
                                ? "vuiLongChonQuayTiepDon"
                                : "vuiLongChonQuayThuNgan"
                            }`
                          ),
                        },
                      ]}
                    >
                      <Select
                        mode="multiple"
                        placeholder={t(
                          `qms.${
                            state?.loaiQms === LIST_LOAI_QMS.QMS_TIEP_DON
                              ? "chonQuayTiepDon"
                              : "chonQuayThuNgan"
                          }`
                        )}
                        data={listQuayTiepDonTongHop}
                      />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Col>

            <Col xs={12}>
              <Row gutter={[12, 12]}>
                {state?.loaiQms !== LIST_LOAI_QMS.QMS_TIEP_DON &&
                  state?.loaiQms !== LIST_LOAI_QMS.QMS_THU_NGAN && (
                    <>
                      <Col span={24} className="thoi-gian-lam-viec">
                        <Form.Item
                          colon={false}
                          label={t("qms.thoiGianLamViec")}
                        ></Form.Item>
                      </Col>
                      {renderTime()}
                    </>
                  )}
                <Col span={24}>
                  <TableWrapper
                    columns={columns}
                    dataSource={dataTable}
                    rowKey={(record) => record.name}
                    rowClassName={() => {
                      return "";
                    }}
                  ></TableWrapper>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name="mauQmsId"
                    label={t("qms.mauQms")}
                    rules={[
                      {
                        required: true,
                        message: t("qms.vuiLongChonMaMau"),
                      },
                    ]}
                    className="custom"
                  >
                    <Select
                      required={true}
                      message={t("qms.vuiLongChonTemplate")}
                      placeholder={t("qms.chonTemplate")}
                      data={listTemplate}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    style={{ width: "100%" }}
                    label={
                      <div className="flex flex-center">
                        {t("danhMuc.kieuGoiSo")}&nbsp;
                        <Popover
                          content={contentPopover}
                          title={t("qms.chuThichTruongKieuGoiSo")}
                        >
                          <SVG.IcQuestion />
                        </Popover>
                      </div>
                    }
                  >
                    {state.goiSo ? (
                      <Input
                        allowClear
                        value={state.goiSo}
                        onChange={(e) => {
                          setState({ goiSo: e?.target?.value });
                        }}
                      />
                    ) : (
                      <Select
                        mode="tags"
                        placeholder={t("danhMuc.chonKieuGoiSo")}
                        onChange={(value, options) => {
                          if (options?.length === 0 || options?.length === 1) {
                            setState({ goiSo: value });
                          }
                        }}
                        value={state.goiSo || []}
                        data={LIST_KIEU_GOI_SO}
                        showArrow
                      ></Select>
                    )}
                  </Form.Item>
                </Col>
                {[
                  LIST_LOAI_QMS.QMS_TIEP_DON,
                  LIST_LOAI_QMS.QMS_THU_NGAN,
                ].includes(state?.loaiQms) && (
                  <Col span={24}>
                    <Form.Item label={t("qms.quayGoiSo")} name="dsQuayGoiSoId">
                      <Select
                        mode="multiple"
                        data={listAllQuayTiepDon}
                        placeholder={t("danhMuc.chonTitle", {
                          title: t("qms.quayGoiSo").toLowerCase(),
                        })}
                      ></Select>
                    </Form.Item>
                  </Col>
                )}
                {[LIST_LOAI_QMS.QMS_CDHA_TDCN].includes(state?.loaiQms) && (
                  <Col span={24}>
                    <Form.Item
                      label={t("editor.loaiHienThi")}
                      name="loaiHienThiQms"
                    >
                      <Select
                        data={listLoaiHienThiQms}
                        placeholder={t("danhMuc.chonTitle", {
                          title: t("editor.loaiHienThi").toLowerCase(),
                        })}
                      ></Select>
                    </Form.Item>
                  </Col>
                )}
                <Col span={24}>
                  <Form.Item label={"Video"} className="custom">
                    <div className="video">
                      <div className="left">
                        <a>{state.fileName}</a>
                      </div>
                      <div className="right">
                        <input
                          style={{ display: "none" }}
                          accept="video/*"
                          type="file"
                          onChange={(e) => selectVideo(e)}
                          ref={refVideo}
                        ></input>
                        <Button
                          onClick={handleUploadFile}
                          className="btn-upload"
                          rightIcon={<SVG.IcAdd />}
                          minWidth="100%"
                        >
                          <span>Upload</span>
                        </Button>
                      </div>
                    </div>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
      </Main>
    </ModalTemplate>
  );
});

export default Modal;
