export const dmThuocTtGoiDvFields = [
  {
    name: "ma",
    i18n: "danhMuc.maThuoc",
    required: true,
  },
  {
    name: "ten",
    i18n: "danhMuc.tenThuoc",
    required: true,
  },
  {
    name: "hoatChatId",
    i18n: "danhMuc.maHoatChat",
    required: true,
  },
  {
    name: "tenHoatChat",
    i18n: "danhMuc.tenHoatChat",
    required: true,
  },
  {
    name: "hamLuong",
    i18n: "danhMuc.hamLuong",
    required: true,
  },
  {
    name: "nhomDvKhoCap1Id",
    i18n: "danhMuc.nhomThuocCap1",
    required: false,
  },
  {
    name: "nhomDvKhoCap2Id",
    i18n: "danhMuc.nhomThuocCap2",
    required: false,
  },
  {
    name: "nhomDvKhoCap3Id",
    i18n: "danhMuc.nhomThuocCap3",
    required: false,
  },
  {
    name: "phanNhomDvKhoId",
    i18n: "danhMuc.phanNhomThuoc",
    required: false,
  },
  {
    name: "phanLoaiDvKhoId",
    i18n: "danhMuc.phanLoaiThuoc",
    required: true,
  },
  {
    name: "dvtSoCapId",
    i18n: "danhMuc.donViSoCap",
    required: true,
  },
  {
    name: "dvtThuCapId",
    i18n: "danhMuc.donViThuCap",
    required: true,
  },
  {
    name: "heSoDinhMuc",
    i18n: "danhMuc.heSoDinhMuc",
    required: true,
  },
  {
    name: "dvtSuDungId",
    i18n: "danhMuc.donViSuDung",
    required: true,
  },
  {
    name: "dungTich",
    i18n: "danhMuc.dungTich",
    required: false,
  },
  {
    name: "quyCach",
    i18n: "danhMuc.quyCach",
    required: true,
  },
  {
    name: "xuatXuId",
    i18n: "danhMuc.nuocSanXuat",
    required: false,
  },
  {
    name: "nhaSanXuatId",
    i18n: "common.nhaSanXuat",
    required: false,
  },
  {
    name: "nhaCungCapId",
    i18n: "danhMuc.nhaCungCap",
    required: false,
  },
  {
    name: "giaNhapSauVat",
    i18n: "danhMuc.giaSauVAT1DvtSoCap",
    required: false,
  },
  {
    name: "tyLeBhTt",
    i18n: "danhMuc.tyLeBhThanhToan",
    required: false,
  },
  {
    name: "tyLeBhTt",
    i18n: "danhMuc.tyLeBhThanhToan",
    required: true,
  },
  {
    name: "tyLeTtDv",
    i18n: "danhMuc.tyLeThanhToanDichVu",
    required: true,
  },
  {
    name: "nhomDichVuCap1Id",
    i18n: "danhMuc.nhomDichVuCap1",
    required: true,
  },
  {
    name: "nhomDichVuCap2Id",
    i18n: "danhMuc.nhomDichVuCap2",
    required: false,
  },
  {
    name: "nhomDichVuCap3Id",
    i18n: "danhMuc.nhomDichVuCap3",
    required: false,
  },
  {
    name: "maTuongDuong",
    i18n: "danhMuc.maTuongDuong",
    required: false,
  },
  {
    name: "tenTuongDuong",
    i18n: "danhMuc.tenTuongDuong",
    required: false,
  },
  {
    name: "soVisa",
    i18n: "danhMuc.soVisa",
    required: true,
  },
  {
    name: "dsDichVuThayTheId",
    i18n: "danhMuc.maVatTuThayThe",
    required: false,
  },
  {
    name: "maLienThong",
    i18n: "danhMuc.maLienThongDuocQuocGia",
    required: false,
  },
  {
    name: "nguonKhacId",
    i18n: "danhMuc.nguonChiTraKhac",
    required: false,
  },
  {
    name: "dsKhoaCdDvtSoCapId",
    i18n: "danhMuc.khoaChiDinhTheoDvtSoCap",
    required: false,
  },
  {
    name: "dsMucDichSuDung",
    i18n: "danhMuc.mucDichSuDung",
    required: false,
  },
  {
    name: "duongDungId",
    i18n: "danhMuc.duongDung",
    required: true,
  },
  {
    name: "huongDanSuDung",
    i18n: "danhMuc.huongDanSuDung",
    required: false,
  },
  {
    name: "maAtc",
    i18n: "danhMuc.maAtc",
    required: false,
  },
  {
    name: "hamLuongDdd",
    i18n: "danhMuc.hamLuongDdd",
    required: false,
  },
  {
    name: "whoDdd",
    i18n: "danhMuc.whoDdd",
    required: false,
  },
  {
    name: "donViDdd",
    i18n: "danhMuc.donViDddWho",
    required: false,
  },
  {
    name: "nhomThau",
    i18n: "danhMuc.nhomThau",
    required: true,
  },
  {
    name: "goiThau",
    i18n: "danhMuc.goiThau",
    required: false,
  },
  {
    name: "thongTinThau",
    i18n: "danhMuc.thongTinThau",
    required: false,
  },
  {
    name: "quyetDinhThau",
    i18n: "danhMuc.quyetDinhThau",
    required: false,
  },
  {
    name: "tenTrungThau",
    i18n: "kho.quyetDinhThau.tenHangHoaTrungThau",
    required: false,
  },
  {
    name: "tenMoiThau",
    i18n: "danhMuc.tenMoiThau",
    required: false,
  },
  {
    name: "phieuLinhId",
    i18n: "danhMuc.maPhieuLinh",
    required: false,
  },
  {
    name: "soNgayCanhBaoHsd",
    i18n: "danhMuc.soNgayCanhBaoHsd",
    required: false,
  },
  {
    name: "loaiLamTron",
    i18n: "danhMuc.loaiLamTron",
    required: false,
  },
  {
    name: "soGioCanhBaoHuy",
    i18n: "danhMuc.soGioCanhBaoThuocHuy",
    required: false,
  },
  {
    name: "dsPhuongPhapCheBienId",
    i18n: "danhMuc.phuongPhapCheBien",
    required: false,
  },
  {
    name: "dangBaoChe",
    i18n: "danhMuc.dangBaoChe",
    required: false,
  },
  {
    name: "soLuongToiThieu",
    i18n: "danhMuc.soLuongTonToiThieu",
    required: false,
  },
  {
    name: "duyetKhangSinh",
    i18n: "danhMuc.mucDoPheDuyetThuocKhangSinh",
    required: false,
  },
  {
    name: "maSinhPham",
    i18n: "danhMuc.maSinhPham",
    required: false,
  },
  {
    name: "maSinhHieu",
    i18n: "danhMuc.maHieuSinhPham",
    required: false,
  },
  {
    name: "maVitimes",
    i18n: "danhMuc.maGuiVitimes",
    required: false,
  },
  {
    name: "tuoiTho",
    i18n: "danhMuc.tuoiThoCuaThuoc",
    required: false,
  },
  {
    name: "canhBao",
    i18n: "danhMuc.canhBaoSuDungThuoc",
    required: false,
  },
  {
    name: "nhaNhapKhau",
    i18n: "danhMuc.nhaNhapKhau",
    required: false,
  },
  {
    name: "dieuKienBaoQuan",
    i18n: "danhMuc.dieuKienBaoQuan",
    required: false,
  },
  {
    name: "sttTt20",
    i18n: "danhMuc.sttTrongTt20",
    required: false,
  },
  {
    name: "ghiChuHoatChat",
    i18n: "danhMuc.ghiChuHoatChat",
    required: false,
  },
  {
    name: "ven",
    i18n: "danhMuc.ven",
    required: false,
  },
  {
    name: "soLuongGioiHan1Ngay",
    i18n: "danhMuc.soLuongGioiHan1Ngay",
    required: false,
  },
  {
    name: "maTckt",
    i18n: "danhMuc.maTckt",
    required: false,
  },
  {
    name: "tenTckt",
    i18n: "danhMuc.tenTckt",
    required: false,
  },
  {
    name: "soLuongGioiHan1LanKham",
    i18n: "danhMuc.slGioiHanLuotKcbNgoaiTruBhyt",
    required: false,
  },
  {
    name: "tuChua",
    i18n: "danhMuc.tuChua",
    required: false,
  },
  {
    name: "dsTaiLieuHdsd",
    i18n: "danhMuc.fileHdsdDungThuoc",
    required: false,
  },
  // Checkbox fields
  {
    name: "chiDinhSlLe",
    i18n: "danhMuc.choPhepKeSlLe",
    required: false,
  },
  {
    name: "theoDoiNgaySd",
    i18n: "danhMuc.theoDoiNgaySd",
    required: false,
  },
  {
    name: "khongTinhTien",
    i18n: "danhMuc.khongTinhTien",
    required: false,
  },
  {
    name: "thuocDauSao",
    i18n: "danhMuc.thuocDauSao",
    required: false,
  },
  {
    name: "kyHieu",
    i18n: "danhMuc.corticoid",
    required: false,
  },
  {
    name: "mucDichSuDung",
    i18n: "danhMuc.apDungTt20",
    required: false,
  },
  {
    name: "trongPtTt",
    i18n: "danhMuc.thuocPhauThuat",
    required: false,
  },
  {
    name: "trungTenThuongMai",
    i18n: "danhMuc.trungTenThuongMai",
    required: false,
  },
  {
    name: "guiVitimes",
    i18n: "danhMuc.guiVitimes",
    required: false,
  },
  {
    name: "mienPhiGiamDocDuyet",
    i18n: "danhMuc.mienPhiGiamDocDuyet",
    required: false,
  },
  {
    name: "online",
    i18n: "danhMuc.guiISC",
    required: false,
  },
  {
    name: "chatLuongCamQuan",
    i18n: "danhMuc.chatLuongCamQuan",
    required: false,
  },
  {
    name: "lieuDung",
    i18n: "danhMuc.yeuCauTheoDoi",
    required: false,
  },
  {
    name: "phaChe",
    i18n: "danhMuc.yeuCauPhaChe",
    required: false,
  },
  {
    name: "lasa",
    i18n: "danhMuc.thuocLasa",
    required: false,
  },
  {
    name: "nguyCoCao",
    i18n: "danhMuc.thuocNguyCoCao",
    required: false,
  },
  {
    name: "kiemTraHangNgay",
    i18n: "danhMuc.kiemTraHangNgay",
    required: false,
  },
  {
    name: "mimsGuid",
    i18n: "danhMuc.mimsGuid",
    required: false,
  },
  {
    name: "mimsType",
    i18n: "danhMuc.mimsType",
    required: false,
  },
  {
    name: "maByt",
    i18n: "danhMuc.maThuocByt",
    required: false,
  },
  {
    name: "active",
    i18n: "danhMuc.coHieuLuc",
    required: false,
  },
];

export const dmVatTuThongTinVatTuFields = [
  {
    name: "ma",
    i18n: "danhMuc.maVatTu",
    required: true,
  },
  {
    name: "ten",
    i18n: "danhMuc.tenVatTu",
    required: true,
  },
  {
    name: "nhomDvKhoCap1Id",
    i18n: "danhMuc.nhomVatTuCap1",
    required: false,
  },
  {
    name: "nhomDvKhoCap2Id",
    i18n: "danhMuc.nhomVatTuCap2",
    required: false,
  },
  {
    name: "nhomDvKhoCap3Id",
    i18n: "danhMuc.nhomVatTuCap3",
    required: false,
  },
  {
    name: "nhomDvKhoCap4Id",
    i18n: "danhMuc.nhomVatTuCap4",
    required: false,
  },
  {
    name: "maKyHieu",
    i18n: "danhMuc.maKyHieuTenThuongMai",
    required: false,
  },
  {
    name: "dvtSoCapId",
    i18n: "danhMuc.donViSoCap",
    required: false,
  },
  {
    name: "dvtThuCapId",
    i18n: "danhMuc.donViThuCap",
    required: true,
  },
  {
    name: "heSoDinhMuc",
    i18n: "danhMuc.heSoDinhMuc",
    required: true,
  },
  {
    name: "thongSoKyThuat",
    i18n: "danhMuc.thongSoKyThuat",
    required: false,
  },
  {
    name: "quyCach",
    i18n: "danhMuc.quyCach",
    required: false,
  },
  {
    name: "xuatXuId",
    i18n: "danhMuc.nuocSanXuat",
    required: false,
  },
  {
    name: "nhaSanXuatId",
    i18n: "common.nhaSanXuat",
    required: false,
  },
  {
    name: "nhaCungCapId",
    i18n: "danhMuc.nhaCungCap",
    required: false,
  },
  {
    name: "giaNhapSauVat",
    i18n: "danhMuc.giaNhap",
    required: false,
  },
  {
    name: "giaKhongBaoHiem",
    i18n: "danhMuc.giaKhongBaoHiem",
    required: false,
  },
  {
    name: "giaBaoHiem",
    i18n: "danhMuc.giaBaoHiem",
    required: false,
  },
  {
    name: "giaPhuThu",
    i18n: "danhMuc.giaPhuThu",
    required: false,
  },
  {
    name: "tranBaoHiem",
    i18n: "danhMuc.tranBaoHiem",
    required: false,
  },
  {
    name: "tyLeBhTt",
    i18n: "danhMuc.tyLeBhThanhToan",
    required: false,
  },
  {
    name: "tyLeTtDv",
    i18n: "danhMuc.tyLeThanhToanDichVu",
    required: true,
  },
  {
    name: "nhomDichVuCap1Id",
    i18n: "danhMuc.nhomDVCap1",
    required: true,
  },
  {
    name: "nhomDichVuCap2Id",
    i18n: "danhMuc.nhomDVCap2",
    required: true,
  },
  {
    name: "nhomDichVuCap3Id",
    i18n: "danhMuc.nhomDVCap3",
    required: false,
  },
  {
    name: "maTuongDuong",
    i18n: "danhMuc.maTuongDuong",
    required: false,
  },
  {
    name: "tenTuongDuong",
    i18n: "danhMuc.tenTuongDuong",
    required: false,
  },
  {
    name: "dsMucDichSuDung",
    i18n: "danhMuc.mucDichSuDung",
    required: false,
  },
  {
    name: "nguonKhacId",
    i18n: "danhMuc.nguonChiTraKhac",
    required: false,
  },
  {
    name: "tenMoiThau",
    i18n: "danhMuc.tenMoiThau",
    required: false,
  },
  {
    name: "quyetDinhThau",
    i18n: "danhMuc.quyetDinhThau",
    required: false,
  },
  {
    name: "nhomThau",
    i18n: "danhMuc.nhomThau",
    required: false,
  },
  {
    name: "goiThau",
    i18n: "danhMuc.goiThau",
    required: false,
  },
  {
    name: "thongTinThau",
    i18n: "danhMuc.thongTinThau",
    required: false,
  },
  {
    name: "tenTrungThau",
    i18n: "kho.quyetDinhThau.tenHangHoaTrungThau",
    required: false,
  },
  {
    name: "soNgayCanhBaoHsd",
    i18n: "danhMuc.soNgayCanhBaoHsd",
    required: false,
  },
  {
    name: "loaiLamTron",
    i18n: "danhMuc.loaiLamTron",
    required: false,
  },
  {
    name: "phieuLinhId",
    i18n: "danhMuc.maPhieuLinh",
    required: false,
  },
  {
    name: "maSinhPham",
    i18n: "danhMuc.maSinhPham",
    required: false,
  },
  {
    name: "maSinhHieu",
    i18n: "danhMuc.maHieuSinhPham",
    required: false,
  },
  {
    name: "maTckt",
    i18n: "danhMuc.maTckt",
    required: false,
  },
  {
    name: "tenTckt",
    i18n: "danhMuc.tenTckt",
    required: false,
  },
  {
    name: "soVisa",
    i18n: "danhMuc.soVisa",
    required: false,
  },
  {
    name: "dsDichVuThayTheId",
    i18n: "danhMuc.maVatTuThayThe",
    required: false,
  },
  {
    name: "phanLoaiDvKhoId",
    i18n: "danhMuc.phanLoaiVTYT",
    required: false,
  },
  {
    name: "khongTinhTien",
    i18n: "danhMuc.khongTinhTien",
    required: false,
  },
  {
    name: "chuaGomTrongGiuong",
    i18n: "danhMuc.chuaGomTrongGiuong",
    required: false,
  },
  // Checkbox fields
  {
    name: "vatTuBo",
    i18n: "danhMuc.vatTuBo",
    required: false,
  },
  {
    name: "vatTuKichCo",
    i18n: "danhMuc.vatTuTheoKichCo",
    required: false,
  },
  {
    name: "stentPhuThuoc",
    i18n: "danhMuc.stentPhuThuoc",
    required: false,
  },
  {
    name: "kyThuatCao",
    i18n: "danhMuc.kyThuatCao",
    required: false,
  },
  {
    name: "vatTuTaiSuDung",
    i18n: "danhMuc.vatTuTaiSuDung",
    required: false,
  },
  {
    name: "chayMay",
    i18n: "danhMuc.vatTuChayMay",
    required: false,
  },
  {
    name: "trongPtTt",
    i18n: "danhMuc.thuocPhauThuat",
    required: false,
  },
  {
    name: "chiDinhSlLe",
    i18n: "danhMuc.choPhepKeSlLe",
    required: false,
  },
  {
    name: "mienPhiGiamDocDuyet",
    i18n: "danhMuc.mienPhiGiamDocDuyet",
    required: false,
  },
  {
    name: "active",
    i18n: "danhMuc.coHieuLuc",
    required: false,
  },
];

export const listAllFields = {
  "dmThuoc.thongTinDichVu": dmThuocTtGoiDvFields,
  "dmVatTu.thongTinVatTu": dmVatTuThongTinVatTuFields,
};

export const DEFAULT_COMPONENTS = {
  "dmThuoc.thongTinDichVu": [
    "ma",
    "ten",
    "hoatChatId",
    "tenHoatChat",
    "hamLuong",
    "nhomDvKhoCap1Id",
    "nhomDvKhoCap2Id",
    "nhomDvKhoCap3Id",
    "phanNhomDvKhoId",
    "phanLoaiDvKhoId",
    "dvtSoCapId",
    "dvtThuCapId",
    "heSoDinhMuc",
    "dvtSuDungId",
    "dungTich",
    "quyCach",
    "xuatXuId",
    "nhaSanXuatId",
    "nhaCungCapId",
    "giaNhapSauVat",
    "tyLeBhTt",
    "tyLeTtDv",
    "nhomDichVuCap1Id",
    "nhomDichVuCap2Id",
    "nhomDichVuCap3Id",
    "maTuongDuong",
    "tenTuongDuong",
    "soVisa",
    "dsDichVuThayTheId",
    "maLienThong",
    "nguonKhacId",
    "dsKhoaCdDvtSoCapId",
    "dsMucDichSuDung",
    "duongDungId",
    "huongDanSuDung",
    "maAtc",
    "hamLuongDdd",
    "whoDdd",
    "donViDdd",
    "nhomThau",
    "goiThau",
    "thongTinThau",
    "quyetDinhThau",
    "tenTrungThau",
    "tenMoiThau",
    "phieuLinhId",
    "soNgayCanhBaoHsd",
    "loaiLamTron",
    "soGioCanhBaoHuy",
    "dsPhuongPhapCheBienId",
    "dangBaoChe",
    "soLuongToiThieu",
    "duyetKhangSinh",
    "maSinhPham",
    "maSinhHieu",
    "maVitimes",
    "tuoiTho",
    "canhBao",
    "nhaNhapKhau",
    "dieuKienBaoQuan",
    "sttTt20",
    "ghiChuHoatChat",
    "ven",
    "soLuongGioiHan1Ngay",
    "maTckt",
    "tenTckt",
    "soLuongGioiHan1LanKham",
    "tuChua",
    "dsTaiLieuHdsd",
    "chiDinhSlLe",
    "theoDoiNgaySd",
    "khongTinhTien",
    "thuocDauSao",
    "kyHieu",
    "mucDichSuDung",
    "trongPtTt",
    "trungTenThuongMai",
    "guiVitimes",
    "mienPhiGiamDocDuyet",
    "online",
    "chatLuongCamQuan",
    "lieuDung",
    "phaChe",
    "lasa",
    "nguyCoCao",
    "kiemTraHangNgay",
    "active",
  ],

  "dmVatTu.thongTinVatTu": [
    "ma",
    "ten",
    "nhomDvKhoCap1Id",
    "nhomDvKhoCap2Id",
    "nhomDvKhoCap3Id",
    "nhomDvKhoCap4Id",
    "maKyHieu",
    "dvtSoCapId",
    "dvtThuCapId",
    "heSoDinhMuc",
    "thongSoKyThuat",
    "quyCach",
    "xuatXuId",
    "nhaSanXuatId",
    "nhaCungCapId",
    "giaNhapSauVat",
    "giaKhongBaoHiem",
    "giaBaoHiem",
    "giaPhuThu",
    "tranBaoHiem",
    "tyLeBhTt",
    "tyLeTtDv",
    "nhomDichVuCap1Id",
    "nhomDichVuCap2Id",
    "nhomDichVuCap3Id",
    "maTuongDuong",
    "tenTuongDuong",
    "dsMucDichSuDung",
    "nguonKhacId",
    "tenMoiThau",
    "quyetDinhThau",
    "nhomThau",
    "goiThau",
    "thongTinThau",
    "tenTrungThau",
    "soNgayCanhBaoHsd",
    "loaiLamTron",
    "phieuLinhId",
    "maSinhPham",
    "maSinhHieu",
    "maTckt",
    "tenTckt",
    "phanLoaiDvKhoId",
    "soVisa",
    "dsDichVuThayTheId",
    "chuaGomTrongGiuong",
    "vatTuBo",
    "vatTuKichCo",
    "stentPhuThuoc",
    "kyThuatCao",
    "khongTinhTien",
    "vatTuTaiSuDung",
    "chayMay",
    "trongPtTt",
    "chiDinhSlLe",
    "mienPhiGiamDocDuyet",
    "active",
  ],
};
