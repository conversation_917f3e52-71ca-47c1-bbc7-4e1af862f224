import { useMemo, useRef, useCallback } from "react";
import { useEnum, useQueryAll } from "hooks";
import { DS_TINH_CHAT_KHOA, ENUM, YES_NO } from "constants/index";
import { safeConvertToArray } from "utils";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";

export const LOAI_HO_SO_SINH = {
  THUONG: 10,
  BENH_LICH: 20,
  THAI_LUU: 40,
};

export const listTinhTrang = [
  { id: "true", ten: "Sống" },
  { id: "false", ten: "Chết" },
];

export const useConstant = () => {
  const listThongTinSanPhu = useRef([
    {
      id: "loaiHoSoSinh",
      i18n: "quanLyNoiTru.thongTinCon.loaiHoSoSinh",
      type: "select",
      required: false,
    },
    {
      id: "tenCha",
      i18n: "giayDayCong.tenCha",
      type: "input",
      required: false,
    },
    {
      id: "hinhThucSinhId",
      i18n: "quanLyNoiTru.thongTinCon.cachThucDe",
      type: "select",
      required: false,
    },
    {
      id: "ngoiThaiId",
      i18n: "quanLyNoiTru.thongTinCon.ngoiThai",
      type: "select",
      required: false,
    },
    {
      id: "lanSinh",
      i18n: "quanLyNoiTru.thongTinCon.deLanThu",
      type: "number",
      required: true,
      min: 0,
    },
    {
      id: "soConSinhLanNay",
      i18n: "quanLyNoiTru.thongTinCon.soConSinhLanNay",
      type: "number",
      required: true,
      maxLength: 1,
      min: 0,
    },
    {
      id: "soMauMat",
      i18n: "quanLyNoiTru.thongTinCon.tongSoMauMat",
      type: "number",
      suffix: "ml",
      min: 0,
    },
    {
      id: "bienPhapCamMauId",
      i18n: "quanLyNoiTru.thongTinCon.cacBienPhapCamMau",
      type: "select",
      required: false,
    },
    {
      id: "bacSiPhauThuatId",
      i18n: "quanLyNoiTru.thongTinCon.bacSiPhauThuat",
      type: "select",
      required: false,
      onGetText: selectMaTen,
      onGetTextSearch: selectMaTen,
    },
    {
      id: "nguoiDo1Id",
      i18n: "quanLyNoiTru.thongTinCon.nguoiDo1",
      type: "select",
      required: false,
      onGetText: selectMaTen,
      onGetTextSearch: selectMaTen,
    },
    {
      id: "nguoiDo2Id",
      i18n: "quanLyNoiTru.thongTinCon.nguoiDo2",
      type: "select",
      required: false,
      onGetText: selectMaTen,
      onGetTextSearch: selectMaTen,
    },
    {
      id: "tienThai",
      i18n: "quanLyNoiTru.thongTinCon.tienThaiPara",
      type: "input",
      maxLength: 4,
      readonly: true,
    },
    {
      id: "soLanSinhConDuThang",
      i18n: "quanLyNoiTru.thongTinCon.soLanSinhConDuThang",
      type: "number",
      maxLength: 1,
      min: 0,
    },
    {
      id: "soLanSinhConThieuThang",
      i18n: "quanLyNoiTru.thongTinCon.soLanSinhConNhieuThang",
      type: "number",
      maxLength: 1,
      min: 0,
    },
    {
      id: "soLanSayThai",
      i18n: "quanLyNoiTru.thongTinCon.soLanSayThaiHoacHutThai",
      type: "number",
      maxLength: 1,
      min: 0,
    },
    {
      id: "soConConSong",
      i18n: "quanLyNoiTru.thongTinCon.soConHienConSong",
      type: "number",
      maxLength: 1,
      min: 0,
    },
    {
      id: "tinhTrangCon",
      i18n: "quanLyNoiTru.thongTinCon.tinhTrangSucKhoe",
      type: "input",
      required: false,
    },
    {
      id: "ketQua",
      i18n: "quanLyNoiTru.thongTinCon.xuLyVaLuuKetQua",
      type: "input",
      required: false,
    },
    {
      id: "corticoid",
      i18n: "quanLyNoiTru.thongTinCon.corticoid",
      type: "select",
      required: true,
      listSelect: YES_NO,
    },
    {
      id: "mgSO4",
      i18n: "quanLyNoiTru.thongTinCon.mgSO4",
      type: "select",
      required: true,
      listSelect: YES_NO,
    },
    {
      id: "diUng",
      i18n: "quanLyNoiTru.thongTinCon.diUng",
      type: "input",
      required: false,
    },
    {
      id: "thoiGianVoOi",
      i18n: "quanLyNoiTru.thongTinCon.thoiGianVoOi",
      type: "datetime",
      showTime: true,
      format: "DD/MM/YYYY HH:mm",
    },
    {
      id: "mauSacOi",
      i18n: "quanLyNoiTru.thongTinCon.mauSacOi",
      type: "multiSelect",
      required: false,
    },
    {
      id: "mauSacOiKhac",
      i18n: "quanLyNoiTru.thongTinCon.mauSacOiKhac",
      type: "input",
      required: false,
    },
    {
      id: "luongOi",
      i18n: "quanLyNoiTru.thongTinCon.luongOi",
      type: "select",
      required: false,
    },
    {
      id: "khoaSinhId",
      i18n: "quanLyNoiTru.thongTinCon.khoaDe",
      type: "select",
      onGetText: selectMaTen,
      onGetTextSearch: selectMaTen,
    },
  ]).current;

  const listTinhTrangSucKhoeMe = useRef([
    {
      id: "tpha",
      i18n: "quanLyNoiTru.thongTinCon.tpha",
      type: "checkbox",
      required: false,
    },
    {
      id: "viemGanB",
      i18n: "quanLyNoiTru.thongTinCon.viemGanB",
      type: "checkbox",
      required: false,
    },
    {
      id: "lienCauKhuanBgbs",
      i18n: "quanLyNoiTru.thongTinCon.lienCauKhuanNhomBgbs",
      type: "checkbox",
      required: false,
    },
    {
      id: "hiv",
      i18n: "quanLyNoiTru.thongTinCon.hiv",
      type: "checkbox",
      required: false,
    },
    {
      id: "hbsAg",
      i18n: "quanLyNoiTru.thongTinCon.hbsAg",
      type: "checkbox",
      required: false,
    },
    {
      id: "hbeAg",
      i18n: "quanLyNoiTru.thongTinCon.hbeAg",
      type: "checkbox",
      required: false,
    },
    {
      id: "tieuDuongThaiKy",
      i18n: "quanLyNoiTru.thongTinCon.tieuDuongThaiKy",
      type: "checkbox",
      required: false,
    },
    {
      id: "tienSanGiat",
      i18n: "quanLyNoiTru.thongTinCon.tienSanGiat",
      type: "checkbox",
      required: false,
    },
    {
      id: "rauTienDao",
      i18n: "quanLyNoiTru.thongTinCon.rauTienDaoCaiRangLuoc",
      type: "checkbox",
      required: false,
    },
    {
      id: "rauBongNon",
      i18n: "quanLyNoiTru.thongTinCon.rauBongNon",
      type: "checkbox",
      required: false,
    },
    {
      id: "tangHuyetAp",
      i18n: "quanLyNoiTru.thongTinCon.tangHuyetAp",
      type: "checkbox",
      required: false,
    },
    {
      id: "benhLyTuyenGiap",
      i18n: "quanLyNoiTru.thongTinCon.benhLyTuyenGiap",
      type: "checkbox",
      required: false,
    },
    {
      id: "sotTruocSinh",
      i18n: "quanLyNoiTru.thongTinCon.sotTruocSinh",
      type: "checkbox",
      required: false,
    },
    {
      id: "viemAmDao",
      i18n: "quanLyNoiTru.thongTinCon.viemAmDao",
      type: "checkbox",
      required: false,
    },
    {
      id: "nhiemTrungTietNieu",
      i18n: "quanLyNoiTru.thongTinCon.nhiemTrungTietNieu",
      type: "checkbox",
      required: false,
    },
  ]).current;

  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listTinhTrangThai] = useEnum(ENUM.TINH_TRANG_THAI);
  const [listNoiSinh] = useEnum(ENUM.NOI_SINH);
  const [listLoaiHoSoSinh] = useEnum(ENUM.LOAI_HO_SO_SINH);
  const [listMauSacOi] = useEnum(ENUM.MAU_SAC_OI);
  const [listLuongOi] = useEnum(ENUM.LUONG_OI);

  const { data: listAllCachThucDe } = useQueryAll(
    query.cachThucDe.queryAllCachThucDe
  );
  const { data: listAllNgoiThai } = useQueryAll(
    query.ngoiThai.queryAllNgoiThai
  );
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );
  const { data: listAllBienPhapCamMau } = useQueryAll(
    query.bienPhapCamMau.queryAllBienPhapCamMau
  );
  const { data: listAllMaBenh } = useQueryAll(query.maBenh.queryAllMabenh);
  const { data: listAllDiTatBamSinh } = useQueryAll(
    query.diTatBamSinh.queryAllDiTatBamSinh
  );
  const { data: listAllKhoaNoiTru } = useQueryAll(
    query.khoa.queryAllKhoa({
      params: {
        dsTinhChatKhoa: [DS_TINH_CHAT_KHOA.NOI_TRU],
      },
    })
  );

  const allFieldDefinitions = useMemo(() => {
    const map = new Map();
    const allLists = [listThongTinSanPhu, listTinhTrangSucKhoeMe];

    allLists.forEach((list) => {
      safeConvertToArray(list).forEach((field) => {
        if (field && field.id) {
          if (map.has(field.id)) {
            console.warn(`Duplicate field ID found: ${field.id}. Overwriting.`);
          }

          let enhancedField = { ...field };

          switch (field.id) {
            case "hinhThucSinhId":
              enhancedField.listSelect = listAllCachThucDe;
              break;
            case "ngoiThaiId":
              enhancedField.listSelect = listAllNgoiThai;
              break;
            case "bacSiPhauThuatId":
            case "nguoiDo1Id":
            case "nguoiDo2Id":
              enhancedField.listSelect = listAllNhanVien;
              break;
            case "bienPhapCamMauId":
              enhancedField.listSelect = listAllBienPhapCamMau;
              break;
            case "loaiHoSoSinh":
              enhancedField.listSelect = listLoaiHoSoSinh;
              break;
            case "luongOi":
              enhancedField.listSelect = listLuongOi;
              break;
            case "mauSacOi":
              enhancedField.listSelect = listMauSacOi;
              break;
            case "khoaSinhId":
              enhancedField.listSelect = listAllKhoaNoiTru;
              break;
            default:
              break;
          }

          map.set(field.id, enhancedField);
        }
      });
    });

    return map;
  }, [
    listThongTinSanPhu,
    listTinhTrangSucKhoeMe,
    listGioiTinh,
    listTinhTrangThai,
    listNoiSinh,
    listLoaiHoSoSinh,
    listMauSacOi,
    listLuongOi,
    listAllCachThucDe,
    listAllNgoiThai,
    listAllNhanVien,
    listAllBienPhapCamMau,
    listAllKhoaNoiTru,
  ]);

  const getFieldDefinitionById = useCallback(
    (fieldId) => {
      return allFieldDefinitions.get(fieldId);
    },
    [allFieldDefinitions]
  );

  return {
    // Field definitions
    listThongTinSanPhu,
    listTinhTrangSucKhoeMe,
    getFieldDefinitionById,
    allFieldDefinitions,

    // Enum data
    listGioiTinh,
    listTinhTrangThai,
    listNoiSinh,
    listLoaiHoSoSinh,
    listMauSacOi,
    listLuongOi,

    // List data
    listAllCachThucDe,
    listAllNgoiThai,
    listAllNhanVien,
    listAllBienPhapCamMau,
    listAllMaBenh,
    listAllDiTatBamSinh,
    listAllKhoaNoiTru,

    // Constants
    LOAI_HO_SO_SINH,
    listTinhTrang,
    YES_NO,
  };
};
