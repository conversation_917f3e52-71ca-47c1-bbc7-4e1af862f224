// Cấu hình layout cho Form Thông Tin Sản Phụ

export const initialLayout = {
  sectionOrder: ["thongTinSanPhu", "tinhTrangSucKhoeMe"],
  sections: {
    thongTinSanPhu: {
      id: "thongTinSanPhu",
      i18n: "quanLyNoiTru.thongTinCon.thongTinSanPhu",
      isCollapsible: true,
      fieldOrder: [
        "loaiHoSoSinh",
        "tenCha",
        "hinhThucSinhId",
        "ngoiThaiId",
        "lanSinh",
        "soConSinhLanNay",
        "soMauMat",
        "bienPhapCamMauId",
        "bacSiPhauThuatId",
        "nguoiDo1Id",
        "nguoiDo2Id",
        "tienThai",
        "soLanSinhConDuThang",
        "soLanSinhConThieuThang",
        "soLanSayThai",
        "soConConSong",
        "tinhTrangCon",
        "ketQua",
        "corticoid",
        "mgSO4",
        "diUng",
        "thoiGianVoOi",
        "mauSacOi",
        "mauSacOiKhac",
        "luongOi",
        "khoaSinhId",
      ],
      fieldLayout: {
        loaiHoSoSinh: {
          span: 6,
          offset: 0,
          active: true,
        },
        tenCha: {
          span: 6,
          offset: 0,
          active: true,
        },
        hinhThucSinhId: {
          span: 6,
          offset: 0,
          active: true,
        },
        ngoiThaiId: {
          span: 6,
          offset: 0,
          active: true,
        },
        lanSinh: {
          span: 6,
          offset: 0,
          active: true,
        },
        soConSinhLanNay: {
          span: 6,
          offset: 0,
          active: true,
        },
        soMauMat: {
          span: 6,
          offset: 0,
          active: true,
        },
        bienPhapCamMauId: {
          span: 6,
          offset: 0,
          active: true,
        },
        bacSiPhauThuatId: {
          span: 6,
          offset: 0,
          active: true,
        },
        nguoiDo1Id: {
          span: 6,
          offset: 0,
          active: true,
        },
        nguoiDo2Id: {
          span: 6,
          offset: 0,
          active: true,
        },
        tienThai: {
          span: 6,
          offset: 0,
          active: true,
        },
        soLanSinhConDuThang: {
          span: 6,
          offset: 0,
          active: true,
        },
        soLanSinhConThieuThang: {
          span: 6,
          offset: 0,
          active: true,
        },
        soLanSayThai: {
          span: 6,
          offset: 0,
          active: true,
        },
        soConConSong: {
          span: 6,
          offset: 0,
          active: true,
        },
        tinhTrangCon: {
          span: 6,
          offset: 0,
          active: true,
        },
        ketQua: {
          span: 6,
          offset: 0,
          active: true,
        },
        corticoid: {
          span: 6,
          offset: 0,
          active: true,
        },
        mgSO4: {
          span: 6,
          offset: 0,
          active: true,
        },
        diUng: {
          span: 6,
          offset: 0,
          active: true,
        },
        thoiGianVoOi: {
          span: 6,
          offset: 0,
          active: true,
        },
        mauSacOi: {
          span: 6,
          offset: 0,
          active: true,
        },
        mauSacOiKhac: {
          span: 6,
          offset: 0,
          active: true,
        },
        luongOi: {
          span: 6,
          offset: 0,
          active: true,
        },
        khoaSinhId: {
          span: 6,
          offset: 0,
          active: true,
        },
      },
    },
    tinhTrangSucKhoeMe: {
      id: "tinhTrangSucKhoeMe",
      i18n: "quanLyNoiTru.thongTinCon.tinhTrangSucKhoeMe",
      isCollapsible: true,
      fieldOrder: [
        "tpha",
        "viemGanB",
        "lienCauKhuanBgbs",
        "hiv",
        "hbsAg",
        "hbeAg",
        "tieuDuongThaiKy",
        "tienSanGiat",
        "rauTienDao",
        "rauBongNon",
        "tangHuyetAp",
        "benhLyTuyenGiap",
        "sotTruocSinh",
        "viemAmDao",
        "nhiemTrungTietNieu",
      ],
      fieldLayout: {
        tpha: {
          span: 6,
          offset: 0,
          active: true,
        },
        viemGanB: {
          span: 6,
          offset: 0,
          active: true,
        },
        lienCauKhuanBgbs: {
          span: 6,
          offset: 0,
          active: true,
        },
        hiv: {
          span: 6,
          offset: 0,
          active: true,
        },
        hbsAg: {
          span: 6,
          offset: 0,
          active: true,
        },
        hbeAg: {
          span: 6,
          offset: 0,
          active: true,
        },
        tieuDuongThaiKy: {
          span: 6,
          offset: 0,
          active: true,
        },
        tienSanGiat: {
          span: 6,
          offset: 0,
          active: true,
        },
        rauTienDao: {
          span: 6,
          offset: 0,
          active: true,
        },
        rauBongNon: {
          span: 6,
          offset: 0,
          active: true,
        },
        tangHuyetAp: {
          span: 6,
          offset: 0,
          active: true,
        },
        benhLyTuyenGiap: {
          span: 6,
          offset: 0,
          active: true,
        },
        sotTruocSinh: {
          span: 6,
          offset: 0,
          active: true,
        },
        viemAmDao: {
          span: 6,
          offset: 0,
          active: true,
        },
        nhiemTrungTietNieu: {
          span: 6,
          offset: 0,
          active: true,
        },
      },
    },
  },
};

// Định nghĩa các field cơ bản
export const fieldDefinitions = {
  // Thông tin sản phụ
  loaiHoSoSinh: {
    i18n: "quanLyNoiTru.thongTinCon.loaiHoSoSinh",
    type: "select",
    required: false,
  },
  tenCha: {
    i18n: "giayDayCong.tenCha",
    type: "input",
    required: false,
  },
  hinhThucSinhId: {
    i18n: "quanLyNoiTru.thongTinCon.cachThucDe",
    type: "select",
    required: false,
  },
  ngoiThaiId: {
    i18n: "quanLyNoiTru.thongTinCon.ngoiThai",
    type: "select",
    required: false,
  },
  lanSinh: {
    i18n: "quanLyNoiTru.thongTinCon.deLanThu",
    type: "number",
    required: true,
  },
  soConSinhLanNay: {
    i18n: "quanLyNoiTru.thongTinCon.soConSinhLanNay",
    type: "number",
    required: true,
  },
  soMauMat: {
    i18n: "quanLyNoiTru.thongTinCon.tongSoMauMat",
    type: "number",
    required: false,
    unit: "ml",
  },
  bienPhapCamMauId: {
    i18n: "quanLyNoiTru.thongTinCon.cacBienPhapCamMau",
    type: "select",
    required: false,
  },
  bacSiPhauThuatId: {
    i18n: "quanLyNoiTru.thongTinCon.bacSiPhauThuat",
    type: "select",
    required: false,
  },
  nguoiDo1Id: {
    i18n: "quanLyNoiTru.thongTinCon.nguoiDo1",
    type: "select",
    required: false,
  },
  nguoiDo2Id: {
    i18n: "quanLyNoiTru.thongTinCon.nguoiDo2",
    type: "select",
    required: false,
  },
  tienThai: {
    i18n: "quanLyNoiTru.thongTinCon.tienThaiPara",
    type: "input",
    required: false,
  },
  soLanSinhConDuThang: {
    i18n: "quanLyNoiTru.thongTinCon.soLanSinhConDuThang",
    type: "number",
    required: false,
  },
  soLanSinhConThieuThang: {
    i18n: "quanLyNoiTru.thongTinCon.soLanSinhConNhieuThang",
    type: "number",
    required: false,
  },
  soLanSayThai: {
    i18n: "quanLyNoiTru.thongTinCon.soLanSayThaiHoacHutThai",
    type: "number",
    required: false,
  },
  soConConSong: {
    i18n: "quanLyNoiTru.thongTinCon.soConHienConSong",
    type: "number",
    required: false,
  },
  tinhTrangCon: {
    i18n: "quanLyNoiTru.thongTinCon.tinhTrangSucKhoe",
    type: "input",
    required: false,
  },
  ketQua: {
    i18n: "quanLyNoiTru.thongTinCon.xuLyVaLuuKetQua",
    type: "input",
    required: false,
  },
  corticoid: {
    i18n: "quanLyNoiTru.thongTinCon.corticoid",
    type: "select",
    required: true,
  },
  mgSO4: {
    i18n: "quanLyNoiTru.thongTinCon.mgSO4",
    type: "select",
    required: true,
  },
  diUng: {
    i18n: "quanLyNoiTru.thongTinCon.diUng",
    type: "input",
    required: false,
  },
  thoiGianVoOi: {
    i18n: "quanLyNoiTru.thongTinCon.thoiGianVoOi",
    type: "datetime",
    required: false,
  },
  mauSacOi: {
    i18n: "quanLyNoiTru.thongTinCon.mauSacOi",
    type: "multiSelect",
    required: false,
  },
  mauSacOiKhac: {
    i18n: "quanLyNoiTru.thongTinCon.mauSacOiKhac",
    type: "input",
    required: false,
  },
  luongOi: {
    i18n: "quanLyNoiTru.thongTinCon.luongOi",
    type: "select",
    required: false,
  },
  khoaSinhId: {
    i18n: "quanLyNoiTru.thongTinCon.khoaDe",
    type: "select",
    required: false,
  },

  // Tình trạng sức khỏe mẹ
  tpha: {
    i18n: "quanLyNoiTru.thongTinCon.tpha",
    type: "checkbox",
    required: false,
  },
  viemGanB: {
    i18n: "quanLyNoiTru.thongTinCon.viemGanB",
    type: "checkbox",
    required: false,
  },
  lienCauKhuanBgbs: {
    i18n: "quanLyNoiTru.thongTinCon.lienCauKhuanNhomBgbs",
    type: "checkbox",
    required: false,
  },
  hiv: {
    i18n: "quanLyNoiTru.thongTinCon.hiv",
    type: "checkbox",
    required: false,
  },
  hbsAg: {
    i18n: "quanLyNoiTru.thongTinCon.hbsAg",
    type: "checkbox",
    required: false,
  },
  hbeAg: {
    i18n: "quanLyNoiTru.thongTinCon.hbeAg",
    type: "checkbox",
    required: false,
  },
  tieuDuongThaiKy: {
    i18n: "quanLyNoiTru.thongTinCon.tieuDuongThaiKy",
    type: "checkbox",
    required: false,
  },
  tienSanGiat: {
    i18n: "quanLyNoiTru.thongTinCon.tienSanGiat",
    type: "checkbox",
    required: false,
  },
  rauTienDao: {
    i18n: "quanLyNoiTru.thongTinCon.rauTienDaoCaiRangLuoc",
    type: "checkbox",
    required: false,
  },
  rauBongNon: {
    i18n: "quanLyNoiTru.thongTinCon.rauBongNon",
    type: "checkbox",
    required: false,
  },
  tangHuyetAp: {
    i18n: "quanLyNoiTru.thongTinCon.tangHuyetAp",
    type: "checkbox",
    required: false,
  },
  benhLyTuyenGiap: {
    i18n: "quanLyNoiTru.thongTinCon.benhLyTuyenGiap",
    type: "checkbox",
    required: false,
  },
  sotTruocSinh: {
    i18n: "quanLyNoiTru.thongTinCon.sotTruocSinh",
    type: "checkbox",
    required: false,
  },
  viemAmDao: {
    i18n: "quanLyNoiTru.thongTinCon.viemAmDao",
    type: "checkbox",
    required: false,
  },
  nhiemTrungTietNieu: {
    i18n: "quanLyNoiTru.thongTinCon.nhiemTrungTietNieu",
    type: "checkbox",
    required: false,
  },
};
