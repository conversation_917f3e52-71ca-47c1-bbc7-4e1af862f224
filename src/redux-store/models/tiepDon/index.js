import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { message } from "antd";
import moment from "moment";
import { select } from "redux-store/stores";
import {
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
  DOI_TUONG,
  CACHE_KEY,
  DOI_TUONG_KCB,
  PHAN_LOAI_DOI_TUONG,
  VALIDATE_CASE_SDT,
} from "constants/index";
import {
  isBoolean,
  isNumber,
  isValidMaTheByht,
  openInNewTab,
} from "utils/index";
import cacheUtils from "lib-utils/cache-utils";
import { t } from "i18next";
import { cloneDeep, get } from "lodash";
import thietLapProvider from "data-access/dm-thiet-lap-provider";
import goiSoProvider from "data-access/goi-so-provider";
import nbTheBaoHiemProvider from "data-access/nb-the-bao-hiem-provider";
import ENV from "module_env/ENV";
import { showError } from "utils/message-utils";

const parseDefaultData = (
  data,
  isTiepDonHenDieuTri = false,
  isTuDongTichCoLichHenKham = false
) => {
  const { nbTheBaoHiem, thoiGianVaoVien, ...rest } = data || {};
  const { tuNgayHoNgheo, denNgayHoNgheo, soHoNgheo, ..._nbTheBaoHiem } =
    nbTheBaoHiem || {};
  const hoNgheo = !!soHoNgheo;
  return {
    ...rest,
    thoiGianVaoVien,
    ...(isTiepDonHenDieuTri ? { boQuaCheckThe: false } : {}),
    ...(nbTheBaoHiem && {
      nbTheBaoHiem: {
        ..._nbTheBaoHiem,
        hoNgheo,
        ...(hoNgheo && {
          tuNgayHoNgheo: tuNgayHoNgheo
            ? moment(tuNgayHoNgheo)
            : thoiGianVaoVien
              ? moment(thoiGianVaoVien).startOf("year")
              : moment().startOf("year"),
          denNgayHoNgheo: denNgayHoNgheo
            ? moment(denNgayHoNgheo)
            : thoiGianVaoVien
              ? moment(thoiGianVaoVien).endOf("year")
              : moment().endOf("year"),
          soHoNgheo,
        }),
        ...(isTiepDonHenDieuTri
          ? {
            boQuaTheLoi: false,
            noiGioiThieuId: null,
            thoiGianDu5Nam: null,
            tuNgayMienCungChiTra: null,
            denNgayMienCungChiTra: null,
            khuVuc: null,
            henKhamLai: null,
            ...(isTuDongTichCoLichHenKham &&
              data?.doiTuong === DOI_TUONG.BAO_HIEM && {
              henKhamLai: true,
            }),
          }
          : {}),
      },
    }),
    ...(isTiepDonHenDieuTri && {
      uuTien: false,
      capCuu: false,
      nbCapCuu: {
        ...data?.nbCapCuu,
        chuaXacDinhDanhTinh: false,
        khongCoNguoiThanDiKem: false,
        loaiCapCuuId: null,
        taiNanThuongTichId: null,
        thoiGianCapCuuId: null,
        viTriChanThuongId: null,
      },
      nbTongKetRaVien: {
        ...data?.nbTongKetRaVien,
        cdNoiGioiThieu: null,
        dsCdNoiGioiThieuId: null,
      },
      nbMienGiam: {
        ...data?.nbMienGiam,
        loaiMienGiam: null,
        nguoiDuyetId: null,
      },
      nbNgoaiVien: {
        ...data?.nbNgoaiVien,
        maHoSo: null,
      },
    }),
  };
};

export default {
  state: {
    nbDiaChi: {},
    theBaoHiem: {},
    thongTinBenhNhan: {},
    boQuaCheckThe: false,
    thietLap: {},
    loaiDoiTuongId: null,
    thongTinThanhToan: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...payload, thietLap: state.thietLap }; // không clear giá trị thiết lập
    },
    updateThietLap(state, payload = {}) {
      return { ...state, thietLap: { ...state.thietLap, ...payload } };
    },
  },
  effects: (dispatch) => ({
    onSetCapCuu: (payload, state) => {
      const { capCuu, id } = state.tiepDon;
      const { quayTiepDonId } = state.goiSo;
      const { dataMA_KHOA_CAP_CUU } = state.thietLap;
      const { listAllQuayTiepDonTaiKhoan = [] } = state.quayTiepDon;
      let newCapCuu = false;
      let khoa = listAllQuayTiepDonTaiKhoan?.find(
        (x) => x.id == quayTiepDonId
      )?.khoa;
      if (khoa && dataMA_KHOA_CAP_CUU && dataMA_KHOA_CAP_CUU === khoa?.ma) {
        newCapCuu = true;
      } else if (id) {
        // giải quyết vấn đề khi vào dịch vụ => sửa thông tin , capCuu bị set thành false
        newCapCuu = capCuu;
      } else {
        newCapCuu = false;
      }
      if (capCuu != newCapCuu)
        dispatch.tiepDon.updateData({
          capCuu: newCapCuu,
        });
    },
    onCheckIsKhamCapCuu: (payload, state) => {
      const { capCuu, dangKyKhamCc } = state.tiepDon;
      const { quayTiepDonId } = state.goiSo;
      const { dataMA_KHOA_CAP_CUU } = state.thietLap;
      const { listAllQuayTiepDonTaiKhoan = [] } = state.quayTiepDon;
      if (!capCuu || !dataMA_KHOA_CAP_CUU) {
        dispatch.tiepDon.updateData({
          dangKyKhamCc: false,
        });
        return;
      }
      let khoa = listAllQuayTiepDonTaiKhoan?.find(
        (x) => x.id == quayTiepDonId
      )?.khoa;
      if (dangKyKhamCc != !(!khoa?.ma || khoa?.ma != dataMA_KHOA_CAP_CUU)) {
        dispatch.tiepDon.updateData({
          dangKyKhamCc: !(!khoa?.ma || khoa?.ma != dataMA_KHOA_CAP_CUU),
        });
      }
    },

    onGetSaveData: ({ data = {}, boQuaChuaThanhToan, ...payload }, state) => {
      const {
        maHoSo,
        loaiDoiTuongId,
        loaiKcb,
        uuTien,
        khamSucKhoe,
        danTocId,
        nbMienGiam,
        maNb,
        nbLaySo = {},
        anhDaiDien,
        covid,
        soBaoHiemXaHoi,
        chiNamSinh,
        ngheNghiepId,
        tuoi,
        thangTuoi,
        capCuu,
        dangKyKham,
        dsPhanLoaiNbId,
        bangLaiXeId,
        maBenhAn,
        nhanVienKinhDoanhId,
        congTyBaoHiemId,
        phanLoaiDoiTuong,
        khoaHenKhamId,
      } = state.tiepDon;

      return {
        data: {
          ...data,
          maHoSo,
          maNb,
          loaiDoiTuongId,
          loaiKcb,
          uuTien,
          khamSucKhoe,
          danTocId,
          nbMienGiam,
          nbLaySoId: nbLaySo?.id,
          anhDaiDien,
          covid,
          soBaoHiemXaHoi,
          chiNamSinh,
          ngheNghiepId,
          tuoi,
          thangTuoi,
          capCuu,
          dangKyKham,
          dsPhanLoaiNbId,
          nhanVienKinhDoanhId: nhanVienKinhDoanhId || null,
          congTyBaoHiemId: congTyBaoHiemId || null,
          bangLaiXeId: bangLaiXeId || null,
          maBenhAn,
          doiTuongKcb: data.doiTuongKcb,
          phanLoaiDoiTuong: phanLoaiDoiTuong || null,
          boQuaChuaThanhToan,
          ...(payload.id
            ? {}
            : {
              khoaId: khoaHenKhamId ?? quayTiepDon?.khoaId,
              quayTiepDonId,
            }), //không update khoa và quầy khi chỉnh sửa thông tin
        },
        ...payload,
      };
    },
    onSaveData: ({ data = {}, boQuaChuaThanhToan, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        const selectedAddressTamTru = state.tiepDon.selectedAddressTamTru;
        const selectedAddress = state.tiepDon.selectedAddress;
        const ngoaiVien = state.tiepDon.ngoaiVien || false;
        if (
          (selectedAddress === false ||
            (data.tiemChung && selectedAddressTamTru === false)) &&
          !ngoaiVien
        ) {
          message.error(t("tiepDon.diaChiHanhChinhKhongCoTrongHeThong"));
          reject(false);
          return;
        }
        dispatch.tiepDon.updateData({
          checkValidate: false,
        });
        const { listAllQuayTiepDon = [] } = state.quayTiepDon;
        const { quayTiepDonId } = state.goiSo;
        const quayTiepDon = listAllQuayTiepDon?.find(
          (item) => item.id == quayTiepDonId
        );

        const {
          maHoSo,
          loaiDoiTuongId,
          loaiKcb,
          uuTien,
          khamSucKhoe,
          danTocId,
          nbMienGiam,
          maNb,
          nbLaySo = {},
          anhDaiDien,
          covid,
          vneid,
          soBaoHiemXaHoi,
          chiNamSinh,
          ngheNghiepId,
          tuoi,
          thangTuoi,
          capCuu,
          dangKyKham,
          dsPhanLoaiNbId,
          bangLaiXeId,
          maBenhAn,
          nhanVienKinhDoanhId,
          congTyBaoHiemId,
          phanLoaiDoiTuong,
          khoaHenKhamId,
        } = state.tiepDon;
        nbDotDieuTriProvider
          .onSaveThongTinNguoiBenh({
            data: {
              ...data,
              maHoSo,
              maNb,
              loaiDoiTuongId,
              loaiKcb,
              uuTien,
              khamSucKhoe,
              danTocId,
              nbMienGiam,
              nbLaySoId: nbLaySo?.id,
              anhDaiDien,
              covid,
              vneid,
              soBaoHiemXaHoi,
              chiNamSinh,
              ngheNghiepId,
              tuoi,
              thangTuoi,
              capCuu,
              dangKyKham,
              dsPhanLoaiNbId: dsPhanLoaiNbId || null,
              nhanVienKinhDoanhId: nhanVienKinhDoanhId || null,
              congTyBaoHiemId: congTyBaoHiemId || null,
              bangLaiXeId: bangLaiXeId || null,
              maBenhAn,
              doiTuongKcb: data.doiTuongKcb,
              phanLoaiDoiTuong: phanLoaiDoiTuong || null,
              boQuaChuaThanhToan,
              ...(payload.id
                ? {}
                : {
                  khoaId: khoaHenKhamId ?? quayTiepDon?.khoaId,
                  quayTiepDonId,
                }), //không update khoa và quầy khi chỉnh sửa thông tin
            },
            ...payload,
          })
          .then((s) => {
            if (s?.code === 0) {
              dispatch.tiepDon.updateData({
                nbLaySo: s.data?.nbLaySo,
              });
              resolve(s);
            } else {
              resolve(s);
              if (
                s?.code !== 7950 &&
                s?.code !== 7920 &&
                s?.code !== 7921 &&
                s?.code !== 7922 &&
                s?.code !== 7923 &&
                s?.code !== 7924 &&
                s?.code !== 7940 &&
                s?.code !== 7968 &&
                s?.code !== 9405
              )
                message.error(s?.message);
            }
          })
          .catch((e) => {
            showError(e?.message);
            reject(e);
          });
      });
    },
    searchMaNbTiepDon: (payload, state) => {
      return new Promise((resolve, reject) => {
        const thongTinChung =
          state.thietLap.thietLapGiaoDien?.tiepDon?.thongTinChung;
        const khoaQuayTiepDonId = state?.goiSo?.khoaQuayTiepDonId;

        nbDotDieuTriProvider
          .kiemTraThanhToan({ ...payload, khoaId: khoaQuayTiepDonId })
          .then((s) => {
            if (s?.data?.thoiGianHen) {
              const _thoiGianHen = moment(s?.data?.thoiGianHen).startOf("day");
              const _soNgay = _thoiGianHen.diff(
                moment().startOf("day"),
                "days"
              );

              if (_soNgay > 0) {
                message.warning(
                  t("tiepDon.nbDenSomSoVoiLichHen", {
                    tenNb: s?.data?.tenNb || "",
                    thoiGianHen: s?.data?.thoiGianHen
                      ? moment(s?.data?.thoiGianHen).format(
                        "DD/MM/YYYY HH:mm:ss"
                      )
                      : "",
                    soNgay: _soNgay,
                  })
                );
              }
            }

            if (
              s.code == 0 ||
              s.code == 7925 ||
              s.code == 7924 ||
              s.code == 7922 ||
              s.code == 7923 ||
              s.code == 7935 ||
              s.code == 7951 ||
              s.code == 7936
            )
              resolve(s);
            else {
              if (s.message) {
                message.error(s.message);
              }
              const phanLoaiDoiTuong = [
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
              ].includes(s?.data?.doiTuongKcb)
                ? s?.data?.trangThaiNb >= 100
                  ? PHAN_LOAI_DOI_TUONG.TAI_KHAM
                  : PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU
                : PHAN_LOAI_DOI_TUONG.TAI_KHAM;
              const dataUpdate = {
                maNb: s?.data?.maNb,
                tenNb: s?.data?.tenNb,
                soDienThoai: s?.data?.soDienThoai,
                ngaySinh: s?.data?.ngaySinh,
                gioiTinh: s?.data?.gioiTinh,
                nbDiaChi: s?.data?.nbDiaChi,
                ngaySinh: s?.data?.ngaySinh,
                quocTichId: s?.data?.quocTichId,
                maHoSo: s?.data?.maHoSo,
                maHoSoCu: s?.data?.maHoSo,
                dsPhanLoaiNbId: s?.data?.dsPhanLoaiNbId,
                bangLaiXeId: s?.data?.bangLaiXeId,
                email: s?.data?.email,
                nbGiayToTuyThan: s?.data?.nbGiayToTuyThan,
                danTocId: s?.data?.danTocId,
                soBaoHiemXaHoi: s?.data?.soBaoHiemXaHoi,
                ngheNghiepId: s?.data?.ngheNghiepId,
                nbNguoiBaoLanh: s?.data?.nbNguoiBaoLanh,
                chiNamSinh: s?.data?.chiNamSinh,
                anhDaiDien: s?.data?.anhDaiDien,
                doiTuong: s?.data?.doiTuong,
                nbTheBaoHiem: s?.data?.nbTheBaoHiem,
                ...((thongTinChung.layout1 || []).includes("phanLoaiDoiTuong")
                  ? { phanLoaiDoiTuong: phanLoaiDoiTuong }
                  : {}), //nếu có thiết lập giao diện hiển thị Phân loại đối tượng => set giá trị phanLoaiDoiTuong
                codeKiemTraThanhToan: s?.code,
                nbTongKetRaVien: {
                  cdNoiGioiThieu: s?.data?.nbTongKetRaVien?.cdNoiGioiThieu,
                },
                maNbCu: s?.data?.maNbCu,
              };
              dispatch.tiepDon.updateDetail(dataUpdate);
            }
          })
          .catch((e) => {
            message.error(
              e.message || t("tiepDon.khongTimThayThongTinBenhNhan")
            );
          });
      });
    },
    getById: (
      id,
      state,
      { isTiepDonHenDieuTri = false, isTuDongTichCoLichHenKham = false } = {}
    ) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .getById(id)
          .then((s) => {
            //SAKURA-27085 thêm điều kiện để update loại đối tượng khi bật popup chỉnh sửa
            if (window.location.pathname === "/tiep-don") {
              dispatch.tiepDon.updateDetail({});
            } else {
              const dataDetail = parseDefaultData(
                s?.data,
                isTiepDonHenDieuTri,
                isTuDongTichCoLichHenKham
              );
              dispatch.tiepDon.updateDetail({
                ...dataDetail,
              });
              dispatch.nbDotDieuTri.updateData({
                thongTinBenhNhan: dataDetail,
              });
              dispatch.nbDotDieuTri.getByTongHopId(id);
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    giamDinhThe: async ({ isOnlyResolve, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        dispatch.tiepDon.updateData({
          verifingCongBaoHiem: true,
        });
        nbTheBaoHiemProvider
          .giamDinhThe(payload)
          .then((s) => {
            const dsMaLoi = (
              state.thietLap[
              "data" + THIET_LAP_CHUNG.MA_LOI_BH_DUOC_TIEP_DON
              ] || ""
            ).split(",");
            if (
              (s.code === 0 && s.data) ||
              dsMaLoi.includes(s.data?.maKetQua)
            ) {
              if (!payload.keepCode) {
                s.code = 0;
              }
              resolve(s);
            } else {
              resolve(s);
            }
          })
          .catch((e) => {
            if (isOnlyResolve) {
              resolve({});
            }
            else {
              reject(e);
              message.error(e?.message);
            }
          });
      })
    },
    hienThiThongTinThe: async (data = {}, state) => {
      const {
        tiepDon: {
          tenNb,
          gioiTinh,
          ngaySinh: ngaySinhTiepDon,
          email,
          nbNguoiBaoLanh,
        },
      } = state;
      const listAllTheBaoHiem = state.theBaoHiem.listAllTheBaoHiem || [];
      const nbTheBaoHiem = state.tiepDon.nbTheBaoHiem;
      const doiTuong = state.tiepDon.doiTuong;
      const auth = state.auth.auth;
      const noiTru = state?.nbDotDieuTri?.thongTinBenhNhan?.noiTru;
      const dataTemp = cloneDeep(state.tiepDon.dataTemp || {});
      const _ngaySinh = state.tiepDon.ngaySinh;
      if (data) {
        if (data?.diaChi || nbTheBaoHiem.diaChi) {
          dataTemp.diaChiBHYT = data?.diaChi || nbTheBaoHiem.diaChi;
        }
        if (!data?.boQuaTheLoi) {
          let gtTheTu =
            data.gtTheTuMoi && moment(data.gtTheTuMoi, "DD/MM/YYYY") <= moment()
              ? data.gtTheTuMoi && data.gtTheTuMoi.split("/")
              : data.gtTheTu && data.gtTheTu.split("/");
          let gtTheDen =
            data.gtTheTuMoi &&
              data.gtTheDenMoi &&
              moment(data.gtTheTuMoi, "DD/MM/YYYY") <= moment()
              ? data.gtTheDenMoi && data.gtTheDenMoi.split("/")
              : data.gtTheDen && data.gtTheDen.split("/");
          let ngaySinh = data?.ngaySinh && data?.ngaySinh.split("/");

          let dateLength2 =
            ngaySinh?.length === 2
              ? moment(`${ngaySinh[1]}/${ngaySinh[0]}/01`)
              : ""; // BE trả về tháng và năm , sẽ thêm 01
          let dateLength3 =
            ngaySinh?.length === 3
              ? moment(`${ngaySinh[2]}/${ngaySinh[1]}/${ngaySinh[0]}`)
              : ""; // BE trả về đầy đủ sẽ lấy hết
          let dateLength4 = data?.ngaySinh?.length === 4 ? _ngaySinh?.date : ""; // BE chỉ trả năm , sẽ thêm ngày tháng mặc định là 01/01/{YYYY}
          let date = dateLength2 || dateLength3 || dateLength4;
          let mucHuong, khongApTran, khongGioiHanMucThanhToan;
          const baoHiem = listAllTheBaoHiem?.find(
            (item) =>
              item.ma.toLowerCase() === data?.maThe?.substr(0, 3).toLowerCase()
          );
          if (baoHiem) {
            mucHuong = baoHiem.mucHuong;
            khongApTran = baoHiem.khongApTran;
            khongGioiHanMucThanhToan = baoHiem.khongGioiHanMucThanhToan;
          }
          const newState = {
            nbTheBaoHiem: {
              ...nbTheBaoHiem,
              tuNgay:
                gtTheTu?.length === 3 &&
                moment(`${gtTheTu[2]}/${gtTheTu[1]}/${gtTheTu[0]}`),
              denNgay:
                gtTheDen?.length === 3 &&
                moment(`${gtTheDen[2]}/${gtTheDen[1]}/${gtTheDen[0]}`),
              tuNgayApDung:
                gtTheTu?.length === 3 &&
                moment(`${gtTheTu[2]}/${gtTheTu[1]}/${gtTheTu[0]}`),
              denNgayApDung:
                gtTheDen?.length === 3 &&
                moment(`${gtTheDen[2]}/${gtTheDen[1]}/${gtTheDen[0]}`),
              noiDangKyId: data?.noiDangKy?.id || nbTheBaoHiem?.noiDangKyId,
              noiDangKy: data?.noiDangKy || nbTheBaoHiem?.noiDangKy,
              maThe:
                data.gtTheTuMoi &&
                  moment(data.gtTheTuMoi, "DD/MM/YYYY") <= moment()
                  ? data?.maTheMoi
                  : data?.maThe,
              thoiGianDu5Nam: data?.thoiGianDu5Nam
                ? moment(data?.thoiGianDu5Nam)
                : "",
              boQuaTheLoi: data?.boQuaTheLoi,
              mucHuong,
              khongApTran,
              khongGioiHanMucThanhToan,
              diaChi: data?.diaChi || nbTheBaoHiem.diaChi,
              khuVuc: data?.maKV,
              noiGioiThieuId: data?.noiGioiThieuId,
            },
            dataTemp,
          };
          if (doiTuong != DOI_TUONG.BAO_HIEM) {
            newState.doiTuong = DOI_TUONG.BAO_HIEM; //set đối tượng =2 là bảo hiểm
            // newState.loaiDoiTuongId = null; //loại đối tượng = null để người dùng chọn lại loại đối tương
            // const listLoaiDoiTuong =
            await dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
              doiTuong: DOI_TUONG.BAO_HIEM,
            });
            // newState.loaiDoiTuongId = listLoaiDoiTuong[0]?.id || null;
          } else {
            if (!newState.loaiDoiTuongId) {
              // const listLoaiDoiTuong =
              await dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
                doiTuong: DOI_TUONG.BAO_HIEM,
              });
              // newState.loaiDoiTuongId = listLoaiDoiTuong[0]?.id || null;
            }
            //ngược lại thì giữ nguyên đối tượng và loại đối tượng hiện tại
          }

          dispatch.tiepDon.updateData({
            ...newState,
            soBaoHiemXaHoi:
              nbNguoiBaoLanh?.soBaoHiemXaHoi ||
              data?.soBaoHiemXaHoi ||
              data?.maSoBHXH,
            ngaySinh: ngaySinhTiepDon || {
              date,
              str:
                data?.ngaySinh?.length === 4
                  ? _ngaySinh.str
                  : date && date?.format("DD/MM/YYYY"),
            },
            tenNb: tenNb?.toUpperCase() || data?.hoTen?.toUpperCase(),
            gioiTinh: gioiTinh || (data?.gioiTinh == "Nam" ? 1 : 2),
            tuoi: date?._d?.getAge(),
            checkNoiGioiThieu:
              noiTru || auth?.benhVien?.id === data?.noiDangKy?.id
                ? false
                : true,
            email: email || data?.email || "",
            nbTongKetRaVien: {
              ...state.tiepDon.nbTongKetRaVien,
              cdNoiGioiThieu:
                data?.nbTongKetRaVien?.cdNoiGioiThieu ||
                state.tiepDon.nbTongKetRaVien?.cdNoiGioiThieu ||
                "",
              lyDoDenKham:
                data?.nbTongKetRaVien?.lyDoDenKham ||
                state?.tiepDon?.nbTongKetRaVien?.lyDoDenKham ||
                "",
            },
          });
        } else {
          let obj = {
            ...nbTheBaoHiem,
            boQuaTheLoi: data?.boQuaTheLoi,
          };
          dispatch.tiepDon.updateData({ nbTheBaoHiem: obj, dataTemp });
        }
      }
    },
    kiemTraThanhToan: (payload, state, data) => {
      const khoaQuayTiepDonId = state?.goiSo?.khoaQuayTiepDonId;
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .kiemTraThanhToan({ ...payload, khoaId: khoaQuayTiepDonId })
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    setMacDinh: ({ id, data } = {}, state) => {
      let nbDiaChi = {};
      nbDiaChi.quocGiaId = data?.quocGia?.id;
      if (id) {
        dispatch.tiepDon.updateData({
          dataMacDinh: data || {},
        });
      } else {
        let doiTuong = data?.doiTuong;
        dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
          doiTuong: doiTuong,
          thoiGianThucHien: moment().format("YYYY-MM-DD"),
        });
        const loaiDoiTuongId =
          state?.tiepDon?.loaiDoiTuongId || data?.loaiDoiTuong?.id;
        const danTocId = state?.tiepDon?.danTocId || data?.danToc?.id;
        const quocTichId = state?.tiepDon?.quocTichId || data?.quocTich?.id;
        dispatch.tiepDon.updateData({
          doiTuong: doiTuong,
          loaiDoiTuongId,
          quocTichId,
          danTocId,
          nbDiaChi: { ...nbDiaChi },
          dataMacDinh: data || {},
          nbNguonNb: {
            nguonNbId: data?.nguonNbId,
          },
        });
      }
    },

    macDinh: async (id, state) => {
      const syncTime = localStorage.getItem("TIME_RELOAD");
      let dataCache = await cacheUtils.read(
        "",
        CACHE_KEY.DATA_TIEP_DON_MAC_DINH,
        {},
        false
      );
      let listNguonNb = await dispatch.nguonNguoiBenh.getListAllNguonNguoiBenh({
        active: true,
        page: "",
        size: "",
      });
      let DATA_NGUON_NGUOI_BENH_MAC_DINH = await thietLapProvider.getGiaTri({
        ma: THIET_LAP_CHUNG.NGUON_NGUOI_BENH_MAC_DINH,
        active: true,
      });

      const getDataMacDinh = async (setMacDinh) => {
        try {
          const s = await nbDotDieuTriProvider.macDinh();
          const data = {
            ...s.data,
            nguonNbId:
              (listNguonNb || []).find(
                (x) => x.ma == DATA_NGUON_NGUOI_BENH_MAC_DINH?.data
              )?.id || "",
          };
          if (setMacDinh) dispatch.tiepDon.setMacDinh({ id, data: data });
          cacheUtils.save(
            "",
            CACHE_KEY.DATA_TIEP_DON_MAC_DINH,
            { data: data, date: new Date().getTime() },
            false
          );
        } catch (error) {
          message.error(error?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        }
      };

      if (dataCache?.data && syncTime - dataCache.date < 0) {
        dispatch.tiepDon.setMacDinh({
          id,
          data: {
            ...dataCache.data,
            nguonNbId:
              (listNguonNb || []).find(
                (x) => x.ma == DATA_NGUON_NGUOI_BENH_MAC_DINH?.data
              )?.id || "",
          },
        });
        await getDataMacDinh(false);
      } else {
        await getDataMacDinh(true);
      }
    },
    updateDetail: async (payload, state) => {
      let { nbChiSoSong, nbChiSoSongUpdate, ...data } = payload || {};
      const listAllQuayTiepDon = state.quayTiepDon.listAllQuayTiepDon || [];
      const quayTiepDonId = state.goiSo.quayTiepDonId;
      let loaiDoiTuongId =
        data.loaiDoiTuongId || state?.tiepDon?.loaiDoiTuongId;

      let doiTuong = data.doiTuong || state?.tiepDon?.doiTuong;
      const quayTiepDon = listAllQuayTiepDon?.find(
        (x) => x.id === quayTiepDonId
      );
      if (
        quayTiepDon?.loaiDoiTuongId &&
        quayTiepDon.doiTuong === doiTuong &&
        !payload.id
      ) {
        loaiDoiTuongId = quayTiepDon?.loaiDoiTuongId;
      }
      let selectedAddress = false;
      if (data.nbDiaChi) {
        selectedAddress = !!(
          data.nbDiaChi.tinhThanhPhoId && data.nbDiaChi.diaChi
        );
      }

      const listLoaiDoiTuong =
        await dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
          doiTuong: doiTuong,
          active: true,
          page: "",
          size: "",
        });

      const newNbTiemChung = cloneDeep(data?.nbTiemChung || {});
      if (newNbTiemChung.ngayBvUvss) {
        newNbTiemChung.ngayBvUvss = {
          str: moment(newNbTiemChung.ngayBvUvss).format("DD/MM/YYYY"),
          date: newNbTiemChung.ngayBvUvss,
        };
      }
      dispatch.tiepDon.updateData({
        ...data,
        ngaySinh: data.ngaySinh && {
          str: data.chiNamSinh
            ? moment(data.ngaySinh).utcOffset("+0700").format("YYYY")
            : moment(data.ngaySinh).utcOffset("+0700").format("DD/MM/YYYY"),
          date: data.ngaySinh.replace("+07:00", ""), //bỏ timezone để ko bị thay đổi theo timezone của thiết bị
        },
        doiTuong: doiTuong,
        checkNgaySinh: false,
        selectedAddress,
        nbTiemChung: newNbTiemChung,
        theTam: data.nbTheBaoHiem?.theTam,
        maThe: data.nbTheBaoHiem?.maThe,
        ...(data.ngoaiVien ? {} : { loaiDoiTuongId: loaiDoiTuongId }),
        // loaiDoiTuongId: data.ngoaiVien
        //   ? listLoaiDoiTuong[0]?.id
        //   : loaiDoiTuongId,
        doiTuongCu: data?.doiTuongCu,
        maHoSoCu: data?.maHoSoCu,
        anhDaiDien: state.tiepDon.anhChupTiepDon || data?.anhDaiDien,
        doiTuongKcbCu: data?.doiTuongKcbCu,
        ...(nbChiSoSongUpdate && {
          nbChiSoSong: {
            ...nbChiSoSongUpdate,
          },
        }),
      });
    },
    resetData: (payload, state) => {
      const listAllQuayTiepDon = state.quayTiepDon.listAllQuayTiepDon || [];
      const quayTiepDonId = state.goiSo.quayTiepDonId;
      const quayTiepDon = listAllQuayTiepDon?.find(
        (x) => x.id === quayTiepDonId
      );
      const doiTuong = state.tiepDon?.dataMacDinh?.doiTuong;
      let loaiDoiTuongId = state.tiepDon.dataMacDinh?.loaiDoiTuong?.id;
      if (quayTiepDon?.loaiDoiTuongId && quayTiepDon.doiTuong === doiTuong) {
        loaiDoiTuongId = quayTiepDon?.loaiDoiTuongId;
      }
      dispatch.tiepDon.clearData({
        dataMacDinh: state.tiepDon?.dataMacDinh,
        quocTichId: state.tiepDon?.dataMacDinh?.quocTich?.id,
        loaiDoiTuongId: loaiDoiTuongId,
        doiTuong: doiTuong || 1,
        nbDiaChi: { quocGiaId: state.tiepDon?.dataMacDinh?.quocGia?.id },
        nbNguonNb: {},
        nbMienGiam: {},
        nbNguoiBaoLanh: {},
        thongTinThanhToan: {},
        ...payload,
      });
    },
    loadNguoiBenhLaySo: (payload, state, ignoreCheckCardInsurance) => {
      let data = payload || {};
      dispatch.goiSo.updateData({ dangTiepDonId: data.id });
      if (data.maTheBhyt && !ignoreCheckCardInsurance) {
        dispatch.tiepDon
          .giamDinhThe({
            hoTen: data.tenNb,
            ngaySinh: data.ngaySinh,
            maThe: data.maTheBhyt,
          })
          .then((res) => {
            const { data = {} } = res;
            const noiDangKy = (state.benhVien.listAllBenhVien || []).find(
              (item) => item.ma == data?.maDKBD
            );
            let day5nam = data?.ngayDu5Nam && data?.ngayDu5Nam.split("/");
            if (day5nam && day5nam.length === 3) {
              day5nam = `${day5nam[2]}/${day5nam[1]}/${day5nam[0]}`;
            }
            let mucHuong, khongApTran;
            const baoHiem = state.theBaoHiem?.listAllTheBaoHiem?.find(
              (item) =>
                item.ma.toLowerCase() ===
                data?.maThe?.substr(0, 3).toLowerCase()
            );
            if (baoHiem) {
              mucHuong = baoHiem.mucHuong;
              khongApTran = baoHiem.khongApTran;
              khongGioiHanMucThanhToan = baoHiem.khongGioiHanMucThanhToan;
            }
            dispatch.tiepDon.updateData({
              verifingCongBaoHiem: false,
              doiTuong: 2,
              ...(data.loaiDoiTuongId
                ? { loaiDoiTuongId: data.loaiDoiTuongId }
                : {}),
              theBaoHiem: data,
              nbTheBaoHiem: {
                ...data,
                tuNgay: moment(data.gtTheTu, "DD/MM/YYYY"),
                denNgay: moment(data.gtTheDen, "DD/MM/YYYY"),
                noiDangKyId: noiDangKy?.id,
                noiDangKy: noiDangKy,
                thoiGianDu5Nam: moment(day5nam),
                mucHuong,
                khongApTran,
                khongGioiHanMucThanhToan,
                soBaoHiemXaHoi: data?.maSoBHXH,
              },
            });
          })
          .catch((e) => {
            dispatch.tiepDon.updateData({
              doiTuong: 2,
              ...(data.loaiDoiTuongId
                ? { loaiDoiTuongId: data.loaiDoiTuongId }
                : {}),
              verifingCongBaoHiem: false,
            });
            message.error(t("tiepDon.layThongTinBaoHiemThatBai"));
          });
      }
      let doiTuong = data?.doiTuong || 1;
      let doiTuongMacDinh = state.tiepDon?.dataMacDinh?.doiTuong;
      if (doiTuong && doiTuong !== doiTuongMacDinh) {
        dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
          doiTuong: doiTuong,
          active: true,
          page: "",
          size: "",
        });
      }
      let tuoi = data?.ngaySinh && moment(data?.ngaySinh)?._d?.getAge();
      let thangTuoi =
        tuoi <= 3 && data?.ngaySinh
          ? moment().diff(moment(data?.ngaySinh), "months")
          : null;
      dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({ doiTuong });
      const newNbTiemChung = cloneDeep(data?.nbTiemChung || {});
      if (newNbTiemChung?.ngayBvUvss) {
        newNbTiemChung.ngayBvUvss = {
          date: newNbTiemChung?.ngayBvUvss,
          str: moment(newNbTiemChung?.ngayBvUvss).format("DD/MM/YYYY"),
        };
      }
      dispatch.tiepDon.updateData({
        verifingCongBaoHiem:
          (!!data?.maThe || !!data?.maTheBhyt) && !ignoreCheckCardInsurance,
        anhDaiDien: data?.anhDaiDien,
        anhDangKyIvirse: data?.anhDaiDien,
        nbLaySo: data,
        stt: data?.stt,
        doiTuong: doiTuong ? doiTuong : doiTuongMacDinh,
        ...(data.loaiDoiTuongId ? { loaiDoiTuongId: data.loaiDoiTuongId } : {}),
        tenNb: data?.tenNb,
        maNb: data?.maNb,
        uuTien: data?.uuTien,
        khamSucKhoe: data?.khamSucKhoe,
        covid: data?.covid,
        nbTheBaoHiem:
          data?.maThe || data?.maTheBhyt
            ? {
              ...state.tiepDon?.nbTheBaoHiem,
              maThe: data?.maThe || data?.maTheBhyt,
            }
            : {},
        soDienThoai: data?.soDienThoai,
        ngaySinh: {
          date: data?.ngaySinh,
          str: data?.ngaySinh && moment(data?.ngaySinh).format("DD/MM/YYYY"),
        },
        tuoi: tuoi,
        thangTuoi: thangTuoi,
        gioiTinh: data?.gioiTinh,
        nbDiaChi: {
          ...state.tiepDon?.nbDiaChi,
          soNha: data?.soNha,
          xaPhuongId: data?.xaPhuongId,
          xaPhuong: data?.xaPhuong,
          quanHuyenId: data?.quanHuyenId,
          quanHuyen: data?.quanHuyen,
          tinhThanhPhoId: data?.tinhThanhPhoId,
          tinhThanhPho: data?.tinhThanhPho,
        },
        tiemChung: data?.tiemChung || state.tiepDon.tiemChung,
        nbTiemChung: newNbTiemChung,
        nbGiayToTuyThan: {
          ...state.tiepDon.nbGiayToTuyThan,
          ngayCap: data?.ngayCapGiayToTuyThan,
          maSo: data?.maSoGiayToTuyThan,
        },
      });
    },
    onChangeDoiTuong: (payload = {}, state) => {
      return new Promise(async (resolve, reject) => {
        const { value } = payload;
        const newData = {};
        // const listLoaiDoiTuong =
        await dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
          doiTuong: value,
          thoiGianThucHien: moment().format("YYYY-MM-DD"),
        });
        // newData.loaiDoiTuongId = listLoaiDoiTuong[0]?.id || null;
        newData.doiTuong = value;
        if (value === 1)
          newData.nbTheBaoHiem = {
            noiGioiThieuId: state.tiepDon?.nbTheBaoHiem?.noiGioiThieuId ?? null,
          };
        dispatch.tiepDon.updateData(newData);
      });
    },
    initPageTiepDon: (payload = {}, state) => {
      return new Promise(async (resolve, reject) => {
        let isEdit = payload.isEdit;
        const isTiemChung = payload.isTiemChung;
        if (isEdit === undefined) isEdit = true;
        dispatch.theBaoHiem.getListAllTheBaoHiem({
          active: true,
          page: "",
          size: "",
        });
        dispatch.thietLap.getThietLap({
          ma: THIET_LAP_CHUNG.MA_LOI_BH_DUOC_TIEP_DON,
        });

        dispatch.tiepDon.resetData({
          initTiepDonFinish: false,
          checkValidate: false,
          checkNgaySinh: false,
          disableTiepDon: !isEdit,
          ...(isTiemChung ? { tiemChung: true, nbTiemChung: {} } : {}),
          // ...(id ? { disableTiepDon: true } : {}),
        }); //nếu đang ở chế độ xem chi tiết 1 bệnh nhân thì bật disable để readonly các trường
        await dispatch.tiepDon.macDinh(payload.nbDotDieuTriId || "");
        dispatch.tiepDon.updateData({ initTiepDonFinish: true });
      });
    },
    updateThongTinBaoHiem: (payload = {}, state) => {
      const nbTheBaoHiem = state.tiepDon.nbTheBaoHiem || {};
      dispatch.tiepDon.updateData({
        nbTheBaoHiem: { ...nbTheBaoHiem, ...payload },
      });
    },
    updateThongTinChanDoan: (payload = {}, state) => {
      const nbTongKetRaVien = state.tiepDon.nbTongKetRaVien || {};
      dispatch.tiepDon.updateData({
        nbTongKetRaVien: { ...nbTongKetRaVien, ...payload },
      });
    },
    updateThongTinTiepDon: async (payload = {}, state) => {
      const { doiTuong, loaiDoiTuongId } = state.tiepDon;
      if (
        payload.hasOwnProperty("doiTuong") &&
        (doiTuong != payload.doiTuong || !loaiDoiTuongId)
      ) {
        // const listLoaiDoiTuong =
        await dispatch.loaiDoiTuong.getListAllLoaiDoiTuong({
          doiTuong: payload.doiTuong,
        });
        // payload.loaiDoiTuongId = listLoaiDoiTuong[0]?.id || null;
      }

      dispatch.tiepDon.updateData(payload);
    },

    updateThongTinNbMienGiam: (payload = {}, state) => {
      const nbMienGiam = state.tiepDon.nbMienGiam || {};
      dispatch.tiepDon.updateData({
        nbMienGiam: { ...nbMienGiam, ...payload },
      });
    },
    updateThongTinChiSoSongKhac: (
      { isReplace = false, data, ...payload } = {},
      state
    ) => {
      let _nbChiSoSong = state.tiepDon.nbChiSoSong || {};
      const nbChiSoSong = cloneDeep(_nbChiSoSong);
      if (isReplace) {
        nbChiSoSong.dsChiSoSongKhac = data;
      } else {
        if (!nbChiSoSong.dsChiSoSongKhac) {
          nbChiSoSong.dsChiSoSongKhac = [];
        }
        const dsChiSoSongKhac = nbChiSoSong.dsChiSoSongKhac || [];
        let curIndex = dsChiSoSongKhac.findIndex(
          (i) => i.chiSoSongId == payload.chiSoSongId
        );
        if (curIndex > -1) {
          nbChiSoSong.dsChiSoSongKhac[curIndex].giaTri = payload.giaTri;
        } else {
          nbChiSoSong.dsChiSoSongKhac.push(payload);
        }
      }
      dispatch.tiepDon.updateData({
        nbChiSoSong: {
          ...nbChiSoSong,
        },
      });
    },
    updateThongTinNb: (payload = {}, state, fieldName) => {
      const data = state.tiepDon[fieldName] || {};
      const thoiGianVaoVien = state.tiepDon.thoiGianVaoVien;
      const newState = { [fieldName]: { ...data, ...payload } };
      const noiTru = state?.nbDotDieuTri?.thongTinBenhNhan?.noiTru;
      if (
        fieldName == "nbTheBaoHiem" &&
        payload.hasOwnProperty("noiDangKyId") //nếu trường thay đổi là trường noiDangKyId
      ) {
        const auth = state.auth.auth;
        if (payload.ignoreCheckNoiGioiThieu) {
          newState.checkNoiGioiThieu = false;
        } else {
          if (auth?.benhVien?.id === payload.noiDangKyId || noiTru) {
            //nếu noiDangKyId trùng với thông tin bệnh viện
            newState.checkNoiGioiThieu = false; //bỏ qua check nơi giới thiệu
          } else {
            newState.checkNoiGioiThieu = true; //ngược lại thì check noi giới thiệu
          }
        }
      }
      if (fieldName == "nbTheBaoHiem" && payload.hasOwnProperty("maThe")) {
        const { maThe: _maThe } = state.tiepDon.nbTheBaoHiem || {};

        const updateMucHuong = (maThe) => {
          const { listAllTheBaoHiem } = state.theBaoHiem;
          const baoHiem = listAllTheBaoHiem?.find(
            (item) =>
              item.ma.toLowerCase() === maThe?.substr(0, 3).toLowerCase()
          );
          if (baoHiem) {
            newState.nbTheBaoHiem.mucHuong = baoHiem.mucHuong; //thì cập nhật giá trị mức hưởng
            newState.nbTheBaoHiem.khongApTran = baoHiem.khongApTran;
            newState.nbTheBaoHiem.khongGioiHanMucThanhToan =
              baoHiem.khongGioiHanMucThanhToan;
          } else {
            newState.nbTheBaoHiem.mucHuong = null;
            newState.nbTheBaoHiem.khongApTran = null;
            newState.nbTheBaoHiem.khongGioiHanMucThanhToan = null;
          }
        };
        if (payload.hasOwnProperty("maThe")) {
          updateMucHuong(payload.maThe);
        }
        const { doiTuong } = state.tiepDon;
        if (doiTuong !== 2 && payload.hasOwnProperty("maThe")) {
          //Đối tượng hiện tại đang không phải đối tượng bảo hiểm
          //thì update lại là đối tượng bảo hiểm
          newState.doiTuong = 2;
          newState.loaiDoiTuongId = null;
          newState.nbTheBaoHiem.maThe = payload.maThe.toUpperCase();
        }
        if (payload.hasOwnProperty("maThe")) {
          newState.nbTheBaoHiem.dangGiuThe = payload.maThe ? true : false;
        }
      }

      if (fieldName == "nbTheBaoHiem" && payload.hasOwnProperty("hoNgheo")) {
        if (payload.hoNgheo) {
          newState.nbTheBaoHiem.tuNgayHoNgheo = data.tuNgayHoNgheo
            ? moment(data.tuNgayHoNgheo)
            : thoiGianVaoVien
              ? moment(thoiGianVaoVien).startOf("year")
              : moment().startOf("year");
          newState.nbTheBaoHiem.denNgayHoNgheo = data.denNgayHoNgheo
            ? moment(data.denNgayHoNgheo)
            : thoiGianVaoVien
              ? moment(thoiGianVaoVien).endOf("year")
              : moment().endOf("year");
        } else {
          newState.nbTheBaoHiem.tuNgayHoNgheo = null;
          newState.nbTheBaoHiem.denNgayHoNgheo = null;
          newState.nbTheBaoHiem.soHoNgheo = null;
        }
      }

      dispatch.tiepDon.updateThongTinTiepDon(newState);
    },
    onSelectAddress: (data, state, isDiaChiTamTru) => {
      return new Promise((resolve, reject) => {
        const nbDiaChi = state.tiepDon.nbDiaChi || {};
        let address = {};
        let thongTinDiaChi = null;
        if (data?.tinhThanhPho && data?.quanHuyen) {
          if (isDiaChiTamTru) {
            thongTinDiaChi = {
              tinhThanhPhoTamTruId: data?.tinhThanhPho?.id,
              quanHuyenTamTruId: data?.quanHuyen?.id,
              xaPhuongTamTruId: data?.id,
              diaChiTamTru: data?.displayText,
            };
          } else {
            thongTinDiaChi = {
              tinhThanhPhoId: data?.tinhThanhPho?.id,
              maTinhThanhPho: data?.tinhThanhPho?.ma,
              quanHuyenId: data?.quanHuyen?.id,
              xaPhuongId: data?.id,
              diaChi: data?.displayText,
              quocGiaId: data?.tinhThanhPho?.quocGia?.id || nbDiaChi?.quocGiaId,
            };
          }
          address = {
            ...nbDiaChi,
            ...thongTinDiaChi,
          };
        } else if (data?.tinhThanhPho) {
          if (isDiaChiTamTru) {
            thongTinDiaChi = {
              tinhThanhPhoTamTruId: data?.tinhThanhPho?.id,
              quanHuyenTamTruId:
                data?.quanHuyen === null || data?.quanHuyen === undefined
                  ? null
                  : data?.id,
              xaPhuongTamTruId:
                data?.quanHuyen === null || data?.quanHuyen === undefined
                  ? data?.id
                  : null,
              diaChiTamTru: data?.displayText,
            };
          } else {
            thongTinDiaChi = {
              tinhThanhPhoId: data?.tinhThanhPho?.id,
              maTinhThanhPho: data?.tinhThanhPho?.ma,
              quanHuyenId:
                data?.quanHuyen === null || data?.quanHuyen === undefined
                  ? null
                  : data?.id,
              xaPhuongId:
                data?.quanHuyen === null || data?.quanHuyen === undefined
                  ? data?.id
                  : null,
              diaChi: data?.displayText,
              quocGiaId: data?.tinhThanhPho?.quocGia?.id || nbDiaChi?.quocGiaId,
            };
          }
          address = {
            ...nbDiaChi,
            ...thongTinDiaChi,
          };
        } else {
          if (isDiaChiTamTru) {
            thongTinDiaChi = {
              tinhThanhPhoTamTruId: data?.id,
              diaChiTamTru: data?.displayText,
              quanHuyenTamTruId: null,
              xaPhuongTamTruId: null,
            };
          } else {
            thongTinDiaChi = {
              tinhThanhPhoId: data?.id,
              maTinhThanhPho: data?.ma,
              diaChi: data?.displayText,
              quocGiaId: data?.tinhThanhPho?.quocGia?.id || nbDiaChi?.quocGiaId,
              quanHuyenId: null,
              xaPhuongId: null,
            };
          }
          address = {
            ...nbDiaChi,
            ...thongTinDiaChi,
          };
        }
        dispatch.tiepDon.updateData(
          isDiaChiTamTru
            ? { selectedAddressTamTru: true, nbDiaChi: address }
            : {
              selectedAddress: true,
              nbDiaChi: address,
            }
        );
        resolve(address);
      });
    },
    tiepDon: (
      {
        boQuaChuaThanhToan,
        boQuaTiepDonTrongNgay,
        boQuaCanhBaoCAQD,
        boQuaKiemTraTraThuocTaiKhamSom,
        ...payload
      },
      state
    ) => {
      return new Promise(async (resolve, reject) => {
        /*
        0: Chỉnh sửa thành công
        1: chưa chọn quầy
        2: địa chỉ sai
        3: chưa thanh toán
        4: thiếu thông tin quân nhân
        5: thiếu thông tin cấp cứu
        */
        const {
          tiepDon: {
            loaiDoiTuongId,
            loaiDoiTuongIdValue,
            canNang,
            tenNb,
            gioiTinh,
            ngaySinh,
            soDienThoai = "",
            quocTichId,
            email,
            doiTuong,
            nbQuanNhan,
            nbDiaChi,
            nbGiayToTuyThan,
            nbNguoiBaoLanh,
            nbTheBaoHiem,
            theBaoHiem,
            nbCapCuu,
            nbTongKetRaVien,
            checkNgaySinh,
            checkNoiGioiThieu,
            requiredNguoiGioiThieu,
            nbNguonNb,
            canNangVaoVien,
            capCuu,
            dangKyKhamCc,
            quanNhan,
            dsDichVu,
            id,
            nbNgoaiVien,
            quayTiepDonId: quayBanDau,
            khoaId,
            tiemChung,
            nbTiemChung,
            chiNamSinh,
            theTam,
            nbChiSoSong,
            thoiGianVaoVien,
            doiTuongCu,
            maHoSoCu,
            thietLap,
            maDoiTuongKcbId,
            congTyBaoHiemId,
            anhDangKyIvirse,
            maNb,
            doiTuongKcb,
            maBenhAn,
            phanLoaiDoiTuong,
            danTocId,
            ngheNghiepId,
            trangThai,
            thongTinThanhToan,
            khoaHenKhamId,
            nganHangId,
            soTaiKhoan,
            tenTaiKhoan,
            codeKiemTraThanhToan,
            dangKyKhamTrucTiep,
            dangKyKham,
            maNbCu,
            maDoiTuongKcb,
            noiSinh,
            maDvQhNs,
            doiTuongKcbCu,
            nbBoSung,
          },
          goiSo: { quayTiepDonId },
        } = state;
        const { daThanhToan, messageChuaThanhToan, boQuaCheckTaiKhamSom } =
          thongTinThanhToan || {};
        const { maNguonNb, isTiepDonHenDieuTri } = payload;

        let tuoi = ngaySinh?.date?._d
          ? ngaySinh?.date?._d.getAge()
          : moment(ngaySinh?.date)?._d?.getAge();
        let thangTuoi = ngaySinh?.date
          ? moment().diff(moment(ngaySinh?.date), "months")
          : null;
        if (!quayTiepDonId && !id && !payload?.isTiepDonNoiTru) {
          dispatch.tiepDon.updateData({ checkValidate: true });
          resolve({ code: 1, message: t("tiepDon.chuaChonQuayTiepDon") });
          return;
        }
        if (nbDiaChi?.diaChi && !nbDiaChi?.tinhThanhPhoId) {
          // nếu có trường thông tin địa chỉ mà không có thông tin tỉnh thành phố
          message.error(t("tiepDon.diaChiHanhChinhKhongHopLe"));
          resolve({
            code: 2,
            message: t("tiepDon.diaChiHanhChinhKhongHopLe"),
          });
          return;
        }

        if (
          !daThanhToan &&
          !boQuaChuaThanhToan &&
          !boQuaCanhBaoCAQD &&
          codeKiemTraThanhToan != 7921 //case tiếp đón NB cũ chưa ra viện => cần check thêm quyền 300108
        ) {
          resolve({
            code: 3,
            message: messageChuaThanhToan,
            data: {
              doiTuong,
              doiTuongCu,
              capCuu,
              maHoSoCu,
              thoiGianVaoVien,
              doiTuongKcbCu,
            },
          });
          return;
        }
        if (quanNhan) {
          if (
            !nbQuanNhan ||
            !nbQuanNhan.chucVuId ||
            !nbQuanNhan.donViId ||
            !nbQuanNhan.quanHamId
          ) {
            message.warning(t("tiepDon.nhapDayDuThongTinDoiTuongQuanNhan"));
            resolve({
              code: 4,
              message: t("tiepDon.nhapDayDuThongTinDoiTuongQuanNhan"),
            });
            return;
          }
        }
        if (capCuu) {
          if (!nbCapCuu || !nbCapCuu.loaiCapCuuId) {
            message.warning(t("tiepDon.nhapDayDuThongTinDoiTuongCapCuu"));
            resolve({
              code: 5,
              message: t("tiepDon.nhapDayDuThongTinDoiTuongCapCuu"),
            });
            return;
          }
        }

        const validXml130Fields = async () => {
          if (tiemChung) return true;

          const dataXML130_TRUONG_BAT_BUOC =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.XML130_TRUONG_BAT_BUOC,
            });
          if (!dataXML130_TRUONG_BAT_BUOC?.eval()) return true;
          const dataBO_BAT_BUOC_NHAP_LY_DO_DEN_KHAM_TIEP_DON =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BO_BAT_BUOC_NHAP_LY_DO_DEN_KHAM_TIEP_DON,
            });

          const {
            thongTinCaNhan,
            thongTinCaNhanBoSung,
            thongTinChung = {},
          } = get(state, "thietLap.thietLapGiaoDien.tiepDon", {});
          // kiểm tra trong thiết lập giao diện
          const listValidField = [
            ...(thongTinCaNhan || []),
            ...(thongTinCaNhanBoSung || []),
            ...Object.values(thongTinChung).flatMap((i) => i),
          ];

          const listKeysCheck = {
            nhapNgheNghiep: {
              value: ngheNghiepId,
              message: t("tiepDon.thieuThongTinNgheNghiep"),
            },
            chonDoiTuongKcb: {
              value: maDoiTuongKcbId,
            },
            ...((theTam && maDoiTuongKcb?.ma === "1.7") ||
              dataBO_BAT_BUOC_NHAP_LY_DO_DEN_KHAM_TIEP_DON?.eval()
              ? {}
              : {
                nhapLyDoDenKham: {
                  value: nbTongKetRaVien?.lyDoDenKham,
                },
              }),
          };

          for (let [key, field] of Object.entries(listKeysCheck)) {
            if (
              listValidField.findIndex((i) => i === key) > -1 &&
              !field.value
            ) {
              if (field.message) message.error(field.message);
              console.log("!validXml130Fields");
              return false;
            }
          }

          return true;
        };

        const validNgaySinh = async () => {
          const dataBAT_BUOC_NHAP_DU_NGAY_THANG_NAM_SINH =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NHAP_DU_NGAY_THANG_NAM_SINH,
            });
          if (!(ngaySinh?.date && (!checkNgaySinh || ngaySinh?.date)))
            return false;
          if (dataBAT_BUOC_NHAP_DU_NGAY_THANG_NAM_SINH?.eval() && chiNamSinh)
            return false;
          return true;
        };

        const validThongTinBaoHiem = async () => {
          const dataBAT_BUOC_NGAY_HIEU_LUC_GIAY_CHUYEN =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NGAY_HIEU_LUC_GIAY_CHUYEN,
            });
          const dataBAT_BUOC_NOI_GIOI_THIEU_KHI_TICH_HEN_KHAM =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NOI_GIOI_THIEU_KHI_TICH_HEN_KHAM,
            });
          const dataHIEN_THI_THONG_TIN_CHUYEN_TUYEN_SU_DUNG_MOT_NAM =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.HIEN_THI_THONG_TIN_CHUYEN_TUYEN_SU_DUNG_MOT_NAM,
            });
          const dataMA_BHYT_BENH_VIEN = await dispatch.thietLap.getThietLap({
            ma: THIET_LAP_CHUNG.MA_BHYT_BENH_VIEN,
          });
          const dataMA_BENH_VIEN = await dispatch.thietLap.getThietLap({
            ma: THIET_LAP_CHUNG.MA_BENH_VIEN,
          });

          const [batBuocNgay = "false"] =
            dataBAT_BUOC_NGAY_HIEU_LUC_GIAY_CHUYEN?.split("/") || [];

          if (
            doiTuong != DOI_TUONG.BAO_HIEM ||
            (payload?.isTiepDonNoiTru && nbTheBaoHiem?.noiDangKyId)
          )
            return true;

          if (
            !!dataHIEN_THI_THONG_TIN_CHUYEN_TUYEN_SU_DUNG_MOT_NAM?.eval() &&
            !isBoolean(nbTheBaoHiem?.giayChuyen1Nam)
          ) {
            let ma = (state.benhVien.listAllBenhVien || []).find(
              (item) => item.id == nbTheBaoHiem?.noiGioiThieuId
            )?.ma;

            let isBatBuocGiayChuyen1Nam =
              nbTheBaoHiem?.noiGioiThieuId && !dataMA_BENH_VIEN.includes(ma);

            if (isBatBuocGiayChuyen1Nam) return false;
          }

          if (
            !isValidMaTheByht(nbTheBaoHiem?.maThe) ||
            !nbTheBaoHiem?.tuNgay ||
            !nbTheBaoHiem?.denNgay ||
            !nbTheBaoHiem?.noiDangKyId
          ) {
            return false;
          }
          // bắt điều kiện với nb ngoại trú
          if (
            ![
              DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
              DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
            ]?.includes(doiTuongKcb)
          ) {
            const hasNoiGioiThieu = !!nbTheBaoHiem?.noiGioiThieuId;
            const missingTuNgay = !nbTheBaoHiem?.tuNgayGiayChuyen;
            const missingDenNgay = !nbTheBaoHiem?.denNgayGiayChuyen;

            const isBatBuoc =
              batBuocNgay?.eval() ||
              dataHIEN_THI_THONG_TIN_CHUYEN_TUYEN_SU_DUNG_MOT_NAM?.eval();

            let isBatBuocTuNgay = isBatBuoc;

            if (isBatBuoc && dataMA_BHYT_BENH_VIEN) {
              const ma = (state.benhVien.listAllBenhVien || []).find(
                (item) => item.id == nbTheBaoHiem?.noiGioiThieuId
              )?.ma;

              isBatBuocTuNgay = !dataMA_BHYT_BENH_VIEN.includes(ma);
            }

            const isBatBuocDenNgay = batBuocNgay?.eval();

            if (
              hasNoiGioiThieu &&
              ((missingTuNgay && isBatBuocTuNgay) ||
                (missingDenNgay && isBatBuocDenNgay))
            ) {
              console.log(
                "Thiếu tuNgayGiayChuyen hoặc denNgayGiayChuyen theo điều kiện bắt buộc",
                {
                  tuNgayGiayChuyen: nbTheBaoHiem?.tuNgayGiayChuyen,
                  denNgayGiayChuyen: nbTheBaoHiem?.denNgayGiayChuyen,
                }
              );
              return false;
            }
          }
          if (
            nbTheBaoHiem.henKhamLai &&
            !nbTheBaoHiem.noiGioiThieuId &&
            dataBAT_BUOC_NOI_GIOI_THIEU_KHI_TICH_HEN_KHAM?.eval()
          ) {
            console.log(
              `nbTheBaoHiem.henKhamLai &&
            !nbTheBaoHiem.noiGioiThieuId &&
            dataBAT_BUOC_NOI_GIOI_THIEU_KHI_TICH_HEN_KHAM?.eval()`,
              "Thiếu noiGioiThieuId"
            );
            return false;
          }

          if (nbTheBaoHiem.thongTuyen) {
            return true;
          }
          const noiDangKy = state.benhVien.listAllBenhVien?.find(
            (item) => item.id == nbTheBaoHiem?.noiDangKyId
          );

          if (
            checkNoiGioiThieu &&
            !nbTheBaoHiem?.henKhamLai &&
            maNguonNb !== thietLap.NGUON_NGUOI_BENH &&
            !thietLap.BHYT_TRAI_TUYEN_NGOAI_TRU &&
            !dataMA_BHYT_BENH_VIEN.includes(noiDangKy?.ma)
          ) {
            //k bắt buộc khi lưu nếu thỏa mãn điều kiện:
            //Nb nội trú: doiTuongKcb =2 3 4 6 9
            //hoặc nb ngoại trú (doiTuongKcb = 1) & trạng thái = chờ lập bệnh án (10) hoặc hủy BA (200)
            const isSuaTTBHTraiTuyen =
              [2, 3, 4, 6, 9].includes(doiTuongKcb) ||
              (doiTuongKcb == 1 && [10, 200].includes(trangThai));

            return (
              isSuaTTBHTraiTuyen ||
              nbTheBaoHiem?.noiGioiThieuId ||
              capCuu ||
              nbNgoaiVien?.maHoSo
            );
          }
          return true;
        };

        const validThongTinHoNgheo = () => {
          if (nbTheBaoHiem?.hoNgheo) {
            return (
              nbTheBaoHiem?.tuNgayHoNgheo &&
              nbTheBaoHiem?.denNgayHoNgheo &&
              nbTheBaoHiem?.soHoNgheo
            );
          }
          return true;
        };

        const validThongTinNguoiBaoLanh = async () => {
          const dataDO_TUOI_BAT_BUOC_NGUOI_BAO_LANH_TIEP_DON =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.DO_TUOI_BAT_BUOC_NGUOI_BAO_LANH_TIEP_DON,
            });
          let tuoiThietLap =
            dataDO_TUOI_BAT_BUOC_NGUOI_BAO_LANH_TIEP_DON?.eval();

          if (
            /^[1-9]\d*$/.test(tuoiThietLap) &&
            isNumber(tuoi) &&
            tuoi < tuoiThietLap
          ) {
            return (
              nbNguoiBaoLanh?.hoTen &&
              (nbNguoiBaoLanh?.soDienThoai &&
                nbNguoiBaoLanh?.soDienThoai.replaceAll(" ", "").isPhoneNumber()
                ? true
                : false) &&
              nbNguoiBaoLanh?.moiQuanHeId
            );
          } else {
            return true;
          }
        };

        const validCanNangTiepDonNoiTruHenDieuTri = async () => {
          const dataBAT_BUOC_NHAP_CAN_NANG =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NHAP_CAN_NANG,
            });
          const dataBAT_BUOC_NHAP_CAN_NANG_CHIEU_CAO_TIEP_DON =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NHAP_CAN_NANG_CHIEU_CAO_TIEP_DON,
            });

          const [tuoiBatBuoc, requireCanNang] = dataBAT_BUOC_NHAP_CAN_NANG
            .split("/")
            .map((str) => str?.eval());
          const canNangValue = canNang || canNangVaoVien;
          if (dataBAT_BUOC_NHAP_CAN_NANG_CHIEU_CAO_TIEP_DON?.eval()) {
            return (
              (tuoi || 0) < tuoiBatBuoc &&
              (requireCanNang || doiTuong === DOI_TUONG.BAO_HIEM) &&
              !nbChiSoSong?.canNang
            );
          } else {
            return (
              (tuoi || 0) < tuoiBatBuoc &&
              (requireCanNang || doiTuong === DOI_TUONG.BAO_HIEM) &&
              !canNangValue &&
              !nbChiSoSong?.canNang
            );
          }
        };
        if (
          (payload?.isTiepDonNoiTru || isTiepDonHenDieuTri) &&
          (await validCanNangTiepDonNoiTruHenDieuTri())
        ) {
          dispatch.tiepDon.updateData({ checkValidate: true });
          resolve({
            code: 6,
            message: t("lapBenhAn.vuiLongNhapCanNang"),
          });
          return;
        }

        const validChieuCaoCanNang = async () => {
          const dataBAT_BUOC_NHAP_CAN_NANG_CHIEU_CAO_TIEP_DON =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NHAP_CAN_NANG_CHIEU_CAO_TIEP_DON,
            });
          const isBatBuocNhapCanNangChieuCao =
            dataBAT_BUOC_NHAP_CAN_NANG_CHIEU_CAO_TIEP_DON?.eval();

          const { thongTinSinhHieu } = get(
            state,
            "thietLap.thietLapGiaoDien.tiepDon",
            []
          );

          let valid = false;
          let canNang = payload?.isTiepDonNoiTru
            ? canNangVaoVien
            : nbChiSoSong?.canNang;

          const listValidField = [...(thongTinSinhHieu || [])];

          const listKeysCheck = {
            nhapCanNang: {
              value: canNang,
            },
            nhapChieuCao: {
              value: nbChiSoSong?.chieuCao,
            },
          };

          if (isBatBuocNhapCanNangChieuCao && !id) {
            valid = false;
            for (let [key, field] of Object.entries(listKeysCheck)) {
              if (listValidField.findIndex((i) => i === key) > -1) {
                if (!isNumber(field.value)) {
                  valid = true;
                  break;
                } else {
                  valid = false;
                }
              } else {
                valid = false;
              }
            }
          }
          if (valid) {
            return true;
          } else {
            return false;
          }
        };

        if (await validChieuCaoCanNang()) {
          dispatch.tiepDon.updateData({ checkValidate: true });
          resolve({
            code: 6,
            message: t("tiepDon.vuiLongNhapChieuCaoCanNang"),
          });
          return;
        }

        const validChieuCaoTiepDonNoiTru = () => {
          const dsCdVaoVienId =
            state.tiepDon?.nbTongKetRaVien?.dsCdVaoVienId?.[0];

          const maBenh = state.maBenh.listAllMaBenh?.find(
            (maBenh) => maBenh.id === dsCdVaoVienId
          );

          return {
            isError: maBenh?.chieuCao && !nbChiSoSong?.chieuCao,
            message: t("tiepDon.batBuocNhapChieuCao", {
              message: `${maBenh?.ma} - ${maBenh?.ten}`,
            }),
          };
        };

        if (payload.isTiepDonNoiTru && validChieuCaoTiepDonNoiTru().isError) {
          message.error(validChieuCaoTiepDonNoiTru().message);
          return;
        }

        if (
          payload?.isTiepDonNoiTru &&
          !id &&
          (!nbTongKetRaVien?.khoaNhapVienId ||
            !nbTongKetRaVien?.dsCdVaoVienId?.length)
        ) {
          if (!nbTongKetRaVien?.khoaNhapVienId) {
            resolve({
              code: 6,
              message: t("tiepDon.tiepDonNoiTruYeuCauKhoaNhapVien"),
            });
          }
          if (!nbTongKetRaVien?.dsCdVaoVienId?.length) {
            resolve({
              code: 6,
              message: t("tiepDon.tiepDonNoiTruYeuCauChanDoanVaoVien"),
            });
          }
          dispatch.tiepDon.updateData({ checkValidate: true });

          return true;
        }

        const isGtttInvalid =
          (state.thietLap.dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN?.eval() === 1 &&
            !nbGiayToTuyThan?.maSo) ||
          (state.thietLap.dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN?.eval() === 2 &&
            (!nbGiayToTuyThan?.maSo ||
              !nbGiayToTuyThan?.noiCap ||
              !nbGiayToTuyThan?.ngayCap));

        if (
          isGtttInvalid ||
          ([VALIDATE_CASE_SDT.VALIDATE_SDT_MA_GTTT].includes(
            state.thietLap.dataBAT_BUOC_NHAP_SDT
          ) &&
            [1, 2].includes(
              state.thietLap.dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN?.eval()
            ) &&
            !nbGiayToTuyThan?.maSo) ||
          ([VALIDATE_CASE_SDT.VALIDATE_SDT_MA_GTTT].includes(
            state.thietLap.dataBAT_BUOC_NHAP_SDT
          ) &&
            state.thietLap.dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN?.eval() === 0 &&
            !soDienThoai &&
            !nbGiayToTuyThan?.maSo) ||
          (nbGiayToTuyThan?.maSo && !nbGiayToTuyThan?.loaiGiayTo) ||
          ([VALIDATE_CASE_SDT.NO_VALIDATE, VALIDATE_CASE_SDT.VALIDATE].includes(
            state.thietLap.dataBAT_BUOC_NHAP_SDT
          ) &&
            [1, 2].includes(
              state.thietLap.dataBAT_BUOC_DIEN_GIAY_TO_TUY_THAN?.eval()
            ) &&
            !nbGiayToTuyThan?.maSo) ||
          ([VALIDATE_CASE_SDT.VALIDATE].includes(
            state.thietLap.dataBAT_BUOC_NHAP_SDT
          ) &&
            !soDienThoai) ||
          (soDienThoai && !soDienThoai.replaceAll(" ", "").isPhoneNumber())
        ) {
          //Đối tượng KCB=1.7 Trường hợp quy định tại khoản 8 Điều 6 Thông tư số 30/2020/TT-BYT (maDoiTuongKcb=1.7)
          //Nb có thẻ tạm = true (theTam= true)
          if (!(theTam && maDoiTuongKcb?.ma === "1.7" && !soDienThoai)) {
            dispatch.tiepDon.updateData({ checkValidate: true });
            resolve({
              code: 7, // Mã FE tự định nghĩa, đá ngược về khi bấm hủy popup xác nhận thay đổi tên
            });
            return;
          }
        }

        if (
          state.thietLap.dataMA_LOAI_DOI_TUONG_BHBL?.includes(
            state.loaiDoiTuong.listAllLoaiDoiTuong?.find(
              (item) => item.id === loaiDoiTuongId
            )?.ma
          ) &&
          !congTyBaoHiemId
        ) {
          console.log(t("tiepDon.loiValidateDuLieu"));
          dispatch.tiepDon.updateData({ checkValidate: true });
          resolve({
            code: 10,
            message: t("tiepDon.loiValidateDuLieu"),
          });
          return;
        }
        if (nbChiSoSong?.huyetApTamTruong || nbChiSoSong?.huyetApTamThu) {
          if (!nbChiSoSong?.huyetApTamTruong || !nbChiSoSong?.huyetApTamThu) {
            console.log("tiepDon.nhapDayDuTamTrungVaTamThu");
            resolve({
              code: 11,
              message: t("tiepDon.nhapDayDuTamTrungVaTamThu"),
            });
            dispatch.tiepDon.updateData({ checkValidate: true });
            return;
          }
          if (nbChiSoSong?.huyetApTamTruong > nbChiSoSong?.huyetApTamThu) {
            console.log("common.huyetApTamThuCanLonHonHuyetApTamTruong");
            resolve({
              code: 11,
              message: t("common.huyetApTamThuCanLonHonHuyetApTamTruong"),
            });
            dispatch.tiepDon.updateData({ checkValidate: true });
            return;
          }
        }

        const validNbMedi = async () => {
          if (tiemChung) return true;
          const BAT_BUOC_NHAP_NGUON_NGUOI_BENH =
            await dispatch.thietLap.getThietLap({
              ma: THIET_LAP_CHUNG.BAT_BUOC_NHAP_NGUON_NGUOI_BENH,
            });

          const BAT_BUOC_NHAP_SDT = await dispatch.thietLap.getThietLap({
            ma: THIET_LAP_CHUNG.BAT_BUOC_NHAP_SDT,
          });

          let listNguonNb =
            await dispatch.nguonNguoiBenh.getListAllNguonNguoiBenh({
              active: true,
              page: "",
              size: "",
            });

          if (
            nbNguonNb?.nguonNbId &&
            !nbNguonNb?.nguoiGioiThieuId &&
            listNguonNb.find((x) => x.id === nbNguonNb?.nguonNbId)
              ?.nguoiGioiThieu
          ) {
            return false;
          }

          if (BAT_BUOC_NHAP_NGUON_NGUOI_BENH?.eval() && !nbNguonNb?.nguonNbId) {
            console.log("!nbNguonNb?.nguonNbId");
            return false;
          }

          if (
            (BAT_BUOC_NHAP_SDT === VALIDATE_CASE_SDT.VALIDATE &&
              !soDienThoai) ||
            (BAT_BUOC_NHAP_SDT === VALIDATE_CASE_SDT.VALIDATE_SDT_MA_GTTT &&
              !soDienThoai &&
              !nbGiayToTuyThan?.maSo)
          ) {
            if (theTam && maDoiTuongKcb?.ma === "1.7" && !soDienThoai) {
              return true;
            }
            console.log("!soDienThoai !nbGiayToTuyThan?.maSo");
            return false;
          }

          return true;
        };

        const validThongTinSinhHieu = () => {
          let canNang = payload?.isTiepDonNoiTru
            ? canNangVaoVien
            : nbChiSoSong?.canNang;

          const validNumber = (data) => {
            return isNumber(data) ? parseFloat(data) < 0 : false;
          };

          if (
            validNumber(nbChiSoSong?.chieuCao) ||
            validNumber(nbChiSoSong?.huyetApTamThu) ||
            validNumber(nbChiSoSong?.huyetApTamTruong) ||
            validNumber(nbChiSoSong?.mach) ||
            validNumber(nbChiSoSong?.nhietDo) ||
            validNumber(nbChiSoSong?.nhipTho) ||
            validNumber(nbChiSoSong?.spo2) ||
            validNumber(canNang)
          ) {
            console.log("!validThongTinSinhHieu");
            return false;
          } else {
            return true;
          }
        };

        // validate định dạng mã số giấy tờ tuỳ thân dựa trên loại giấy tờ
        const validMaSoGiayToTuyThan = async () => {
          const QUOC_TICH_VIET_NAM = await dispatch.thietLap.getThietLap({
            ma: THIET_LAP_CHUNG.QUOC_TICH_VIET_NAM,
          });

          const getQuocGiaById = select.ttHanhChinh.listAllQuocGia(state)[1];

          const isQuocTichVietNam = QUOC_TICH_VIET_NAM
            ? quocTichId &&
            getQuocGiaById(quocTichId)?.ma === QUOC_TICH_VIET_NAM
            : true;

          if (!isQuocTichVietNam) return true;

          let isValueValid = true;
          if (nbGiayToTuyThan?.maSo && nbGiayToTuyThan?.loaiGiayTo) {
            switch (nbGiayToTuyThan.loaiGiayTo) {
              case 1:
              case 8:
                isValueValid = /^\d{12}$/.test(nbGiayToTuyThan.maSo);
                break;
              case 2:
                isValueValid = /^\d{9}$|^\d{12}$/.test(nbGiayToTuyThan.maSo);
                break;
              case 3:
                isValueValid = /^.{0,9}$/.test(nbGiayToTuyThan.maSo);
                break;
              default:
                break;
            }
          }
          if (!isValueValid) console.log("!validMaSoGiayToTuyThan");
          return isValueValid;
        };

        const validLoaiDoiTuong = () => {
          const listAllLoaiDoiTuong = state.loaiDoiTuong.listAllLoaiDoiTuong;
          const listAllQuayTiepDon = state.quayTiepDon.listAllQuayTiepDon;
          const quayTiepDonId = state.goiSo.quayTiepDonId;

          const quayTiepDon = listAllQuayTiepDon.find(
            (x) => x.id === quayTiepDonId
          );

          //nếu có setting loại đối tượng giới hạn => lọc những loại đối tượng giới hạn theo đối tượng
          if (quayTiepDon?.dsLoaiDoiTuongGioiHan?.length) {
            const _dsLoaiDoiTuongGioiHanIds = (
              quayTiepDon?.dsLoaiDoiTuongGioiHan || []
            )
              .filter((item) => !doiTuong || item.doiTuong === doiTuong)
              .map((item) => item.id);

            return listAllLoaiDoiTuong
              .filter((x) => _dsLoaiDoiTuongGioiHanIds.includes(x.id))
              .findIndex((item) => item.id === loaiDoiTuongId) > -1
              ? loaiDoiTuongId
              : null;
          }

          //case ko có setting loại đối tượng giới hạn thì trả về all loại đối tượng
          const loaiDoiTuong =
            listAllLoaiDoiTuong.findIndex(
              (item) => item.id === loaiDoiTuongId
            ) > -1
              ? loaiDoiTuongId
              : null;
          if (!loaiDoiTuong) {
            console.log("!validLoaiDoiTuong");
          }
          return loaiDoiTuong;
        };

        //validate mã số DVQHNS
        const validMaSoDVQHNS = () => {
          let _isValid = !maDvQhNs || /^\d{7}$/.test(maDvQhNs);

          if (!_isValid) {
            console.error("!validMaSoDVQHNS");
            message.error(t("tiepDon.validateMaSoDVQHNS"));
          }
          return _isValid;
        };

        const dataMA_THE_BH_BO_QUA_CHECK_CONG =
          await dispatch.thietLap.getThietLap({
            ma: THIET_LAP_CHUNG.MA_THE_BH_BO_QUA_CHECK_CONG,
          });

        const validThoiGianGiayChuyen = () => {
          if (doiTuong != DOI_TUONG.BAO_HIEM || !nbTheBaoHiem?.noiGioiThieuId)
            return false;
          if (
            nbTheBaoHiem?.denNgayGiayChuyen &&
            new Date().setHours(0, 0, 0, 0) >
            new Date(nbTheBaoHiem?.denNgayGiayChuyen).setHours(0, 0, 0, 0)
          ) {
            return true;
          }
        };
        if (validThoiGianGiayChuyen()) {
          dispatch.tiepDon.updateData({ checkValidate: true });
          resolve({
            code: 6,
            message: t("tiepDon.hieuLucGiayChuyenDaHetHieuLuc", {
              hieuLuc: moment(nbTheBaoHiem?.denNgayGiayChuyen).format(
                "YYYY-MM-DD"
              ),
            }),
          });
          return;
        }

        if (
          danTocId &&
          (!nbNguonNb?.ghiChu || nbNguonNb?.ghiChu?.length <= 256) &&
          doiTuong &&
          validLoaiDoiTuong() && //dùng biến này để check case loaiDoiTuongId ko nằm trong datalist
          tenNb?.length &&
          (await validXml130Fields()) &&
          (await validMaSoGiayToTuyThan()) &&
          (await validNgaySinh()) &&
          gioiTinh &&
          quocTichId &&
          !(nbChiSoSong?.huyetApTamTruong > nbChiSoSong?.huyetApTamThu) &&
          (soDienThoai
            ? soDienThoai.replaceAll(" ", "").isPhoneNumber()
            : true) &&
          (nbDiaChi?.diaChi || nbDiaChi?.tinhThanhPhoId) && //với trường hợp người bệnh bảo hiểm lấy địa chỉ từ bảo hiểm
          (await validThongTinBaoHiem()) &&
          (await validThongTinNguoiBaoLanh()) &&
          validThongTinHoNgheo() &&
          (await validNbMedi()) &&
          validThongTinSinhHieu() &&
          (requiredNguoiGioiThieu
            ? nbNguonNb?.nguoiGioiThieuId
              ? true
              : false
            : true) &&
          validMaSoDVQHNS()
        ) {
          const quayTiepDon = (
            state.quayTiepDon?.listAllQuayTiepDon || []
          ).find((x) => x.id === (quayBanDau ?? quayTiepDonId));

          const kiemTraTraThuocTaiKhamSom = async () => {
            const dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM =
              await dispatch.thietLap.getThietLap({
                ma: THIET_LAP_CHUNG.BAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM,
              });
            if (
              !id &&
              dataBAT_BUOC_TRA_THUOC_BHYT_KHI_TAI_KHAM_SOM?.eval() &&
              !capCuu &&
              !boQuaKiemTraTraThuocTaiKhamSom
            ) {
              const res = await nbDotDieuTriProvider.kiemTraThanhToan({
                maNb,
                doiTuong,
                loaiDoiTuongId,
                capCuu,
              });
              if (res?.code === 7936) {
                resolve({
                  code: 7936,
                  boQuaChuaThanhToan,
                  boQuaTiepDonTrongNgay,
                  data: res?.data,
                });
                return false;
              }
              return true;
            }
            return true;
          };

          if (!(await kiemTraTraThuocTaiKhamSom())) {
            return;
          }

          const submit = () => {
            const newNbTiemChung = cloneDeep(nbTiemChung || {});
            if (tiemChung) {
              newNbTiemChung.ngayBvUvss =
                newNbTiemChung?.ngayBvUvss?.date instanceof moment
                  ? newNbTiemChung?.ngayBvUvss?.date.format("YYYY-MM-DD")
                  : newNbTiemChung?.ngayBvUvss?.date;
            }
            const dataSubmit = {
              data: {
                id: id,
                tenNb: tenNb.trim(),
                gioiTinh: gioiTinh,
                ngaySinh:
                  ngaySinh?.date instanceof moment
                    ? ngaySinh?.date.format("DD/MM/YYYY HH:mm:ss")
                    : ngaySinh?.date instanceof Date &&
                      ngaySinh?.date.isValidDate()
                      ? ngaySinh?.date.format("dd/MM/yyyy HH:mm:ss")
                      : ngaySinh?.date,
                soDienThoai: soDienThoai?.replaceAll(" ", ""),
                quocTichId: quocTichId,
                email: email,
                doiTuong: doiTuong,
                loaiDoiTuongId: loaiDoiTuongId,
                nbQuanNhan: nbQuanNhan,
                nbDiaChi: {
                  ...nbDiaChi,
                  soNha: nbDiaChi?.soNha?.trim(),
                  soNhaTamTru: nbDiaChi?.soNhaTamTru?.trim(),
                },
                nbGiayToTuyThan: {
                  ...nbGiayToTuyThan,
                  ngayCap:
                    nbGiayToTuyThan?.ngayCap &&
                    moment(nbGiayToTuyThan?.ngayCap).format("YYYY-MM-DD"),
                },
                nbNguoiBaoLanh: {
                  ...nbNguoiBaoLanh,
                  soDienThoai: nbNguoiBaoLanh?.soDienThoai?.replaceAll(" ", ""),
                  hoTen: nbNguoiBaoLanh?.hoTen?.toUpperCase().trim(),
                  hoTen2: nbNguoiBaoLanh?.hoTen2?.toUpperCase(),
                },
                nbTheBaoHiem: {
                  ...nbTheBaoHiem,
                  theTam: theTam,
                  diaChi: theBaoHiem?.diaChi || nbTheBaoHiem?.diaChi,
                  khuVuc: nbTheBaoHiem?.khuVuc || null,
                  noiGioiThieuId: nbTheBaoHiem?.noiGioiThieuId || null,
                  tuNgay:
                    nbTheBaoHiem?.tuNgay &&
                    moment(nbTheBaoHiem?.tuNgay).format("YYYY-MM-DD"),
                  denNgay:
                    nbTheBaoHiem?.denNgay &&
                    moment(nbTheBaoHiem?.denNgay).format("YYYY-MM-DD"),
                  thoiGianDu5Nam:
                    nbTheBaoHiem?.thoiGianDu5Nam &&
                    moment(nbTheBaoHiem?.thoiGianDu5Nam).format("YYYY-MM-DD"),
                  tuNgayMienCungChiTra:
                    nbTheBaoHiem?.tuNgayMienCungChiTra &&
                    moment(nbTheBaoHiem?.tuNgayMienCungChiTra).format(
                      "YYYY-MM-DD"
                    ),
                  denNgayMienCungChiTra:
                    nbTheBaoHiem?.denNgayMienCungChiTra &&
                    moment(nbTheBaoHiem?.denNgayMienCungChiTra).format(
                      "YYYY-MM-DD"
                    ),
                  tuNgayApDung: nbTheBaoHiem?.tuNgayApDung
                    ? moment(nbTheBaoHiem?.tuNgayApDung).format("YYYY-MM-DD")
                    : nbTheBaoHiem?.tuNgay &&
                    moment(nbTheBaoHiem?.tuNgay).format("YYYY-MM-DD"),

                  denNgayApDung: nbTheBaoHiem?.denNgayApDung
                    ? moment(nbTheBaoHiem?.denNgayApDung).format("YYYY-MM-DD")
                    : nbTheBaoHiem?.denNgay &&
                    moment(nbTheBaoHiem?.denNgay).format("YYYY-MM-DD"),
                  tuNgayHoNgheo:
                    nbTheBaoHiem?.tuNgayHoNgheo &&
                    moment(nbTheBaoHiem?.tuNgayHoNgheo).format("YYYY-MM-DD"),
                  denNgayHoNgheo:
                    nbTheBaoHiem?.denNgayHoNgheo &&
                    moment(nbTheBaoHiem?.denNgayHoNgheo).format("YYYY-MM-DD"),
                  boQuaTiepDonTrongNgay,
                  denNgayGiayChuyen:
                    nbTheBaoHiem?.denNgayGiayChuyen &&
                    moment(nbTheBaoHiem?.denNgayGiayChuyen).format(
                      "YYYY-MM-DD"
                    ),
                },
                nbCapCuu: nbCapCuu,
                nbTongKetRaVien: nbTongKetRaVien,
                nbNguonNb: nbNguonNb,
                nbNgoaiVien: nbNgoaiVien,
                quayTiepDonId: quayBanDau,
                khoaId,
                doiTuongKcb: payload?.isTiepDonNoiTru
                  ? doiTuongKcb ?? 3
                  : phanLoaiDoiTuong ===
                    PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU && !maBenhAn
                    ? 5
                    : maBenhAn
                      ? doiTuongKcb
                      : null,
                tiemChung,
                nbTiemChung:
                  tiemChung || nbTiemChung?.maTiemChung ? newNbTiemChung : null,
                nbChiSoSong: {
                  ...nbChiSoSong,
                  chiDinhTuLoaiDichVu: nbChiSoSong?.id
                    ? nbChiSoSong.chiDinhTuLoaiDichVu
                    : payload?.isTiepDonNoiTru
                      ? LOAI_DICH_VU.NOI_TRU
                      : LOAI_DICH_VU.TIEP_DON,
                  chiDinhTuDichVuId: nbChiSoSong?.id
                    ? nbChiSoSong?.chiDinhTuDichVuId
                    : id || null,
                  khoaChiDinhId:
                    nbChiSoSong?.khoaChiDinhId ||
                    khoaId ||
                    khoaHenKhamId ||
                    quayTiepDon?.khoaId,
                },
                thoiGianVaoVien: id ? thoiGianVaoVien : null,
                canNang: id ? canNang : nbChiSoSong?.canNang,
                maDoiTuongKcbId,
                ...(payload.isTiepDonNoiTru && {
                  canNangVaoVien,
                }),
                nganHangId: nganHangId || null,
                soTaiKhoan: soTaiKhoan || null,
                tenTaiKhoan: tenTaiKhoan || null,
                maNbCu: maNbCu || null,
                noiSinh: noiSinh || null,
                maDvQhNs: maDvQhNs || null,
                nbBoSung: nbBoSung,
              },
              id: id,
              boQuaChuaThanhToan: boQuaChuaThanhToan || boQuaCheckTaiKhamSom,
            };
            delete dataSubmit.data.nbTheBaoHiem.nguoiThucHien?.dsTenChuyenKhoa;
            delete dataSubmit.data.nbTheBaoHiem.hoNgheo;
            if (ENV.ALLOW_GENERATE_AUDIO)
              goiSoProvider.generateAudio({
                textToAudio: tenNb,
                folder: "name",
              });

            if (isTiepDonHenDieuTri) {
              const _data = dispatch.tiepDon.onGetSaveData(dataSubmit);

              resolve({
                code: 7777,
                data: _data,
              });
              return;
            }

            const isTheCAQD = dataMA_THE_BH_BO_QUA_CHECK_CONG
              ?.split(",")
              .includes(
                dataSubmit?.data?.nbTheBaoHiem?.maThe
                  ?.toUpperCase()
                  ?.substring(0, 2)
              );
            if (isTheCAQD && !boQuaCanhBaoCAQD && !boQuaTiepDonTrongNgay) {
              resolve({
                code: 7778,
                boQuaChuaThanhToan,
                boQuaTiepDonTrongNgay,
              });
              return;
            }

            dispatch.tiepDon
              .onSaveData(dataSubmit)
              .then(async (s) => {
                switch (s?.code) {
                  case 0:
                    if (anhDangKyIvirse && !maNb) {
                      try {
                        const dataFACE_ID_IVIRSE_ON_OFF =
                          await dispatch.thietLap.getThietLap({
                            ma: THIET_LAP_CHUNG.FACE_ID_IVIRSE_ON_OFF,
                          });
                        if (dataFACE_ID_IVIRSE_ON_OFF?.eval()) {
                          const dataIVIRSEKEY =
                            await dispatch.thietLap.getThietLap({
                              ma: THIET_LAP_CHUNG.IVIRSEKEY,
                            });
                          await dispatch.ketNoiIvirse.dangKyKhuonMat({
                            id: s?.data?.maNb,
                            username: tenNb,
                            file: anhDangKyIvirse,
                            Ivirsekey: dataIVIRSEKEY,
                          });
                          message.success(t("common.dangKyKhuonMatThanhCong"));
                        }
                      } catch (e) {
                        e?.message && message.error(e.message);
                      }
                    }
                    let nbNgoaiVien = false;
                    if (dsDichVu?.length) {
                      nbNgoaiVien = true;
                      let data = dsDichVu.map((item) => {
                        return {
                          nbDotDieuTriId: s?.data?.id,
                          nbDvKyThuat: {
                            phongThucHienId: item?.phongId,
                          },
                          nbDichVu: {
                            dichVuId: item?.dichVuId,
                            soLuong: 1,
                            chiDinhTuDichVuId: s?.data?.id,
                            chiDinhTuLoaiDichVu: 200,
                            khoaChiDinhId: s?.data?.khoaId,
                            loaiDichVu: item?.loaiDichVu,
                            khongThuTien: item?.khongThuTien,
                            ngoaiVienId: {
                              id: item?.ngoaiVienId,
                            },
                          },
                        };
                      });
                      await dispatch.tiepDonDichVu
                        .keDichVuKham({ data })
                        .then((payload) => {
                          (payload || []).forEach((item) =>
                            (item?.data || []).forEach((item2) => {
                              data.forEach((element) => {
                                if (
                                  `${element.nbDichVu?.dichVuId} - ${element?.nbDvKyThuat?.phongThucHienId}` ===
                                  `${item2.nbDichVu?.dichVuId} - ${item2?.nbDvKyThuat?.phongThucHienId}`
                                ) {
                                  element.id = item2.id;
                                }
                              });
                            })
                          );
                          if (data.length > 0) {
                            dispatch.tiepDonDichVu.getPhieuKhamBenh({
                              id: s?.data?.id,
                              data: data.filter(
                                (x) =>
                                  x.nbDichVu?.loaiDichVu !== LOAI_DICH_VU.KHAM
                              ),
                            });
                          }
                        });
                    }
                    resolve({
                      code: 0,
                      message: t("common.daLuuDuLieu"),
                      id: s?.data?.id || "",
                      edit: !!id,
                      khamCapCuu: !id ? dangKyKhamCc : false,
                      khamTrucTiep:
                        !id && dangKyKham ? dangKyKhamTrucTiep : false,
                      nbNgoaiVien: nbNgoaiVien,
                      successMessage: s?.data?.message,
                    });
                    if (!id && dangKyKhamCc) {
                      dispatch.tiepDon.resetData();
                      openInNewTab(
                        `/kham-benh/${s?.data?.phongKhamCapCuuId}/${s?.data?.maHoSo}/${s?.data?.nbDichVuKhamCapCuuId}`
                      );
                    }
                    if (!id && dangKyKhamTrucTiep && dangKyKham) {
                      dispatch.tiepDon.resetData({ dangKyKhamTrucTiep: true });
                      await dispatch.tiepDon.macDinh();
                      openInNewTab(
                        `/kham-benh/${s?.data?.phongKhamId}/${s?.data?.maHoSo}/${s?.data?.nbDichVuKhamId}`
                      );
                    }
                    break;
                  case 7950:
                    resolve({
                      code: 7950,
                      message: s?.message,
                    });
                    break;
                  case 7920:
                    resolve({
                      code: 7920,
                      message: `${s?.message}`,
                    });
                    break;
                  case 7921:
                    resolve({
                      code: 7921,
                      data: s?.data,
                      message: `${s?.message}`,
                    });
                    break;
                  case 7922:
                    if (capCuu) {
                      resolve({
                        code: 7922,
                        message: `${s?.message} . ${t(
                          "tiepDon.banCoMuonTiepTucTiepDonNguoiBenhCapCuu"
                        )}`,
                        isCapCuu: true,
                      });
                    } else {
                      resolve({
                        code: 7922,
                        data: s?.data,
                        message: `${s?.message}`,
                      });
                      // message.error(s?.message);
                    }
                    break;
                  case 7923:
                    resolve({
                      code: 7923,
                      message: `${s?.message}`,
                      id: s?.data?.id || "",
                      dsDichVu: dsDichVu,
                      data: s?.data,
                    });
                    break;
                  case 7924:
                    resolve({
                      code: 7924,
                      message: `${s?.message}`,
                      id: s?.data?.id || "",
                      dsDichVu: dsDichVu,
                      data: s?.data,
                    });
                    break;
                  case 7968:
                    resolve({
                      code: 7968,
                      message: `${s?.message}`,
                      id: s?.data?.id || "",
                      dsDichVu: dsDichVu,
                      data: s?.data,
                    });
                    break;
                  case 7940:
                    resolve({
                      code: 7940,
                      message: `${s?.message}`,
                      id: s?.data?.id || "",
                      nbThongTinId: s.data?.nbThongTinId,
                    });
                    break;
                  default:
                    resolve({
                      code: s?.code,
                      message: `${s?.message}`,
                    });
                    break;
                }
              })
              .catch((e) => {
                reject(e);
              });
          };

          submit();
        } else {
          console.log(t("tiepDon.loiValidateDuLieu"));
          dispatch.tiepDon.updateData({ checkValidate: true });
          resolve({
            code: 10,
            message: t("tiepDon.loiValidateDuLieu"),
          });
          return;
        }
      });
    },
    onUpdate: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .capNhatDotDieuTri(payload)
          .then((s) => {
            if (s?.code === 0) {
              const dataDetail = parseDefaultData(s?.data);
              dispatch.tiepDon.updateData({
                thongTinBenhNhan: dataDetail,
              });
              resolve(s);
            } else {
              resolve(s);
              if (s?.code !== 7950 && s?.code !== 7920)
                message.error(s?.message);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    searchNbMaThetek: (payload, state) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .getNbMaTheTek(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            message.error(
              e.message || t("tiepDon.khongTimThayThongTinBenhNhan")
            );
          });
      });
    },
  }),
};
