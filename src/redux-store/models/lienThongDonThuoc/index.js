import { message } from "antd";
import nbDonThuocProvider from "data-access/nb-don-thuoc-provider";
import { combineSort } from "utils";
import { t } from "i18next";
import { cloneDeep } from "lodash";

const initialState = {
  listLienThongDonThuoc: [],
  page: 0,
  size: 0,
  totalElements: 0,
  dataSortColumn: {},
  listDanhSachDayDonThuoc: [],
};

export default {
  state: cloneDeep(initialState),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initialState), ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }, state) => {
      dispatch.lienThongDonThuoc.updateData({
        page: 0,
        ...rest,
      });
      dispatch.lienThongDonThuoc.onSearch({ rest, isSizeChange: true });
    },
    onSearch: ({ page = 0, isSizeChange, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.lienThongDonThuoc.updateData(newState);
      let size = payload.size || state.lienThongDonThuoc.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.lienThongDonThuoc.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.lienThongDonThuoc.dataSearch || {};
      nbDonThuocProvider
        .searchTongHop({
          size,
          sort,
          page,
          ...dataSearch,
          ...(isSizeChange && { size }),
        })
        .then((s) => {
          dispatch.lienThongDonThuoc.updateData({
            listLienThongDonThuoc: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
            // size,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.lienThongDonThuoc.updateData({
            listLienThongDonThuoc: [],
            isLoading: false,
          });
        });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.lienThongDonThuoc.dataSortColumn,
        ...payload,
      };
      dispatch.lienThongDonThuoc.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.lienThongDonThuoc.onSearch({
        page: 0,
        dataSortColumn,
      });
    },
    onChangeInputSearch: ({ page, size, ...payload }, state) => {
      const dataSearch = {
        ...(state.lienThongDonThuoc.dataSearch || {}),
        ...payload,
      };
      dispatch.lienThongDonThuoc.updateData({
        page: 0,
        size: size || state.lienThongDonThuoc.size || 10,
        dataSearch,
      });
      dispatch.lienThongDonThuoc.onSearch({
        dataSearch,
      });
    },
    dayLienThong: (id) => {
      return new Promise((resolve, reject) => {
        nbDonThuocProvider
          .dayLienThong(id)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("common.dayLienThongThanhCong"));
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getDsLienThongHangLoat: (payload, state) => {
      let size = payload.size || 10;
      let page = payload.page || 0;
      return new Promise((resolve, reject) => {
        nbDonThuocProvider
          .searchTongHop(payload)
          .then((s) => {
            resolve(s);

            dispatch.lienThongDonThuoc.updateData({
              listDanhSachDayDonThuoc: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              totalElementsHangLoat: s?.totalElements || 0,
              pageHangLoat: page,
              sizeHangLoat: size,
            });
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            dispatch.lienThongDonThuoc.updateData({
              listDanhSachDayDonThuoc: [],
            });
          });
      });
    },
    dayLienThongHangLoat: (dsId) => {
      return new Promise((resolve, reject) => {
        nbDonThuocProvider
          .dayLienThongHangLoat(dsId)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("common.dayLienThongThanhCong"));
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
  }),
};
