import auth from "./auth";
import application from "./application";
import address from "./address";
import toaNha from "./toaNha";
import vanBang from "./categories/vanBang";
import tiepDon from "./tiepDon";
import tiemChung from "./tiemChung";
import quayTiepDon from "./categories/quayTiepDon";
import utils from "./utils";
import danToc from "./categories/danToc";
import ngheNghiep from "./ngheNghiep";
import moiQuanHe from "./categories/moiQuanHe";
import lyDoDoiTra from "./categories/lyDoDoiTra";
import lyDoTamUng from "./categories/lyDoTamUng";
import benhPham from "./categories/benhPham";
import chuyenKhoa from "./categories/chuyenKhoa";
import goiSo from "./goiSo";
import tiepDonDichVu from "./tiepDonDichVu";
import danhSachNbTiepDon from "./tiepDon/danhSachNbTiepDon";
import danhSachNbKSK from "./tiepDon/danhSachNbKSK";
import danhSachNbHuyTiepDon from "./tiepDon/danhSachNbHuyTiepDon";
import ngoaiVien from "./tiepDon/ngoaiVien";
import danhSachDichVuNbTiepDon from "./tiepDon/danhSachDichVuNbTiepDon";
import dashboardCDHA from "./tiepDon/dashboardCDHA";
import nbDvThuoc from "./nbDvThuoc";
import dkHanhNghe from "./categories/dkHanhNghe";

// cetegories
import mauKetQuaXN from "./categories/mauKetQuaXN";
import mauKetQuaPTTT from "./categories/mauKetQuaPTTT";
import mauKetQuaKsk from "./categories/mauKetQuaKsk";
import phuongThucTT from "./categories/phuongThucTT";
import dichVu from "./categories/dichVu";
import loaiCapCuu from "./categories/loaiCapCuu";
import taiNanThuongTich from "./categories/taiNanThuongTich";
import thoiGianCapCuu from "./categories/thoiGianCapCuu";
import nhomChiSo from "./categories/nhomChiSo";
import viTriChanThuong from "./categories/viTriChanThuong";
import nhomVatTu from "./categories/nhomVatTu";
import nhomHoatChat from "./categories/nhomVatTu/nhomHoatChat";
import phuongPhapVoCam from "./categories/phuongPhapVoCam";
import phuongPhapNhuom from "./categories/phuongPhapNhuom";
import viTriSinhThiet from "./categories/viTriSinhThiet";
import hocHamHocVi from "./categories/hocHamHocVi";
import loaiBenhAn from "./categories/loaiBenhAn";
import theBaoHiem from "./categories/theBaoHiem";
import goiDichVu from "./categories/goiDichVu";
import doiTac from "./categories/doiTac";
import goiDichVuChiTiet from "./categories/goiDichVuChiTiet";
import noiLayBenhPham from "./categories/noiLayBenhPham";
import powerBI from "./categories/powerBI";
import viKhuan from "./categories/viKhuan";

import danhMucThuoc from "./categories/danhMucThuoc";
import lieuDungThuoc from "./categories/danhMucThuoc/lieuDungThuoc";
import danhMucVatTu from "./categories/danhMucVatTu";
import kichCo from "./categories/kichCo";
import khoaChiDinhDichVu from "./categories/goiDichVu/khoaChiDinhDichVu";
import baoCao from "./categories/baoCao";
import mayIn from "./categories/mayIn";
import dichVuKemTheo from "./categories/dichVuKemTheo";
import chiSoCon from "./categories/chiSoCon";
import mauKetQua from "./categories/goiDichVu/mauKetQua";
import mauDienBien from "./categories/mauDienBien";
import chiSoSong from "./categories/chiSoSong";
import ngayNghiLe from "./categories/ngayNghiLe";
import nhanVien from "./categories/nhanVien";
import hauQuaTuongTac from "./categories/hauQuaTuongTac";
import mauBenhAnVaoVien from "./categories/mauBenhAnVaoVien";
import dacTinhDuocLy from "./categories/dacTinhDuocLy";
import mucDoTuongTac from "./categories/mucDoTuongTac";
import mucDoBangChung from "./categories/mucDoBangChung";
import tuongTacThuoc from "./categories/tuongTacThuoc";
import tuongTacThuocXetNghiem from "./categories/tuongTacThuocXetNghiem";
import tuongTacThuocXetNghiemCsc from "./categories/tuongTacThuocXetNghiemCSC";
import mucDichSuDung from "./categories/mucDichSuDung";
import caLamViec from "./categories/caLamViec";
import tuongTacThuocTuoi from "./categories/tuongTacThuocTuoi";

import benhVien from "./categories/benhVien";
import loaGoiSo from "./categories/loaGoiSo";
import loiDan from "./categories/loiDan";
import khoa from "./categories/khoa";
import lieuDung from "./categories/lieuDung";
import nhomChiPhi from "./categories/nhomChiPhi";
import duongDung from "./categories/duongDung";
import doiTacCon from "./categories/doiTacCon";
import maMay from "./categories/maMay";
import hoatChat from "./categories/hoatChat";
import dichVuTongHop from "./categories/nhomDichVu/All";
import nhomDichVuCap1 from "./categories/nhomDichVu/Level1";
import nhomDichVuCap2 from "./categories/nhomDichVu/Level2";
import nhomDichVuCap3 from "./categories/nhomDichVu/Level3";
import nhomDichVuCap1BaoCao from "./categories/nhomDichVuBaoCao/Level1BaoCao";
import nhomDichVuCap2BaoCao from "./categories/nhomDichVuBaoCao/Level2BaoCao";
import nhomDichVuCap3BaoCao from "./categories/nhomDichVuBaoCao/Level3BaoCao";
import donVi from "./categories/donVi";
import chucVu from "./categories/chucVu";
import quanHam from "./categories/quanHam";
import nguoiDaiDien from "./categories/nguoiDaiDien";
import phong from "./categories/phong";
import donViTinh from "./categories/donViTinh";
import phanLoaiThuoc from "./categories/phanLoaiThuoc";
import phanNhomDichVuKho from "./categories/phanNhomDichVuKho";
import nhomDichVuKho from "./categories/nhomDichVuKho";
import dichVuKho from "./categories/dichVuKho";
import chuongBenh from "./categories/chuongBenh";
import nhomBenh from "./categories/nhomBenh";
import loaiBenh from "./categories/loaiBenh";
import maBenh from "./categories/maBenh";
import chuongPttt from "./categories/chuongPttt";
import nhomPttt from "./categories/nhomPttt";
import loaiPttt from "./categories/loaiPttt";
import maPttt from "./categories/maPttt";
import dmNhomPhaChe from "./phaCheThuoc/dmNhomPhaChe";
import dmChiTietPhaChe from "./phaCheThuoc/dmChiTietPhaChe";
import phongThucHien from "./categories/phongThucHien";
import tuyChonGia from "./categories/tuyChonGia";
import loaiDoiTuong from "./categories/loaiDoiTuong";
import loaiDoiTuongLoaiHinhTT from "./categories/loaiDoiTuongLoaiHinhTT";
import loaiDoiTuongPhuongThucTT from "./categories/loaiDoiTuongPhuongThucTT";
import ttHanhChinh from "./categories/ttHanhChinh";
import xaTongHop from "./categories/ttHanhChinh/xaTongHop";
import huyenTongHop from "./categories/ttHanhChinh/huyenTongHop";
import tinhTongHop from "./categories/ttHanhChinh/tinhTongHop";
import quocGiaTongHop from "./categories/ttHanhChinh/quocGiaTongHop";
import nhomTinhNang from "./categories/nhomTinhNang";
import quyen from "./categories/quyen";
import dkThanhToanBH from "./categories/dkThanhToanBH";
import maPtttDv from "./categories/maPttt/maPtttDv";

import dichVuKyThuat from "./categories/dichVuKyThuat";
import chiPhiHapSay from "./categories/chiPhiHapSay";
import chiPhiHapSayVTYT from "./categories/chiPhiHapSayVTYT";
import khangNguyen from "./categories/khangNguyen";
import phuongPhapCheBien from "./categories/phuongPhapCheBien";
import phanLoaiNB from "./categories/phanLoaiNB";
import hinhThucNhapXuat from "./categories/hinhThucNhapXuat";
import danhMucDichVuTongHop from "./categories/dichVuTongHop";

import nguoiGioiThieu from "./categories/nguoiGioiThieu";
import nguoiGioiThieuThanhToan from "./categories/nguoiGioiThieuThanhToan";
import nguonNguoiBenh from "./categories/nguonNguoiBenh";
import xuatXu from "./categories/xuatXu";
import thangSoBanLe from "./categories/thangSoBanLe";
import thietLapTichDiem from "./categories/thietLapTichDiem";
import thietLapLuuTruBenhAn from "./categories/thietLapLuuTruBenhAn";
import thietLapDongMoPhongThucHien from "./categories/thietLapDongMoPhongThucHien";
import thietLapDongMoDichVuPhong from "./categories/thietLapDongMoPhongThucHien/thietLapDongMoDichVuPhong";
import thietLapDongMoKhoaChiDinh from "./categories/thietLapDongMoPhongThucHien/thietLapDongMoKhoaChiDinh";
import template from "./categories/templateQms";

import thuocKeNgoai from "./categories/thuocKeNgoai";
import thuocKeNgoaiLieuDung from "./categories/thuocKeNgoaiLieuDung";
import danhMucHoaChat from "./categories/danhMucHoaChat";
import loaiBuaAn from "./categories/loaiBuaAn";
import dichVuAn from "./categories/dichVuAn";
import loaiHinhThanhToan from "./categories/loaiHinhThanhToan";
import phanLoaiBmi from "./categories/phanLoaiBmi";
import danhMucVacXin from "./categories/danhMucVacXin";
import manHinhPhieuIn from "./categories/manHinhPhieuIn";
import viTriPhieuIn from "./categories/viTriPhieuIn";
import dmPhieuIn from "./categories/dmPhieuIn";

import nguonKhac from "./categories/nguonKhac";
import vanDeThuoc from "./categories/vanDeThuoc";
import vanDeThuocChung from "./categories/vanDeThuoc/vanDeThuocChung";
import vanDeThuocChiTiet from "./categories/vanDeThuoc/vanDeThuocChiTiet";
import ngoiThai from "./categories/ngoiThai";
import cachThucDe from "./categories/cachThucDe";
import bienPhapCamMau from "./categories/bienPhapCamMau";
import chinhSachHoaHong from "./categories/chinhSachHoaHong";
import nhomDichVuHoaHong from "./categories/nhomDichVuHoaHong";
import doiTacHoaHong from "./categories/doiTacHoaHong";
import dichVuHoaHong from "./categories/dichVuHoaHong";
import tienVatTu from "./categories/tienVatTu";
import danhMucHoaHongChiTiet from "./categories/danhMucHoaHongChiTiet";
import coPhim from "./categories/coPhim";
import phanLoaiVatTu from "./categories/phanLoaiVatTu";
import thuocVatTuBanGiao from "./categories/thuocVatTuBanGiao";
import phanTangNguyCo from "./categories/phanTangNguyCo";

import thuNgan from "./thuNgan";
import danhSachPhieuThu from "./thuNgan/danhSachPhieuThu";
import danhSachDichVu from "./thuNgan/danhSachDichVu";
import danhSachPhieuChi from "./thuNgan/danhSachPhieuChi";
import nbDotDieuTri from "./nbDotDieuTri";
import danhSachPhieuYeuCauHoan from "./thuNgan/danhSachPhieuYeuCauHoan";
import dsHoaDonDienTu from "./thuNgan/dsHoaDonDienTu";
import quanLyTamUng from "./thuNgan/quanLyTamUng";
import thuTamUng from "./thuNgan/thuTamUng";
import deNghiTamUng from "./thuNgan/deNghiTamUng";
import hoanTamUng from "./thuNgan/hoanTamUng";
import huyTamUng from "./thuNgan/huyTamUng";
import nguonTaiTro from "./thuNgan/nguonTaiTro";
import dsHDDTPhatHanhLoi from "./thuNgan/dsHDDTPhatHanhLoi";
import nvChotSo from "./thuNgan/nvChotSo";
import danhSachNbTongKetThanhToan from "./thuNgan/danhSachNbTongKetThanhToan";

import layMauXN from "./xetNghiem/layMauXN";
import xetNghiem from "./xetNghiem";
import xetNghiemChiSoCon from "./xetNghiem/chiSoCon";
import nbXetNghiem from "./xetNghiem/nbXetNghiem";
import xnHuyetHocSinhHoa from "./xetNghiem/xnHuyetHocSinhHoa";
import xnGiaiPhauBenhViSinh from "./xetNghiem/xnGiaiPhauBenhViSinh";
import boChiDinh from "./categories/boChiDinh";
import boChiDinhChiTiet from "./categories/boChiDinhChiTiet";
import chuongTrinhGiamGia from "./categories/chuongTrinhGiamGia";
import chiDinhDvNhomDvCtgg from "./categories/chuongTrinhGiamGia/chiDinhDvNhomDvCtgg";
import maGiamGia from "./categories/maGiamGia";
import hangThe from "./categories/hangThe";
import quyenKy from "./categories/quyenKy";
import loaiPhieu from "./categories/loaiPhieu";
import thietLapHangDoi from "./categories/thietLapHangDoi";
import kiosk from "./categories/kiosk";
import thietLapPhieuIn from "./categories/thietLapPhieuIn";
import hdsd from "./categories/huongDanSuDung";
import mauKetQuaCDHA from "./categories/mauKetQuaCDHA";
import hoiDongKiemKe from "./categories/hoiDongKiemKe";
import hoiDongChiTiet from "./categories/hoiDongChiTiet";
import chiNhanh from "./categories/chiNhanh";
import donViYTe from "./categories/donViYTe";
import loaiGiuong from "./categories/loaiGiuong";
import soHieuGiuong from "./categories/soHieuGiuong";
import dichVuGiuong from "./categories/dichVuGiuong";
import thietLapChonGiuong from "./categories/dichVuGiuong/thietLapChonGiuong";
import bacSiNgoaiVien from "./categories/bacSiNgoaiVien";
import nbPhieuLinhSuatAn from "./categories/nbPhieuLinhSuatAn";
import cheDoChamSoc from "./categories/cheDoChamSoc";
import goiDV from "./categories/goiDV";
import goiDVChiTiet from "./categories/goiDVChiTiet";
import khoaChiDinh from "./categories/goiDV/khoaChiDinh";
import maPhieuLinh from "./categories/maPhieuLinh";
import cauHoiKhamSL from "./categories/cauHoiKhamSL/cauHoiKhamSL";
import phieuKhamSL from "./categories/cauHoiKhamSL/phieuKhamSL";
import mauKqXnDotBien from "./categories/mauKqXnDotBien";
import quyTrinhXetNghiem from "./categories/quyTrinhXetNghiem";
import phanLoaiPHCN from "./categories/phanLoaiPHCN";
import thiLuc from "./categories/thiLuc";
import donViSph from "./categories/donViSph";
import tatKhucXa from "./categories/tatKhucXa";
import donViAxis from "./categories/donViAxis";
import donViCyl from "./categories/donViCyl";
import nhanAp from "./categories/nhanAp";
import xangDau from "./categories/xangDau";
import phanLoaiPhuongPhapVoCam from "./categories/phanLoaiPhuongPhapVoCam";
import loaiNhiemKhuan from "./categories/loaiNhiemKhuan";
import luocDoPt from "./categories/luocDoPt";
import benhYHocCoTruyen from "./categories/benhYHocCoTruyen";
import khoaDuLieuBaoCaoKho from "./categories/khoaDuLieuBaoCaoKho";
import doiTuongKcb from "./categories/doiTuongKcb";
import hangBangLaiXe from "./categories/hangBangLaiXe";
import traCuuTt from "./categories/traCuuTt";
import laoKhangThuoc from "./categories/laoKhangThuoc";
import dichVuTrongDinhMuc from "./categories/dichVuTrongDinhMuc";
import phuongPhapChanDoan from "./categories/phuongPhapChanDoan";
import dinhMucThuocVTYT from "./categories/dinhMucThuocVTYT";
import phuongPhapCdLaoKhangThuoc from "./categories/phuongPhapCdLaoKhangThuoc";
import phanLoaiTienSu from "./categories/phanLoaiTienSu";
import xnSaoBenhAn from "./categories/xnSaoBenhAn";
import chePhamDinhDuong from "./categories/chePhamDinhDuong";
import diTatBamSinh from "./categories/diTatBamSinh";
import lienIn from "./categories/lienIn";
import thietLapGiaTriCSS from "./categories/thietLapGiaTriCSS";
import thietLapNhomDvBaoCao from "./categories/thietLapNhomDvBaoCao";
import huongDieuTri from "./categories/huongDieuTri";
import ketQuaDieuTri from "./categories/ketQuaDieuTri";
import nhomLoaiDoiTuong from "./categories/nhomLoaiDoiTuong";
import goTat from "./categories/goTat";
import thietLapPhongThucHien from "./categories/thietLapPhongThucHien";
import thietLapCauHinh from "./categories/thietLapPhongThucHien/thietLapCauHinh";
import doiTacThanhToan from "./categories/doiTacThanhToan";
import nhomDaiPhieuNhapXuat from "./categories/nhomDaiPhieuNhapXuat";
import thietLapLoaiDichVuNhomChiPhi from "./categories/thietLapLoaiDichVuNhomChiPhi";
import lyDoDenKham from "./categories/lyDoDenKham";
import lyDoChiDinhDichVu from "./categories/lyDoChiDinhDichVu";
import phuCapDvKyThuat from "./categories/phuCapDvKyThuat";
import danhMucMaTuyChinh from "./categories/danhMucMaTuyChinh";
import logNguoiDung from "./categories/logNguoiDung";
import nhomLoaiBenhAn from "./categories/nhomLoaiBenhAn";
import thangBaoCao from "./categories/thangBaoCao";
import thietLapSinhSoThuTuThuNgan from "./thuNgan/thietLapSinhSo";
import chiSoDinhDuongTreEm from "./categories/chiSoDinhDuongTreEm";
import maLuuTruBa from "./categories/maLuuTruBa";
import tacNhanDiUng from "./categories/tacNhanDiUng";
import thamGiaHoiChan from "./categories/thamGiaHoiChan";
import dieuTriKetHop from "./categories/dieuTriKetHop";
import dieuKienChiDinh from "./categories/dieuKienChiDinh";
import nhomHuongPhuCapPTTT from "./categories/nhomHuongPhuCapPTTT";
import huongPhuCapPTTT from "./categories/huongPhuCapPTTT";
import nhomPhuCapPtTt from "./categories/nhomPhuCapPtTt";
import loaiHienThiPhieuIn from "./categories/loaiHienThiPhieuIn";
import protocolTenTruong from "./categories/protocolTenTruong";
import protocol from "./categories/protocol";
import protocolChiTiet from "./categories/protocolChiTiet";
import khaiBaoHangHoaDungKemDvkt from "./categories/khaiBaoHangHoaDungKemDvkt";
import chuyenKhoaRaVien from "./categories/thietLapChuyenKhoaRaVien";
import loaiChungChiHanhNghe from "./categories/loaiChungChiHanhNghe";

// khamBenh
import khamBenh from "./khamBenh";
import nbKhamBenh from "./khamBenh/nbKhamBenh";
import chiDinhKhamBenh from "./khamBenh/chiDinhKhamBenh";
import ketQuaKham from "./khamBenh/ketQuaKham";
import nbDichVuKhamKSK from "./khamBenh/nbDichVuKhamKSK";
import nbBoChiDinh from "./khamBenh/nbBoChiDinh";

import thietLap from "./categories/thietLap";
import adminVaiTroHeThong from "./admin/vaiTroHeThong";
import adminTaiKhoanHeThong from "./admin/taiKhoanHeThong";

// thongBao
import thongBao from "./thongBao";

import tachGopPhieuXN from "./categories/tachGopPhieuXN";

import tachGopPhieuDVKT from "./categories/tachGopPhieuDVKT";

import nguonNhapKho from "./categories/nguonNhapKho";

//kho
import kho from "./kho";
import phieuNhapXuat from "./kho/phieuNhapXuat";
import thietLapChonKho from "./kho/thietLapChonKho";
import thietLapThoiGian from "./kho/thietLapThoiGian";
import quanTriKho from "./kho/quanTriKho";
import quyetDinhThau from "./kho/quyetDinhThau";
import quyetDinhThauChiTiet from "./kho/quyetDinhThauChiTiet";
import tonKho from "./kho/tonKho";
import phieuNhapDuTru from "./kho/phieuNhapDuTru";
import nhapKho from "./kho/nhapKho";
import phieuXuat from "./kho/phieuXuat";
import nhapKhoChiTiet from "./kho/nhapKhoChiTiet";
import danhSachDichVuKho from "./kho/danhSachDichVuKho";
import danhSachDichVuKhoChiTiet from "./kho/danhSachDichVuKho/danhSachDichVuKhoChiTiet";
import themMoiThuoc from "./kho/themMoiThuoc";
import thuocChiTiet from "./kho/thuocChiTiet";
import thuocKho from "./kho/thuocKho";
import nhanVienKho from "./kho/nhanVienKho";
import chanKyKho from "./kho/chanKyKho";
import phatThuocNgoaiTru from "./kho/phatThuocNgoaiTru";
import lichSuSDThuoc from "./kho/phatThuocNgoaiTru/lichSuSDThuoc";
import nbDuyetDuocLamSang from "./kho/nbCapPhatThuoc/nbDuyetDuocLamSang";
import nbDvThuocDuocLamSang from "./kho/nbCapPhatThuoc/nbDvThuocDuocLamSang";
import nbCapPhatThuoc from "./kho/nbCapPhatThuoc/";
import nbTuVanThuoc from "./kho/tuVanThuoc/nbTuVanThuoc";
import nbDvKho from "./kho/nbDvKho";
import quyetDinhThauChiTietGiamGia from "./kho/quyetDinhThauChiTietGiamGia";
import quyetDinhThauChiTietCoSo from "./kho/quyetDinhThauChiTietCoSo";

import dsBenhNhan from "./chanDoanHinhAnh/dsBenhNhan";
import choTiepDonDV from "./chanDoanHinhAnh/choTiepDonDV";
import tiepNhanCDHA from "./chanDoanHinhAnh/tiepNhanCDHA";
import chanDoanHinhAnh from "./chanDoanHinhAnh";
import information from "./information";
import chiDinhDichVuCls from "./chanDoanHinhAnh/chiDinhDichVuCls";
import chiDinhDichVuKho from "./khamBenh/chiDinhDichVuKho";

import hoSoBenhAn from "./hoSoBenhAn";
import dsDichVuKyThuat from "./hoSoBenhAn/dsDichVuKyThuat";
import dsThuoc from "./hoSoBenhAn/dsThuoc";
import dsVatTu from "./hoSoBenhAn/dsVatTu";
import dvGiuong from "./hoSoBenhAn/dvGiuong";
import dsMau from "./hoSoBenhAn/dsMau";
import dsLuuTruBa from "./hoSoBenhAn/dsLuuTruBa";
import dsHoaChat from "./hoSoBenhAn/dsHoaChat";
import dsSuatAn from "./hoSoBenhAn/dsSuatAn";
import dsVacxin from "./hoSoBenhAn/dsVacxin";
import dsChePhamDD from "./hoSoBenhAn/dsChePhamDD";
import dsPhieuThuHoaDon from "./hoSoBenhAn/dsPhieuThuHoaDon";
import dsDvNgoaiDieuTri from "./hoSoBenhAn/dsDvNgoaiDieuTri";

//kho máu
import nhapKhoMau from "./khoMau/nhapKhoMau";
import dsDVKhoMau from "./khoMau/dsDVKhoMau";
import truyenPhatMau from "./khoMau/truyenPhatMau";
import xuatKhoMau from "./khoMau/xuatKhoMau";
import danhSachTraMau from "./khoMau/danhSachTraMau";

//theo dõi người bệnh
import chiTietTheoDoiNguoiBenh from "./theoDoiNguoiBenh/DanhSachNguoiBenh/ChiTietTheoDoiNguoiBenh";
import danhSachCovid from "./theoDoiNguoiBenh/DanhSachNguoiBenh/DanhSachNguoiBenh";
import nbDocThuocCovid from "./theoDoiNguoiBenh/DanhSachNguoiBenh/NbDonThuocCovid";

//ký số
import thietLapQuyenKy from "./kySo/thietLapQuyenKy";
import lichSuKyDanhSachNguoiBenh from "./kySo/lichSuKy/lichSuKyDanhSachNguoiBenh";
import lichSuKyDanhSachPhieu from "./kySo/lichSuKy/lichSuKyDanhSachPhieu";
import danhSachPhieuChoKy from "./kySo/danhSachPhieuChoKy";
import qms from "./qms";

// báo cáo đã in

import baoCaoDaIn from "./baoCaoDaIn";
import phimTat from "./phimTat";

import chiDinhDichVuTuTruc from "./chanDoanHinhAnh/chiDinhDichVuTuTruc";
import nbDvHoan from "./chanDoanHinhAnh/nbDvHoan";
import chiDinhDichVuVatTu from "./chanDoanHinhAnh/chiDinhDichVuVatTu";

//quyết toán BHYT
import quyetToanBhyt from "./quyetToanBHYT";
import danhSachNguoiBenhChoTaoHoSoQuyetToanBHYT from "./quyetToanBHYT/danhSachNguoiBenhChoTaoHoSoQuyetToanBHYT";
import danhSachHoSoBaoHiem79A46QD4201 from "./quyetToanBHYT/danhSachHoSoBaoHiem79A46QD4201";
import danhSachHoSoBaoHiem79A46QD4201Xml2 from "./quyetToanBHYT/danhSachHoSoBaoHiem79A46QD4201/xml2";
import danhSachHoSoBaoHiem79A46QD4201Xml3 from "./quyetToanBHYT/danhSachHoSoBaoHiem79A46QD4201/xml3";
import danhSachHoSoBaoHiem79A46QD4201Xml4 from "./quyetToanBHYT/danhSachHoSoBaoHiem79A46QD4201/xml4";
import danhSachHoSoBaoHiem79A46QD4201Xml5 from "./quyetToanBHYT/danhSachHoSoBaoHiem79A46QD4201/xml5";
import danhSachHoSoBaoHiemDaXoa from "./quyetToanBHYT/danhSachHoSoBaoHiemDaXoa";

//quyetToanBHYT QD130
import danhSachHoSoBaoHiemQD130 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130";
import danhSachHoSoBaoHiemQD130xml0 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml0";
import danhSachHoSoBaoHiemQD130xml1 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml1";
import danhSachHoSoBaoHiemQD130xml2 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml2";
import danhSachHoSoBaoHiemQD130xml3 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml3";
import danhSachHoSoBaoHiemQD130xml4 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml4";
import danhSachHoSoBaoHiemQD130xml5 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml5";
import danhSachHoSoBaoHiemQD130xml6 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml6";
import danhSachHoSoBaoHiemQD130xml7 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml7";
import danhSachHoSoBaoHiemQD130xml8 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml8";
import danhSachHoSoBaoHiemQD130xml9 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml9";
import danhSachHoSoBaoHiemQD130xml10 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml10";
import danhSachHoSoBaoHiemQD130xml11 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml11";
import danhSachHoSoBaoHiemQD130xml13 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml13";
import danhSachHoSoBaoHiemQD130xml14 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml14";
import danhSachHoSoBaoHiemQD130xml15 from "./quyetToanBHYT/danhSachHoSoBaoHiemQD130/xml15";

//pacs
import pacs from "./chanDoanHinhAnh/pacs";
import phieuIn from "./phieuIn";

// quản lí nội trú
import quanLyNoiTru from "./quanLyNoiTru"; // lập bệnh án
import danhSachNguoiBenhNoiTru from "./quanLyNoiTru/danhSachNguoiBenhNoiTru";
import nbChuyenKhoa from "./quanLyNoiTru/nbChuyenKhoa";
import nbChuyenVien from "./quanLyNoiTru/nbChuyenVien";
import toDieuTri from "./quanLyNoiTru/toDieuTri";
import nbDieuTriSoKet from "./quanLyNoiTru/nbDieuTriSoKet";
import dvNgoaiTru from "./quanLyNoiTru/dvNgoaiTru";
import dvNoiTru from "./quanLyNoiTru/dvNoiTru";
import dvNgoaiDieuTri from "./quanLyNoiTru/dvNgoaiDieuTri";
import noiTruPhongGiuong from "./quanLyNoiTru/noiTruPhongGiuong";
import soDoPhongGiuong from "./quanLyNoiTru/soDoPhongGiuong";
import nbBienBanHoiChan from "./quanLyNoiTru/nbBienBanHoiChan";
import phanPhongGiuong from "./quanLyNoiTru/phanPhongGiuong";
import giaHanTheChuyenDoiTuong from "./quanLyNoiTru/giaHanTheChuyenDoiTuong";
import vitalSigns from "./quanLyNoiTru/vitalSigns";
import vitalSignsCommon from "./quanLyNoiTru/vitalSignsCommon";
import traHangHoa from "./quanLyNoiTru/traHangHoa";
import nvQuanLyGiuong from "./quanLyNoiTru/nvQuanLyGiuong";
import chiDinhThuocDaKe from "./quanLyNoiTru/chiDinhThuocDaKe";
import nbDichVuKyThuat from "./nbDichVuKyThuat";
import nbThongTinSanPhu from "./quanLyNoiTru/nbThongTinSanPhu";
import nbTheoDoiChuyenDa from "./quanLyNoiTru/nbTheoDoiChuyenDa";
import dsPhieuThuChotDotDieuTri from "./quanLyNoiTru/dsPhieuThuChotDotDieuTri";
import donThuoc30Ngay from "./quanLyNoiTru/donThuoc30Ngay";

//Dieu tri dai han
import dieuTriDaiHan from "./dieuTriDaiHan";
//ksk
import khamSucKhoe from "./khamSucKhoe";
import dichVuKSK from "./khamSucKhoe/dichVuKSK";
import hopDongKSK from "./khamSucKhoe/hopDongKSK";
import nbKSK from "./khamSucKhoe/nbKSK";
//editor
import config from "./editor/config";
import component from "./editor/component";
import documents from "./editor/documents";
import files from "./editor/files";
import signer from "./signer";
//nhaThuoc
import lienThongGpp from "./nhaThuoc/lienThongGpp";

//goiDichVu
import nbGoiDv from "./nbGoiDv";
import nbThongTin from "./nbThongTin";
import dichVuDaSuDung from "./nbGoiDv/dichVuDaSuDung";
import thanhToanGoi from "./nbGoiDv/thanhToanGoi";
import dichVuTrongGoi from "./nbGoiDv/dichVuTrongGoi";
import chiDinhDvGoi from "./nbGoiDv/chiDinhDvGoi";
// pttt
import pttt from "./pttt";
import chiDinhDichVuThuoc from "./chiDinhDichVu/dichVuThuoc";
import chiDinhVatTu from "./chiDinhDichVu/dichVuVatTu";
import chiDinhSuatAn from "./chiDinhDichVu/dichVuSuatAn";
import chiDinhNgoaiDieuTri from "./chiDinhDichVu/dichVuNgoaiDieuTri";
import chiDinhGoiPTTT from "./chiDinhDichVu/goiPTTT";
import chiDinhHoaChat from "./chiDinhDichVu/dichVuHoaChat";
import chiDinhMau from "./chiDinhDichVu/dichVuMau";
import chiDinhPHCN from "./chiDinhDichVu/dichVuPHCN";
import chiDinhChePhamDinhDuong from "./chiDinhDichVu/dichVuChePhamDinhDuong";

// giấy đẩy cổng
import giayNghiHuong from "./giayDayCong/giayNghiHuong";
import nbTuVong from "./giayDayCong/nbTuVong";
import nbGiayChungSinh from "./giayDayCong/nbGiayChungSinh";
import nbRaVien from "./giayDayCong/nbRaVien";
import phieuTomTatBa from "./giayDayCong/phieuTomTatBa";
import giayKskLaiXe from "./giayDayCong/giayKskLaiXe";
import nbGiayNghiDuongThai from "./giayDayCong/nbGiayNghiDuongThai";

//gói phẫu thuật thủ thuật
import goiPttt from "./categories/goiPttt";
import goiPtttChiTiet from "./categories/goiPtttChiTiet";
//sinh hiệu
import sinhHieu from "./sinhHieu";
import vatTuKyGui from "./kho/vatTuKyGui";

//dashboard
import soLieuBenhVien from "./dashboard/soLieuBenhVien";
import dashboard from "./dashboard";
import soLieuNgoaiTru from "./dashboard/soLieuNgoaiTru";
import soLieuDoanhThu from "./dashboard/soLieuDoanhThu";
import soLieuGiuongPhong from "./dashboard/soLieuGiuongPhong";
import thoiGianChoKham from "./dashboard/thoiGianChoKham";

import nbTheNb from "./nbTheNB/nbTheNb";
import nbTheNbHuy from "./nbTheNB/nbTheNbHuy";
import khuVuc from "./categories/khuVuc";
import nhanTheoDoi from "./categories/nhanTheoDoi";

//phục hồi chức năng
import dieuTriPHCN from "./phucHoiChucNang/dieuTriPHCN";
import nbLuongGiaPHCN from "./phucHoiChucNang/nbLuongGiaPHCN";
import nbPHCNKham from "./phucHoiChucNang/nbPHCNKham";
import troGiup from "./troGiup";
import danhSachLichHen from "./tiepDon/danhSachLichHen";

//tiêm chủng
import dsDayCongTcqg from "./tiemChung/dsDayCongTcqg";
import dsNbTiemChung from "./tiemChung/dsNbTiemChung";
import dsKhamSL from "./tiemChung/dsKhamSL";
import dsTheoDoiSauTiem from "./tiemChung/dsTheoDoiSauTiem";
import danhSachTiem from "./tiemChung/danhSachTiem";
import danhSachLichSuTiem from "./tiemChung/danhSachLichSuTiem";
import trangChu from "./trangChu";

import logBanGhi from "./logBanGhi";
import nbPhaChe from "./phaCheThuoc/nbPhaChe";
import nbPhaCheLichSuPhaChe from "./phaCheThuoc/nbPhaCheLichSuPhaChe";
import nbPhieuXuatPhaChe from "./phaCheThuoc/nbPhieuXuatPhaChe";
import nbPhaCheChiTiet from "./phaCheThuoc/nbPhaCheChiTiet";
import nbPhaCheThuoc from "./phaCheThuoc/nbPhaCheThuoc";
import nbChotPhaCheThuoc from "./phaCheThuoc/nbChotPhaCheThuoc";

//kế hoạch tổng hợp
import dsDuyetBH from "./keHoachTongHop/dsDuyetBH";
import dsHSGiamDinhBH from "./keHoachTongHop/dsHSGiamDinhBH";
import lienThongDonThuoc from "./lienThongDonThuoc";
import danhSachThuoc from "./lienThongDonThuoc/danhSachThuoc";
import dsPhieuMuonBA from "./keHoachTongHop/dsPhieuMuonBA";
import dsPhieuTraBA from "./keHoachTongHop/dsPhieuTraBA";
import dsBAMuon from "./keHoachTongHop/dsBAMuon";
import lichSuMuonTraBA from "./keHoachTongHop/lichSuMuonTraBA";
import dsBaoCaoThangTheoKhoa from "./keHoachTongHop/dsBaoCaoThangTheoKhoa";

//kiểm soát nhiễm khuẩn
import kiemSoatNhiemKhuan from "./kiemSoatNhiemKhuan";
import phacDoDieuTri from "./phacDoDieuTri";
import khoaChiDinhPhacDoDieuTri from "./phacDoDieuTri/khoaChiDinhPhacDoDieuTri";
import dmPhacDoDieuTriDichVu from "./phacDoDieuTri/dmPhacDoDieuTriDichVu";
import nbPhacDoDieuTri from "./phacDoDieuTri/nbPhacDoDieuTri";
import danhSachNbApDungPhacDoDieuTri from "./phacDoDieuTri/danhSachNbApDungPhacDoDieuTri";
import danhSachDichVuChiDinhPhacDo from "./phacDoDieuTri/danhSachDichVuChiDinhPhacDo";

//quản lý dinh dưỡng
import sangLocSuyDd from "./quanLyDinhDuong/sangLocSuyDd";
import quanLyDinhDuong from "./quanLyDinhDuong";
import danhSachSuatAnChuaLinhDuyetTra from "./quanLyDinhDuong/danhSachSuatAnChuaLinhDuyetTra";
import misa from "./miSa";
import khaiBaoPhuCapPttt from "./categories/khaiBaoPhuCapPttt";
import khaiBaoPhuCapPtttChiTiet from "./categories/khaiBaoPhuCapPttt/khaiBaoPhuCapPtttChiTiet";
import viTriChamCong from "./categories/viTriChamCong";
import danhSachDichVuChungTu from "./miSa/danhSachDichVuChungTu";
import luongGiaPhcn from "./categories/luongGiaPhcn";
import luongGiaPhcnChiTiet from "./categories/luongGiaPhcn/luongGiaPhcnChiTiet";
// đo thị lực
import doThiLuc from "./doThiLuc";

// báo cáo adr
import quanLyBaoCaoAdr from "./quanLyBaoCaoAdr";

// chỉ số kpis
import chiSoKPIs from "./kpis/chiSoKPIs";
// kết nối Ivirse
import ketNoiIvirse from "./ketNoiIvirse";

//thuốc lao
import quanLyDieuTriLao from "./quanLyDieuTriLao";
import nguoiBenhDieuTriLao from "./quanLyDieuTriLao/nguoiBenhDieuTriLao";
import dvThuocNbDieuTriLao from "./quanLyDieuTriLao/dvThuocNbDieuTriLao";

//hẹn nội soi
import thoiGianHenNoiSoi from "./henNoiSoi/thoiGianHenNoiSoi";
import xetNghiemHenNoiSoi from "./henNoiSoi/xetNghiemHenNoiSoi";
import henNoiSoi from "./henNoiSoi";

//quản lý nhân lực
import quanLyNhanLuc from "./quanLyNhanLuc";
import nvThoiGianLamViec from "./quanLyNhanLuc/nvThoiGianLamViec";

import aiAssistant from "./aiAssistant";
import dinhDuongDuyetTra from "./kho/hangHoaDuyetTra/dinhDuongDuyetTra";
import mauDuyetTra from "./kho/hangHoaDuyetTra/mauDuyetTra";
import suatAnDuyetTra from "./kho/hangHoaDuyetTra/suatAnDuyetTra";
import thuocDuyetTra from "./kho/hangHoaDuyetTra/thuocDuyetTra";
import hoaChatDuyetTra from "./kho/hangHoaDuyetTra/hoaChatDuyetTra";
import vatTuDuyetTra from "./kho/hangHoaDuyetTra/vatTuDuyetTra";
import danhSachHangHoaChoTra from "./kho/danhSachHangHoaChoTra";
import danhSachHangHoaChoLinh from "./kho/danhSachHangHoaChoLinh";
import nbMaBenhAn from "./nbMaBenhAn";

import hoaHong from "./hoaHong";

//quản lý yêu cầu
import quanLyYeuCau from "./quanLyYeuCau";

export {
  soLieuDoanhThu,
  soLieuGiuongPhong,
  thoiGianChoKham,
  soLieuNgoaiTru,
  soLieuBenhVien,
  dashboard,
  application,
  dichVuKyThuat,
  chiPhiHapSay,
  chiPhiHapSayVTYT,
  khangNguyen,
  phuongPhapCheBien,
  phanLoaiNB,
  dichVuTongHop,
  xaTongHop,
  huyenTongHop,
  tinhTongHop,
  quocGiaTongHop,
  kho,
  dichVuKemTheo,
  chiSoCon,
  dichVu,
  dichVuKho,
  phongThucHien,
  dkThanhToanBH,
  tuyChonGia,
  ttHanhChinh,
  nhomHoatChat,
  maBenh,
  chuongPttt,
  nhomPttt,
  maPttt,
  loaiPttt,
  loaiBenh,
  nhomBenh,
  chuongBenh,
  phanLoaiThuoc,
  nhomDichVuCap1,
  nhomDichVuCap2,
  nhomDichVuCap3,
  nhomDichVuCap1BaoCao,
  nhomDichVuCap2BaoCao,
  nhomDichVuCap3BaoCao,
  hoatChat,
  tacNhanDiUng,
  maMay,
  duongDung,
  lieuDung,
  nhomChiPhi,
  doiTacCon,
  loiDan,
  toaNha,
  vanBang,
  auth,
  address,
  tiepDon,
  tiemChung,
  quayTiepDon,
  utils,
  loaiDoiTuong,
  danToc,
  ngheNghiep,
  moiQuanHe,
  phong,
  goiSo,
  tiepDonDichVu,
  danhSachNbTiepDon,
  danhSachNbKSK,
  danhSachNbHuyTiepDon,
  danhSachDichVuNbTiepDon,
  dashboardCDHA,
  dmNhomPhaChe,
  dmChiTietPhaChe,
  // categries
  loaiDoiTuongLoaiHinhTT,
  loaiDoiTuongPhuongThucTT,
  phuongThucTT,
  mauKetQuaXN,
  mauKetQuaKsk,
  mauKetQuaPTTT,
  loaiCapCuu,
  thoiGianCapCuu,
  taiNanThuongTich,
  benhVien,
  viTriChanThuong,
  nhomVatTu,
  loaGoiSo,
  khoa,
  donVi,
  chucVu,
  quanHam,
  phuongPhapVoCam,
  phuongPhapNhuom,
  viTriSinhThiet,
  hocHamHocVi,
  theBaoHiem,
  nguoiDaiDien,
  information,
  loaiBenhAn,
  lyDoDoiTra,
  lyDoTamUng,
  benhPham,
  chuyenKhoa,
  donViTinh,
  phanNhomDichVuKho,
  nhomDichVuKho,
  goiDV,
  goiDVChiTiet,
  khoaChiDinh,
  goiDichVu,
  goiDichVuChiTiet,
  doiTac,
  nhomChiSo,
  noiLayBenhPham,
  boChiDinh,
  boChiDinhChiTiet,
  chuongTrinhGiamGia,
  chiDinhDvNhomDvCtgg,
  maGiamGia,
  hangThe,
  quyenKy,
  loaiPhieu,
  hdsd,
  hoiDongKiemKe,
  hoiDongChiTiet,
  nbPhieuLinhSuatAn,
  cheDoChamSoc,
  dichVuAn,
  loaiHinhThanhToan,
  cauHoiKhamSL,
  phieuKhamSL,
  mauKqXnDotBien,
  manHinhPhieuIn,
  viTriPhieuIn,
  dmPhieuIn,
  powerBI,
  nguonKhac,
  vanDeThuoc,
  vanDeThuocChung,
  vanDeThuocChiTiet,
  dichVuHoaHong,
  tienVatTu,
  danhMucHoaHongChiTiet,
  coPhim,
  phanLoaiVatTu,
  lyDoDenKham,
  lyDoChiDinhDichVu,
  thuocVatTuBanGiao,
  phanTangNguyCo,
  phuCapDvKyThuat,
  // danh-muc/thuoc
  danhMucThuoc,
  lieuDungThuoc,
  // danh-muc/vat-tu
  danhMucVatTu,
  kichCo,
  khoaChiDinhDichVu,
  baoCao,
  mayIn,
  mauKetQua,
  mauDienBien,
  chiSoSong,
  phanLoaiBmi,
  ngayNghiLe,
  nhomTinhNang,
  quyen,
  thietLapHangDoi,
  // danh-muc/nguon-gioi-thieu
  nguoiGioiThieu,
  nguoiGioiThieuThanhToan,
  nguonNguoiBenh,
  xuatXu,
  thangSoBanLe,
  thietLapTichDiem,
  thietLapLuuTruBenhAn,
  thuocKeNgoai,
  thuocKeNgoaiLieuDung,
  danhMucHoaChat,
  danhMucMaTuyChinh,
  logNguoiDung,
  // danh-muc/don-vi-chi-nhanh
  chiNhanh,
  donViYTe,
  loaiGiuong,
  soHieuGiuong,
  dichVuGiuong,
  thietLapChonGiuong,
  bacSiNgoaiVien,
  hangBangLaiXe,
  traCuuTt,
  laoKhangThuoc,
  dichVuTrongDinhMuc,
  phuongPhapChanDoan,
  dinhMucThuocVTYT,
  phuongPhapCdLaoKhangThuoc,
  phanLoaiTienSu,
  xnSaoBenhAn,
  diTatBamSinh,
  lienIn,
  chuyenKhoaRaVien,
  thietLapGiaTriCSS,
  thietLapNhomDvBaoCao,
  thietLapPhongThucHien,
  thietLapCauHinh,
  thietLapDongMoPhongThucHien,
  thietLapDongMoDichVuPhong,
  thietLapDongMoKhoaChiDinh,
  huongDieuTri,
  ketQuaDieuTri,
  nhomLoaiDoiTuong,
  nhomLoaiBenhAn,
  thangBaoCao,
  maLuuTruBa,
  thamGiaHoiChan,
  goTat,
  nhomDaiPhieuNhapXuat,
  thietLapLoaiDichVuNhomChiPhi,
  thietLapSinhSoThuTuThuNgan,
  chiSoDinhDuongTreEm,
  dieuTriKetHop,
  dieuKienChiDinh,
  nhomHuongPhuCapPTTT,
  huongPhuCapPTTT,
  nhomPhuCapPtTt,
  loaiHienThiPhieuIn,
  protocolTenTruong,
  protocol,
  protocolChiTiet,
  khaiBaoHangHoaDungKemDvkt,
  loaiChungChiHanhNghe,
  // Kios
  kiosk,
  // ThuNgan
  thuNgan,
  danhSachPhieuThu,
  danhSachDichVu,
  danhSachPhieuChi,
  nbDotDieuTri,
  danhSachPhieuYeuCauHoan,
  dsHoaDonDienTu,
  quanLyTamUng,
  thuTamUng,
  deNghiTamUng,
  hoanTamUng,
  huyTamUng,
  nguonTaiTro,
  dsHDDTPhatHanhLoi,

  // Config
  thietLap,
  adminVaiTroHeThong,
  adminTaiKhoanHeThong,
  // XN
  layMauXN,
  xetNghiemChiSoCon,
  xnHuyetHocSinhHoa,
  xnGiaiPhauBenhViSinh,
  xetNghiem,
  nbXetNghiem,
  // kham Benh
  khamBenh,
  nbKhamBenh,
  chiDinhKhamBenh,
  ketQuaKham,
  nbDichVuKhamKSK,
  nbBoChiDinh,
  //Nhan vien
  nhanVien,
  thongBao,
  hinhThucNhapXuat,
  tachGopPhieuXN,
  tachGopPhieuDVKT,
  nguonNhapKho,
  //Kho
  thietLapChonKho,
  thietLapThoiGian,
  quanTriKho,
  quyetDinhThau,
  quyetDinhThauChiTiet,
  nhapKho,
  phieuXuat,
  nhapKhoChiTiet,
  danhSachDichVuKho,
  danhSachDichVuKhoChiTiet,
  tonKho,
  phieuNhapDuTru,
  themMoiThuoc,
  thuocKho,
  phieuNhapXuat,
  nhanVienKho,
  chanKyKho,
  phatThuocNgoaiTru,
  lichSuSDThuoc,
  nbDuyetDuocLamSang,
  nbDvThuocDuocLamSang,
  nbCapPhatThuoc,
  nbDvKho,
  nbTuVanThuoc,
  chePhamDinhDuong,
  danhSachHangHoaChoTra,
  danhSachHangHoaChoLinh,
  danhSachSuatAnChuaLinhDuyetTra,
  //Kho máu
  nhapKhoMau,
  xuatKhoMau,
  dsDVKhoMau,
  truyenPhatMau,
  danhSachTraMau,
  //CDHA
  dsBenhNhan,
  choTiepDonDV,
  tiepNhanCDHA,
  chanDoanHinhAnh,
  chiDinhDichVuCls,
  chiDinhDichVuKho,
  thuocChiTiet,
  hoSoBenhAn,
  dsDichVuKyThuat,
  dsThuoc,
  dsVatTu,
  dsMau,
  dvGiuong,
  dsLuuTruBa,
  dsVacxin,
  dsChePhamDD,
  dsPhieuThuHoaDon,
  dsDvNgoaiDieuTri,
  //chi tiết theo dõi người bệnh
  chiTietTheoDoiNguoiBenh,
  danhSachCovid,
  nbDocThuocCovid,
  //Ký số
  thietLapQuyenKy,
  lichSuKyDanhSachNguoiBenh,
  danhSachPhieuChoKy,
  lichSuKyDanhSachPhieu,
  qms,
  template,
  thietLapPhieuIn,
  // báo cáo đã in
  baoCaoDaIn,
  phimTat,
  chiDinhDichVuTuTruc,
  nbDvHoan,
  chiDinhDichVuVatTu,
  mauKetQuaCDHA,
  //quyết toán BHYT
  quyetToanBhyt,
  danhSachNguoiBenhChoTaoHoSoQuyetToanBHYT,
  danhSachHoSoBaoHiem79A46QD4201,
  danhSachHoSoBaoHiem79A46QD4201Xml2,
  danhSachHoSoBaoHiem79A46QD4201Xml3,
  danhSachHoSoBaoHiem79A46QD4201Xml4,
  danhSachHoSoBaoHiem79A46QD4201Xml5,
  danhSachHoSoBaoHiemDaXoa,
  //quyet dinh 130
  danhSachHoSoBaoHiemQD130,
  danhSachHoSoBaoHiemQD130xml0,
  danhSachHoSoBaoHiemQD130xml1,
  danhSachHoSoBaoHiemQD130xml2,
  danhSachHoSoBaoHiemQD130xml3,
  danhSachHoSoBaoHiemQD130xml4,
  danhSachHoSoBaoHiemQD130xml5,
  danhSachHoSoBaoHiemQD130xml6,
  danhSachHoSoBaoHiemQD130xml7,
  danhSachHoSoBaoHiemQD130xml8,
  danhSachHoSoBaoHiemQD130xml9,
  danhSachHoSoBaoHiemQD130xml10,
  danhSachHoSoBaoHiemQD130xml11,
  danhSachHoSoBaoHiemQD130xml13,
  danhSachHoSoBaoHiemQD130xml14,
  danhSachHoSoBaoHiemQD130xml15,
  pacs,
  phieuIn,
  ngoaiVien,
  //nội trú
  quanLyNoiTru,
  danhSachNguoiBenhNoiTru,
  nbChuyenKhoa,
  nbChuyenVien,
  traHangHoa,
  toDieuTri,
  nbDieuTriSoKet,
  dvNgoaiTru,
  dvNoiTru,
  dvNgoaiDieuTri,
  noiTruPhongGiuong,
  soDoPhongGiuong,
  nbBienBanHoiChan,
  phanPhongGiuong,
  giaHanTheChuyenDoiTuong,
  vitalSigns,
  vitalSignsCommon,
  nvQuanLyGiuong,
  chiDinhThuocDaKe,
  nbDichVuKyThuat,
  nbThongTinSanPhu,
  dsPhieuThuChotDotDieuTri,
  nbTheoDoiChuyenDa,
  nbPhaCheThuoc,
  //kham suc khoe
  khamSucKhoe,
  dichVuKSK,
  hopDongKSK,
  nbKSK,
  //editor
  config,
  component,
  documents,
  files,
  signer,
  //nha thuoc
  lienThongGpp,
  //pttt
  pttt,
  //goiDv
  nbGoiDv,
  dichVuDaSuDung,
  nbThongTin,
  thanhToanGoi,
  dichVuTrongGoi,
  chiDinhDvGoi,
  //
  chiDinhDichVuThuoc,
  chiDinhVatTu,
  chiDinhSuatAn,
  chiDinhPHCN,
  chiDinhMau,
  chiDinhGoiPTTT,
  chiDinhHoaChat,
  chiDinhChePhamDinhDuong,
  chiDinhNgoaiDieuTri,
  loaiBuaAn,
  //giấy đẩy cổng
  giayNghiHuong,
  nbTuVong,
  nbGiayChungSinh,
  nbRaVien,
  phieuTomTatBa,
  giayKskLaiXe,
  nbGiayNghiDuongThai,
  //gói pttt
  goiPttt,
  goiPtttChiTiet,
  //sinh hiệu
  sinhHieu,
  hauQuaTuongTac,
  mauBenhAnVaoVien,
  dacTinhDuocLy,
  mucDoTuongTac,
  mucDoBangChung,
  tuongTacThuoc,
  tuongTacThuocXetNghiem,
  tuongTacThuocXetNghiemCsc,
  vatTuKyGui,
  mucDichSuDung,
  nbTheNb,
  nbTheNbHuy,
  khuVuc,
  nhanTheoDoi,
  doiTacThanhToan,
  //phục hồi chức năng
  dieuTriPHCN,
  nbLuongGiaPHCN,
  nbPHCNKham,
  troGiup,
  quyetDinhThauChiTietGiamGia,
  quyetDinhThauChiTietCoSo,
  maPhieuLinh,
  danhSachLichHen,
  danhMucVacXin,
  caLamViec,
  //tiêm chủng
  dsDayCongTcqg,
  dsNbTiemChung,
  dsKhamSL,
  dsTheoDoiSauTiem,
  danhSachTiem,
  danhSachLichSuTiem,
  trangChu,
  // dieu tri dai han
  dieuTriDaiHan,
  dsHoaChat,
  dsSuatAn,
  quyTrinhXetNghiem,
  phanLoaiPHCN,
  logBanGhi,
  nbPhaChe,
  nbPhaCheLichSuPhaChe,
  nbPhieuXuatPhaChe,
  nbPhaCheChiTiet,
  nbChotPhaCheThuoc,
  // kế hoạch tổng hợp
  dsDuyetBH,
  dsHSGiamDinhBH,
  dsPhieuMuonBA,
  dsPhieuTraBA,
  dsBAMuon,
  lichSuMuonTraBA,
  thiLuc,
  donViSph,
  tatKhucXa,
  donViAxis,
  donViCyl,
  nhanAp,
  xangDau,
  phanLoaiPhuongPhapVoCam,
  loaiNhiemKhuan,
  luocDoPt,
  benhYHocCoTruyen,
  lienThongDonThuoc,
  danhSachThuoc,
  //kiểm soát nhiễm khuẩn
  kiemSoatNhiemKhuan,
  maPtttDv,
  phacDoDieuTri,
  khoaChiDinhPhacDoDieuTri,
  dmPhacDoDieuTriDichVu,
  khoaDuLieuBaoCaoKho,
  nbPhacDoDieuTri,
  danhSachNbApDungPhacDoDieuTri,
  danhSachDichVuChiDinhPhacDo,
  //quản lý dinh dưỡng
  sangLocSuyDd,
  quanLyDinhDuong,
  viKhuan,
  doiTuongKcb,
  doThiLuc,
  misa,
  khaiBaoPhuCapPttt,
  khaiBaoPhuCapPtttChiTiet,
  viTriChamCong,
  danhSachDichVuChungTu,
  luongGiaPhcn,
  luongGiaPhcnChiTiet,
  //báo cáo adr
  quanLyBaoCaoAdr,
  dkHanhNghe,
  //kpis
  chiSoKPIs,
  // kết nối ivirse
  ketNoiIvirse,
  //quản lý điều trị lao
  quanLyDieuTriLao,
  nguoiBenhDieuTriLao,
  dvThuocNbDieuTriLao,

  //hẹn nội soi
  thoiGianHenNoiSoi,
  xetNghiemHenNoiSoi,
  henNoiSoi,

  //quản lý nhân lực
  quanLyNhanLuc,
  nvThoiGianLamViec,

  //other
  ngoiThai,
  cachThucDe,
  bienPhapCamMau,
  chinhSachHoaHong,
  nhomDichVuHoaHong,
  doiTacHoaHong,
  aiAssistant,
  tuongTacThuocTuoi,
  dsBaoCaoThangTheoKhoa,
  dinhDuongDuyetTra,
  thuocDuyetTra,
  mauDuyetTra,
  vatTuDuyetTra,
  hoaChatDuyetTra,
  suatAnDuyetTra,
  nbMaBenhAn,
  hoaHong,
  nvChotSo,
  quanLyYeuCau,
  danhSachNbTongKetThanhToan,
  danhMucDichVuTongHop,
  nbDvThuoc,
  donThuoc30Ngay,
};
