import { cloneDeep, uniqBy } from "lodash";
import { message } from "antd";
import { t } from "i18next";
import { combineSort, isArray } from "utils";
import nvThoiGianLamViecProvider from "data-access/nv-thoi-gian-lam-viec-provider";
import apiBase from "data-access/api-base";

const initData = {
  listData: [],
  totalElements: 0,
  page: 0,
  dataSearch: {},
  dataSortColumn: {},
  isLoading: false,
  isLoadMore: false,
  isLoadFinish: false,
  isAutoPreloading: false,
};

export default {
  state: cloneDeep(initData),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initData), ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }) => {
      dispatch.nvThoiGianLamViec.updateData({
        page: 0,
        dataSearch,
        ...rest,
      });
      dispatch.nvThoiGianLamViec.onSearch({ ...rest, isSizeChange: true });
    },

    searchByParams: ({ page = 0, size = 500, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...state.nvThoiGianLamViec.dataSearch,
          ...payload,
        },
      };

      dispatch.nvThoiGianLamViec.updateData({
        page: 0,
        size,
        ...obj,
      });
      dispatch.nvThoiGianLamViec.onSearch({ ...obj });
    },

    onLoadMoreData: ({ page = 0, size = 500, loadMore, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...state.nvThoiGianLamViec.dataSearch,
          ...payload,
        },
        loadMore,
        page,
      };

      dispatch.nvThoiGianLamViec.updateData({
        page,
        size,
        ...obj,
      });
      dispatch.nvThoiGianLamViec.onSearch({ ...obj });
    },

    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.nvThoiGianLamViec.dataSortColumn,
        ...payload,
      };
      dispatch.nvThoiGianLamViec.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.nvThoiGianLamViec.onSearch({
        page: 0,
        dataSortColumn,
      });
    },

    onSearch: (
      { page = 0, isSizeChange, loadMore, isAutoPreload, phongId, ...payload },
      state
    ) => {
      let newState = {
        isLoading: true,
        isLoadFinish: false,
        page,
        ...(loadMore && { isLoadMore: true }),
        ...(isAutoPreload && { isAutoPreloading: true }),
      };
      dispatch.nvThoiGianLamViec.updateData(newState);
      let size = payload.size || state.nvThoiGianLamViec.size || 500;
      const sort = combineSort(
        payload.dataSortColumn || state.nvThoiGianLamViec.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.nvThoiGianLamViec.dataSearch || {};

      nvThoiGianLamViecProvider
        .search({
          page,
          size,
          sort,
          ...dataSearch,
          ...(phongId && { phongId }), // Override phongId nếu có
          ...(isSizeChange && { size }),
        })
        .then((s) => {
          let listData = s?.data || [];
          if (loadMore) {
            listData = uniqBy(
              [...state.nvThoiGianLamViec.listData, ...listData],
              "id"
            );
          }
          dispatch.nvThoiGianLamViec.updateData({
            listData,
            isLoading: false,
            isLoadFinish: true,
            ...(loadMore && { isLoadMore: false }),
            isAutoPreloading: false, // Reset auto preloading state
            totalElements: s?.totalElements || 0,
            hasNext: page + 1 < (s?.totalPages || 0),
            page,
            dataSearch,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.nvThoiGianLamViec.updateData({
            listData: [],
            isLoading: false,
            isLoadFinish: true,
            ...(loadMore && { isLoadMore: false }),
            isAutoPreloading: false, // Reset auto preloading state on error
          });
        });
    },
    onImport: async (payload, state) => {
      apiBase
        .onImport(payload, nvThoiGianLamViecProvider.import)
        .then((res) => {
          dispatch.nvThoiGianLamViec.onSearch({});
        });
    },
    crudNvThoiGianLamViec: ({ isEdit, payload }) => {
      return new Promise((resolve, reject) => {
        const provider = isEdit
          ? nvThoiGianLamViecProvider.put(payload)
          : isArray(payload, true)
          ? nvThoiGianLamViecProvider.batch(payload)
          : nvThoiGianLamViecProvider.post(payload);

        provider
          .then((s) => {
            const { data } = s || {};
            let _message = "";
            if (isArray(data, true)) {
              _message = data
                .reduce((acc, cur) => {
                  if (!acc.includes(cur.message)) {
                    acc.push(cur.message);
                  }
                  return acc;
                }, [])
                .join(", ");
            }
            if (_message) {
              message.error(_message);
              reject(_message);
            } else {
              resolve(s);
              const msg = isEdit
                ? t("quanLyNoiTru.chinhSuaThongTinDieuDuongThanhCong")
                : t("quanLyNoiTru.themMoiThongTinDieuDuongThanhCong");
              message.success(msg);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    onDelete: (payload) => {
      return new Promise((resolve, reject) => {
        nvThoiGianLamViecProvider
          .delete(payload)
          .then((s) => {
            resolve(s);
            message.success(t("nhaThuoc.xoaThanhCong"));
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    // Load data cho 1 phòng cụ thể
    loadRoomData: async ({
      phongId,
      khoaId,
      tuThoiGian,
      denThoiGian,
      page = 0,
      size = 500,
    }) => {
      try {
        console.log(`🔄 Loading data for phongId: ${phongId}`);

        const sort = combineSort({});
        const result = await nvThoiGianLamViecProvider.search({
          page,
          size,
          sort,
          phongId,
          khoaId,
          tuThoiGian,
          denThoiGian,
        });

        console.log(
          `✅ Loaded ${result?.data?.length || 0} items for phongId: ${phongId}`
        );
        return result?.data || [];
      } catch (error) {
        console.error(`❌ Error loading room ${phongId}:`, error);
        throw error;
      }
    },
  }),
};
