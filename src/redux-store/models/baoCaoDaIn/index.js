import baoCaoDaInProvider from "data-access/bao-cao-da-in-provider";
import { PAGE_SIZE, PAGE_DEFAULT, LOAI_BC_THONG_TIN_NB } from "constants/index";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvCdhaProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { locPhieuLisPacs } from "utils";

export default {
  state: {
    listAllData: [],
    listData: [],
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataEditDefault: {},
    dataSearch: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    getBaoCao: ({ payload, callApi }, state) => {
      return new Promise((resolve, reject) => {
        callApi(payload)
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              resolve(s?.data);
            }
            reject(s);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    /*
     * --- Báo cáo dịch vụ ---
     */
    getBc01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc01,
        payload,
      }),
    getBc02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc02,
        payload,
      }),
    getBc02_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc02_1,
        payload,
      }),
    getBc03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc03,
        payload,
      }),
    getBc04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc04,
        payload,
      }),
    getBc05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc05,
        payload,
      }),
    getBc06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc06,
        payload,
      }),
    getBc07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc07,
        payload,
      }),
    getBc08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc08,
        payload,
      }),
    getBc09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc09,
        payload,
      }),
    getBc10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc10,
        payload,
      }),
    getBc10_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc10_1,
        payload,
      }),
    getBc10_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc10_2,
        payload,
      }),
    getBc10_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc10_3,
        payload,
      }),
    getBc11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc11,
        payload,
      }),
    getBc11_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc11_1,
        payload,
      }),
    getBc11_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc11_2,
        payload,
      }),
    getBc12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc12,
        payload,
      }),
    getBc14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc14,
        payload,
      }),
    getBc14_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc14_1,
        payload,
      }),
    getBc14_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc14_2,
        payload,
      }),
    getBc15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc15,
        payload,
      }),
    getBc16: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc16,
        payload,
      }),
    getBc17: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc17,
        payload,
      }),
    getBc18: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc18,
        payload,
      }),
    getBc19: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc19,
        payload,
      }),
    getBc20: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc20,
        payload,
      }),
    getBc21: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc21,
        payload,
      }),
    getBc22: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc22,
        payload,
      }),
    getBc23: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc23,
        payload,
      }),
    getBc24: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc24,
        payload,
      }),
    getBc25: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc25,
        payload,
      }),
    getBc26: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc26,
        payload,
      }),
    getBc27: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc27,
        payload,
      }),
    getBc28: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc28,
        payload,
      }),
    getBc29: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc29,
        payload,
      }),
    getBc29_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc29_2,
        payload,
      }),
    getBc30: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc30,
        payload,
      }),
    getBc31: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc31,
        payload,
      }),
    getBc32: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc32,
        payload,
      }),
    getBc33: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc33,
        payload,
      }),
    getBc34: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc34,
        payload,
      }),
    getBc35: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc35,
        payload,
      }),
    getBc36: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc36,
        payload,
      }),
    getBc37: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc37,
        payload,
      }),
    getBc38: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc38,
        payload,
      }),
    getBc39: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc39,
        payload,
      }),
    getBc40: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc40,
        payload,
      }),
    getBc41: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc41,
        payload,
      }),
    getBc42: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc42,
        payload,
      }),
    getBc43: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBc43,
        payload,
      }),
    /*
     * --- Báo cáo thuốc pha chế ---
     */
    getPc01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPc01,
        payload,
      }),
    getPc02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPc02,
        payload,
      }),
    getPc04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPc04,
        payload,
      }),
    /*
     * --- Báo cáo tài chính ---
     */
    getTc01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01,
        payload,
      }),
    getTc01_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_1,
        payload,
      }),
    getTc01_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_2,
        payload,
      }),
    getTc01_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_3,
        payload,
      }),
    getTc01_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_4,
        payload,
      }),
    getTc01_5: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_5,
        payload,
      }),
    getTc01_6: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_6,
        payload,
      }),
    getTc01_7: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_7,
        payload,
      }),
    getTc01_8: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_8,
        payload,
      }),
    getTc01_9: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_9,
        payload,
      }),
    getTc01_10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_10,
        payload,
      }),
    getTc01_11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_11,
        payload,
      }),
    getTc01_12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_12,
        payload,
      }),
    getTc01_13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_13,
        payload,
      }),
    getTc01_14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_14,
        payload,
      }),
    getTc01_15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_15,
        payload,
      }),
    getTc01_16: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc01_16,
        payload,
      }),
    getTc02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc02,
        payload,
      }),
    getTc02_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc02_1,
        payload,
      }),
    getTc02_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc02_2,
        payload,
      }),
    getTc03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc03,
        payload,
      }),
    getTc03_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc03_2,
        payload,
      }),
    getTc04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc04,
        payload,
      }),
    getTc05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc05,
        payload,
      }),
    getTc06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc06,
        payload,
      }),
    getTc06_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc06_1,
        payload,
      }),
    getTc06_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc06_2,
        payload,
      }),
    getTc06_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc06_4,
        payload,
      }),
    getTc06_5: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc06_5,
        payload,
      }),
    getTc08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc08,
        payload,
      }),
    getTc07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc07,
        payload,
      }),
    getTc09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc09,
        payload,
      }),
    getTc10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc10,
        payload,
      }),
    getTc11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc11,
        payload,
      }),
    getTc12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc12,
        payload,
      }),
    getTc12_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc12_1,
        payload,
      }),
    getTc13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc13,
        payload,
      }),
    getTc13_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc13_1,
        payload,
      }),
    getTc13_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc13_2,
        payload,
      }),
    getTc13_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc13_3,
        payload,
      }),
    getTc14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc14,
        payload,
      }),
    getTc14_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc14_1,
        payload,
      }),
    getTc15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc15,
        payload,
      }),
    getTc15_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc15_1,
        payload,
      }),
    getTc15_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc15_2,
        payload,
      }),
    getTc15_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc15_3,
        payload,
      }),
    getTc15_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc15_4,
        payload,
      }),
    getTc15_5: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc15_5,
        payload,
      }),
    getTc16: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc16,
        payload,
      }),
    getTc16_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc16_1,
        payload,
      }),
    getTc17: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc17,
        payload,
      }),
    getTc17_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc17_1,
        payload,
      }),
    getTc17_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc17_2,
        payload,
      }),
    getTc17_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc17_3,
        payload,
      }),
    getTc18: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc18,
        payload,
      }),
    getTc18_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc18_1,
        payload,
      }),
    getTc18_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc18_2,
        payload,
      }),
    getTc19: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc19,
        payload,
      }),
    getTc20: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc20,
        payload,
      }),
    getTc20_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc20_1,
        payload,
      }),
    getTc21: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc21,
        payload,
      }),
    getTc21_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc21_1,
        payload,
      }),
    getTc21_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc21_2,
        payload,
      }),
    getTc21_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc21_3,
        payload,
      }),
    getTc21_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc21_4,
        payload,
      }),
    getTc22: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc22,
        payload,
      }),
    getTc22_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc22_1,
        payload,
      }),
    getTc22_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc22_2,
        payload,
      }),
    getTc22_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc22_3,
        payload,
      }),
    getTc23: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc23,
        payload,
      }),
    getTc24: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc24,
        payload,
      }),
    getTc25: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc25,
        payload,
      }),
    getTc26: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc26,
        payload,
      }),
    getTc27: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc27,
        payload,
      }),
    getTc28: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc28,
        payload,
      }),
    getTc28_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc28_1,
        payload,
      }),
    getTc29: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc29,
        payload,
      }),
    getTc29_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc29_1,
        payload,
      }),
    getTc29_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc29_2,
        payload,
      }),
    getTc31: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc31,
        payload,
      }),
    getTc33: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc33,
        payload,
      }),
    getTc33_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc33_1,
        payload,
      }),
    getTc34: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc34,
        payload,
      }),
    getTc36: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc36,
        payload,
      }),
    getTc37: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc37,
        payload,
      }),
    getTc38: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc38,
        payload,
      }),
    getTc39: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc39,
        payload,
      }),
    getTc40: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc40,
        payload,
      }),
    getTc41: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc41,
        payload,
      }),
    getTc41_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc41_1,
        payload,
      }),
    getTc42: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42,
        payload,
      }),
    getTc42_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42_1,
        payload,
      }),
    getTc42_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42_2,
        payload,
      }),
    getTc42_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42_3,
        payload,
      }),
    getTc42_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42_4,
        payload,
      }),
    getTc42_5: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42_5,
        payload,
      }),
    getTc42_6: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc42_6,
        payload,
      }),
    getTc46: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc46,
        payload,
      }),
    getTc48: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc48,
        payload,
      }),
    getTc49: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc49,
        payload,
      }),
    getTc50: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc50,
        payload,
      }),
    getTc51: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc51,
        payload,
      }),
    getTc51_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc51_1,
        payload,
      }),
    getTc53: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc53,
        payload,
      }),
    getTc54: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc54,
        payload,
      }),
    getTc54_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc54_1,
        payload,
      }),
    getTc55: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc55,
        payload,
      }),
    getTc55_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc55_1,
        payload,
      }),
    getTc56: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc56,
        payload,
      }),
    getTc57: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc57,
        payload,
      }),
    getTc57_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc57_1,
        payload,
      }),
    getTc58: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc58,
        payload,
      }),
    getTc58_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc58_1,
        payload,
      }),
    getTc59: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc59,
        payload,
      }),
    getTc59_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc59_1,
        payload,
      }),
    getTc59_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc59_2,
        payload,
      }),
    getTc59_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc59_3,
        payload,
      }),
    getTc60: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc60,
        payload,
      }),
    getTc61: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc61,
        payload,
      }),
    getTc62: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc62,
        payload,
      }),
    getTc63: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc63,
        payload,
      }),
    getTc63_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc63_1,
        payload,
      }),
    getTc64: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc64,
        payload,
      }),
    getTc64_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc64_1,
        payload,
      }),
    getTc64_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc64_2,
        payload,
      }),
    getTc66: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc66,
        payload,
      }),
    getTc67: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc67,
        payload,
      }),
    getTc67_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc67_1,
        payload,
      }),
    getTc67_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc67_2,
        payload,
      }),
    getTc68: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc68,
        payload,
      }),
    getTc69: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc69,
        payload,
      }),
    getTc69_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc69_1,
        payload,
      }),
    getTc69_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc69_2,
        payload,
      }),
    getTc70: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc70,
        payload,
      }),
    getTc71: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc71,
        payload,
      }),
    getTc72: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc72,
        payload,
      }),
    getTc73: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc73,
        payload,
      }),
    getTc73_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc73_1,
        payload,
      }),
    getTc74: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc74,
        payload,
      }),
    getTc75: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc75,
        payload,
      }),
    getTc75_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc75_1,
        payload,
      }),
    getTc76: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc76,
        payload,
      }),
    getTc77: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc77,
        payload,
      }),
    getTc78: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc78,
        payload,
      }),
    getTc79: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc79,
        payload,
      }),
    getTc79_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc79_1,
        payload,
      }),
    getTc80: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc80,
        payload,
      }),
    getTc81: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc81,
        payload,
      }),
    getTc81_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc81_1,
        payload,
      }),
    getTc80_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc80_1,
        payload,
      }),
    getTc80_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc80_2,
        payload,
      }),
    getTc80_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc80_3,
        payload,
      }),
    getTc80_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc80_4,
        payload,
      }),
    getTc82: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc82,
        payload,
      }),
    getTc82_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc82_1,
        payload,
      }),
    getTc84: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc84,
        payload,
      }),
    getTc85: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTc85,
        payload,
      }),
    /*
     * --- Báo cáo phòng khám ---
     */
    getBcDsNbKhamChiTiet: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getBcDsNbKhamChiTiet,
        payload,
      }),
    getPk02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk02,
        payload,
      }),
    getPk03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk03,
        payload,
      }),
    getPk04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk04,
        payload,
      }),
    getPk06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk06,
        payload,
      }),
    getPk07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk07,
        payload,
      }),
    getPk08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk08,
        payload,
      }),
    getPk09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk09,
        payload,
      }),
    getPk10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk10,
        payload,
      }),
    getPk11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk11,
        payload,
      }),
    getPk12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk12,
        payload,
      }),
    getPk13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk13,
        payload,
      }),
    getPk14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk14,
        payload,
      }),
    getPk15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPk15,
        payload,
      }),
    /*
     * --- Báo cáo kho ---
     */
    getk01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK01,
        payload,
      }),
    getk01_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK01_1,
        payload,
      }),
    getk01_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK01_2,
        payload,
      }),
    getK01_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK01_3,
        payload,
      }),
    getK02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02,
        payload,
      }),
    getK02_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_1,
        payload,
      }),
    getK02_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_2,
        payload,
      }),
    getK02_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_3,
        payload,
      }),
    getK02_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_4,
        payload,
      }),
    getK02_5: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_5,
        payload,
      }),
    getK02_6: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_6,
        payload,
      }),
    getK20_7: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_7,
        payload,
      }),

    getK02_8: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_8,
        payload,
      }),
    getK02_10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK02_10,
        payload,
      }),
    getK03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK03,
        payload,
      }),
    getK04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK04,
        payload,
      }),
    getK04_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK04_1,
        payload,
      }),
    getK04_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK04_2,
        payload,
      }),
    getK04_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK04_3,
        payload,
      }),
    getK04_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK04_4,
        payload,
      }),
    getK05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK05,
        payload,
      }),
    getK05_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK05_1,
        payload,
      }),
    getK07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK07,
        payload,
      }),
    getK07_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK07_1,
        payload,
      }),
    getK08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK08,
        payload,
      }),
    getK10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK10,
        payload,
      }),
    getK11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK11,
        payload,
      }),
    getK12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK12,
        payload,
      }),
    getK13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK13,
        payload,
      }),
    getK14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK14,
        payload,
      }),
    getK14_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK14_1,
        payload,
      }),
    getK14_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK14_2,
        payload,
      }),
    getK15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK15,
        payload,
      }),
    getKvt04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKvt04,
        payload,
      }),
    getK20: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK20,
        payload,
      }),
    getK20_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK20_1,
        payload,
      }),
    getK20_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK20_2,
        payload,
      }),
    getK20_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK20_3,
        payload,
      }),
    getK21: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK21,
        payload,
      }),
    getK22: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK22,
        payload,
      }),
    getK23: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK23,
        payload,
      }),
    getK24: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK24,
        payload,
      }),
    getK25: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK25,
        payload,
      }),
    getK26: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK26,
        payload,
      }),
    getK27: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK27,
        payload,
      }),
    getK28: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK28,
        payload,
      }),
    getK29: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK29,
        payload,
      }),
    getK30: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK30,
        payload,
      }),
    getK31: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK31,
        payload,
      }),
    getK32: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK32,
        payload,
      }),
    getK33: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK33,
        payload,
      }),
    getK34: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK34,
        payload,
      }),
    getK35: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK35,
        payload,
      }),
    getK36: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK36,
        payload,
      }),
    getK37: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK37,
        payload,
      }),
    getK38: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK38,
        payload,
      }),
    getK39: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK39,
        payload,
      }),
    getK40: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK40,
        payload,
      }),
    getK42: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK42,
        payload,
      }),
    getK43: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK43,
        payload,
      }),
    getK44: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK44,
        payload,
      }),
    getK45: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK45,
        payload,
      }),
    getK46: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK46,
        payload,
      }),
    getK47: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK47,
        payload,
      }),
    getK48: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK48,
        payload,
      }),
    getK49: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK49,
        payload,
      }),
    getK50: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK50,
        payload,
      }),
    getK51: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK51,
        payload,
      }),
    getK52: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK52,
        payload,
      }),
    getK53: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK53,
        payload,
      }),
    getK54: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK54,
        payload,
      }),
    getK55: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK55,
        payload,
      }),
    getK56: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK56,
        payload,
      }),
    getK57: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK57,
        payload,
      }),
    getK58: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK58,
        payload,
      }),
    getK58_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK58_1,
        payload,
      }),
    getK59: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK59,
        payload,
      }),
    getK60: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK60,
        payload,
      }),
    getK61: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK61,
        payload,
      }),
    getK62: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK62,
        payload,
      }),
    getK63: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK63,
        payload,
      }),
    getK64: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK64,
        payload,
      }),
    getK65: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK65,
        payload,
      }),
    getK66: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK66,
        payload,
      }),
    getK67: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK67,
        payload,
      }),
    getK68: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK68,
        payload,
      }),
    getK69: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK69,
        payload,
      }),
    getK70: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK70,
        payload,
      }),
    getK71: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK71,
        payload,
      }),
    getK72: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK72,
        payload,
      }),
    getK73: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK73,
        payload,
      }),
    getK74: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK74,
        payload,
      }),
    getK75: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK75,
        payload,
      }),
    getK76: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK76,
        payload,
      }),
    getK77: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK77,
        payload,
      }),
    getK78: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK78,
        payload,
      }),
    getK79: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK79,
        payload,
      }),
    getK80: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK80,
        payload,
      }),
    getK83: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK83,
        payload,
      }),
    getK82: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK82,
        payload,
      }),
    getK84: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getK84,
        payload,
      }),
    /*
     * --- Báo cáo nhà thuốc ---
     */
    getKnt01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt01,
        payload,
      }),
    getKnt02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt02,
        payload,
      }),
    getKnt03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt03,
        payload,
      }),
    getKnt03_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt03_1,
        payload,
      }),
    getKnt04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt04,
        payload,
      }),
    getKnt05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt05,
        payload,
      }),
    getKnt06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt06,
        payload,
      }),
    getKnt07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt07,
        payload,
      }),
    getKnt08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt08,
        payload,
      }),
    getKnt08_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt08_1,
        payload,
      }),
    getKnt10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt10,
        payload,
      }),
    getKnt11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt11,
        payload,
      }),
    getKnt12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt12,
        payload,
      }),
    getKnt13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt13,
        payload,
      }),
    getKnt14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt14,
        payload,
      }),
    getKnt15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt15,
        payload,
      }),
    getKnt15_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt15_1,
        payload,
      }),
    getKnt16: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt16,
        payload,
      }),
    getKnt17: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt17,
        payload,
      }),
    getKnt18: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt18,
        payload,
      }),
    getKnt19: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt19,
        payload,
      }),
    getKnt20: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt20,
        payload,
      }),
    getKnt21: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt21,
        payload,
      }),
    getKnt22: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt22,
        payload,
      }),
    getKnt23: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt23,
        payload,
      }),
    getKnt24: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt24,
        payload,
      }),
    getKnt25: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt25,
        payload,
      }),
    getKnt26: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKnt26,
        payload,
      }),
    /*
     * --- Báo cáo kho vật tư/ hóa chất ---
     */
    getKvt02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKvt02,
        payload,
      }),
    getKvt03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKvt03,
        payload,
      }),
    getKvt05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKvt05,
        payload,
      }),
    getKvt06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKvt06,
        payload,
      }),
    getKvt07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKvt07,
        payload,
      }),
    /*
     * --- Báo cáo khám sức khỏe ---
     */
    getKsk01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk01,
        payload,
      }),
    getKsk01_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk01_1,
        payload,
      }),
    getKsk02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk02,
        payload,
      }),
    getKsk04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk04,
        payload,
      }),
    getKsk05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk05,
        payload,
      }),
    getKsk12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk12,
        payload,
      }),
    getKsk13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk13,
        payload,
      }),
    getKsk15: ({ nbDotDieuTriId, dsLoai }, state) => {
      return new Promise((resolve, reject) => {
        Promise.allSettled([
          ...Object.values(LOAI_BC_THONG_TIN_NB)
            .filter((x) => ![60].includes(x))
            .filter((x) => dsLoai.includes(x))
            .map((loai) =>
              nbDotDieuTriProvider.getPhieuThongTinNb({
                nbDotDieuTriId,
                loai,
              })
            ),
          ...(dsLoai.includes(1)
            ? [
                nbDvXetNghiemProvider.getPhieuKetQua({
                  nbDotDieuTriId: nbDotDieuTriId,
                }),
              ]
            : []),
          ...(dsLoai.includes(2)
            ? [
                nbDvCdhaProvider.getPhieuKetQua({
                  nbDotDieuTriId,
                }),
              ]
            : []),
          ...Object.values(LOAI_BC_THONG_TIN_NB)
            .filter((x) => [60].includes(x)) //mục đích để chuyển bc_chup_chieu_x_quang xuống cuối
            .filter((x) => dsLoai.includes(x))
            .map((loai) =>
              nbDotDieuTriProvider.getPhieuThongTinNb({
                nbDotDieuTriId,
                loai,
              })
            ),
        ])
          .then(async (resAll) => {
            //xử lý lọc phiếu kết quả xét nghiệm / cdha theo điều kiện lọc lis-pacs
            [dsLoai.indexOf(1), dsLoai.indexOf(2)]
              .filter((x) => x > -1)
              .forEach((index) => {
                if (resAll[index].status === "fulfilled") {
                  resAll[index].value.data = locPhieuLisPacs(
                    resAll[index].value.data,
                    { allData: true, isLocPhieu: true, isAllHisLisPacs: true }
                  );
                }
              });

            const filesList = resAll.filter((x) => x.status === "fulfilled");
            if (filesList.length > 0) {
              const mergedPdfFile = filesList.map((item) => item?.value?.data);

              resolve(mergedPdfFile);
            } else {
              reject(resAll);
            }
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    getKsk16: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk16,
        payload,
      }),
    getKsk17: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk17,
        payload,
      }),
    getKsk18: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk18,
        payload,
      }),
    getKsk19: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk19,
        payload,
      }),
    getKsk20: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk20,
        payload,
      }),
    getKsk20_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk20_1,
        payload,
      }),
    getKsk21: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsk21,
        payload,
      }),
    /*
     * --- Báo cáo gói liệu trình ---
     */
    getG01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG01,
        payload,
      }),
    getG02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG02,
        payload,
      }),
    getG03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG03,
        payload,
      }),
    getG04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG04,
        payload,
      }),
    getG05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG05,
        payload,
      }),
    getG06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG06,
        payload,
      }),
    getG07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getG07,
        payload,
      }),
    /*
     * --- Báo cáo kế hoạch tổng hợp ---
     */
    getKhth01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth01,
        payload,
      }),
    getKhth02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth02,
        payload,
      }),
    getKhth03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth03,
        payload,
      }),
    getKhth04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth04,
        payload,
      }),
    getKhth05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth05,
        payload,
      }),
    getKhth06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth06,
        payload,
      }),
    getKhth07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth07,
        payload,
      }),
    getKhth08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth08,
        payload,
      }),
    getKhth08_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth08_1,
        payload,
      }),
    getKhth08_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth08_2,
        payload,
      }),
    getKhth09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth09,
        payload,
      }),
    getKhth10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth10,
        payload,
      }),
    getKhth11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth11,
        payload,
      }),
    getKhth12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth12,
        payload,
      }),
    getKhth12_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth12_1,
        payload,
      }),
    getKhth13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth13,
        payload,
      }),
    getKhth14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth14,
        payload,
      }),
    getKhth15: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth15,
        payload,
      }),
    getKhth16: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth16,
        payload,
      }),
    getKhth17: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth17,
        payload,
      }),
    getKhth18: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth18,
        payload,
      }),
    getKhth19: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth19,
        payload,
      }),
    getKhth20: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth20,
        payload,
      }),
    getKhth21: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth21,
        payload,
      }),
    getKhth22: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth22,
        payload,
      }),
    getKhth23: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth23,
        payload,
      }),
    getKhth24: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth24,
        payload,
      }),
    getKhth25: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth25,
        payload,
      }),
    getKhth26: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth26,
        payload,
      }),
    getKhth27: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth27,
        payload,
      }),
    getKhth29: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth29,
        payload,
      }),
    getKhth30: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth30,
        payload,
      }),
    getKhth31: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth31,
        payload,
      }),
    getKhth33: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth33,
        payload,
      }),
    getKhth34: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth34,
        payload,
      }),
    getKhth35: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth35,
        payload,
      }),
    getKhth37: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth37,
        payload,
      }),
    getKhth38: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth38,
        payload,
      }),
    getKhth40: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth40,
        payload,
      }),
    getKhth41: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth41,
        payload,
      }),
    getKhth42: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth42,
        payload,
      }),
    getKhth43: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth43,
        payload,
      }),
    getKhth44: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth44,
        payload,
      }),
    getKhth45: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth45,
        payload,
      }),
    getKhth46: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth46,
        payload,
      }),
    getKhth47: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth47,
        payload,
      }),
    getKhth48: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth48,
        payload,
      }),
    getKhth49: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth49,
        payload,
      }),
    getKhth57: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKhth57,
        payload,
      }),
    /*
     * --- Báo cáo phẫu thuật, thủ thuật ---
     */
    getPttt01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt01,
        payload,
      }),
    getPttt02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt02,
        payload,
      }),
    getPttt03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt03,
        payload,
      }),
    getPttt04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04,
        payload,
      }),
    getPttt04_1: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_1,
        payload,
      }),
    getPttt04_2: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_2,
        payload,
      }),
    getPttt04_3: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_3,
        payload,
      }),
    getPttt04_4: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_4,
        payload,
      }),
    getPttt04_5: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_5,
        payload,
      }),
    getPttt04_7: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_7,
        payload,
      }),
    getPttt04_8: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_8,
        payload,
      }),
    getPttt04_9: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt04_9,
        payload,
      }),
    getPttt05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt05,
        payload,
      }),
    getPttt06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt06,
        payload,
      }),
    getPttt08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt08,
        payload,
      }),
    getPttt07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt07,
        payload,
      }),
    getPttt09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt09,
        payload,
      }),
    getPttt10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getPttt10,
        payload,
      }),
    /*
     * --- Báo cáo suất ăn ---
     */
    getDd01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDd01,
        payload,
      }),
    getDd03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDd03,
        payload,
      }),
    getDd04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDd04,
        payload,
      }),
    /*
     * --- Báo cáo tiêm chủng ---
     */
    getTiem01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTiem01,
        payload,
      }),
    getTiem02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTiem02,
        payload,
      }),
    getTiem03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTiem03,
        payload,
      }),
    getTiem04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTiem04,
        payload,
      }),
    getTiem05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getTiem05,
        payload,
      }),
    /*
     * --- Báo cáo kiểm soát nhiễm khuẩn ---
     */
    getKsnk_01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsnk_01,
        payload,
      }),
    getKsnk_02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKsnk_02,
        payload,
      }),
    /*
     * --- Báo cáo lao ---
     */
    getLao01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getLao01,
        payload,
      }),
    /*
     * --- Báo cáo Kho dinh dưỡng ---
     */
    getKdd01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd01,
        payload,
      }),
    getKdd02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd02,
        payload,
      }),
    getKdd03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd03,
        payload,
      }),
    getKdd04: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd04,
        payload,
      }),
    getKdd05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd05,
        payload,
      }),
    getKdd06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd06,
        payload,
      }),
    getKdd09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd09,
        payload,
      }),
    getKdd10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd10,
        payload,
      }),
    getKdd11: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd11,
        payload,
      }),
    getKdd12: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd12,
        payload,
      }),
    getKdd07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd07,
        payload,
      }),
    getKdd08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd08,
        payload,
      }),
    getKdd13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd13,
        payload,
      }),
    getKdd14: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getKdd14,
        payload,
      }),
    /*
     * --- Báo cáo quản trị ---
     */
    getQt01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getQt01,
        payload,
      }),
    getQt02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getQt02,
        payload,
      }),
    getQt03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getQt03,
        payload,
      }),
    getQtbh13: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getQtbh13,
        payload,
      }),
    /*
     * --- Báo cáo duyệt dược lâm sàng ---
     */
    getDdls05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDdls05,
        payload,
      }),
    getDdls06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDdls06,
        payload,
      }),
    getDdls07: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDdls07,
        payload,
      }),
    getDdls08: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDdls08,
        payload,
      }),
    getDdls09: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDdls09,
        payload,
      }),
    getDdls10: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getDdls10,
        payload,
      }),
    getSldd01: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getSldd01,
        payload,
      }),
    getSldd02: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getSldd02,
        payload,
      }),
    getSldd03: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getSldd03,
        payload,
      }),
    getSldd05: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getSldd05,
        payload,
      }),
    getSldd06: (payload, state) =>
      dispatch.baoCaoDaIn.getBaoCao({
        callApi: baoCaoDaInProvider.getSldd06,
        payload,
      }),
  }),
};
