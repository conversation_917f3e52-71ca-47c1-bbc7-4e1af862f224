import { cloneDeep } from "lodash";
import nbKhamKskLaiXeProvider from "data-access/nb-kham-ksk-lai-xe-provider";
import { message } from "antd";
import { combineSort } from "utils";
import { t } from "i18next";
import isofhToolProvider from "data-access/isofh-tool-provider";
import stringUtils from "mainam-react-native-string-utils";
import fileUtils from "utils/file-utils";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";

const initData = {
  listData: [],
  totalElements: 0,
  page: 0,
  dataSearch: {},
  dataSortColumn: {},

  selectedRowKeys: [],
};

export default {
  state: cloneDeep(initData),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initData), ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }) => {
      dispatch.giayKskLaiXe.updateData({
        page: 0,
        ...rest,
      });
      dispatch.giayKskLaiXe.onSearch({ ...rest });
    },

    searchGiayKskLaiXeByParams: ({ page = 0, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...state.giayKskLaiXe.dataSearch,
          ...payload,
        },
      };

      dispatch.giayKskLaiXe.updateData({
        page: 0,
        ...obj,
      });
      dispatch.giayKskLaiXe.onSearch({ ...obj });
    },

    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.giayKskLaiXe.dataSortColumn,
        ...payload,
      };
      dispatch.giayKskLaiXe.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.giayKskLaiXe.onSearch({
        page: 0,
        dataSortColumn,
      });
    },

    onSearch: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.giayKskLaiXe.updateData(newState);
      let size = payload.size || state.giayKskLaiXe.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.giayKskLaiXe.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.giayKskLaiXe.dataSearch || {};

      nbKhamKskLaiXeProvider
        .searchAll({ page, size, sort, ...dataSearch })
        .then((s) => {
          dispatch.giayKskLaiXe.updateData({
            listData: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.giayKskLaiXe.updateData({
            listData: [],
            isLoading: false,
          });
        });
    },

    dayGiayKskLaiXeById: (id) => {
      return new Promise((resolve, reject) => {
        nbKhamKskLaiXeProvider
          .dayGiayKskLaiXe(id)
          .then((s) => {
            message.success(t("giayDayCong.message.dayGiayKskLaiXeThanhCong"));
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    dayGiayKskLaiXe: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbKhamKskLaiXeProvider
          .dayGiayKskLaiXeHangLoat(payload)
          .then((s) => {
            message.success(t("giayDayCong.message.dayGiayKskLaiXeThanhCong"));
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    huyGiayKskLaiXeById: (id) => {
      return new Promise((resolve, reject) => {
        nbKhamKskLaiXeProvider
          .huyGiayKskLaiXe(id)
          .then((s) => {
            message.success(t("giayDayCong.message.huyGiayKskLaiXeThanhCong"));
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    kyHangLoat: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbKhamKskLaiXeProvider
          .kyHangLoat(payload)
          .then((s) => {
            const dsKyToken = (s?.data || []).filter(
              (x) => x.duLieu && x.code === 0
            );
            const erros = (s?.data || []).filter((x) => x.code !== 0);
            let messageErros = "";
            if (erros?.length) {
              messageErros = erros.map((item) => item.message)?.join(", ");
            }
            if (dsKyToken?.length) {
              async function kyToKen() {
                for (const item of dsKyToken) {
                  const { theChuKy, fileTruocKy, chuKySo, viTri, id } =
                    item.duLieu;
                  const base64 = await isofhToolProvider.kyXml({
                    xmlBase64String: fileTruocKy,
                    theChuKy,
                  });
                  if (base64) {
                    console.log("base64", base64);
                    const file = fileUtils.base64ToFile(
                      base64,
                      stringUtils.guid() + ".xml",
                      "application/xml"
                    );
                    await danhSachPhieuChoKyProvider.token({
                      file,
                      viTri,
                      chuKySo,
                      id,
                    });
                  }
                  message.success(
                    t("giayDayCong.message.kyGiayKSKLaiXeThanhCong")
                  );
                }
              }
              kyToKen();
            } else if (!erros?.length) {
              message.success(
                t("giayDayCong.message.kyGiayKSKLaiXeThanhCong")
              );
            }
            resolve({ data: s?.data, messageError: messageErros });
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
