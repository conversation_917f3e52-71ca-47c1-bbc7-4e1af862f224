import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";
import { message } from "antd";
import { PAGE_SIZE, PAGE_DEFAULT } from "constants/index";
import { combineSort } from "utils";
import { t } from "i18next";

export default {
  state: {
    listData: [],
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataEditDefault: {},
    dataSortColumn: {},
    dataSearch: {},
    chiTiet: true,
    // dsTrangThai: [10], // trạng thái tạo mới , mặc định
    dsKhoId: [],
    selectionIdx: [],
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }, state) => {
      dispatch.danhSachPhieuChoKy.updateData({
        page: 0,
        dataSearch: { ...state?.danhSachPhieuChoKy?.dataSearch, ...dataSearch },
        ...rest,
      });
      dispatch.danhSachPhieuChoKy.getList({ ...rest });
    },
    getList: (
      { page = 0, dataSortColumn, isSelectAll = false, ...payload },
      state
    ) => {
      let size = payload?.size || state.danhSachPhieuChoKy.size || 10;
      dispatch.danhSachPhieuChoKy.updateData({
        isLoading: true,
      });
      const sort = combineSort(
        dataSortColumn || state.danhSachPhieuChoKy.dataSortColumn || {}
      );
      // const dataSearch = payload.dataSearch || state.danhSachPhieuChoKy.dataSearch || {};
      const dataSearch = {
        ...state.danhSachPhieuChoKy.dataSearch,
        ...payload,
      };
      danhSachPhieuChoKyProvider
        .search({
          page,
          size,
          sort,
          ...dataSearch,
        })
        .then((s) => {
          const listData = (s?.data || []).map((item, index) => {
            item.index = page * size + index + 1;
            return item;
          });

          dispatch.danhSachPhieuChoKy.updateData({
            listData,
            selectionIdx: isSelectAll
              ? (listData || []).map((x) => x.index)
              : [],
            totalElements: s?.totalElements || 0,
            page,
            size,
            isSelectAll,
            first: s?.first,
            last: s?.last,
            isLoading: false,
          });
        })
        .catch((e) => {
          reject(e);
          dispatch.danhSachPhieuChoKy.updateData({
            isLoading: false,
          });
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        });
    },
    searchByParams: ({ page = 0, ...payload }, state) => {
      const obj = {
        ...payload,
      };
      dispatch.danhSachPhieuChoKy.updateData({
        page: 0,
        dataSearch: { ...state?.danhSachPhieuChoKy?.dataSearch, ...payload },
        ...obj,
      });
      dispatch.danhSachPhieuChoKy.getList({ ...obj });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.danhSachPhieuChoKy.dataSortColumn,
        ...payload,
      };
      dispatch.danhSachPhieuChoKy.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.danhSachPhieuChoKy.getList({
        page: 0,
        dataSortColumn,
      });
    },
    getListPhieu: (payload, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .searchPhieu(payload)
          .then((s) => {
            dispatch.danhSachPhieuChoKy.updateData({
              listDataPhieu: s?.data,
            });
            return resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getById: ({ id, isAlwayResolve, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        danhSachPhieuChoKyProvider
          .getById(id)
          .then((s) => {
            resolve([{ ...s.data, lichSuKyId: s.data.id }]);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            if (isAlwayResolve) {
              resolve(e);
            } else {
              reject(e);
            }
          });
      });
    },
  }),
};
