import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import {
  DANH_SACH_PHIEU_CHO_KY,
  NB_79A_XML1,
  NB_CHO_TAO_HO_SO,
} from "client/api";

export default {
  search: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_79A_XML1}${NB_CHO_TAO_HO_SO}`, {
            page: page + "",
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  searchById: ({ page = 0, sort, size = 10, id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_79A_XML1}${NB_CHO_TAO_HO_SO}/${id}`,
            {
              ...payload,
            }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  searchWithPhieuMoiNhat: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_79A_XML1}${NB_CHO_TAO_HO_SO}`, {
            phieuMoiNhat: true,
            page: page + "",
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  post: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_79A_XML1}`, payload)
        // .post(
        //     combineUrlParams(`${dataPath}${NB_79A_XML1}${NB_CHO_TAO_HO_SO}/don-thuoc`, {
        //         // page: page + "",
        //         // sort,
        //         // size,
        //         payload,
        //     }))
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  sign: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_79A_XML1}${NB_CHO_TAO_HO_SO}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kiemTraThoiGianQuyetToan: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_79A_XML1}/kiem-tra/thoi-gian-quyet-toan`,
            {
              ...payload,
            }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
