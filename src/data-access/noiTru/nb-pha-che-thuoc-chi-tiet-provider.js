import { NB_PHA_CHE_THUOC_CHI_TIET } from "client/api";
import apiBase from "../api-base";
import { client, dataPath } from "client/request";

export default {
  ...apiBase.init({ API: NB_PHA_CHE_THUOC_CHI_TIET }),
  themPhaCheThuocChiTiet: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_PHA_CHE_THUOC_CHI_TIET}/batch`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  capNhatPhaCheThuocChiTiet: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_PHA_CHE_THUOC_CHI_TIET}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};
