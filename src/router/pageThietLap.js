import { ROLES } from "constants/index";
import { Page } from "pages/constants";
import React from "react";
const SubPageThietLap = React.lazy(() => import("pages/home/<USER>/ThietLap"));
const ThietLap = React.lazy(() => import("pages/danhMuc2/thietLap"));
const ThietLapPhieuIn = React.lazy(() =>
  import("pages/thietLap/thietLapPhieuIn")
);
const TachGopPhieuXN = React.lazy(() =>
  import("pages/thietLap/tachGopPhieuXN")
);
const TachGopPhieuDVKT = React.lazy(() =>
  import("pages/thietLap/tachGopPhieuDVKT")
);
const TichDiem = React.lazy(() => import("pages/thietLap/thietLapTichDiem"));
const ThongSoHangDoi = React.lazy(() =>
  import("pages/danhMuc2/thongSoHangDoi")
);
const ThietLapPhieuLinhTra = React.lazy(() =>
  import("pages/thietLap/thietLapPhieuLinhTra")
);
const ThietLapChonGiuong = React.lazy(() =>
  import("pages/danhMuc2/thietLapChonGiuong")
);
const ThietLapPowerBI = React.lazy(() => import("pages/danhMuc2/powerBi"));
const ThietLapDieuKienChuyenKhoaRaVien = React.lazy(() =>
  import("pages/thietLap/thietLapDieuKienChuyenKhoaRaVien")
);
const LuuTruBenhAn = React.lazy(() =>
  import("pages/thietLap/thietLapLuuTruBenhAn")
);
const ThietLapDoiMaNguoiBenh = React.lazy(() =>
  import("pages/thietLap/thietLapDoiMaNguoiBenh")
);
const ThietLapDoiMaHoSo = React.lazy(() =>
  import("pages/thietLap/thietLapDoiMaHoSo")
);
const LienIn = React.lazy(() => import("pages/danhMuc2/lienIn"));
// const ThietLapPhongThucHien = React.lazy(() =>
//   import("pages/danhMuc2/thietLapPhongThucHien")
// );
const ThietLapPhongThucHien = React.lazy(() =>
  import("pages/danhMucn1/thietLapPhongThucHien")
);
const ThietLapGiaTriCSS = React.lazy(() =>
  import("pages/thietLap/thietLapGiaTriCSS")
);
const ThietLapNhomDvBaoCao = React.lazy(() =>
  import("pages/thietLap/thietLapNhomDvBaoCao")
);
const HuongDieuTri = React.lazy(() => import("pages/danhMucn1/huongDieuTri"));
const ThietLapDongMoPhongThucHien = React.lazy(() =>
  import("pages/thietLap/thietLapDongMoPhongThucHien")
);
const ThietLapLoaiDichVuNhomChiPhi = React.lazy(() =>
  import("pages/danhMuc2/thietLapLoaiDichVuNhomChiPhi")
);
const ThietLapSinhSoThuTuThuNgan = React.lazy(() =>
  import("pages/thietLap/thietLapSinhSoThuNgan")
);
const ThietLapTuDongTatToan = React.lazy(() =>
  import("pages/thietLap/thietLapTuDongTatToan")
);

export default {
  subPageThietLap: {
    component: Page(SubPageThietLap, []),
    accessRoles: [],
    path: "/thiet-lap",
    exact: true,
  },
  thietLapChung: {
    component: Page(ThietLap, [ROLES.THIET_LAP_CHUNG]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-chung",
    exact: true,
  },
  thietLapPhieuIn: {
    component: Page(ThietLapPhieuIn, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_PHIEU_IN,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-phieu-in",
    exact: true,
  },
  tachGopPhieuXN: {
    component: Page(TachGopPhieuXN, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_TACH_GOP_PHIEU_XET_NGHIEM,
    ]),
    accessRoles: [],
    path: "/thiet-lap/tach-gop-phieu-xet-nghiem",
    exact: true,
  },
  tachGopPhieuDVKT: {
    component: Page(TachGopPhieuDVKT, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_TACH_GOP_PHIEU_DKVT,
    ]),
    accessRoles: [],
    path: "/thiet-lap/tach-gop-phieu-dvkt",
    exact: true,
  },
  thietLapTichDiem: {
    component: Page(TichDiem, [ROLES["THIET_LAP"].XEM_THIET_LAP_TICH_DIEM]),
    accessRoles: [],
    path: "/thiet-lap/tich-diem",
    exact: true,
  },
  thongSoHangDoi: {
    component: Page(ThongSoHangDoi, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_THONG_SO_HANG_DOI,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thong-so-hang-doi",
    exact: true,
  },
  thietLapPhieuLinhTra: {
    component: Page(ThietLapPhieuLinhTra, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_PHIEU_LINH_TRA,
    ]),
    accessRoles: [],
    path: "/thiet-lap/phieu-linh-tra",
    exact: true,
  },
  thietLapChonGiuong: {
    component: Page(ThietLapChonGiuong, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_CHON_GIUONG,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-chon-giuong",
    exact: true,
  },
  thietLapPowerBI: {
    component: Page(ThietLapPowerBI, [ROLES["DANH_MUC"].POWERBI]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-nhung-link",
    exact: true,
  },
  thietLapDieuKienChuyenKhoaRaVien: {
    component: Page(ThietLapDieuKienChuyenKhoaRaVien, [
      ROLES["DANH_MUC"].DIEU_KIEN_CHUYEN_KHOA_RA_VIEN,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-dieu-kien-chuyen-khoa-ra-vien",
    exact: true,
  },
  thietLapLuuTruBenhAn: {
    component: Page(LuuTruBenhAn, [ROLES["THIET_LAP"].XEM_LUU_TRU_BA]),
    accessRoles: [],
    path: "/thiet-lap/luu-tru-benh-an",
    exact: true,
  },
  thietLapTuDongTatToan: {
    component: Page(ThietLapTuDongTatToan, [
      ROLES["THIET_LAP"].XEM_TU_DONG_TAT_TOAN,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-tu-dong-tat-toan",
    exact: true,
  },
  thietLapDoiMaNguoiBenh: {
    component: Page(ThietLapDoiMaNguoiBenh, [ROLES["THIET_LAP"].XEM_DOI_MA_NB]),
    accessRoles: [],
    path: "/thiet-lap/doi-ma-nguoi-benh",
    exact: true,
  },
  thietLapDoiMaHoSo: {
    component: Page(ThietLapDoiMaHoSo, [ROLES["THIET_LAP"].XEM_DOI_MA_HO_SO]),
    accessRoles: [],
    path: "/thiet-lap/doi-ma-ho-so",
    exact: true,
  },
  lienIn: {
    component: Page(LienIn, [ROLES["DANH_MUC"].LIEN_IN]),
    accessRoles: [ROLES["DANH_MUC"].LIEN_IN],
    path: "/thiet-lap/lien-in",
    exact: true,
  },
  thietLapGiaTriCSS: {
    component: Page(ThietLapGiaTriCSS, [
      ROLES["DANH_MUC"].THIET_LAP_GIA_TRI_CSS,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-gia-tri-css",
    exact: true,
  },
  thietLapNhomDvBaoCao: {
    component: Page(ThietLapNhomDvBaoCao, []),
    accessRoles: [],
    path: "/thiet-lap/nhom-dv-bao-cao",
    exact: true,
  },
  HuongDieuTri: {
    component: Page(HuongDieuTri, [ROLES["THIET_LAP"].HUONG_DIEU_TRI]),
    accessRoles: [],
    path: "/thiet-lap/huong-dieu-tri",
    exact: true,
  },
  ThietLapPhongThucHien: {
    component: Page(ThietLapPhongThucHien, [
      ROLES["THIET_LAP"].THIET_LAP_PHONG_THUC_HIEN,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-phong-thuc-hien",
    exact: true,
  },
  ThietLapDongMoPhongThucHien: {
    component: Page(ThietLapDongMoPhongThucHien, [
      ROLES["THIET_LAP"].THIET_LAP_DONG_MO_PHONG_THUC_HIEN,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-dong-mo-phong-thuc-hien-va-dv-cdha-tdcn",
    exact: true,
  },
  thietLapLoaiDichVuNhomChiPhi: {
    component: Page(ThietLapLoaiDichVuNhomChiPhi, [
      ROLES["THIET_LAP"].THIET_LAP_LOAI_DICH_VU_NHOM_CHI_PHI,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-loai-dich-vu-nhom-chi-phi",
    exact: true,
  },
  thietLapSinhSoThuTuThuNgan: {
    component: Page(ThietLapSinhSoThuTuThuNgan, [
      ROLES["THIET_LAP"].XEM_THIET_LAP_SINH_SO_THU_TU_THU_NGAN,
    ]),
    accessRoles: [],
    path: "/thiet-lap/thiet-lap-sinh-so-thu-tu-thu-ngan",
    exact: true,
  },
};
